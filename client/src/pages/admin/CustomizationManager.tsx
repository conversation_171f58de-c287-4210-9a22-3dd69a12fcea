import { useState, useEffect } from "react";
import { motion } from "framer-motion";
import AdminLayout from "./AdminLayout";
import { CustomizationGroup, CustomizationOption } from "@shared/schema";
import {
  getCustomizationGroups,
  createCustomizationGroup,
  updateCustomizationGroup,
  deleteCustomizationGroup,
  getCustomizationOptions,
  createCustomizationOption,
  updateCustomizationOption,
  deleteCustomizationOption,
} from "@/api/customizationApi";
import { useToast } from "@/hooks/use-toast";
import { Pencil, Trash2, Plus, Settings, ChefHat } from "lucide-react";

interface CustomizationGroupWithOptions extends CustomizationGroup {
  options: CustomizationOption[];
}

const CustomizationManager = () => {
  const [groups, setGroups] = useState<CustomizationGroupWithOptions[]>([]);
  const [allOptions, setAllOptions] = useState<CustomizationOption[]>([]);
  const [loading, setLoading] = useState(true);
  const [editingGroup, setEditingGroup] = useState<CustomizationGroup | null>(null);
  const [editingOption, setEditingOption] = useState<CustomizationOption | null>(null);
  const [showGroupForm, setShowGroupForm] = useState(false);
  const [showOptionForm, setShowOptionForm] = useState(false);
  const [selectedGroupId, setSelectedGroupId] = useState<number | null>(null);
  const { toast } = useToast();

  // Form states
  const [groupForm, setGroupForm] = useState({ title: "" });
  const [optionForm, setOptionForm] = useState({
    name: "",
    extraPrice: 0,
    imageUrl: "",
    groupId: 0,
  });

  useEffect(() => {
    fetchData();
  }, []);

  const fetchData = async () => {
    try {
      setLoading(true);
      const [groupsData, optionsData] = await Promise.all([
        getCustomizationGroups(),
        getCustomizationOptions(),
      ]);

      // Group options by their group ID
      const groupsWithOptions = groupsData.map(group => ({
        ...group,
        options: optionsData.filter(option => option.groupId === group.id),
      }));

      setGroups(groupsWithOptions);
      setAllOptions(optionsData);
    } catch (error) {
      console.error("Error fetching customization data:", error);
      toast({
        title: "Error",
        description: "Failed to fetch customization data",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const handleCreateGroup = async () => {
    try {
      if (!groupForm.title.trim()) {
        toast({
          title: "Error",
          description: "Group title is required",
          variant: "destructive",
        });
        return;
      }

      await createCustomizationGroup(groupForm);
      toast({
        title: "Success",
        description: "Customization group created successfully",
      });

      setGroupForm({ title: "" });
      setShowGroupForm(false);
      fetchData();
    } catch (error) {
      console.error("Error creating group:", error);
      toast({
        title: "Error",
        description: "Failed to create customization group",
        variant: "destructive",
      });
    }
  };

  const handleUpdateGroup = async () => {
    try {
      if (!editingGroup || !groupForm.title.trim()) return;

      await updateCustomizationGroup(editingGroup.id, groupForm);
      toast({
        title: "Success",
        description: "Customization group updated successfully",
      });

      setEditingGroup(null);
      setGroupForm({ title: "" });
      fetchData();
    } catch (error) {
      console.error("Error updating group:", error);
      toast({
        title: "Error",
        description: "Failed to update customization group",
        variant: "destructive",
      });
    }
  };

  const handleDeleteGroup = async (id: number) => {
    try {
      if (!confirm("Are you sure you want to delete this group? This will also delete all options in this group.")) {
        return;
      }

      await deleteCustomizationGroup(id);
      toast({
        title: "Success",
        description: "Customization group deleted successfully",
      });
      fetchData();
    } catch (error) {
      console.error("Error deleting group:", error);
      toast({
        title: "Error",
        description: "Failed to delete customization group",
        variant: "destructive",
      });
    }
  };

  const handleCreateOption = async () => {
    try {
      if (!optionForm.name.trim() || !optionForm.groupId) {
        toast({
          title: "Error",
          description: "Option name and group are required",
          variant: "destructive",
        });
        return;
      }

      await createCustomizationOption(optionForm);
      toast({
        title: "Success",
        description: "Customization option created successfully",
      });

      setOptionForm({ name: "", extraPrice: 0, imageUrl: "", groupId: 0 });
      setShowOptionForm(false);
      setSelectedGroupId(null);
      fetchData();
    } catch (error) {
      console.error("Error creating option:", error);
      toast({
        title: "Error",
        description: "Failed to create customization option",
        variant: "destructive",
      });
    }
  };

  const handleUpdateOption = async () => {
    try {
      if (!editingOption || !optionForm.name.trim()) return;

      await updateCustomizationOption(editingOption.id, optionForm);
      toast({
        title: "Success",
        description: "Customization option updated successfully",
      });

      setEditingOption(null);
      setOptionForm({ name: "", extraPrice: 0, imageUrl: "", groupId: 0 });
      fetchData();
    } catch (error) {
      console.error("Error updating option:", error);
      toast({
        title: "Error",
        description: "Failed to update customization option",
        variant: "destructive",
      });
    }
  };

  const handleDeleteOption = async (id: number) => {
    try {
      if (!confirm("Are you sure you want to delete this option?")) {
        return;
      }

      await deleteCustomizationOption(id);
      toast({
        title: "Success",
        description: "Customization option deleted successfully",
      });
      fetchData();
    } catch (error) {
      console.error("Error deleting option:", error);
      toast({
        title: "Error",
        description: "Failed to delete customization option",
        variant: "destructive",
      });
    }
  };

  const startEditingGroup = (group: CustomizationGroup) => {
    setEditingGroup(group);
    setGroupForm({ title: group.title });
    setShowGroupForm(true);
  };

  const startEditingOption = (option: CustomizationOption) => {
    setEditingOption(option);
    setOptionForm({
      name: option.name,
      extraPrice: option.extraPrice || 0,
      imageUrl: option.imageUrl || "",
      groupId: option.groupId,
    });
    setShowOptionForm(true);
  };

  const startAddingOption = (groupId: number) => {
    setSelectedGroupId(groupId);
    setOptionForm({ name: "", extraPrice: 0, imageUrl: "", groupId });
    setShowOptionForm(true);
  };

  const cancelForms = () => {
    setShowGroupForm(false);
    setShowOptionForm(false);
    setEditingGroup(null);
    setEditingOption(null);
    setSelectedGroupId(null);
    setGroupForm({ title: "" });
    setOptionForm({ name: "", extraPrice: 0, imageUrl: "", groupId: 0 });
  };

  if (loading) {
    return (
      <AdminLayout>
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-purple-600"></div>
        </div>
      </AdminLayout>
    );
  }

  return (
    <AdminLayout>
      <div className="space-y-8">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div className="p-3 bg-gradient-to-br from-purple-600 to-purple-800 rounded-xl">
              <ChefHat className="w-8 h-8 text-white" />
            </div>
            <div>
              <h1 className="text-3xl font-bold text-white">Customization Manager</h1>
              <p className="text-gray-400">Manage menu item customization groups and options</p>
            </div>
          </div>

          <button
            onClick={() => setShowGroupForm(true)}
            className="px-6 py-3 bg-gradient-to-r from-purple-600 to-purple-800 hover:from-purple-500 hover:to-purple-700
                     text-white rounded-lg transition-all duration-300 shadow-lg hover:shadow-purple-500/30 flex items-center"
          >
            <Plus className="w-5 h-5 mr-2" />
            Add Group
          </button>
        </div>

        {/* Groups and Options */}
        <div className="grid gap-6">
          {groups.map((group) => (
            <motion.div
              key={group.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              className="bg-gray-800/50 backdrop-blur-sm rounded-xl border border-gray-700/50 p-6"
            >
              {/* Group Header */}
              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center space-x-3">
                  <Settings className="w-6 h-6 text-purple-400" />
                  <h3 className="text-xl font-semibold text-white">{group.title}</h3>
                  <span className="px-2 py-1 bg-purple-600/20 text-purple-300 rounded-full text-sm">
                    {group.options.length} options
                  </span>
                </div>

                <div className="flex items-center space-x-2">
                  <button
                    onClick={() => startAddingOption(group.id)}
                    className="px-3 py-2 bg-cyan-600/20 hover:bg-cyan-600/30 text-cyan-300 rounded-lg transition-colors"
                  >
                    <Plus className="w-4 h-4" />
                  </button>
                  <button
                    onClick={() => startEditingGroup(group)}
                    className="px-3 py-2 bg-blue-600/20 hover:bg-blue-600/30 text-blue-300 rounded-lg transition-colors"
                  >
                    <Pencil className="w-4 h-4" />
                  </button>
                  <button
                    onClick={() => handleDeleteGroup(group.id)}
                    className="px-3 py-2 bg-red-600/20 hover:bg-red-600/30 text-red-300 rounded-lg transition-colors"
                  >
                    <Trash2 className="w-4 h-4" />
                  </button>
                </div>
              </div>

              {/* Options Grid */}
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {group.options.map((option) => (
                  <div
                    key={option.id}
                    className="bg-gray-700/30 rounded-lg p-4 border border-gray-600/30"
                  >
                    <div className="flex items-center justify-between mb-2">
                      <h4 className="font-medium text-white">{option.name}</h4>
                      <div className="flex items-center space-x-1">
                        <button
                          onClick={() => startEditingOption(option)}
                          className="p-1 text-blue-400 hover:text-blue-300 transition-colors"
                        >
                          <Pencil className="w-4 h-4" />
                        </button>
                        <button
                          onClick={() => handleDeleteOption(option.id)}
                          className="p-1 text-red-400 hover:text-red-300 transition-colors"
                        >
                          <Trash2 className="w-4 h-4" />
                        </button>
                      </div>
                    </div>
                    <p className="text-sm text-gray-400">
                      {option.extraPrice ? `+${option.extraPrice} kr` : 'Included'}
                    </p>
                  </div>
                ))}
              </div>
            </motion.div>
          ))}
        </div>

        {/* Group Form Modal */}
        {showGroupForm && (
          <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4">
            <motion.div
              initial={{ opacity: 0, scale: 0.95 }}
              animate={{ opacity: 1, scale: 1 }}
              className="bg-gray-800 rounded-xl border border-gray-700 p-6 w-full max-w-md"
            >
              <h3 className="text-xl font-semibold text-white mb-4">
                {editingGroup ? 'Edit Group' : 'Add New Group'}
              </h3>

              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">
                    Group Title
                  </label>
                  <input
                    type="text"
                    value={groupForm.title}
                    onChange={(e) => setGroupForm({ title: e.target.value })}
                    className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-purple-500"
                    placeholder="e.g., Sauce Options, Protein Choices"
                  />
                </div>
              </div>

              <div className="flex justify-end space-x-3 mt-6">
                <button
                  onClick={cancelForms}
                  className="px-4 py-2 text-gray-300 hover:text-white transition-colors"
                >
                  Cancel
                </button>
                <button
                  onClick={editingGroup ? handleUpdateGroup : handleCreateGroup}
                  className="px-4 py-2 bg-purple-600 hover:bg-purple-700 text-white rounded-lg transition-colors"
                >
                  {editingGroup ? 'Update' : 'Create'}
                </button>
              </div>
            </motion.div>
          </div>
        )}

        {/* Option Form Modal */}
        {showOptionForm && (
          <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4">
            <motion.div
              initial={{ opacity: 0, scale: 0.95 }}
              animate={{ opacity: 1, scale: 1 }}
              className="bg-gray-800 rounded-xl border border-gray-700 p-6 w-full max-w-md"
            >
              <h3 className="text-xl font-semibold text-white mb-4">
                {editingOption ? 'Edit Option' : 'Add New Option'}
              </h3>

              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">
                    Option Name
                  </label>
                  <input
                    type="text"
                    value={optionForm.name}
                    onChange={(e) => setOptionForm({ ...optionForm, name: e.target.value })}
                    className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-purple-500"
                    placeholder="e.g., Extra Cheese, Spicy Sauce"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">
                    Extra Price (kr)
                  </label>
                  <input
                    type="number"
                    value={optionForm.extraPrice}
                    onChange={(e) => setOptionForm({ ...optionForm, extraPrice: parseInt(e.target.value) || 0 })}
                    className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-purple-500"
                    placeholder="0"
                    min="0"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">
                    Image URL (optional)
                  </label>
                  <input
                    type="text"
                    value={optionForm.imageUrl}
                    onChange={(e) => setOptionForm({ ...optionForm, imageUrl: e.target.value })}
                    className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-purple-500"
                    placeholder="https://..."
                  />
                </div>

                {!editingOption && (
                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-2">
                      Group
                    </label>
                    <select
                      value={optionForm.groupId}
                      onChange={(e) => setOptionForm({ ...optionForm, groupId: parseInt(e.target.value) })}
                      className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-purple-500"
                    >
                      <option value={0}>Select a group</option>
                      {groups.map((group) => (
                        <option key={group.id} value={group.id}>
                          {group.title}
                        </option>
                      ))}
                    </select>
                  </div>
                )}
              </div>

              <div className="flex justify-end space-x-3 mt-6">
                <button
                  onClick={cancelForms}
                  className="px-4 py-2 text-gray-300 hover:text-white transition-colors"
                >
                  Cancel
                </button>
                <button
                  onClick={editingOption ? handleUpdateOption : handleCreateOption}
                  className="px-4 py-2 bg-purple-600 hover:bg-purple-700 text-white rounded-lg transition-colors"
                >
                  {editingOption ? 'Update' : 'Create'}
                </button>
              </div>
            </motion.div>
          </div>
        )}
      </div>
    </AdminLayout>
  );
};

export default CustomizationManager;
