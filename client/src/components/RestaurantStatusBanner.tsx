import { useRestaurantStatus } from "@/context/RestaurantStatusContext";

const RestaurantStatusBanner = () => {
  const { isOpen, isLoading } = useRestaurantStatus();

  // Don't show anything while loading
  if (isLoading) {
    return null;
  }

  // Only show the banner when the restaurant is closed
  if (isOpen) {
    return null;
  }

  return (
    <div className="bg-red-900/80 text-white py-2 px-4 text-center border-b border-red-700 sticky top-0 z-50">
      <p className="flex items-center justify-center">
        <span className="inline-block w-2 h-2 bg-red-500 rounded-full mr-2 animate-pulse"></span>
        Restaurant is currently CLOSED - Online ordering is unavailable
      </p>
    </div>
  );
};

export default RestaurantStatusBanner;
