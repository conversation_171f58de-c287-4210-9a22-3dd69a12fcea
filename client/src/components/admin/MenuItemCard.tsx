import { motion } from "framer-motion";
import { Pencil, Trash2 } from "lucide-react";
import { MenuItem } from "@/api/adminApi";

interface MenuItemCardProps {
  item: MenuItem;
  onEdit: (item: MenuItem) => void;
  onDelete: (id: number) => void;
}

const MenuItemCard = ({ item, onEdit, onDelete }: MenuItemCardProps) => {
  return (
    <motion.div
      className="group bg-gray-800/50 backdrop-blur-sm rounded-lg overflow-hidden border border-gray-700 hover:border-cyan-700/30 transition-all duration-300 shadow-md hover:shadow-cyan-900/20"
      whileHover={{ scale: 1.02 }}
    >
      {/* Item Image */}
      {item.image_url && (
        <div className="h-40 overflow-hidden">
          <img 
            src={item.image_url} 
            alt={item.name} 
            className="w-full h-full object-cover transition-transform duration-500 group-hover:scale-105"
            onError={(e) => (e.target as HTMLImageElement).src = 'https://placehold.co/400x250/1f2937/ffffff?text=No+Image'} 
          />
        </div>
      )}
      
      {/* Item Content */}
      <div className="p-4">
        <div className="flex justify-between items-start mb-1">
          <h3 className="font-medium text-white text-lg">{item.name}</h3>
          <span className="font-bold text-cyan-400">{item.price.toFixed(2)} kr</span>
        </div>
        
        <div className="flex items-center mb-3">
          <span className="text-sm text-gray-400 bg-gray-700/50 px-2 py-0.5 rounded">
            {item.category_name || 'Uncategorized'}
          </span>
          <span className={`ml-2 text-xs px-2 py-1 rounded-full ${item.available ? 'bg-green-900/30 text-green-400 border border-green-800/30' : 'bg-red-900/30 text-red-400 border border-red-800/30'}`}>
            {item.available ? 'Available' : 'Unavailable'}
          </span>
        </div>
        
        <p className="text-sm text-gray-400 mb-4 line-clamp-2">{item.description || 'No description provided'}</p>
        
        <div className="pt-3 border-t border-gray-700 flex justify-end space-x-3">
          <button
            onClick={() => onEdit(item)}
            className="flex items-center px-3 py-1.5 rounded-lg bg-cyan-700 hover:bg-cyan-600 text-white transition-colors"
          >
            <Pencil className="w-4 h-4 mr-1" />
            Edit
          </button>
          <button
            onClick={() => onDelete(item.id)}
            className="flex items-center px-3 py-1.5 rounded-lg bg-red-800 hover:bg-red-700 text-white transition-colors"
          >
            <Trash2 className="w-4 h-4 mr-1" />
            Delete
          </button>
        </div>
      </div>
    </motion.div>
  );
};

export default MenuItemCard;