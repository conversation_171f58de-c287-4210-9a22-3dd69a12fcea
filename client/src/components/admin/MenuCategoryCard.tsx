import { motion } from "framer-motion";
import { Pencil, Trash2 } from "lucide-react";
import { Category } from "@/api/adminApi";

interface MenuCategoryCardProps {
  category: Category;
  onEdit: (category: Category) => void;
  onDelete: (id: number) => void;
}

const MenuCategoryCard = ({ category, onEdit, onDelete }: MenuCategoryCardProps) => {
  return (
    <motion.div
      className="bg-gray-800/50 backdrop-blur-sm rounded-lg border border-gray-700 overflow-hidden shadow-md hover:shadow-purple-900/20 hover:border-purple-700/30 transition-all duration-300"
      whileHover={{ scale: 1.02 }}
    >
      {category.image_url && (
        <div className="h-32 w-full overflow-hidden">
          <img 
            src={category.image_url} 
            alt={category.name} 
            className="w-full h-full object-cover"
            onError={(e) => (e.target as HTMLImageElement).src = 'https://placehold.co/400x200/1f2937/ffffff?text=No+Image'} 
          />
        </div>
      )}
      
      <div className="p-4">
        <div className="flex justify-between items-center">
          <h3 className="font-bold text-white text-lg">{category.name}</h3>
          <span className="text-sm text-gray-400">ID: {category.id}</span>
        </div>
        
        <div className="mt-4 pt-4 border-t border-gray-700 flex justify-end space-x-3">
          <button
            onClick={() => onEdit(category)}
            className="flex items-center px-3 py-1.5 rounded-lg bg-amber-700 hover:bg-amber-600 text-white transition-colors"
          >
            <Pencil className="w-4 h-4 mr-1" />
            Edit
          </button>
          <button
            onClick={() => onDelete(category.id)}
            className="flex items-center px-3 py-1.5 rounded-lg bg-red-800 hover:bg-red-700 text-white transition-colors"
          >
            <Trash2 className="w-4 h-4 mr-1" />
            Delete
          </button>
        </div>
      </div>
    </motion.div>
  );
};

export default MenuCategoryCard;