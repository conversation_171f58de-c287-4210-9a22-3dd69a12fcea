import { motion } from "framer-motion";
import { Clock } from "lucide-react";

interface SingleUpdateButtonProps {
  currentStatus: string;
  nextStatus: string | null;
  nextStatusLabel: string;
  isUpdating: boolean;
  onUpdate: () => void;
}

const SingleUpdateButton = ({ 
  currentStatus, 
  nextStatus, 
  nextStatusLabel, 
  isUpdating, 
  onUpdate 
}: SingleUpdateButtonProps) => {
  if (!nextStatus) {
    return (
      <div className="text-center text-gray-400 py-2">
        This order is {currentStatus === 'completed' ? 'already completed' : 'in its final state'}
      </div>
    );
  }

  return (
    <motion.button
      className="flex items-center justify-center px-6 py-3 rounded-lg bg-gradient-to-r from-cyan-600 to-cyan-800 hover:from-cyan-500 hover:to-cyan-700 text-white font-medium text-sm transition-all w-full shadow-[0_0_15px_rgba(8,145,178,0.3)]"
      onClick={onUpdate}
      disabled={isUpdating}
      whileHover={{ scale: 1.03 }}
      whileTap={{ scale: 0.97 }}
    >
      {isUpdating ? (
        <span className="mr-2 animate-spin">
          <Clock className="w-5 h-5" />
        </span>
      ) : (
        <span className="mr-2 text-cyan-300">
          <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7"></path>
          </svg>
        </span>
      )}
      Update to {nextStatusLabel}
    </motion.button>
  );
};

export default SingleUpdateButton;