2025-05-24 10:37:41.906 [info] Can't use the Electron fetcher in this environment.
2025-05-24 10:37:41.906 [info] Using the Node fetch fetcher.
2025-05-24 10:37:41.906 [info] Initializing Git extension service.
2025-05-24 10:37:41.906 [info] Successfully activated the vscode.git extension.
2025-05-24 10:37:41.906 [info] Enablement state of the vscode.git extension: true.
2025-05-24 10:37:41.906 [info] Successfully registered Git commit message provider.
2025-05-24 10:37:43.362 [info] Logged in as mazen91111
2025-05-24 10:37:44.202 [info] Got Copilot token for mazen91111
2025-05-24 10:37:45.192 [info] Fetched model metadata in 980ms 728283ef-6380-405f-9d2a-ece4fc9d7a87
2025-05-24 10:37:45.236 [info] activationBlocker from 'languageModelAccess' took for 3239ms
2025-05-24 10:37:45.806 [info] copilot token chat_enabled: true, sku: free_limited_copilot
2025-05-24 10:37:45.806 [info] GitHub.vscode-pull-request-github extension is not yet activated.
2025-05-24 10:37:45.836 [info] Registering default platform agent...
2025-05-24 10:37:46.349 [info] BYOK: Copilot Chat known models list fetched successfully.
