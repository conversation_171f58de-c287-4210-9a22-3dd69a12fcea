2025-05-24 10:37:38.609 [info] Extension host with pid 654 started
2025-05-24 10:37:38.856 [info] ExtensionService#_doActivateExtension vscode.emmet, startup: false, activationEvent: 'onLanguage'
2025-05-24 10:37:39.127 [info] ExtensionService#_doActivateExtension vscode.git-base, startup: true, activationEvent: '*', root cause: vscode.git
2025-05-24 10:37:39.312 [info] ExtensionService#_doActivateExtension vscode.git, startup: true, activationEvent: '*'
2025-05-24 10:37:39.313 [info] ExtensionService#_doActivateExtension vscode.github, startup: true, activationEvent: '*'
2025-05-24 10:37:39.652 [info] Eager extensions activated
2025-05-24 10:37:39.653 [info] ExtensionService#_doActivateExtension vscode.debug-auto-launch, startup: false, activationEvent: 'onStartupFinished'
2025-05-24 10:37:39.653 [info] ExtensionService#_doActivateExtension vscode.merge-conflict, startup: false, activationEvent: 'onStartupFinished'
2025-05-24 10:37:39.654 [info] ExtensionService#_doActivateExtension Augment.vscode-augment, startup: false, activationEvent: 'onStartupFinished'
2025-05-24 10:37:39.654 [info] ExtensionService#_doActivateExtension coderabbit.coderabbit-vscode, startup: false, activationEvent: 'onStartupFinished'
2025-05-24 10:37:39.654 [info] ExtensionService#_doActivateExtension GitHub.copilot, startup: false, activationEvent: 'onStartupFinished'
2025-05-24 10:37:39.654 [info] ExtensionService#_doActivateExtension GitHub.copilot-chat, startup: false, activationEvent: 'onStartupFinished'
2025-05-24 10:38:08.062 [info] Extension host terminating: received terminate message from renderer
2025-05-24 10:38:08.097 [info] Extension host with pid 654 exiting with code 0
