2025-05-24 10:41:09.227 [info] 'AugmentConfigListener' settings parsed successfully
2025-05-24 10:41:09.227 [info] 'AugmentConfigListener' Config changed from <unset> to {"apiToken":"","completionURL":"","modelName":"","conflictingCodingAssistantCheck":true,"codeInstruction":{},"chat":{"enableEditableHistory":false,"useRichTextHistory":true,"smartPasteUsePrecomputation":true,"experimentalFullFilePaste":false,"modelDisplayNameToId":{"Augment":null},"userGuidelines":""},"agent":{},"autofix":{"enabled":false},"oauth":{"clientID":"augment-vscode-extension","url":"https://auth.augmentcode.com"},"enableUpload":true,"shortcutsDisplayDelayMS":2000,"enableEmptyFileHint":true,"enableDataCollection":false,"enableDebugFeatures":false,"enableReviewerWorkflows":false,"completions":{"enableAutomaticCompletions":true,"disableCompletionsByLanguage":{},"enableQuickSuggestions":true,"timeoutMs":800,"maxWaitMs":1600,"addIntelliSenseSuggestions":true},"openFileManager":{},"nextEdit":{"backgroundEnabled":true,"useCursorDecorations":false,"allowDuringDebugging":false,"useMockResults":false,"noDiffModeUseCodeLens":false,"enableBackgroundSuggestions":true,"enableGlobalBackgroundSuggestions":false,"highlightSuggestionsInTheEditor":false,"showDiffInHover":false,"enableAutoApply":true},"recencySignalManager":{"collectTabSwitchEvents":false},"preferenceCollection":{"enable":false,"enableRetrievalDataCollection":false,"enableRandomizedMode":true},"vcs":{"watcherEnabled":false},"git":{"enableCommitIndexing":false,"maxCommitsToIndex":100},"smartPaste":{},"instructions":{},"integrations":{},"mcpServers":[],"advanced":{}}
2025-05-24 10:41:09.228 [info] 'FeatureFlagManager' feature flags changed from <unset> to {"gitDiff":false,"gitDiffPollingFrequencyMSec":0,"additionalChatModels":"","smallSyncThreshold":15,"bigSyncThreshold":1000,"enableWorkspaceManagerUi":true,"enableInstructions":false,"enableSmartPaste":false,"enableSmartPasteMinVersion":"","enablePromptEnhancer":false,"enableViewTextDocument":false,"bypassLanguageFilter":false,"enableHindsight":false,"maxUploadSizeBytes":131072,"vscodeNextEditBottomPanelMinVersion":"","vscodeNextEditMinVersion":"","vscodeNextEditUx1MaxVersion":"","vscodeNextEditUx2MaxVersion":"","vscodeFlywheelMinVersion":"","vscodeExternalSourcesInChatMinVersion":"","vscodeShareMinVersion":"","maxTrackableFileCount":250000,"maxTrackableFileCountWithoutPermission":150000,"minUploadedPercentageWithoutPermission":90,"memoryClassificationOnFirstToken":false,"vscodeSourcesMinVersion":"","vscodeChatHintDecorationMinVersion":"","nextEditDebounceMs":400,"enableCompletionFileEditEvents":false,"vscodeEnableCpuProfile":false,"verifyFolderIsSourceRepo":false,"refuseToSyncHomeDirectories":false,"enableFileLimitsForSyncingPermission":false,"enableChatMermaidDiagrams":false,"enableSummaryTitles":false,"smartPastePrecomputeMode":"visible-hover","vscodeNewThreadsMenuMinVersion":"","vscodeEditableHistoryMinVersion":"","vscodeEnableChatMermaidDiagramsMinVersion":"","userGuidelinesLengthLimit":2000,"workspaceGuidelinesLengthLimit":2000,"enableGuidelines":false,"useCheckpointManagerContextMinVersion":"","validateCheckpointManagerContext":false,"vscodeDesignSystemRichTextEditorMinVersion":"","allowClientFeatureFlagOverrides":false,"vscodeChatWithToolsMinVersion":"","vscodeChatMultimodalMinVersion":"","vscodeAgentModeMinVersion":"","vscodeAgentModeMinStableVersion":"","vscodeBackgroundAgentsMinVersion":"","vscodeAgentEditTool":"backend_edit_tool","vscodeRichCheckpointInfoMinVersion":"","vscodeDirectApplyMinVersion":"","memoriesParams":{},"eloModelConfiguration":{"highPriorityModels":[],"regularBattleModels":[],"highPriorityThreshold":0.5},"vscodeVirtualizedMessageListMinVersion":"","vscodeChatStablePrefixTruncationMinVersion":"","agentEditToolMinViewSize":0,"agentEditToolSchemaType":"StrReplaceEditorToolDefinitionNested","agentEditToolEnableFuzzyMatching":false,"agentEditToolFuzzyMatchSuccessMessage":"Replacement successful. old_str and new_str were slightly modified to match the original file content.","agentEditToolFuzzyMatchMaxDiff":50,"agentEditToolFuzzyMatchMaxDiffRatio":0.15,"agentEditToolFuzzyMatchMinAllMatchStreakBetweenDiffs":5,"agentEditToolInstructionsReminder":false,"agentEditToolShowResultSnippet":true,"agentSaveFileToolInstructionsReminder":false,"vscodePersonalitiesMinVersion":"","useMemorySnapshotManager":false,"vscodeGenerateCommitMessageMinVersion":"","enableRules":false,"memoriesTextEditorEnabled":false,"enableModelRegistry":false,"openFileManagerV2Enabled":false,"modelRegistry":{},"vscodeTaskListMinVersion":"","vscodeRemoteAgentSSHMinVersion":"","clientAnnouncement":""}
2025-05-24 10:41:09.228 [info] 'AugmentExtension' Retrieving model config
2025-05-24 10:41:10.432 [info] 'AugmentExtension' Retrieved model config
2025-05-24 10:41:10.432 [info] 'AugmentExtension' Returning model config
2025-05-24 10:41:10.615 [info] 'FeatureFlagManager' feature flags changed:
  - additionalChatModels: "" to "{}"
  - enableInstructions: false to true
  - enableSmartPasteMinVersion: "" to "0.267.0"
  - enablePromptEnhancer: false to true
  - enableViewTextDocument: false to true
  - bypassLanguageFilter: false to true
  - enableHindsight: false to true
  - maxUploadSizeBytes: 131072 to 524288
  - vscodeNextEditBottomPanelMinVersion: "" to "0.394.0"
  - vscodeNextEditMinVersion: "" to "0.343.0"
  - vscodeFlywheelMinVersion: "" to "0.282.0"
  - vscodeExternalSourcesInChatMinVersion: "" to "0.243.2"
  - vscodeShareMinVersion: "" to "0.314.0"
  - memoryClassificationOnFirstToken: false to true
  - vscodeChatHintDecorationMinVersion: "" to "0.274.0"
  - enableCompletionFileEditEvents: false to true
  - verifyFolderIsSourceRepo: false to true
  - refuseToSyncHomeDirectories: false to true
  - enableFileLimitsForSyncingPermission: false to true
  - enableSummaryTitles: false to true
  - smartPastePrecomputeMode: "visible-hover" to "visible"
  - vscodeNewThreadsMenuMinVersion: "" to "0.305.0"
  - vscodeEditableHistoryMinVersion: "" to "0.330.0"
  - vscodeEnableChatMermaidDiagramsMinVersion: "" to "0.314.0"
  - userGuidelinesLengthLimit: 2000 to 24576
  - workspaceGuidelinesLengthLimit: 2000 to 24576
  - enableGuidelines: false to true
  - useCheckpointManagerContextMinVersion: "" to "0.323.0"
  - vscodeDesignSystemRichTextEditorMinVersion: "" to "0.363.0"
  - vscodeChatMultimodalMinVersion: "" to "0.384.0"
  - vscodeAgentModeMinVersion: "" to "0.395.0"
  - vscodeAgentModeMinStableVersion: "" to "0.399.1"
  - vscodeAgentEditTool: "backend_edit_tool" to "str_replace_editor_tool"
  - eloModelConfiguration > highPriorityModels: [] to undefined
  - eloModelConfiguration > regularBattleModels: [] to undefined
  - eloModelConfiguration > highPriorityThreshold: 0.5 to undefined
  - vscodeChatStablePrefixTruncationMinVersion: "" to "0.402.0"
  - agentEditToolMinViewSize: 0 to 500
  - agentEditToolSchemaType: "StrReplaceEditorToolDefinitionNested" to "StrReplaceEditorToolDefinitionFlat"
  - agentEditToolEnableFuzzyMatching: false to true
  - agentEditToolInstructionsReminder: false to true
  - agentSaveFileToolInstructionsReminder: false to true
  - useMemorySnapshotManager: false to true
  - openFileManagerV2Enabled: false to true
  - vscodeRemoteAgentSSHMinVersion: "" to "0.456.0"
  - clientAnnouncement: "" to "🎉 Your Agents are now using Claude Sonnet 4!"
2025-05-24 10:41:10.615 [info] 'SyncingPermissionTracker' Initial syncing permission: syncing permission granted for workspace. Folders:
    /home/<USER>/workspace (explicit) at 5/21/2025, 11:43:10 PM
2025-05-24 10:41:10.615 [info] 'WorkspaceManager' OpenFileManagerProxy created. V2 enabled: [true]
2025-05-24 10:41:10.615 [info] 'BlobsCheckpointManager' BlobsCheckpointManager created. checkpointThreshold: 1000
2025-05-24 10:41:10.616 [info] 'SyncingPermissionTracker' Permission to sync folder /home/<USER>/workspace granted at 5/21/2025, 11:43:10 PM; type = explicit
2025-05-24 10:41:10.616 [info] 'WorkspaceManager' Adding workspace folder workspace; folderRoot = /home/<USER>/workspace; syncingPermission = granted
2025-05-24 10:41:10.616 [info] 'SyncingPermissionTracker' Updating syncing permission: syncing permission granted for workspace. Folders:
    /home/<USER>/workspace (explicit) at 5/21/2025, 11:43:10 PM
2025-05-24 10:41:10.647 [info] 'RemoteAgentsMessenger' RemoteAgentsMessenger initialized, setting up onDidChangeTextDocument listener
2025-05-24 10:41:10.648 [info] 'HotKeyHints' HotKeyHints initialized
2025-05-24 10:41:10.650 [info] 'ToolsModel' Loaded saved chat mode: AGENT
2025-05-24 10:41:10.705 [info] 'ToolsModel' Tools Mode: AGENT (3 hosts)
2025-05-24 10:41:10.706 [info] 'ToolsModel' Host: localToolHost (9 tools: 154 enabled, 0 disabled})
 + str-replace-editor
 + open-browser
 + diagnostics
 + read-terminal
 + launch-process
 + kill-process
 + read-process
 + write-process
 + list-processes

2025-05-24 10:41:13.060 [info] 'StallDetector' Event loop delay: Timer(100 msec) ran 2066 msec late.
2025-05-24 10:41:13.804 [info] 'WorkspaceManager[workspace]' Start tracking
2025-05-24 10:41:18.518 [info] 'StallDetector' Event loop delay: Timer(100 msec) ran 4225 msec late.
2025-05-24 10:41:18.694 [info] 'ToolsModel' Host: remoteToolHost (1 tools: 13 enabled, 0 disabled})
 + web-search

2025-05-24 10:41:18.694 [info] 'ToolsModel' Host: sidecarToolHost (7 tools: 101 enabled, 0 disabled})
 + web-fetch
 + codebase-retrieval
 + remove-files
 + save-file
 + remember
 + render-mermaid
 + view

2025-05-24 10:41:18.805 [info] 'PathMap' Opened source folder /home/<USER>/workspace with id 100
2025-05-24 10:41:18.805 [info] 'OpenFileManager' Opened source folder 100
2025-05-24 10:41:18.811 [info] 'MtimeCache[workspace]' reading blob name cache from /home/<USER>/.vscode-server/data/User/workspaceStorage/28d495afc6b3165b645c0d6d358123ef/Augment.vscode-augment/ce248d0eb8d4f662cc202e5206f37824b70c818932a8dbfb169463d063d70aa1/mtime-cache.json
2025-05-24 10:41:20.010 [info] 'ToolsModel' Tools Mode: AGENT (3 hosts)
2025-05-24 10:41:20.010 [info] 'ToolsModel' Host: localToolHost (9 tools: 154 enabled, 0 disabled})
 + str-replace-editor
 + open-browser
 + diagnostics
 + read-terminal
 + launch-process
 + kill-process
 + read-process
 + write-process
 + list-processes

2025-05-24 10:41:20.011 [info] 'ToolsModel' Host: remoteToolHost (1 tools: 13 enabled, 0 disabled})
 + web-search

2025-05-24 10:41:20.011 [info] 'ToolsModel' Host: sidecarToolHost (7 tools: 101 enabled, 0 disabled})
 + web-fetch
 + codebase-retrieval
 + remove-files
 + save-file
 + remember
 + render-mermaid
 + view

2025-05-24 10:41:20.299 [info] 'MtimeCache[workspace]' read 2024 entries from /home/<USER>/.vscode-server/data/User/workspaceStorage/28d495afc6b3165b645c0d6d358123ef/Augment.vscode-augment/ce248d0eb8d4f662cc202e5206f37824b70c818932a8dbfb169463d063d70aa1/mtime-cache.json
2025-05-24 10:41:30.690 [info] 'RemoteAgentsMessenger' Registering RemoteAgentsMessenger with AsyncMsgHandler
2025-05-24 10:41:31.589 [error] 'RemoteAgentsMessenger' Unexpected message type: main-panel-loaded
2025-05-24 10:41:31.771 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 10:41:31.774 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 10:41:31.774 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 10:41:31.774 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 10:41:31.774 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 10:41:31.774 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 10:41:31.782 [info] 'TaskManager' Setting current root task UUID to a4bcbb9b-6697-471a-bf78-8826179b54e0
2025-05-24 10:41:31.782 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 10:41:31.782 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 10:41:31.782 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 10:41:31.782 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 10:41:31.782 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 10:41:31.782 [error] 'RemoteAgentsMessenger' Unexpected message type: update-shared-webview-state
2025-05-24 10:41:31.794 [warning] 'CheckpointHydration' Failed to hydrate checkpoint: Error: Checkpoint document not found at: checkpoint-documents/8cd18cae-af48-4628-881a-aae2b3ddde1c/document-_home_runner_workspace_server_seed-customizations.ts-1748048617991-8c5e7b14-d76a-4280-a659-edf7d3da8a5b.json
2025-05-24 10:41:31.798 [warning] 'CheckpointHydration' Failed to hydrate checkpoint: Error: Checkpoint document not found at: checkpoint-documents/8cd18cae-af48-4628-881a-aae2b3ddde1c/document-_home_runner_workspace_server_mock-orders.ts-1748048618167-8c5e7b14-d76a-4280-a659-edf7d3da8a5b.json
2025-05-24 10:41:31.800 [warning] 'CheckpointHydration' Failed to hydrate checkpoint: Error: Checkpoint document not found at: checkpoint-documents/8cd18cae-af48-4628-881a-aae2b3ddde1c/document-_home_runner_workspace_scripts_run_seed.js-1748048618347-8c5e7b14-d76a-4280-a659-edf7d3da8a5b.json
2025-05-24 10:41:31.802 [warning] 'CheckpointHydration' Failed to hydrate checkpoint: Error: Checkpoint document not found at: checkpoint-documents/8cd18cae-af48-4628-881a-aae2b3ddde1c/document-_home_runner_workspace_scripts_run_seed.cjs-1748048619094-8c5e7b14-d76a-4280-a659-edf7d3da8a5b.json
2025-05-24 10:41:31.806 [warning] 'CheckpointHydration' Failed to hydrate checkpoint: Error: Checkpoint document not found at: checkpoint-documents/8cd18cae-af48-4628-881a-aae2b3ddde1c/document-_home_runner_workspace_scripts_seed_database.sql-1748048619852-8c5e7b14-d76a-4280-a659-edf7d3da8a5b.json
2025-05-24 10:41:31.808 [warning] 'CheckpointHydration' Failed to hydrate checkpoint: Error: Checkpoint document not found at: checkpoint-documents/8cd18cae-af48-4628-881a-aae2b3ddde1c/document-_home_runner_workspace_scripts_seed_users.ts-1748048620617-8c5e7b14-d76a-4280-a659-edf7d3da8a5b.json
2025-05-24 10:41:31.809 [warning] 'CheckpointHydration' Failed to hydrate checkpoint: Error: Checkpoint document not found at: checkpoint-documents/8cd18cae-af48-4628-881a-aae2b3ddde1c/document-_home_runner_workspace_scripts_setup_auth_db.ts-1748048621340-8c5e7b14-d76a-4280-a659-edf7d3da8a5b.json
2025-05-24 10:41:31.817 [error] 'RemoteAgentsMessenger' Unexpected message type: get-orientation-status
2025-05-24 10:41:31.906 [warning] 'CheckpointHydration' Failed to hydrate checkpoint: Error: Checkpoint document not found at: checkpoint-documents/8cd18cae-af48-4628-881a-aae2b3ddde1c/document-_home_runner_workspace_attached_assets_Pasted--Build-Admin-Panel-for-Restaurant-Settings-Menu-Management-Revenue-Analytics-React-TailwindCSS-1747871652816.txt-1748048658355-b0880c75-7002-4c38-b387-5713346dc75c.json
2025-05-24 10:41:31.909 [warning] 'CheckpointHydration' Failed to hydrate checkpoint: Error: Checkpoint document not found at: checkpoint-documents/8cd18cae-af48-4628-881a-aae2b3ddde1c/document-_home_runner_workspace_attached_assets_Pasted-Build-Order-Confirmation-Order-Tracker-Page-React-TailwindCSS-Framer-Motion-Objective-Cr-1747870703232.txt-1748048659150-b0880c75-7002-4c38-b387-5713346dc75c.json
2025-05-24 10:41:31.910 [warning] 'CheckpointHydration' Failed to hydrate checkpoint: Error: Checkpoint document not found at: checkpoint-documents/8cd18cae-af48-4628-881a-aae2b3ddde1c/document-_home_runner_workspace_attached_assets_Pasted-Build-Order-Confirmation-Order-Tracker-Page-React-TailwindCSS-Framer-Motion-Objective-Cr-1747871115566.txt-1748048659996-b0880c75-7002-4c38-b387-5713346dc75c.json
2025-05-24 10:41:31.912 [warning] 'CheckpointHydration' Failed to hydrate checkpoint: Error: Checkpoint document not found at: checkpoint-documents/8cd18cae-af48-4628-881a-aae2b3ddde1c/document-_home_runner_workspace_attached_assets_Pasted-Build-Order-Confirmation-Order-Tracker-Page-React-TailwindCSS-Framer-Motion-Objective-Cr-1747871623392.txt-1748048660733-b0880c75-7002-4c38-b387-5713346dc75c.json
2025-05-24 10:41:31.918 [warning] 'CheckpointHydration' Failed to hydrate checkpoint: Error: Checkpoint document not found at: checkpoint-documents/8cd18cae-af48-4628-881a-aae2b3ddde1c/document-_home_runner_workspace_attached_assets_Pasted-Build-a-Backend-API-with-Express-PostgreSQL-for-Admin-Panel-Settings-Menu-Analytics-Objective--1747871722377.txt-1748048661453-b0880c75-7002-4c38-b387-5713346dc75c.json
2025-05-24 10:41:31.920 [warning] 'CheckpointHydration' Failed to hydrate checkpoint: Error: Checkpoint document not found at: checkpoint-documents/8cd18cae-af48-4628-881a-aae2b3ddde1c/document-_home_runner_workspace_attached_assets_Pasted-Build-a-visually-luxurious-responsive-and-interactive-Cart-page-using-React-TailwindCSS-with-ani-1747870351144.txt-1748048662286-b0880c75-7002-4c38-b387-5713346dc75c.json
2025-05-24 10:41:31.923 [warning] 'CheckpointHydration' Failed to hydrate checkpoint: Error: Checkpoint document not found at: checkpoint-documents/8cd18cae-af48-4628-881a-aae2b3ddde1c/document-_home_runner_workspace_attached_assets_Pasted-Build-the-Driver-Order-Page-for-Delivery-Control-React-TailwindCSS-Framer-Motion-Objective--1747874211427.txt-1748048663089-b0880c75-7002-4c38-b387-5713346dc75c.json
2025-05-24 10:41:31.925 [warning] 'CheckpointHydration' Failed to hydrate checkpoint: Error: Checkpoint document not found at: checkpoint-documents/8cd18cae-af48-4628-881a-aae2b3ddde1c/document-_home_runner_workspace_attached_assets_Pasted-Build-the-Manager-Kitchen-Page-for-Order-Status-Control-React-TailwindCSS-Framer-Motion-Obj-1747873346833.txt-1748048663800-b0880c75-7002-4c38-b387-5713346dc75c.json
2025-05-24 10:41:31.927 [warning] 'CheckpointHydration' Failed to hydrate checkpoint: Error: Checkpoint document not found at: checkpoint-documents/8cd18cae-af48-4628-881a-aae2b3ddde1c/document-_home_runner_workspace_attached_assets_Pasted-Build-the-Manager-Kitchen-Page-for-Order-Status-Control-React-TailwindCSS-Framer-Motion-Obj-1747873980911.txt-1748048664520-b0880c75-7002-4c38-b387-5713346dc75c.json
2025-05-24 10:41:32.004 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 10:41:32.072 [warning] 'CheckpointHydration' Failed to hydrate checkpoint: Error: Checkpoint document not found at: checkpoint-documents/8cd18cae-af48-4628-881a-aae2b3ddde1c/document-_home_runner_workspace_server_seed-customizations.ts-1748048617991-8c5e7b14-d76a-4280-a659-edf7d3da8a5b.json
2025-05-24 10:41:32.073 [warning] 'CheckpointHydration' Failed to hydrate checkpoint: Error: Checkpoint document not found at: checkpoint-documents/8cd18cae-af48-4628-881a-aae2b3ddde1c/document-_home_runner_workspace_server_mock-orders.ts-1748048618167-8c5e7b14-d76a-4280-a659-edf7d3da8a5b.json
2025-05-24 10:41:32.074 [warning] 'CheckpointHydration' Failed to hydrate checkpoint: Error: Checkpoint document not found at: checkpoint-documents/8cd18cae-af48-4628-881a-aae2b3ddde1c/document-_home_runner_workspace_attached_assets_Pasted-Build-the-Manager-Kitchen-Page-for-Order-Status-Control-React-TailwindCSS-Framer-Motion-Obj-1747874559621.txt-1748048677793-9a837d3f-46df-4bae-99cb-2e2a909888f2.json
2025-05-24 10:41:32.074 [warning] 'CheckpointHydration' Failed to hydrate checkpoint: Error: Checkpoint document not found at: checkpoint-documents/8cd18cae-af48-4628-881a-aae2b3ddde1c/document-_home_runner_workspace_scripts_run_seed.js-1748048618347-8c5e7b14-d76a-4280-a659-edf7d3da8a5b.json
2025-05-24 10:41:32.076 [warning] 'CheckpointHydration' Failed to hydrate checkpoint: Error: Checkpoint document not found at: checkpoint-documents/8cd18cae-af48-4628-881a-aae2b3ddde1c/document-_home_runner_workspace_scripts_run_seed.cjs-1748048619094-8c5e7b14-d76a-4280-a659-edf7d3da8a5b.json
2025-05-24 10:41:32.077 [warning] 'CheckpointHydration' Failed to hydrate checkpoint: Error: Checkpoint document not found at: checkpoint-documents/8cd18cae-af48-4628-881a-aae2b3ddde1c/document-_home_runner_workspace_scripts_seed_database.sql-1748048619852-8c5e7b14-d76a-4280-a659-edf7d3da8a5b.json
2025-05-24 10:41:32.077 [warning] 'CheckpointHydration' Failed to hydrate checkpoint: Error: Checkpoint document not found at: checkpoint-documents/8cd18cae-af48-4628-881a-aae2b3ddde1c/document-_home_runner_workspace_scripts_seed_users.ts-1748048620617-8c5e7b14-d76a-4280-a659-edf7d3da8a5b.json
2025-05-24 10:41:32.078 [warning] 'CheckpointHydration' Failed to hydrate checkpoint: Error: Checkpoint document not found at: checkpoint-documents/8cd18cae-af48-4628-881a-aae2b3ddde1c/document-_home_runner_workspace_scripts_setup_auth_db.ts-1748048621340-8c5e7b14-d76a-4280-a659-edf7d3da8a5b.json
2025-05-24 10:41:32.080 [warning] 'CheckpointHydration' Failed to hydrate checkpoint: Error: Checkpoint document not found at: checkpoint-documents/8cd18cae-af48-4628-881a-aae2b3ddde1c/document-_home_runner_workspace_attached_assets_Pasted--Build-Admin-Panel-for-Restaurant-Settings-Menu-Management-Revenue-Analytics-React-TailwindCSS-1747871652816.txt-1748048658355-b0880c75-7002-4c38-b387-5713346dc75c.json
2025-05-24 10:41:32.080 [warning] 'CheckpointHydration' Failed to hydrate checkpoint: Error: Checkpoint document not found at: checkpoint-documents/8cd18cae-af48-4628-881a-aae2b3ddde1c/document-_home_runner_workspace_attached_assets_Pasted-Create-a-complete-dynamic-Menu-page-menu-for-a-luxurious-restaurant-website-using-React-Tail-1747869064381.txt-1748048678594-9a837d3f-46df-4bae-99cb-2e2a909888f2.json
2025-05-24 10:41:32.081 [warning] 'CheckpointHydration' Failed to hydrate checkpoint: Error: Checkpoint document not found at: checkpoint-documents/8cd18cae-af48-4628-881a-aae2b3ddde1c/document-_home_runner_workspace_attached_assets_Pasted-Build-Order-Confirmation-Order-Tracker-Page-React-TailwindCSS-Framer-Motion-Objective-Cr-1747870703232.txt-1748048659150-b0880c75-7002-4c38-b387-5713346dc75c.json
2025-05-24 10:41:32.081 [warning] 'CheckpointHydration' Failed to hydrate checkpoint: Error: Checkpoint document not found at: checkpoint-documents/8cd18cae-af48-4628-881a-aae2b3ddde1c/document-_home_runner_workspace_attached_assets_Pasted-Build-Order-Confirmation-Order-Tracker-Page-React-TailwindCSS-Framer-Motion-Objective-Cr-1747871115566.txt-1748048659996-b0880c75-7002-4c38-b387-5713346dc75c.json
2025-05-24 10:41:32.082 [warning] 'CheckpointHydration' Failed to hydrate checkpoint: Error: Checkpoint document not found at: checkpoint-documents/8cd18cae-af48-4628-881a-aae2b3ddde1c/document-_home_runner_workspace_attached_assets_Pasted-Build-Order-Confirmation-Order-Tracker-Page-React-TailwindCSS-Framer-Motion-Objective-Cr-1747871623392.txt-1748048660733-b0880c75-7002-4c38-b387-5713346dc75c.json
2025-05-24 10:41:32.082 [warning] 'CheckpointHydration' Failed to hydrate checkpoint: Error: Checkpoint document not found at: checkpoint-documents/8cd18cae-af48-4628-881a-aae2b3ddde1c/document-_home_runner_workspace_attached_assets_Pasted-Build-a-Backend-API-with-Express-PostgreSQL-for-Admin-Panel-Settings-Menu-Analytics-Objective--1747871722377.txt-1748048661453-b0880c75-7002-4c38-b387-5713346dc75c.json
2025-05-24 10:41:32.083 [warning] 'CheckpointHydration' Failed to hydrate checkpoint: Error: Checkpoint document not found at: checkpoint-documents/8cd18cae-af48-4628-881a-aae2b3ddde1c/document-_home_runner_workspace_attached_assets_Pasted-Build-a-visually-luxurious-responsive-and-interactive-Cart-page-using-React-TailwindCSS-with-ani-1747870351144.txt-1748048662286-b0880c75-7002-4c38-b387-5713346dc75c.json
2025-05-24 10:41:32.084 [warning] 'CheckpointHydration' Failed to hydrate checkpoint: Error: Checkpoint document not found at: checkpoint-documents/8cd18cae-af48-4628-881a-aae2b3ddde1c/document-_home_runner_workspace_attached_assets_Pasted-Build-the-Driver-Order-Page-for-Delivery-Control-React-TailwindCSS-Framer-Motion-Objective--1747874211427.txt-1748048663089-b0880c75-7002-4c38-b387-5713346dc75c.json
2025-05-24 10:41:32.084 [warning] 'CheckpointHydration' Failed to hydrate checkpoint: Error: Checkpoint document not found at: checkpoint-documents/8cd18cae-af48-4628-881a-aae2b3ddde1c/document-_home_runner_workspace_attached_assets_Pasted-Create-a-full-frontend-project-using-React-and-TailwindCSS-for-a-high-end-restaurant-website-1747868088702.txt-1748048679343-9a837d3f-46df-4bae-99cb-2e2a909888f2.json
2025-05-24 10:41:32.084 [warning] 'CheckpointHydration' Failed to hydrate checkpoint: Error: Checkpoint document not found at: checkpoint-documents/8cd18cae-af48-4628-881a-aae2b3ddde1c/document-_home_runner_workspace_attached_assets_Pasted-Build-the-Manager-Kitchen-Page-for-Order-Status-Control-React-TailwindCSS-Framer-Motion-Obj-1747873346833.txt-1748048663800-b0880c75-7002-4c38-b387-5713346dc75c.json
2025-05-24 10:41:32.085 [warning] 'CheckpointHydration' Failed to hydrate checkpoint: Error: Checkpoint document not found at: checkpoint-documents/8cd18cae-af48-4628-881a-aae2b3ddde1c/document-_home_runner_workspace_attached_assets_Pasted-Build-the-Manager-Kitchen-Page-for-Order-Status-Control-React-TailwindCSS-Framer-Motion-Obj-1747873980911.txt-1748048664520-b0880c75-7002-4c38-b387-5713346dc75c.json
2025-05-24 10:41:32.085 [warning] 'CheckpointHydration' Failed to hydrate checkpoint: Error: Checkpoint document not found at: checkpoint-documents/8cd18cae-af48-4628-881a-aae2b3ddde1c/document-_home_runner_workspace_attached_assets_Pasted-Build-the-Manager-Kitchen-Page-for-Order-Status-Control-React-TailwindCSS-Framer-Motion-Obj-1747874559621.txt-1748048677793-9a837d3f-46df-4bae-99cb-2e2a909888f2.json
2025-05-24 10:41:32.086 [warning] 'CheckpointHydration' Failed to hydrate checkpoint: Error: Checkpoint document not found at: checkpoint-documents/8cd18cae-af48-4628-881a-aae2b3ddde1c/document-_home_runner_workspace_attached_assets_Pasted-Create-a-complete-dynamic-Menu-page-menu-for-a-luxurious-restaurant-website-using-React-Tail-1747869064381.txt-1748048678594-9a837d3f-46df-4bae-99cb-2e2a909888f2.json
2025-05-24 10:41:32.086 [warning] 'CheckpointHydration' Failed to hydrate checkpoint: Error: Checkpoint document not found at: checkpoint-documents/8cd18cae-af48-4628-881a-aae2b3ddde1c/document-_home_runner_workspace_attached_assets_Pasted-Create-a-full-frontend-project-using-React-and-TailwindCSS-for-a-high-end-restaurant-website-1747868088702.txt-1748048679343-9a837d3f-46df-4bae-99cb-2e2a909888f2.json
2025-05-24 10:41:32.087 [warning] 'CheckpointHydration' Failed to hydrate checkpoint: Error: Checkpoint document not found at: checkpoint-documents/8cd18cae-af48-4628-881a-aae2b3ddde1c/document-_home_runner_workspace_attached_assets_Pasted-Create-a-scalable-REST-API-using-Node-js-Express-and-PostgreSQL-for-a-restaurant-website-This-API-1747869135315.txt-1748048680110-9a837d3f-46df-4bae-99cb-2e2a909888f2.json
2025-05-24 10:41:32.088 [warning] 'CheckpointHydration' Failed to hydrate checkpoint: Error: Checkpoint document not found at: checkpoint-documents/8cd18cae-af48-4628-881a-aae2b3ddde1c/document-_home_runner_workspace_attached_assets_Pasted-Create-a-scalable-REST-API-using-Node-js-Express-and-PostgreSQL-for-a-restaurant-website-This-API-1747869135315.txt-1748048680110-9a837d3f-46df-4bae-99cb-2e2a909888f2.json
2025-05-24 10:41:32.096 [warning] 'CheckpointHydration' Failed to hydrate checkpoint: Error: Checkpoint document not found at: checkpoint-documents/8cd18cae-af48-4628-881a-aae2b3ddde1c/document-_home_runner_workspace_attached_assets_Pasted-Generate-Seeder-Script-for-PostgreSQL-Menu-Categories-Settings-Orders-Prompt-csharp-Copy-Edit-1747872099827.txt-1748048680289-9a837d3f-46df-4bae-99cb-2e2a909888f2.json
2025-05-24 10:41:32.096 [warning] 'CheckpointHydration' Failed to hydrate checkpoint: Error: Checkpoint document not found at: checkpoint-documents/8cd18cae-af48-4628-881a-aae2b3ddde1c/document-_home_runner_workspace_attached_assets_Pasted-Generate-Seeder-Script-for-PostgreSQL-Menu-Categories-Settings-Orders-Prompt-csharp-Copy-Edit-1747872099827.txt-1748048680289-9a837d3f-46df-4bae-99cb-2e2a909888f2.json
2025-05-24 10:41:32.099 [warning] 'CheckpointHydration' Failed to hydrate checkpoint: Error: Checkpoint document not found at: checkpoint-documents/8cd18cae-af48-4628-881a-aae2b3ddde1c/document-_home_runner_workspace_attached_assets_Pasted-Generate-Seeder-Script-for-PostgreSQL-Menu-Categories-Settings-Orders-Prompt-csharp-Copy-Edit-1747873285345.txt-1748048681007-9a837d3f-46df-4bae-99cb-2e2a909888f2.json
2025-05-24 10:41:32.099 [warning] 'CheckpointHydration' Failed to hydrate checkpoint: Error: Checkpoint document not found at: checkpoint-documents/8cd18cae-af48-4628-881a-aae2b3ddde1c/document-_home_runner_workspace_attached_assets_Pasted-Generate-Seeder-Script-for-PostgreSQL-Menu-Categories-Settings-Orders-Prompt-csharp-Copy-Edit-1747873285345.txt-1748048681007-9a837d3f-46df-4bae-99cb-2e2a909888f2.json
2025-05-24 10:41:32.102 [warning] 'CheckpointHydration' Failed to hydrate checkpoint: Error: Checkpoint document not found at: checkpoint-documents/8cd18cae-af48-4628-881a-aae2b3ddde1c/document-_home_runner_workspace_attached_assets_Pasted-Redesign-the-Home-page-of-the-Barbecuez-Restaurant-website-using-React-TailwindCSS-to-re-1747869566148.txt-1748048681774-9a837d3f-46df-4bae-99cb-2e2a909888f2.json
2025-05-24 10:41:32.102 [warning] 'CheckpointHydration' Failed to hydrate checkpoint: Error: Checkpoint document not found at: checkpoint-documents/8cd18cae-af48-4628-881a-aae2b3ddde1c/document-_home_runner_workspace_attached_assets_Pasted-Redesign-the-Home-page-of-the-Barbecuez-Restaurant-website-using-React-TailwindCSS-to-re-1747869566148.txt-1748048681774-9a837d3f-46df-4bae-99cb-2e2a909888f2.json
2025-05-24 10:41:32.104 [warning] 'CheckpointHydration' Failed to hydrate checkpoint: Error: Checkpoint document not found at: checkpoint-documents/8cd18cae-af48-4628-881a-aae2b3ddde1c/document-_home_runner_workspace_attached_assets_Pasted-Redesign-the-Menu-Page-to-provide-a-luxurious-animated-interactive-food-ordering-experience-that-a-1747869938419.txt-1748048682494-9a837d3f-46df-4bae-99cb-2e2a909888f2.json
2025-05-24 10:41:32.104 [warning] 'CheckpointHydration' Failed to hydrate checkpoint: Error: Checkpoint document not found at: checkpoint-documents/8cd18cae-af48-4628-881a-aae2b3ddde1c/document-_home_runner_workspace_attached_assets_Pasted-Redesign-the-Menu-Page-to-provide-a-luxurious-animated-interactive-food-ordering-experience-that-a-1747869938419.txt-1748048682494-9a837d3f-46df-4bae-99cb-2e2a909888f2.json
2025-05-24 10:41:32.107 [warning] 'CheckpointHydration' Failed to hydrate checkpoint: Error: Checkpoint document not found at: checkpoint-documents/8cd18cae-af48-4628-881a-aae2b3ddde1c/document-_home_runner_workspace_attached_assets_image_1747873176091.png-1748048683215-9a837d3f-46df-4bae-99cb-2e2a909888f2.json
2025-05-24 10:41:32.108 [warning] 'CheckpointHydration' Failed to hydrate checkpoint: Error: Checkpoint document not found at: checkpoint-documents/8cd18cae-af48-4628-881a-aae2b3ddde1c/document-_home_runner_workspace_attached_assets_image_1747873176091.png-1748048683215-9a837d3f-46df-4bae-99cb-2e2a909888f2.json
2025-05-24 10:41:32.131 [warning] 'CheckpointHydration' Failed to hydrate checkpoint: Error: Checkpoint document not found at: checkpoint-documents/8cd18cae-af48-4628-881a-aae2b3ddde1c/document-_home_runner_workspace_attached_assets_image_1747874186898.png-1748048684084-9a837d3f-46df-4bae-99cb-2e2a909888f2.json
2025-05-24 10:41:32.132 [warning] 'CheckpointHydration' Failed to hydrate checkpoint: Error: Checkpoint document not found at: checkpoint-documents/8cd18cae-af48-4628-881a-aae2b3ddde1c/document-_home_runner_workspace_attached_assets_image_1747874186898.png-1748048684084-9a837d3f-46df-4bae-99cb-2e2a909888f2.json
2025-05-24 10:41:35.332 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 10:41:35.332 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 10:41:35.353 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 10:41:35.749 [warning] 'AggregateCheckpointManager' Cannot get aggregate checkpoint for .. No checkpoints found.
2025-05-24 10:41:35.749 [warning] 'AggregateCheckpointManager' Cannot get aggregate checkpoint for .. No checkpoints found.
2025-05-24 10:41:47.161 [info] 'WorkspaceManager[workspace]' Tracking enabled
2025-05-24 10:41:47.161 [info] 'WorkspaceManager[workspace]' Path metrics:
  - directories emitted: 361
  - files emitted: 1761
  - other paths emitted: 4
  - total paths emitted: 2126
  - timing stats:
    - readDir: 6 ms
    - filter: 198 ms
    - yield: 25 ms
    - total: 311 ms
2025-05-24 10:41:47.161 [info] 'WorkspaceManager[workspace]' File metrics:
  - paths accepted: 1638
  - paths not accessible: 0
  - not plain files: 0
  - large files: 27
  - blob name calculation fails: 0
  - encoding errors: 0
  - mtime cache hits: 1531
  - mtime cache misses: 107
  - probe batches: 9
  - blob names probed: 1693
  - files read: 316
  - blobs uploaded: 57
  - timing stats:
    - ingestPath: 7 ms
    - probe: 6231 ms
    - stat: 26 ms
    - read: 2217 ms
    - upload: 5882 ms
2025-05-24 10:41:47.161 [info] 'WorkspaceManager[workspace]' Startup metrics:
  - create SourceFolder: 5004 ms
  - read MtimeCache: 1492 ms
  - pre-populate PathMap: 403 ms
  - create PathFilter: 1791 ms
  - create PathNotifier: 0 ms
  - enumerate paths: 316 ms
  - purge stale PathMap entries: 7 ms
  - enumerate: 0 ms
  - await DiskFileManager quiesced: 24341 ms
  - enable persist: 3 ms
  - total: 33357 ms
2025-05-24 10:41:47.161 [info] 'WorkspaceManager' Workspace startup complete in 36567 ms
2025-05-24 10:41:53.659 [error] 'RemoteAgentsMessenger' Unexpected message type: used-chat
2025-05-24 10:41:53.659 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 10:41:53.659 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 10:41:53.672 [warning] 'AggregateCheckpointManager' Cannot get aggregate checkpoint for .. No checkpoints found.
2025-05-24 10:41:53.949 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 10:41:54.161 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 10:41:54.163 [warning] 'AggregateCheckpointManager' Cannot get aggregate checkpoint for .. No checkpoints found.
2025-05-24 10:42:01.782 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 10:42:04.456 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 10:42:04.728 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 10:42:07.345 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 10:42:07.348 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 10:42:07.610 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 10:42:10.759 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 10:42:10.760 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 10:42:10.760 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 10:42:10.763 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 10:42:11.146 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 10:42:14.981 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 10:42:15.243 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 10:42:22.851 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 10:42:22.851 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 10:42:23.155 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 10:42:23.156 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 10:42:23.523 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 10:42:23.523 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 10:42:23.572 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 10:42:23.572 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 10:42:23.891 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 10:42:23.892 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 10:42:24.255 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 10:42:24.255 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 10:42:24.847 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 10:42:25.111 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 10:42:25.509 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 10:42:25.510 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 10:42:27.800 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 10:42:28.118 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 10:42:31.791 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 10:42:36.384 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 10:42:37.083 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 10:42:37.124 [info] 'ViewTool' Tool called with path: package.json and view_range: [1,50]
2025-05-24 10:42:37.546 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 10:42:37.808 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 10:42:44.288 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 10:42:44.593 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 10:42:47.903 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 10:42:48.176 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 10:42:54.583 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 10:42:54.876 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 10:42:54.889 [info] 'ViewTool' Tool called with path: migrations/003_add_order_details.sql and view_range: undefined
2025-05-24 10:42:55.265 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 10:42:55.544 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 10:43:01.791 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 10:43:09.545 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 10:43:09.815 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 10:43:11.897 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 10:43:11.898 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 10:43:11.900 [warning] 'AggregateCheckpointManager' Cannot get aggregate checkpoint for .. No checkpoints found.
2025-05-24 10:43:12.185 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 10:43:17.647 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 10:43:17.908 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 10:43:19.811 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 10:43:20.092 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 10:43:29.265 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 10:43:29.529 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 10:43:29.538 [info] 'ToolFileUtils' Reading file: fix-orders-table.js
2025-05-24 10:43:29.538 [info] 'ToolFileUtils' Successfully read file: fix-orders-table.js (1831 bytes)
2025-05-24 10:43:29.938 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 10:43:30.029 [warning] 'AggregateCheckpointManager' Cannot get aggregate checkpoint for .. No checkpoints found.
2025-05-24 10:43:30.932 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 10:43:30.933 [warning] 'AggregateCheckpointManager' Cannot get aggregate checkpoint for .. No checkpoints found.
2025-05-24 10:43:31.741 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 10:43:31.743 [warning] 'AggregateCheckpointManager' Cannot get aggregate checkpoint for .. No checkpoints found.
2025-05-24 10:43:31.776 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 10:43:31.800 [info] 'WorkspaceManager[workspace]' Directory created: .config/.vscode-server/data/User/History/-51e27ad6
2025-05-24 10:43:32.502 [info] 'ToolFileUtils' Reading file: fix-orders-table.js
2025-05-24 10:43:32.503 [info] 'ToolFileUtils' Successfully read file: fix-orders-table.js (1902 bytes)
2025-05-24 10:43:32.679 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 10:43:32.680 [warning] 'AggregateCheckpointManager' Cannot get aggregate checkpoint for .. No checkpoints found.
2025-05-24 10:43:35.689 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 10:43:35.947 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 10:43:40.884 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 10:43:41.144 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 10:43:46.210 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 10:43:46.491 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 10:43:52.039 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 10:43:52.040 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 10:43:52.696 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 10:43:52.991 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 10:43:53.205 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 10:43:53.477 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 10:43:58.066 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 10:43:58.341 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 10:43:58.533 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 10:43:58.815 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 10:44:01.774 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 10:44:04.550 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 10:44:04.822 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 10:44:05.089 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 10:44:05.392 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 10:44:09.813 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 10:44:10.081 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 10:44:10.296 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 10:44:10.563 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 10:44:15.098 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 10:44:15.362 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 10:44:26.663 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 10:44:26.963 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 10:44:31.462 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 10:44:31.736 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 10:44:31.781 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 10:44:31.929 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 10:44:32.233 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 10:44:38.016 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 10:44:38.281 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 10:44:38.548 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 10:44:38.814 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 10:44:45.120 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 10:44:45.383 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 10:44:46.999 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 10:44:47.272 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 10:44:53.692 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 10:44:53.951 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 10:44:55.519 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 10:44:55.790 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 10:45:01.490 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 10:45:01.758 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 10:45:01.783 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 10:45:02.016 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 10:45:02.365 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 10:45:06.908 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 10:45:07.182 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 10:45:07.411 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 10:45:07.680 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 10:45:14.400 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 10:45:14.665 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 10:45:16.304 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 10:45:16.573 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 10:45:21.714 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 10:45:21.979 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 10:45:23.587 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 10:45:23.845 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 10:45:28.586 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 10:45:28.852 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 10:45:29.144 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 10:45:30.068 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 10:45:31.780 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 10:45:34.629 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 10:45:34.896 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 10:45:35.291 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 10:45:35.673 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 10:45:41.005 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 10:45:41.270 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 10:45:41.529 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 10:45:41.795 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 10:45:47.097 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 10:45:47.380 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 10:45:47.389 [info] 'ViewTool' Tool called with path: server/index.ts and view_range: [1,50]
2025-05-24 10:45:48.319 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 10:45:48.594 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 10:45:55.962 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 10:45:56.234 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 10:45:57.863 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 10:45:58.129 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 10:46:01.781 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 10:46:06.869 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 10:46:07.144 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 10:46:07.157 [info] 'ToolFileUtils' Reading file: server/index.ts
2025-05-24 10:46:07.167 [info] 'ToolFileUtils' Successfully read file: server/index.ts (2296 bytes)
2025-05-24 10:46:07.363 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 10:46:07.365 [warning] 'AggregateCheckpointManager' Cannot get aggregate checkpoint for .. No checkpoints found.
2025-05-24 10:46:08.543 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 10:46:08.736 [warning] 'AggregateCheckpointManager' Cannot get aggregate checkpoint for .. No checkpoints found.
2025-05-24 10:46:09.607 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 10:46:09.614 [warning] 'AggregateCheckpointManager' Cannot get aggregate checkpoint for .. No checkpoints found.
2025-05-24 10:46:10.487 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 10:46:10.489 [warning] 'AggregateCheckpointManager' Cannot get aggregate checkpoint for .. No checkpoints found.
2025-05-24 10:46:11.348 [info] 'ToolFileUtils' Reading file: server/index.ts
2025-05-24 10:46:11.348 [info] 'ToolFileUtils' Successfully read file: server/index.ts (2316 bytes)
2025-05-24 10:46:11.528 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 10:46:11.546 [warning] 'AggregateCheckpointManager' Cannot get aggregate checkpoint for .. No checkpoints found.
2025-05-24 10:46:14.569 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 10:46:14.844 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 10:46:20.270 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 10:46:20.547 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 10:46:20.803 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 10:46:21.066 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 10:46:25.658 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 10:46:25.931 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 10:46:26.119 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 10:46:26.555 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 10:46:31.349 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 10:46:31.615 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 10:46:31.782 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 10:46:31.808 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 10:46:32.072 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 10:46:39.375 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 10:46:39.849 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 10:46:41.799 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 10:46:42.663 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 10:46:50.341 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 10:46:50.604 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 10:46:50.612 [info] 'ToolFileUtils' Reading file: server/index.ts
2025-05-24 10:46:50.613 [info] 'ToolFileUtils' Successfully read file: server/index.ts (2316 bytes)
2025-05-24 10:46:51.022 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 10:46:51.073 [warning] 'AggregateCheckpointManager' Cannot get aggregate checkpoint for .. No checkpoints found.
2025-05-24 10:46:52.025 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 10:46:52.026 [warning] 'AggregateCheckpointManager' Cannot get aggregate checkpoint for .. No checkpoints found.
2025-05-24 10:46:52.975 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 10:46:52.976 [warning] 'AggregateCheckpointManager' Cannot get aggregate checkpoint for .. No checkpoints found.
2025-05-24 10:46:53.797 [info] 'ToolFileUtils' Reading file: server/index.ts
2025-05-24 10:46:53.797 [info] 'ToolFileUtils' Successfully read file: server/index.ts (2296 bytes)
2025-05-24 10:46:53.974 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 10:46:53.975 [warning] 'AggregateCheckpointManager' Cannot get aggregate checkpoint for .. No checkpoints found.
2025-05-24 10:46:56.983 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 10:46:57.251 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 10:47:01.776 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 10:47:01.815 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 10:47:01.815 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 10:47:01.816 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 10:47:02.278 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 10:47:02.921 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 10:47:03.457 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 10:47:03.854 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 10:47:06.994 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 10:47:08.889 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 10:47:09.154 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 10:47:09.347 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 10:47:09.612 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 10:47:14.181 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 10:47:14.446 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 10:47:14.639 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 10:47:14.920 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 10:47:20.596 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 10:47:20.867 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 10:47:22.527 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 10:47:22.795 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 10:48:03.981 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 10:48:04.011 [info] 'AugmentConfigListener' settings parsed successfully
2025-05-24 10:48:04.157 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 10:48:04.157 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 10:48:05.019 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 10:48:05.388 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 10:48:05.778 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 10:48:10.969 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 10:48:11.384 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 10:48:12.067 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 10:48:12.357 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 10:48:17.815 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 10:48:18.118 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 10:48:19.738 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 10:48:20.014 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 10:48:24.786 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 10:48:25.069 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 10:48:26.668 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 10:48:26.950 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 10:48:31.782 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 10:48:44.437 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 10:48:44.708 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 10:48:46.562 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 10:48:46.562 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 10:48:46.565 [warning] 'AggregateCheckpointManager' Cannot get aggregate checkpoint for .. No checkpoints found.
2025-05-24 10:48:46.849 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 10:48:51.540 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 10:48:51.815 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 10:48:54.480 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 10:48:54.781 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 10:49:00.449 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 10:49:00.451 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 10:49:01.043 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 10:49:01.043 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 10:49:01.776 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 10:49:02.225 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 10:49:02.226 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 10:49:02.794 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 10:49:02.795 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 10:49:05.828 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 10:49:05.829 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 10:49:07.284 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 10:49:07.285 [warning] 'AggregateCheckpointManager' Cannot get aggregate checkpoint for .. No checkpoints found.
2025-05-24 10:49:07.653 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 10:49:07.655 [warning] 'AggregateCheckpointManager' Cannot get aggregate checkpoint for .. No checkpoints found.
2025-05-24 10:49:08.164 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 10:49:08.166 [warning] 'AggregateCheckpointManager' Cannot get aggregate checkpoint for .. No checkpoints found.
2025-05-24 10:49:31.782 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 10:50:01.786 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 10:50:31.784 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 10:51:01.794 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 10:51:31.791 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 10:51:34.853 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 10:51:35.034 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 10:51:35.036 [warning] 'AggregateCheckpointManager' Cannot get aggregate checkpoint for .. No checkpoints found.
2025-05-24 10:52:01.788 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 10:52:31.793 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 10:53:01.787 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 10:53:31.783 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 10:54:01.783 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 10:54:31.779 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 10:55:01.780 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 10:55:31.786 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 10:56:01.779 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 10:56:31.808 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 10:57:01.786 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 10:57:31.795 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 10:58:01.778 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 10:58:31.792 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 10:59:01.789 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 10:59:31.782 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:00:01.794 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:00:31.796 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:01:01.793 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:01:31.785 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:02:01.783 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:02:31.790 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:03:01.782 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:04:36.787 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:04:36.788 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:04:36.964 [info] 'AugmentConfigListener' settings parsed successfully
2025-05-24 11:04:37.043 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:05:01.791 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:05:31.795 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:06:01.791 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:06:31.785 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:07:01.795 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:07:31.791 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:08:01.794 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:08:31.788 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:09:01.797 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:09:31.795 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:10:01.783 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:10:31.795 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:11:01.794 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:11:06.839 [info] 'AugmentExtension' Retrieving model config
2025-05-24 11:11:07.155 [info] 'AugmentExtension' Retrieved model config
2025-05-24 11:11:07.155 [info] 'AugmentExtension' Returning model config
2025-05-24 11:11:31.816 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:12:01.795 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:12:31.789 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:13:01.795 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:13:31.793 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:14:01.795 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:14:31.802 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:15:01.794 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:16:52.873 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:16:52.874 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:16:53.052 [info] 'AugmentConfigListener' settings parsed successfully
2025-05-24 11:16:53.122 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:17:01.785 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:17:31.785 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:18:01.805 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:18:31.795 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:18:32.488 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:18:32.672 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:18:32.672 [info] 'ToolsWebviewMessageHandler' Received closeAllToolProcesses message
2025-05-24 11:18:32.672 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:18:32.672 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:18:32.672 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:18:32.854 [info] 'TaskManager' Setting current root task UUID to e32d22d4-ad72-4243-b521-68cf64daa6ee
2025-05-24 11:18:32.854 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:18:32.854 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:18:32.854 [info] 'TaskManager' Setting current root task UUID to e32d22d4-ad72-4243-b521-68cf64daa6ee
2025-05-24 11:18:32.854 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:18:33.171 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:18:52.404 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:18:52.592 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:19:00.994 [error] 'RemoteAgentsMessenger' Unexpected message type: used-chat
2025-05-24 11:19:00.994 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:19:00.994 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:19:01.011 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:19:01.171 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:19:01.505 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:19:01.789 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:19:20.930 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:19:20.930 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:19:21.111 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:19:23.401 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:19:23.403 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:19:23.581 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:19:23.583 [info] 'ViewTool' Tool called with path: shared/schema.ts and view_range: undefined
2025-05-24 11:19:23.944 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:19:24.161 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:19:28.181 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:19:30.951 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:19:31.138 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:19:31.793 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:19:36.675 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:19:37.034 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:19:42.584 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:19:42.584 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:19:43.138 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:19:43.141 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:19:43.318 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:19:43.319 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:19:43.429 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:19:43.429 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:19:43.871 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:19:43.873 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:19:44.177 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:19:44.178 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:19:46.653 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:19:46.835 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:19:52.195 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:19:52.379 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:19:59.130 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:19:59.315 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:19:59.318 [info] 'ViewTool' Tool called with path: server/storage.ts and view_range: [1070,1100]
2025-05-24 11:19:59.680 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:19:59.866 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:20:01.800 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:20:05.607 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:20:05.792 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:20:05.794 [info] 'ViewTool' Tool called with path: server/storage.ts and view_range: [1,50]
2025-05-24 11:20:06.379 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:20:06.566 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:20:20.666 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:20:20.856 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:20:20.858 [info] 'ToolFileUtils' Reading file: server/storage.ts
2025-05-24 11:20:20.858 [info] 'ToolFileUtils' Successfully read file: server/storage.ts (54465 bytes)
2025-05-24 11:20:21.215 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:20:22.042 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:20:22.545 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:20:22.870 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:20:22.871 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:20:22.871 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:20:22.872 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:20:22.872 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:20:22.873 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:20:22.873 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:20:22.874 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:20:22.874 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:20:22.875 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:20:22.875 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:20:22.875 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:20:22.875 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:20:22.876 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:20:22.876 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:20:22.877 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:20:22.877 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:20:22.877 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:20:22.878 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:20:22.879 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:20:22.879 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:20:22.880 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:20:23.379 [info] 'ToolFileUtils' Reading file: server/storage.ts
2025-05-24 11:20:23.379 [info] 'ToolFileUtils' Successfully read file: server/storage.ts (54510 bytes)
2025-05-24 11:20:23.558 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:20:24.560 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:20:24.748 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:20:26.122 [info] 'WorkspaceManager[workspace]' Directory created: .config/.vscode-server/data/User/workspaceStorage/28d495afc6b3165b645c0d6d358123ef/Augment.vscode-augment/augment-user-assets/checkpoint-documents/b5078cc1-8262-4251-ac4a-ee911cf891ed
2025-05-24 11:20:31.878 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:20:32.047 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:20:32.048 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:20:32.412 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:20:32.413 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:20:37.129 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:20:37.323 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:20:37.325 [info] 'ToolFileUtils' Reading file: server/storage.ts
2025-05-24 11:20:37.325 [info] 'ToolFileUtils' Successfully read file: server/storage.ts (54510 bytes)
2025-05-24 11:20:37.977 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:20:38.978 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:20:40.025 [info] 'ToolFileUtils' Reading file: server/storage.ts
2025-05-24 11:20:40.025 [info] 'ToolFileUtils' Successfully read file: server/storage.ts (55024 bytes)
2025-05-24 11:20:40.029 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:20:40.412 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:20:40.412 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:20:41.162 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:20:56.881 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:20:57.070 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:20:57.072 [info] 'ToolFileUtils' Reading file: server/storage.ts
2025-05-24 11:20:57.072 [info] 'ToolFileUtils' Successfully read file: server/storage.ts (55024 bytes)
2025-05-24 11:20:57.455 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:20:58.497 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:20:59.280 [info] 'ToolFileUtils' Reading file: server/storage.ts
2025-05-24 11:20:59.281 [info] 'ToolFileUtils' Successfully read file: server/storage.ts (55174 bytes)
2025-05-24 11:20:59.464 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:20:59.464 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:20:59.658 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:21:02.068 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:21:03.244 [info] 'StallDetector' Recent work: [{"name":"get-subscription-info","durationMs":1065.008957,"timestamp":"2025-05-24T11:21:03.132Z"}]
2025-05-24 11:21:07.073 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:21:07.262 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:21:07.264 [info] 'ToolFileUtils' Reading file: server/storage.ts
2025-05-24 11:21:07.264 [info] 'ToolFileUtils' Successfully read file: server/storage.ts (55174 bytes)
2025-05-24 11:21:07.860 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:21:08.812 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:21:09.317 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:21:09.834 [info] 'ToolFileUtils' Reading file: server/storage.ts
2025-05-24 11:21:09.835 [info] 'ToolFileUtils' Successfully read file: server/storage.ts (55205 bytes)
2025-05-24 11:21:10.014 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:21:13.396 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:21:13.618 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:21:21.082 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:21:21.355 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:21:21.358 [info] 'ToolFileUtils' Reading file: server/storage.ts
2025-05-24 11:21:21.358 [info] 'ToolFileUtils' Successfully read file: server/storage.ts (55205 bytes)
2025-05-24 11:21:22.141 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:21:23.090 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:21:23.587 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:21:24.137 [info] 'ToolFileUtils' Reading file: server/storage.ts
2025-05-24 11:21:24.137 [info] 'ToolFileUtils' Successfully read file: server/storage.ts (55401 bytes)
2025-05-24 11:21:24.322 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:21:24.322 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:21:24.626 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:21:31.791 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:21:32.938 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:21:33.139 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:21:33.153 [info] 'ToolFileUtils' Reading file: server/storage.ts
2025-05-24 11:21:33.154 [info] 'ToolFileUtils' Successfully read file: server/storage.ts (55401 bytes)
2025-05-24 11:21:34.643 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:21:35.051 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:21:37.520 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:21:38.296 [info] 'ToolFileUtils' Reading file: server/storage.ts
2025-05-24 11:21:38.296 [info] 'ToolFileUtils' Successfully read file: server/storage.ts (55356 bytes)
2025-05-24 11:21:38.758 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:21:38.758 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:21:39.219 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:21:46.833 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:21:47.023 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:21:47.311 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:21:47.499 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:21:51.333 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:21:51.525 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:21:51.810 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:21:52.008 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:21:55.913 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:21:56.104 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:21:56.318 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:21:56.506 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:22:00.768 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:22:00.957 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:22:01.188 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:22:01.418 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:22:01.797 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:22:05.937 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:22:06.127 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:22:06.312 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:22:06.503 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:22:11.061 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:22:11.254 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:22:11.256 [info] 'ViewTool' Tool called with path: package.json and view_range: undefined
2025-05-24 11:22:12.171 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:22:12.360 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:22:17.513 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:22:17.714 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:22:18.015 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:22:18.216 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:22:22.467 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:22:22.656 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:22:22.883 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:22:23.072 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:22:28.165 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:22:28.356 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:22:31.818 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:22:42.785 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:22:42.983 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:22:50.011 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:22:50.205 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:22:50.482 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:22:50.677 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:22:58.398 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:22:58.810 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:22:58.993 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:22:59.343 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:23:01.868 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:23:04.667 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:23:04.857 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:23:04.860 [info] 'ViewTool' Tool called with path: .env and view_range: undefined
2025-05-24 11:23:05.036 [info] 'ViewTool' Path does not exist: .env
2025-05-24 11:23:05.810 [info] 'ToolFileUtils' File not found: .env. No similar files found
2025-05-24 11:23:06.002 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:23:06.202 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:23:11.469 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:23:11.663 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:23:11.666 [info] 'ViewTool' Tool called with path: .env.example and view_range: undefined
2025-05-24 11:23:11.925 [info] 'ViewTool' Path does not exist: .env.example
2025-05-24 11:23:12.497 [info] 'ToolFileUtils' File not found: .env.example. No similar files found
2025-05-24 11:23:12.685 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:23:12.880 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:23:18.833 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:23:19.026 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:23:19.029 [info] 'ViewTool' Tool called with path: server/index.ts and view_range: [1,50]
2025-05-24 11:23:19.954 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:23:20.142 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:23:26.784 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:23:26.974 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:23:28.573 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:23:28.766 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:23:31.792 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:23:34.296 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:23:34.488 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:23:34.720 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:23:34.958 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:23:40.599 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:23:40.790 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:23:40.978 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:23:41.169 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:23:47.015 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:23:47.209 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:23:50.641 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:23:50.832 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:23:57.813 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:23:58.009 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:24:01.799 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:24:04.292 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:24:04.501 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:24:09.603 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:24:09.794 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:24:10.077 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:24:10.268 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:24:14.917 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:24:15.109 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:24:16.671 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:24:16.889 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:24:22.383 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:24:22.574 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:24:24.186 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:24:24.379 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:24:31.201 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:24:31.396 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:24:31.633 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:24:31.785 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:24:31.924 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:24:36.051 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:24:36.243 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:24:36.427 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:24:36.616 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:24:41.571 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:24:41.763 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:24:41.995 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:24:42.188 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:24:46.913 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:24:47.108 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:24:47.327 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:24:47.518 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:24:52.916 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:24:53.115 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:24:54.701 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:24:54.897 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:25:01.216 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:25:01.409 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:25:01.792 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:25:03.622 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:25:03.815 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:25:10.027 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:25:10.028 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:25:10.711 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:25:10.711 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:25:10.761 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:25:10.761 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:25:11.057 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:25:11.057 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:25:11.327 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:25:11.327 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:25:11.365 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:25:11.366 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:25:11.692 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:25:11.693 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:25:11.742 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:25:11.743 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:25:11.997 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:25:11.998 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:25:12.376 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:25:12.376 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:25:12.845 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:25:12.846 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:25:13.111 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:25:13.308 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:25:13.678 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:25:13.872 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:25:20.464 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:25:20.465 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:25:20.494 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:25:20.494 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:25:20.494 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:25:20.495 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:25:20.651 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:25:20.653 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:25:21.610 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:25:21.611 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:25:21.611 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:25:21.612 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:25:21.612 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:25:21.612 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:25:23.957 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:25:23.958 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:25:23.958 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:25:23.959 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:25:23.959 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:25:23.959 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:25:23.959 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:25:23.959 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:25:24.087 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:25:24.087 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:25:24.579 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:25:24.579 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:25:25.094 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:25:25.095 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:25:25.564 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:25:25.564 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:25:26.042 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:25:26.042 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:25:26.758 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:25:27.115 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:25:27.615 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:25:31.801 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:25:36.543 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:25:36.723 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:26:01.799 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:26:31.805 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:26:43.758 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:26:43.997 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:26:45.328 [error] 'RemoteAgentsMessenger' Unexpected message type: used-chat
2025-05-24 11:26:45.329 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:26:45.329 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:26:45.357 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:26:45.715 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:26:45.833 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:26:49.531 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:26:49.537 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:26:50.648 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:26:56.335 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:26:56.336 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:26:56.541 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:26:57.187 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:27:01.822 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:27:02.773 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:27:02.973 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:27:09.636 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:27:09.842 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:27:14.412 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:27:14.668 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:27:21.561 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:27:21.561 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:27:21.770 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:27:21.770 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:27:22.302 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:27:22.303 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:27:22.493 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:27:22.494 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:27:23.974 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:27:24.180 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:27:24.187 [info] 'ViewTool' Tool called with path: server/routes.ts and view_range: [105,140]
2025-05-24 11:27:24.547 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:27:24.748 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:27:31.826 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:27:36.885 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:27:37.085 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:27:37.089 [info] 'ToolFileUtils' Reading file: client/src/api/adminApi.ts
2025-05-24 11:27:37.620 [info] 'ToolFileUtils' Successfully read file: client/src/api/adminApi.ts (3802 bytes)
2025-05-24 11:27:37.831 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:27:38.823 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:27:39.085 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:27:39.091 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:27:39.091 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:27:39.091 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:27:39.091 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:27:39.091 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:27:39.091 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:27:39.091 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:27:39.091 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:27:39.091 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:27:39.091 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:27:39.091 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:27:39.091 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:27:39.092 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:27:39.093 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:27:39.093 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:27:40.267 [info] 'ToolFileUtils' Reading file: client/src/api/adminApi.ts
2025-05-24 11:27:40.267 [info] 'ToolFileUtils' Successfully read file: client/src/api/adminApi.ts (3808 bytes)
2025-05-24 11:27:40.442 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:27:43.505 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:27:43.706 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:27:50.037 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:27:50.451 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:27:50.477 [info] 'ViewTool' Tool called with path: client/src/api/adminApi.ts and view_range: [105,135]
2025-05-24 11:27:51.371 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:27:51.645 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:27:53.401 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:27:53.401 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:27:53.401 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:27:53.401 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:27:57.408 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:27:57.408 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:27:58.026 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:27:58.026 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:28:01.791 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:28:03.844 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:28:04.055 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:28:04.058 [info] 'ToolFileUtils' Reading file: client/src/api/adminApi.ts
2025-05-24 11:28:04.058 [info] 'ToolFileUtils' Successfully read file: client/src/api/adminApi.ts (3808 bytes)
2025-05-24 11:28:04.430 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:28:05.956 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:28:07.079 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:28:07.904 [info] 'ToolFileUtils' Reading file: client/src/api/adminApi.ts
2025-05-24 11:28:07.904 [info] 'ToolFileUtils' Successfully read file: client/src/api/adminApi.ts (3820 bytes)
2025-05-24 11:28:08.147 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:28:11.105 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:28:11.303 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:28:18.897 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:28:19.111 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:28:19.115 [info] 'ViewTool' Tool called with path: server/admin-api.ts and view_range: [320,370]
2025-05-24 11:28:20.129 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:28:20.331 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:28:26.887 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:28:27.087 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:28:28.710 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:28:28.912 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:28:31.792 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:28:33.835 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:28:34.040 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:28:34.401 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:28:34.603 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:28:42.145 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:28:42.145 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:28:42.146 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:28:42.146 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:28:42.180 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:28:42.181 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:28:42.360 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:28:42.361 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:28:42.575 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:28:42.576 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:28:43.391 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:28:43.392 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:28:46.444 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:28:46.447 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:28:46.447 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:28:46.447 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:28:46.447 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:28:46.447 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:28:46.448 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:28:46.448 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:28:46.448 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:28:46.448 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:28:46.448 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:28:46.448 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:28:46.614 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:28:46.615 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:28:46.615 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:28:46.615 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:28:46.615 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:28:46.615 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:28:46.756 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:28:46.757 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:28:46.757 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:28:46.757 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:28:46.858 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:28:46.858 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:28:47.074 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:28:47.074 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:28:49.104 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:28:49.461 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:28:49.975 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:29:01.792 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:29:31.797 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:30:01.789 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:30:05.116 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:30:05.317 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:30:15.885 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:30:16.235 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:30:17.716 [error] 'RemoteAgentsMessenger' Unexpected message type: used-chat
2025-05-24 11:30:17.717 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:30:17.717 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:30:17.737 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:30:17.737 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:30:18.093 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:30:18.217 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:30:20.747 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:30:21.421 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:30:24.880 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:30:24.884 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:30:24.904 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:30:25.969 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:30:29.899 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:30:29.899 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:30:30.108 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:30:31.792 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:30:35.104 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:30:36.721 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:30:36.923 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:30:45.777 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:30:45.777 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:30:46.029 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:30:46.031 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:30:46.228 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:30:46.229 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:30:46.229 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:30:46.229 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:30:46.499 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:30:46.499 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:30:46.768 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:30:46.769 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:30:46.812 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:30:46.818 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:30:46.819 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:30:46.820 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:30:46.972 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:30:46.972 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:30:46.972 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:30:46.973 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:30:47.241 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:30:47.242 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:30:47.242 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:30:47.242 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:30:47.544 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:30:47.545 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:30:47.545 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:30:47.546 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:30:47.985 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:30:47.985 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:30:47.985 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:30:47.985 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:30:48.621 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:30:48.821 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:30:50.529 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:30:50.729 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:30:55.579 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:30:55.782 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:30:57.472 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:30:57.681 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:31:01.787 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:31:02.738 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:31:02.739 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:31:03.107 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:31:03.107 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:31:03.107 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:31:03.108 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:31:03.383 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:31:03.383 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:31:03.467 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:31:03.467 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:31:04.088 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:31:04.088 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:31:04.317 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:31:04.318 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:31:05.183 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:31:05.183 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:31:05.202 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:31:05.202 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:31:05.799 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:31:05.799 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:31:08.760 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:31:08.968 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:31:08.974 [info] 'ToolFileUtils' Reading file: client/src/api/api.ts
2025-05-24 11:31:09.516 [info] 'ToolFileUtils' Successfully read file: client/src/api/api.ts (1147 bytes)
2025-05-24 11:31:09.700 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:31:10.696 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:31:11.398 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:31:11.518 [info] 'WorkspaceManager[workspace]' Directory created: .config/.vscode-server/data/User/History/-36a14e78
2025-05-24 11:31:12.218 [info] 'ToolFileUtils' Reading file: client/src/api/api.ts
2025-05-24 11:31:12.218 [info] 'ToolFileUtils' Successfully read file: client/src/api/api.ts (1146 bytes)
2025-05-24 11:31:12.395 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:31:15.594 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:31:16.447 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:31:22.339 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:31:22.543 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:31:24.173 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:31:24.377 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:31:30.039 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:31:30.245 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:31:31.789 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:31:35.539 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:31:35.768 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:31:41.938 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:31:41.939 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:31:41.939 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:31:41.939 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:31:44.740 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:31:44.953 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:31:56.225 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:31:56.436 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:32:01.433 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:32:01.647 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:32:01.795 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:32:07.035 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:32:07.239 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:32:11.451 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:32:11.660 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:32:12.013 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:32:12.258 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:32:17.080 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:32:17.294 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:32:17.661 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:32:17.867 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:32:23.169 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:32:23.375 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:32:25.114 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:32:25.325 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:32:29.489 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:32:29.695 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:32:31.782 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:32:31.799 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:32:31.993 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:32:35.818 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:32:36.033 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:32:36.219 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:32:36.427 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:32:40.887 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:32:41.095 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:32:43.012 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:32:43.219 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:32:48.735 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:32:48.941 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:32:49.303 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:32:49.519 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:32:55.991 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:32:56.202 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:32:56.208 [info] 'ViewTool' Tool called with path: client/src/pages/Menu.tsx and view_range: [187,205]
2025-05-24 11:32:57.436 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:32:57.642 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:33:01.798 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:33:04.022 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:33:04.022 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:33:04.089 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:33:04.090 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:33:05.300 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:33:05.301 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:33:05.368 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:33:05.622 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:33:05.628 [info] 'ViewTool' Tool called with path: client/src/pages/Menu.tsx and view_range: [130,170]
2025-05-24 11:33:06.027 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:33:06.237 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:33:16.653 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:33:16.655 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:33:18.233 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:33:18.234 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:33:18.234 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:33:18.234 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:33:19.160 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:33:19.161 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:33:19.329 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:33:19.329 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:33:19.509 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:33:19.510 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:33:19.611 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:33:19.612 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:33:19.792 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:33:19.793 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:33:26.420 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:33:26.638 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:33:26.647 [info] 'ToolFileUtils' Reading file: client/src/pages/Menu.tsx
2025-05-24 11:33:26.647 [info] 'ToolFileUtils' Successfully read file: client/src/pages/Menu.tsx (24120 bytes)
2025-05-24 11:33:26.832 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:33:27.830 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:33:28.532 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:33:29.386 [info] 'ToolFileUtils' Reading file: client/src/pages/Menu.tsx
2025-05-24 11:33:29.386 [info] 'ToolFileUtils' Successfully read file: client/src/pages/Menu.tsx (24420 bytes)
2025-05-24 11:33:29.565 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:33:31.789 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:33:32.568 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:33:32.812 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:33:36.639 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:33:36.848 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:33:37.307 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:33:37.517 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:33:42.129 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:33:42.129 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:33:42.747 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:33:42.747 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:33:43.341 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:33:43.555 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:33:43.917 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:33:44.129 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:33:50.112 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:33:50.113 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:33:51.238 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:33:51.446 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:33:58.686 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:33:58.903 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:34:01.795 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:34:04.896 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:34:04.896 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:34:05.763 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:34:05.763 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:34:06.970 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:34:06.970 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:34:07.268 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:34:07.270 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:34:14.440 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:34:14.663 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:34:14.670 [info] 'ToolFileUtils' Reading file: client/src/pages/Menu.tsx
2025-05-24 11:34:14.670 [info] 'ToolFileUtils' Successfully read file: client/src/pages/Menu.tsx (24420 bytes)
2025-05-24 11:34:15.215 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:34:16.224 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:34:16.727 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:34:17.196 [info] 'ToolFileUtils' Reading file: client/src/pages/Menu.tsx
2025-05-24 11:34:17.197 [info] 'ToolFileUtils' Successfully read file: client/src/pages/Menu.tsx (24120 bytes)
2025-05-24 11:34:17.388 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:34:20.392 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:34:20.605 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:34:27.111 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:34:27.111 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:34:27.142 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:34:27.142 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:34:28.135 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:34:28.136 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:34:28.651 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:34:28.652 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:34:29.015 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:34:29.231 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:34:29.237 [info] 'ViewTool' Tool called with path: client/src/pages/admin/Menu.tsx and view_range: [110,125]
2025-05-24 11:34:30.164 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:34:30.380 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:34:31.797 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:34:37.810 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:34:37.811 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:34:37.811 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:34:37.811 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:34:38.323 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:34:38.323 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:34:39.129 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:34:39.129 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:34:39.299 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:34:39.299 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:34:39.825 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:34:40.035 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:34:40.044 [info] 'ViewTool' Tool called with path: client/src/api/adminApi.ts and view_range: [105,110]
2025-05-24 11:34:40.943 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:34:41.160 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:34:46.735 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:34:46.735 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:34:49.261 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:34:49.262 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:34:49.530 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:34:49.530 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:34:49.851 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:34:50.148 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:34:50.154 [info] 'ViewTool' Tool called with path: server/storage.ts and view_range: [1095,1110]
2025-05-24 11:34:50.512 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:34:50.725 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:34:56.206 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:34:56.206 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:34:56.400 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:34:56.400 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:34:56.713 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:34:56.713 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:34:57.128 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:34:57.131 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:34:58.002 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:34:58.003 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:34:58.003 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:34:58.003 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:34:58.380 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:34:58.381 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:35:01.790 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:35:01.964 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:35:02.182 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:35:02.189 [info] 'ToolFileUtils' Reading file: client/src/api/adminApi.ts
2025-05-24 11:35:02.190 [info] 'ToolFileUtils' Successfully read file: client/src/api/adminApi.ts (3820 bytes)
2025-05-24 11:35:02.978 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:35:03.987 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:35:04.494 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:35:04.864 [info] 'ToolFileUtils' Reading file: client/src/api/adminApi.ts
2025-05-24 11:35:04.864 [info] 'ToolFileUtils' Successfully read file: client/src/api/adminApi.ts (3818 bytes)
2025-05-24 11:35:05.039 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:35:08.137 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:35:08.404 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:35:16.413 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:35:16.640 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:35:16.647 [info] 'ToolFileUtils' Reading file: client/src/pages/admin/Menu.tsx
2025-05-24 11:35:16.648 [info] 'ToolFileUtils' Successfully read file: client/src/pages/admin/Menu.tsx (26920 bytes)
2025-05-24 11:35:16.834 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:35:17.905 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:35:18.688 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:35:19.502 [info] 'ToolFileUtils' Reading file: client/src/pages/admin/Menu.tsx
2025-05-24 11:35:19.502 [info] 'ToolFileUtils' Successfully read file: client/src/pages/admin/Menu.tsx (26896 bytes)
2025-05-24 11:35:19.680 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:35:22.853 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:35:23.076 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:35:31.797 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:35:32.311 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:35:32.531 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:35:32.538 [info] 'ToolFileUtils' Reading file: client/src/pages/admin/Menu.tsx
2025-05-24 11:35:32.538 [info] 'ToolFileUtils' Successfully read file: client/src/pages/admin/Menu.tsx (26896 bytes)
2025-05-24 11:35:32.970 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:35:33.919 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:35:34.422 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:35:34.803 [info] 'ToolFileUtils' Reading file: client/src/pages/admin/Menu.tsx
2025-05-24 11:35:34.803 [info] 'ToolFileUtils' Successfully read file: client/src/pages/admin/Menu.tsx (26892 bytes)
2025-05-24 11:35:34.985 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:35:37.989 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:35:38.200 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:35:45.801 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:35:46.030 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:35:46.038 [info] 'ToolFileUtils' Reading file: client/src/pages/admin/Menu.tsx
2025-05-24 11:35:46.038 [info] 'ToolFileUtils' Successfully read file: client/src/pages/admin/Menu.tsx (26892 bytes)
2025-05-24 11:35:46.493 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:35:47.473 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:35:47.981 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:35:48.347 [info] 'ToolFileUtils' Reading file: client/src/pages/admin/Menu.tsx
2025-05-24 11:35:48.348 [info] 'ToolFileUtils' Successfully read file: client/src/pages/admin/Menu.tsx (26891 bytes)
2025-05-24 11:35:48.537 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:35:51.531 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:35:51.745 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:35:59.511 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:35:59.727 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:35:59.733 [info] 'ToolFileUtils' Reading file: client/src/pages/admin/Menu.tsx
2025-05-24 11:35:59.733 [info] 'ToolFileUtils' Successfully read file: client/src/pages/admin/Menu.tsx (26891 bytes)
2025-05-24 11:36:00.106 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:36:01.102 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:36:01.612 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:36:01.800 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:36:02.083 [info] 'ToolFileUtils' Reading file: client/src/pages/admin/Menu.tsx
2025-05-24 11:36:02.083 [info] 'ToolFileUtils' Successfully read file: client/src/pages/admin/Menu.tsx (26889 bytes)
2025-05-24 11:36:02.259 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:36:05.267 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:36:05.488 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:36:14.474 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:36:14.702 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:36:14.708 [info] 'ToolFileUtils' Reading file: client/src/pages/admin/Menu.tsx
2025-05-24 11:36:14.708 [info] 'ToolFileUtils' Successfully read file: client/src/pages/admin/Menu.tsx (26889 bytes)
2025-05-24 11:36:15.093 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:36:16.098 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:36:16.598 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:36:16.996 [info] 'ToolFileUtils' Reading file: client/src/pages/admin/Menu.tsx
2025-05-24 11:36:16.997 [info] 'ToolFileUtils' Successfully read file: client/src/pages/admin/Menu.tsx (26887 bytes)
2025-05-24 11:36:17.186 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:36:20.204 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:36:20.421 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:36:29.191 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:36:29.409 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:36:29.415 [info] 'ToolFileUtils' Reading file: client/src/pages/admin/Menu.tsx
2025-05-24 11:36:29.416 [info] 'ToolFileUtils' Successfully read file: client/src/pages/admin/Menu.tsx (26887 bytes)
2025-05-24 11:36:29.828 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:36:30.787 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:36:31.322 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:36:31.724 [info] 'ToolFileUtils' Reading file: client/src/pages/admin/Menu.tsx
2025-05-24 11:36:31.724 [info] 'ToolFileUtils' Successfully read file: client/src/pages/admin/Menu.tsx (26885 bytes)
2025-05-24 11:36:31.801 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:36:31.901 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:36:35.043 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:36:35.264 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:36:40.583 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:36:40.800 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:36:40.806 [info] 'ViewTool' Tool called with path: client/src/pages/admin/Menu.tsx and view_range: [650,670]
2025-05-24 11:36:41.169 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:36:41.387 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:36:51.776 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:36:51.992 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:36:51.999 [info] 'ToolFileUtils' Reading file: client/src/pages/admin/Menu.tsx
2025-05-24 11:36:51.999 [info] 'ToolFileUtils' Successfully read file: client/src/pages/admin/Menu.tsx (26885 bytes)
2025-05-24 11:36:52.377 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:36:53.383 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:36:54.228 [info] 'ToolFileUtils' Reading file: client/src/pages/admin/Menu.tsx
2025-05-24 11:36:54.228 [info] 'ToolFileUtils' Successfully read file: client/src/pages/admin/Menu.tsx (26883 bytes)
2025-05-24 11:36:54.403 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:36:57.412 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:36:57.686 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:37:01.798 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:37:03.314 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:37:03.533 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:37:03.544 [info] 'ViewTool' Tool called with path: client/src/pages/admin/Menu.tsx and view_range: [70,90]
2025-05-24 11:37:03.905 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:37:04.123 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:37:15.013 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:37:15.241 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:37:15.249 [info] 'ToolFileUtils' Reading file: client/src/pages/admin/Menu.tsx
2025-05-24 11:37:15.250 [info] 'ToolFileUtils' Successfully read file: client/src/pages/admin/Menu.tsx (26883 bytes)
2025-05-24 11:37:15.783 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:37:16.706 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:37:17.211 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:37:17.780 [info] 'ToolFileUtils' Reading file: client/src/pages/admin/Menu.tsx
2025-05-24 11:37:17.780 [info] 'ToolFileUtils' Successfully read file: client/src/pages/admin/Menu.tsx (26881 bytes)
2025-05-24 11:37:17.958 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:37:21.006 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:37:21.244 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:37:22.711 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:37:22.893 [error] 'RemoteAgentsMessenger' Unexpected message type: open-file
2025-05-24 11:37:29.700 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:37:29.929 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:37:30.045 [info] 'ToolFileUtils' Reading file: client/src/pages/admin/Menu.tsx
2025-05-24 11:37:30.045 [info] 'ToolFileUtils' Successfully read file: client/src/pages/admin/Menu.tsx (26881 bytes)
2025-05-24 11:37:30.259 [error] 'ChangeTracker' invalid chunk: 
2025-05-24 11:37:30.534 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:37:31.469 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:37:31.815 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:37:31.968 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:37:32.470 [info] 'ToolFileUtils' Reading file: client/src/pages/admin/Menu.tsx
2025-05-24 11:37:32.470 [info] 'ToolFileUtils' Successfully read file: client/src/pages/admin/Menu.tsx (26880 bytes)
2025-05-24 11:37:32.653 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:37:34.652 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:37:34.877 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:37:40.387 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:37:40.611 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:37:40.800 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:37:41.018 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:37:45.681 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:37:45.912 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:37:46.680 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:37:46.913 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:37:52.947 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:37:52.948 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:37:52.948 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:37:52.951 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:37:54.056 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:37:54.056 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:37:54.056 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:37:54.056 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:37:54.680 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:37:54.680 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:37:57.658 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:37:57.882 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:37:57.888 [info] 'ViewTool' Tool called with path: client/src/components/admin/MenuItemCard.tsx and view_range: [35,45]
2025-05-24 11:37:58.780 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:37:59.005 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:38:01.795 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:38:04.429 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:38:04.430 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:38:04.761 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:38:04.986 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:38:05.347 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:38:05.570 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:38:10.108 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:38:10.108 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:38:10.731 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:38:10.731 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:38:13.148 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:38:13.149 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:38:13.149 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:38:13.150 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:38:13.150 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:38:13.151 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:38:13.151 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:38:13.152 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:38:13.152 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:38:13.153 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:38:13.153 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:38:13.153 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:38:13.153 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:38:13.153 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:38:13.153 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:38:13.153 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:38:13.153 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:38:13.153 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:38:13.154 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:38:13.154 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:38:13.154 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:38:13.154 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:38:13.480 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:38:13.480 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:38:13.615 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:38:13.615 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:38:14.779 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:38:14.779 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:38:14.779 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:38:14.780 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:38:15.398 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:38:15.398 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:38:15.398 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:38:15.398 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:38:15.745 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:38:15.745 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:38:15.798 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:38:15.798 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:38:15.995 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:38:15.996 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:38:16.080 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:38:16.080 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:38:16.080 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:38:16.081 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:38:16.349 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:38:16.349 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:38:17.992 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:38:18.351 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:38:18.861 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:38:31.804 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:38:49.396 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:38:49.598 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:39:01.792 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:39:31.792 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:39:51.290 [info] 'WorkspaceManager[workspace]' Directory created: .cache/replit/transfers
2025-05-24 11:40:01.812 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:40:31.794 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:41:01.794 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:41:07.347 [info] 'AugmentExtension' Retrieving model config
2025-05-24 11:41:08.390 [info] 'AugmentExtension' Retrieved model config
2025-05-24 11:41:08.390 [info] 'AugmentExtension' Returning model config
2025-05-24 11:41:31.797 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:42:01.799 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:42:31.799 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:43:01.799 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
