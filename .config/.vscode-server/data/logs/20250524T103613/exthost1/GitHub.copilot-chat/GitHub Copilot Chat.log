2025-05-24 10:36:23.449 [info] Can't use the Electron fetcher in this environment.
2025-05-24 10:36:23.449 [info] Using the Node fetch fetcher.
2025-05-24 10:36:23.449 [info] Initializing Git extension service.
2025-05-24 10:36:23.449 [info] Successfully activated the vscode.git extension.
2025-05-24 10:36:23.449 [info] Enablement state of the vscode.git extension: true.
2025-05-24 10:36:23.449 [info] Successfully registered Git commit message provider.
2025-05-24 10:36:24.660 [info] Logged in as mazen91111
2025-05-24 10:36:25.529 [info] Got Copilot token for mazen91111
2025-05-24 10:36:26.542 [info] Fetched model metadata in 1003ms 4d4d2c1b-c8cb-47b1-9008-948c2317e609
2025-05-24 10:36:26.587 [info] activationBlocker from 'languageModelAccess' took for 3085ms
2025-05-24 10:36:27.150 [info] copilot token chat_enabled: true, sku: free_limited_copilot
2025-05-24 10:36:27.150 [info] GitHub.vscode-pull-request-github extension is not yet activated.
2025-05-24 10:36:27.205 [info] Registering default platform agent...
2025-05-24 10:36:27.989 [info] BYOK: Copilot Chat known models list fetched successfully.
