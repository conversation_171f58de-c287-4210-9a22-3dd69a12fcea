2025-05-24 10:36:17.294 [info] Extension host with pid 246 started
2025-05-24 10:36:17.457 [info] ExtensionService#_doActivateExtension vscode.emmet, startup: false, activationEvent: 'onLanguage'
2025-05-24 10:36:17.719 [info] ExtensionService#_doActivateExtension vscode.git-base, startup: true, activationEvent: '*', root cause: vscode.git
2025-05-24 10:36:17.902 [info] ExtensionService#_doActivateExtension vscode.git, startup: true, activationEvent: '*'
2025-05-24 10:36:17.903 [info] ExtensionService#_doActivateExtension vscode.github, startup: true, activationEvent: '*'
2025-05-24 10:36:18.515 [info] Eager extensions activated
2025-05-24 10:36:18.515 [info] ExtensionService#_doActivateExtension vscode.debug-auto-launch, startup: false, activationEvent: 'onStartupFinished'
2025-05-24 10:36:18.516 [info] ExtensionService#_doActivateExtension vscode.merge-conflict, startup: false, activationEvent: 'onStartupFinished'
2025-05-24 10:36:18.516 [info] ExtensionService#_doActivateExtension Augment.vscode-augment, startup: false, activationEvent: 'onStartupFinished'
2025-05-24 10:36:18.517 [info] ExtensionService#_doActivateExtension coderabbit.coderabbit-vscode, startup: false, activationEvent: 'onStartupFinished'
2025-05-24 10:36:18.517 [info] ExtensionService#_doActivateExtension GitHub.copilot, startup: false, activationEvent: 'onStartupFinished'
2025-05-24 10:36:18.517 [info] ExtensionService#_doActivateExtension GitHub.copilot-chat, startup: false, activationEvent: 'onStartupFinished'
2025-05-24 10:37:40.602 [info] Extension host terminating: received terminate message from renderer
2025-05-24 10:37:40.639 [info] Extension host with pid 246 exiting with code 0
