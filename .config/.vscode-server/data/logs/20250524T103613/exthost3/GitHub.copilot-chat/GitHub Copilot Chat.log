2025-05-24 10:40:35.680 [info] Can't use the Electron fetcher in this environment.
2025-05-24 10:40:35.680 [info] Using the Node fetch fetcher.
2025-05-24 10:40:35.680 [info] Initializing Git extension service.
2025-05-24 10:40:35.680 [info] Successfully activated the vscode.git extension.
2025-05-24 10:40:35.680 [info] Enablement state of the vscode.git extension: true.
2025-05-24 10:40:35.680 [info] Successfully registered Git commit message provider.
2025-05-24 10:40:37.339 [info] Logged in as mazen91111
2025-05-24 10:40:38.596 [info] Got Copilot token for mazen91111
2025-05-24 10:40:39.646 [info] Fetched model metadata in 1039ms b0a53c5a-4ecd-49c5-83c5-1d8607e88bf0
2025-05-24 10:40:39.693 [info] activationBlocker from 'languageModelAccess' took for 3381ms
2025-05-24 10:40:40.308 [info] copilot token chat_enabled: true, sku: free_limited_copilot
2025-05-24 10:40:40.308 [info] GitHub.vscode-pull-request-github extension is not yet activated.
2025-05-24 10:40:40.343 [info] Registering default platform agent...
2025-05-24 10:40:44.183 [info] BYOK: Copilot Chat known models list fetched successfully.
