2025-05-24 11:44:15.018 [info] 'AugmentConfigListener' settings parsed successfully
2025-05-24 11:44:15.019 [info] 'AugmentConfigListener' Config changed from <unset> to {"apiToken":"","completionURL":"","modelName":"","conflictingCodingAssistantCheck":true,"codeInstruction":{},"chat":{"enableEditableHistory":false,"useRichTextHistory":true,"smartPasteUsePrecomputation":true,"experimentalFullFilePaste":false,"modelDisplayNameToId":{"Augment":null},"userGuidelines":""},"agent":{},"autofix":{"enabled":false},"oauth":{"clientID":"augment-vscode-extension","url":"https://auth.augmentcode.com"},"enableUpload":true,"shortcutsDisplayDelayMS":2000,"enableEmptyFileHint":true,"enableDataCollection":false,"enableDebugFeatures":false,"enableReviewerWorkflows":false,"completions":{"enableAutomaticCompletions":true,"disableCompletionsByLanguage":{},"enableQuickSuggestions":true,"timeoutMs":800,"maxWaitMs":1600,"addIntelliSenseSuggestions":true},"openFileManager":{},"nextEdit":{"backgroundEnabled":true,"useCursorDecorations":false,"allowDuringDebugging":false,"useMockResults":false,"noDiffModeUseCodeLens":false,"enableBackgroundSuggestions":true,"enableGlobalBackgroundSuggestions":false,"highlightSuggestionsInTheEditor":false,"showDiffInHover":false,"enableAutoApply":true},"recencySignalManager":{"collectTabSwitchEvents":false},"preferenceCollection":{"enable":false,"enableRetrievalDataCollection":false,"enableRandomizedMode":true},"vcs":{"watcherEnabled":false},"git":{"enableCommitIndexing":false,"maxCommitsToIndex":100},"smartPaste":{},"instructions":{},"integrations":{},"mcpServers":[],"advanced":{}}
2025-05-24 11:44:15.019 [info] 'FeatureFlagManager' feature flags changed from <unset> to {"gitDiff":false,"gitDiffPollingFrequencyMSec":0,"additionalChatModels":"","smallSyncThreshold":15,"bigSyncThreshold":1000,"enableWorkspaceManagerUi":true,"enableInstructions":false,"enableSmartPaste":false,"enableSmartPasteMinVersion":"","enablePromptEnhancer":false,"enableViewTextDocument":false,"bypassLanguageFilter":false,"enableHindsight":false,"maxUploadSizeBytes":131072,"vscodeNextEditBottomPanelMinVersion":"","vscodeNextEditMinVersion":"","vscodeNextEditUx1MaxVersion":"","vscodeNextEditUx2MaxVersion":"","vscodeFlywheelMinVersion":"","vscodeExternalSourcesInChatMinVersion":"","vscodeShareMinVersion":"","maxTrackableFileCount":250000,"maxTrackableFileCountWithoutPermission":150000,"minUploadedPercentageWithoutPermission":90,"memoryClassificationOnFirstToken":false,"vscodeSourcesMinVersion":"","vscodeChatHintDecorationMinVersion":"","nextEditDebounceMs":400,"enableCompletionFileEditEvents":false,"vscodeEnableCpuProfile":false,"verifyFolderIsSourceRepo":false,"refuseToSyncHomeDirectories":false,"enableFileLimitsForSyncingPermission":false,"enableChatMermaidDiagrams":false,"enableSummaryTitles":false,"smartPastePrecomputeMode":"visible-hover","vscodeNewThreadsMenuMinVersion":"","vscodeEditableHistoryMinVersion":"","vscodeEnableChatMermaidDiagramsMinVersion":"","userGuidelinesLengthLimit":2000,"workspaceGuidelinesLengthLimit":2000,"enableGuidelines":false,"useCheckpointManagerContextMinVersion":"","validateCheckpointManagerContext":false,"vscodeDesignSystemRichTextEditorMinVersion":"","allowClientFeatureFlagOverrides":false,"vscodeChatWithToolsMinVersion":"","vscodeChatMultimodalMinVersion":"","vscodeAgentModeMinVersion":"","vscodeAgentModeMinStableVersion":"","vscodeBackgroundAgentsMinVersion":"","vscodeAgentEditTool":"backend_edit_tool","vscodeRichCheckpointInfoMinVersion":"","vscodeDirectApplyMinVersion":"","memoriesParams":{},"eloModelConfiguration":{"highPriorityModels":[],"regularBattleModels":[],"highPriorityThreshold":0.5},"vscodeVirtualizedMessageListMinVersion":"","vscodeChatStablePrefixTruncationMinVersion":"","agentEditToolMinViewSize":0,"agentEditToolSchemaType":"StrReplaceEditorToolDefinitionNested","agentEditToolEnableFuzzyMatching":false,"agentEditToolFuzzyMatchSuccessMessage":"Replacement successful. old_str and new_str were slightly modified to match the original file content.","agentEditToolFuzzyMatchMaxDiff":50,"agentEditToolFuzzyMatchMaxDiffRatio":0.15,"agentEditToolFuzzyMatchMinAllMatchStreakBetweenDiffs":5,"agentEditToolInstructionsReminder":false,"agentEditToolShowResultSnippet":true,"agentSaveFileToolInstructionsReminder":false,"vscodePersonalitiesMinVersion":"","useMemorySnapshotManager":false,"vscodeGenerateCommitMessageMinVersion":"","enableRules":false,"memoriesTextEditorEnabled":false,"enableModelRegistry":false,"openFileManagerV2Enabled":false,"modelRegistry":{},"vscodeTaskListMinVersion":"","vscodeRemoteAgentSSHMinVersion":"","clientAnnouncement":""}
2025-05-24 11:44:18.285 [info] 'AugmentExtension' Retrieving model config
2025-05-24 11:44:18.336 [info] 'AugmentConfigListener' settings parsed successfully
2025-05-24 11:44:19.084 [info] 'AugmentExtension' Retrieved model config
2025-05-24 11:44:19.084 [info] 'AugmentExtension' Returning model config
2025-05-24 11:44:19.121 [info] 'FeatureFlagManager' feature flags changed:
  - additionalChatModels: "" to "{}"
  - enableInstructions: false to true
  - enableSmartPasteMinVersion: "" to "0.267.0"
  - enablePromptEnhancer: false to true
  - enableViewTextDocument: false to true
  - bypassLanguageFilter: false to true
  - enableHindsight: false to true
  - maxUploadSizeBytes: 131072 to 524288
  - vscodeNextEditBottomPanelMinVersion: "" to "0.394.0"
  - vscodeNextEditMinVersion: "" to "0.343.0"
  - vscodeFlywheelMinVersion: "" to "0.282.0"
  - vscodeExternalSourcesInChatMinVersion: "" to "0.243.2"
  - vscodeShareMinVersion: "" to "0.314.0"
  - memoryClassificationOnFirstToken: false to true
  - vscodeChatHintDecorationMinVersion: "" to "0.274.0"
  - enableCompletionFileEditEvents: false to true
  - verifyFolderIsSourceRepo: false to true
  - refuseToSyncHomeDirectories: false to true
  - enableFileLimitsForSyncingPermission: false to true
  - enableSummaryTitles: false to true
  - smartPastePrecomputeMode: "visible-hover" to "visible"
  - vscodeNewThreadsMenuMinVersion: "" to "0.305.0"
  - vscodeEditableHistoryMinVersion: "" to "0.330.0"
  - vscodeEnableChatMermaidDiagramsMinVersion: "" to "0.314.0"
  - userGuidelinesLengthLimit: 2000 to 24576
  - workspaceGuidelinesLengthLimit: 2000 to 24576
  - enableGuidelines: false to true
  - useCheckpointManagerContextMinVersion: "" to "0.323.0"
  - vscodeDesignSystemRichTextEditorMinVersion: "" to "0.363.0"
  - vscodeChatMultimodalMinVersion: "" to "0.384.0"
  - vscodeAgentModeMinVersion: "" to "0.395.0"
  - vscodeAgentModeMinStableVersion: "" to "0.399.1"
  - vscodeAgentEditTool: "backend_edit_tool" to "str_replace_editor_tool"
  - eloModelConfiguration > highPriorityModels: [] to undefined
  - eloModelConfiguration > regularBattleModels: [] to undefined
  - eloModelConfiguration > highPriorityThreshold: 0.5 to undefined
  - vscodeChatStablePrefixTruncationMinVersion: "" to "0.402.0"
  - agentEditToolMinViewSize: 0 to 500
  - agentEditToolSchemaType: "StrReplaceEditorToolDefinitionNested" to "StrReplaceEditorToolDefinitionFlat"
  - agentEditToolEnableFuzzyMatching: false to true
  - agentEditToolInstructionsReminder: false to true
  - agentSaveFileToolInstructionsReminder: false to true
  - useMemorySnapshotManager: false to true
  - openFileManagerV2Enabled: false to true
  - vscodeRemoteAgentSSHMinVersion: "" to "0.456.0"
  - clientAnnouncement: "" to "🎉 Your Agents are now using Claude Sonnet 4!"
2025-05-24 11:44:19.121 [info] 'SyncingPermissionTracker' Initial syncing permission: syncing permission granted for workspace. Folders:
    /home/<USER>/workspace (explicit) at 5/21/2025, 11:43:10 PM
2025-05-24 11:44:19.121 [info] 'WorkspaceManager' OpenFileManagerProxy created. V2 enabled: [true]
2025-05-24 11:44:19.121 [info] 'BlobsCheckpointManager' BlobsCheckpointManager created. checkpointThreshold: 1000
2025-05-24 11:44:19.121 [info] 'SyncingPermissionTracker' Permission to sync folder /home/<USER>/workspace granted at 5/21/2025, 11:43:10 PM; type = explicit
2025-05-24 11:44:19.121 [info] 'WorkspaceManager' Adding workspace folder workspace; folderRoot = /home/<USER>/workspace; syncingPermission = granted
2025-05-24 11:44:19.121 [info] 'SyncingPermissionTracker' Updating syncing permission: syncing permission granted for workspace. Folders:
    /home/<USER>/workspace (explicit) at 5/21/2025, 11:43:10 PM
2025-05-24 11:44:19.141 [info] 'RemoteAgentsMessenger' RemoteAgentsMessenger initialized, setting up onDidChangeTextDocument listener
2025-05-24 11:44:19.141 [info] 'RemoteAgentsMessenger' Registering RemoteAgentsMessenger with AsyncMsgHandler
2025-05-24 11:44:19.141 [info] 'HotKeyHints' HotKeyHints initialized
2025-05-24 11:44:19.141 [info] 'ToolsModel' Loaded saved chat mode: AGENT
2025-05-24 11:44:19.157 [info] 'ToolsModel' Tools Mode: AGENT (3 hosts)
2025-05-24 11:44:19.158 [info] 'ToolsModel' Host: localToolHost (9 tools: 154 enabled, 0 disabled})
 + str-replace-editor
 + open-browser
 + diagnostics
 + read-terminal
 + launch-process
 + kill-process
 + read-process
 + write-process
 + list-processes

2025-05-24 11:44:19.587 [error] 'RemoteAgentsMessenger' Unexpected message type: main-panel-loaded
2025-05-24 11:44:19.944 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:44:19.945 [info] 'WorkspaceManager[workspace]' Start tracking
2025-05-24 11:44:19.946 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:44:19.946 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:44:19.953 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:44:19.953 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:44:19.953 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:44:19.953 [info] 'TaskManager' Setting current root task UUID to e32d22d4-ad72-4243-b521-68cf64daa6ee
2025-05-24 11:44:19.953 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:44:19.953 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:44:19.953 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:44:19.953 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:44:19.953 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:44:19.953 [error] 'RemoteAgentsMessenger' Unexpected message type: update-shared-webview-state
2025-05-24 11:44:19.960 [info] 'PathMap' Opened source folder /home/<USER>/workspace with id 100
2025-05-24 11:44:19.960 [info] 'OpenFileManager' Opened source folder 100
2025-05-24 11:44:19.961 [info] 'MtimeCache[workspace]' reading blob name cache from /home/<USER>/.vscode-server/data/User/workspaceStorage/28d495afc6b3165b645c0d6d358123ef/Augment.vscode-augment/ce248d0eb8d4f662cc202e5206f37824b70c818932a8dbfb169463d063d70aa1/mtime-cache.json
2025-05-24 11:44:19.968 [info] 'MtimeCache[workspace]' read 1644 entries from /home/<USER>/.vscode-server/data/User/workspaceStorage/28d495afc6b3165b645c0d6d358123ef/Augment.vscode-augment/ce248d0eb8d4f662cc202e5206f37824b70c818932a8dbfb169463d063d70aa1/mtime-cache.json
2025-05-24 11:44:20.056 [error] 'RemoteAgentsMessenger' Unexpected message type: get-orientation-status
2025-05-24 11:44:20.221 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:44:20.301 [info] 'ToolsModel' Tools Mode: AGENT (3 hosts)
2025-05-24 11:44:20.301 [info] 'ToolsModel' Host: localToolHost (9 tools: 154 enabled, 0 disabled})
 + str-replace-editor
 + open-browser
 + diagnostics
 + read-terminal
 + launch-process
 + kill-process
 + read-process
 + write-process
 + list-processes

2025-05-24 11:44:20.301 [info] 'ToolsModel' Host: remoteToolHost (1 tools: 13 enabled, 0 disabled})
 + web-search

2025-05-24 11:44:20.301 [info] 'ToolsModel' Host: sidecarToolHost (7 tools: 101 enabled, 0 disabled})
 + web-fetch
 + codebase-retrieval
 + remove-files
 + save-file
 + remember
 + render-mermaid
 + view

2025-05-24 11:44:20.433 [info] 'ToolsModel' Host: remoteToolHost (1 tools: 13 enabled, 0 disabled})
 + web-search

2025-05-24 11:44:20.433 [info] 'ToolsModel' Host: sidecarToolHost (7 tools: 101 enabled, 0 disabled})
 + web-fetch
 + codebase-retrieval
 + remove-files
 + save-file
 + remember
 + render-mermaid
 + view

2025-05-24 11:44:20.619 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:44:20.619 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:44:20.619 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:44:20.619 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:44:21.406 [info] 'WorkspaceManager[workspace]' Directory created: .config/.vscode-server/data/logs/20250524T114410/exthost1/output_logging_20250524T114412
2025-05-24 11:44:21.776 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:44:21.776 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:44:21.776 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:44:21.776 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:44:21.776 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:44:21.776 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:44:21.988 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:44:21.996 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:44:21.996 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:44:21.996 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:44:21.996 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:44:21.996 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:44:21.996 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:44:21.996 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:44:21.997 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:44:21.997 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:44:21.997 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:44:21.997 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:44:21.997 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:44:21.997 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:44:21.997 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:44:21.997 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:44:21.997 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:44:21.997 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:44:21.997 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:44:21.997 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:44:21.997 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:44:21.997 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:44:21.997 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:44:21.997 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:44:21.997 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:44:21.997 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:44:21.998 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:44:21.998 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:44:21.998 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:44:21.998 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:44:21.998 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:44:21.998 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:44:21.998 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:44:21.998 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:44:21.998 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:44:21.998 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:44:22.673 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:44:23.069 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:44:27.373 [info] 'StallDetector' Event loop delay: Timer(100 msec) ran 3198 msec late.
2025-05-24 11:44:37.672 [error] 'AugmentExtension' API request 1a011b5a-c0c3-4760-bded-1a61edcc0b57 to https://i1.api.augmentcode.com/record-session-events response 502: Bad Gateway
2025-05-24 11:44:37.976 [error] 'AgentSessionEventReporter' Error uploading metrics: Error: HTTP error: 502 Bad Gateway Error: HTTP error: 502 Bad Gateway
    at Function.fromResponse (/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.464.0/out/extension.js:236:11786)
    at xQ.callApi (/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.464.0/out/extension.js:1135:13364)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)
    at xQ.callApi (/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.464.0/out/extension.js:1135:54293)
    at xQ.logAgentSessionEvent (/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.464.0/out/extension.js:1135:33544)
    at /home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.464.0/out/extension.js:236:15614
    at ts (/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.464.0/out/extension.js:236:14159)
    at e._doUpload (/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.464.0/out/extension.js:236:15508)
    at /home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.464.0/out/extension.js:236:15179
2025-05-24 11:44:37.977 [info] 'AgentSessionEventReporter' Operation failed with error Error: HTTP error: 502 Bad Gateway, retrying in 100 ms; retries = 0
2025-05-24 11:44:38.374 [info] 'AgentSessionEventReporter' Operation succeeded after 1 transient failures
2025-05-24 11:44:42.902 [info] 'WorkspaceManager[workspace]' Tracking enabled
2025-05-24 11:44:42.902 [info] 'WorkspaceManager[workspace]' Path metrics:
  - directories emitted: 374
  - files emitted: 1842
  - other paths emitted: 4
  - total paths emitted: 2220
  - timing stats:
    - readDir: 11 ms
    - filter: 106 ms
    - yield: 32 ms
    - total: 162 ms
2025-05-24 11:44:42.902 [info] 'WorkspaceManager[workspace]' File metrics:
  - paths accepted: 1708
  - paths not accessible: 0
  - not plain files: 0
  - large files: 28
  - blob name calculation fails: 0
  - encoding errors: 0
  - mtime cache hits: 1634
  - mtime cache misses: 74
  - probe batches: 8
  - blob names probed: 1741
  - files read: 262
  - blobs uploaded: 29
  - timing stats:
    - ingestPath: 11 ms
    - probe: 8801 ms
    - stat: 28 ms
    - read: 1879 ms
    - upload: 3692 ms
2025-05-24 11:44:42.903 [info] 'WorkspaceManager[workspace]' Startup metrics:
  - create SourceFolder: 15 ms
  - read MtimeCache: 8 ms
  - pre-populate PathMap: 78 ms
  - create PathFilter: 805 ms
  - create PathNotifier: 0 ms
  - enumerate paths: 168 ms
  - purge stale PathMap entries: 0 ms
  - enumerate: 0 ms
  - await DiskFileManager quiesced: 21879 ms
  - enable persist: 3 ms
  - total: 22956 ms
2025-05-24 11:44:42.903 [info] 'WorkspaceManager' Workspace startup complete in 23797 ms
2025-05-24 11:44:49.945 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:45:19.956 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:45:49.949 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:46:19.955 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:46:49.957 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:47:19.961 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:47:49.957 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:48:19.953 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:48:49.953 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:48:57.316 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:48:59.192 [info] 'StallDetector' Recent work: [{"name":"open-confirmation-modal","durationMs":1806.058978,"timestamp":"2025-05-24T11:48:59.122Z"}]
2025-05-24 11:48:59.990 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:49:00.196 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:49:00.197 [info] 'ToolsWebviewMessageHandler' Received closeAllToolProcesses message
2025-05-24 11:49:00.197 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:49:00.197 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:49:00.197 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:49:00.377 [info] 'TaskManager' Setting current root task UUID to eeca5ce5-787d-4538-8540-43173a4419d7
2025-05-24 11:49:00.377 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:49:00.377 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:49:00.377 [info] 'TaskManager' Setting current root task UUID to eeca5ce5-787d-4538-8540-43173a4419d7
2025-05-24 11:49:00.377 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:49:00.694 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:49:19.954 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:49:49.957 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:50:19.951 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:50:49.953 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:51:20.257 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:51:49.949 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:52:19.951 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:52:49.948 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:53:19.951 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:53:49.960 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:54:19.947 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:54:49.949 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:55:19.961 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:55:49.959 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:56:19.954 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:56:49.949 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:57:19.948 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:57:49.968 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:58:19.953 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:58:49.957 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:59:19.950 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:59:49.955 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:00:19.960 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:00:49.963 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:01:19.961 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:01:49.953 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:02:19.953 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:02:49.953 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:03:19.960 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:03:49.967 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:04:19.951 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:04:49.966 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:05:19.952 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:05:49.951 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:06:19.951 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:06:49.963 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:07:19.952 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:07:49.963 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:08:19.962 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:08:49.965 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:09:19.955 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:09:49.951 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:10:19.959 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:10:49.968 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:11:19.966 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:11:49.961 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:12:19.953 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:12:49.955 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:13:19.960 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:13:49.955 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:14:14.397 [info] 'AugmentExtension' Retrieving model config
2025-05-24 12:14:14.684 [info] 'AugmentExtension' Retrieved model config
2025-05-24 12:14:14.684 [info] 'AugmentExtension' Returning model config
2025-05-24 12:14:19.965 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:14:49.957 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:15:14.655 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:15:14.836 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:15:19.968 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
