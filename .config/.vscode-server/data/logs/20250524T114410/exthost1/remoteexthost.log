2025-05-24 11:44:12.875 [info] Extension host with pid 12729 started
2025-05-24 11:44:12.875 [error] Error: EEXIST: file already exists, open '/home/<USER>/.vscode-server/data/User/workspaceStorage/28d495afc6b3165b645c0d6d358123ef/vscode.lock'
2025-05-24 11:44:12.875 [info] Lock '/home/<USER>/.vscode-server/data/User/workspaceStorage/28d495afc6b3165b645c0d6d358123ef/vscode.lock': Could not acquire lock, checking if the file is stale.
2025-05-24 11:44:12.880 [info] Lock '/home/<USER>/.vscode-server/data/User/workspaceStorage/28d495afc6b3165b645c0d6d358123ef/vscode.lock': The pid 1039 appears to be gone.
2025-05-24 11:44:12.881 [info] Lock '/home/<USER>/.vscode-server/data/User/workspaceStorage/28d495afc6b3165b645c0d6d358123ef/vscode.lock': Deleting a stale lock.
2025-05-24 11:44:12.898 [info] Lock '/home/<USER>/.vscode-server/data/User/workspaceStorage/28d495afc6b3165b645c0d6d358123ef/vscode.lock': Lock acquired.
2025-05-24 11:44:13.307 [info] ExtensionService#_doActivateExtension Augment.vscode-augment, startup: false, activationEvent: 'onView:augment-chat'
2025-05-24 11:44:13.309 [info] ExtensionService#_doActivateExtension vscode.git-base, startup: false, activationEvent: '*', root cause: vscode.git
2025-05-24 11:44:13.311 [info] ExtensionService#_doActivateExtension vscode.typescript-language-features, startup: false, activationEvent: 'onLanguage:typescript'
2025-05-24 11:44:13.317 [info] ExtensionService#_doActivateExtension vscode.emmet, startup: false, activationEvent: 'onLanguage'
2025-05-24 11:44:14.046 [info] ExtensionService#_doActivateExtension vscode.configuration-editing, startup: false, activationEvent: 'onLanguage:jsonc'
2025-05-24 11:44:14.060 [info] ExtensionService#_doActivateExtension vscode.json-language-features, startup: false, activationEvent: 'onLanguage:jsonc'
2025-05-24 11:44:14.328 [info] ExtensionService#_doActivateExtension vscode.npm, startup: true, activationEvent: 'workspaceContains:package.json'
2025-05-24 11:44:14.641 [info] ExtensionService#_doActivateExtension vscode.git, startup: false, activationEvent: '*'
2025-05-24 11:44:14.641 [info] ExtensionService#_doActivateExtension vscode.github, startup: false, activationEvent: '*'
2025-05-24 11:44:15.302 [info] Eager extensions activated
2025-05-24 11:44:15.303 [info] ExtensionService#_doActivateExtension vscode.debug-auto-launch, startup: false, activationEvent: 'onStartupFinished'
2025-05-24 11:44:15.304 [info] ExtensionService#_doActivateExtension vscode.merge-conflict, startup: false, activationEvent: 'onStartupFinished'
2025-05-24 11:44:15.304 [info] ExtensionService#_doActivateExtension coderabbit.coderabbit-vscode, startup: false, activationEvent: 'onStartupFinished'
2025-05-24 11:44:15.305 [info] ExtensionService#_doActivateExtension GitHub.copilot, startup: false, activationEvent: 'onStartupFinished'
2025-05-24 11:44:15.305 [info] ExtensionService#_doActivateExtension GitHub.copilot-chat, startup: false, activationEvent: 'onStartupFinished'
2025-05-24 11:44:18.307 [info] ExtensionService#_doActivateExtension vscode.tunnel-forwarding, startup: false, activationEvent: 'onTunnel'
2025-05-24 11:44:21.668 [info] ExtensionService#_doActivateExtension vscode.extension-editing, startup: false, activationEvent: 'onLanguage:json'
2025-05-24 11:44:28.916 [error] CodeExpectedError: cannot open vscode-userdata:c%3A%5CUsers%5Csnowp%5CAppData%5CRoaming%5CCode%5CUser%5Ckeybindings.json. Detail: Unable to resolve filesystem provider with relative file path 'vscode-userdata:c:\Users\<USER>\AppData\Roaming\Code\User\keybindings.json'
    at n_e.$tryOpenDocument (vscode-file://vscode-app/c:/Users/<USER>/AppData/Local/Programs/Microsoft%20VS%20Code/resources/app/out/vs/workbench/workbench.desktop.main.js:1276:8652)
2025-05-24 11:48:58.687 [error] Error: Webview not initialized
    at _WebviewMessageManager.sendMessage (/home/<USER>/workspace/.config/.vscode-server/extensions/coderabbit.coderabbit-vscode-0.7.8/dist/extension.js:18912:13)
    at getFilesToReview (/home/<USER>/workspace/.config/.vscode-server/extensions/coderabbit.coderabbit-vscode-0.7.8/dist/extension.js:23641:26)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)
2025-05-24 11:48:58.753 [error] Error: Webview not initialized
    at _WebviewMessageManager.sendMessage (/home/<USER>/workspace/.config/.vscode-server/extensions/coderabbit.coderabbit-vscode-0.7.8/dist/extension.js:18912:13)
    at getBranchInfo2 (/home/<USER>/workspace/.config/.vscode-server/extensions/coderabbit.coderabbit-vscode-0.7.8/dist/extension.js:23608:26)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)
2025-05-24 11:56:47.491 [error] Error: Webview not initialized
    at _WebviewMessageManager.sendMessage (/home/<USER>/workspace/.config/.vscode-server/extensions/coderabbit.coderabbit-vscode-0.7.8/dist/extension.js:18912:13)
    at getFilesToReview (/home/<USER>/workspace/.config/.vscode-server/extensions/coderabbit.coderabbit-vscode-0.7.8/dist/extension.js:23641:26)
2025-05-24 11:56:47.641 [error] Error: Webview not initialized
    at _WebviewMessageManager.sendMessage (/home/<USER>/workspace/.config/.vscode-server/extensions/coderabbit.coderabbit-vscode-0.7.8/dist/extension.js:18912:13)
    at getFilesToReview (/home/<USER>/workspace/.config/.vscode-server/extensions/coderabbit.coderabbit-vscode-0.7.8/dist/extension.js:23641:26)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)
2025-05-24 11:56:47.842 [error] Error: Webview not initialized
    at _WebviewMessageManager.sendMessage (/home/<USER>/workspace/.config/.vscode-server/extensions/coderabbit.coderabbit-vscode-0.7.8/dist/extension.js:18912:13)
    at getBranchInfo2 (/home/<USER>/workspace/.config/.vscode-server/extensions/coderabbit.coderabbit-vscode-0.7.8/dist/extension.js:23608:26)
2025-05-24 12:14:38.076 [error] Error: Webview not initialized
    at _WebviewMessageManager.sendMessage (/home/<USER>/workspace/.config/.vscode-server/extensions/coderabbit.coderabbit-vscode-0.7.8/dist/extension.js:18912:13)
    at getFilesToReview (/home/<USER>/workspace/.config/.vscode-server/extensions/coderabbit.coderabbit-vscode-0.7.8/dist/extension.js:23641:26)
2025-05-24 12:14:38.079 [error] Error: Webview not initialized
    at _WebviewMessageManager.sendMessage (/home/<USER>/workspace/.config/.vscode-server/extensions/coderabbit.coderabbit-vscode-0.7.8/dist/extension.js:18912:13)
    at getFilesToReview (/home/<USER>/workspace/.config/.vscode-server/extensions/coderabbit.coderabbit-vscode-0.7.8/dist/extension.js:23641:26)
2025-05-24 12:14:38.475 [error] Error: Webview not initialized
    at _WebviewMessageManager.sendMessage (/home/<USER>/workspace/.config/.vscode-server/extensions/coderabbit.coderabbit-vscode-0.7.8/dist/extension.js:18912:13)
    at getBranchInfo2 (/home/<USER>/workspace/.config/.vscode-server/extensions/coderabbit.coderabbit-vscode-0.7.8/dist/extension.js:23608:26)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)
