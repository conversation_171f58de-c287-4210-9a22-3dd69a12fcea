2025-05-24 11:44:19.035 [info] Can't use the Electron fetcher in this environment.
2025-05-24 11:44:19.035 [info] Using the Node fetch fetcher.
2025-05-24 11:44:19.035 [info] Initializing Git extension service.
2025-05-24 11:44:19.035 [info] Successfully activated the vscode.git extension.
2025-05-24 11:44:19.035 [info] Enablement state of the vscode.git extension: true.
2025-05-24 11:44:19.035 [info] Successfully registered Git commit message provider.
2025-05-24 11:44:21.437 [info] Logged in as mazen91111
2025-05-24 11:44:21.948 [info] TypeScript server plugin activated.
2025-05-24 11:44:21.949 [info] Registered TypeScript context provider with Copilot inline completions.
2025-05-24 11:44:22.401 [info] Got Copilot token for mazen91111
2025-05-24 11:44:23.754 [info] Fetched model metadata in 1341ms 880a246c-7afd-49a9-a045-84bd2af577f6
2025-05-24 11:44:23.845 [info] activationBlocker from 'languageModelAccess' took for 5079ms
2025-05-24 11:44:26.965 [info] copilot token chat_enabled: true, sku: free_limited_copilot
2025-05-24 11:44:26.965 [info] GitHub.vscode-pull-request-github extension is not yet activated.
2025-05-24 11:44:26.988 [info] Registering default platform agent...
2025-05-24 11:44:27.446 [info] BYOK: Copilot Chat known models list fetched successfully.
2025-05-24 12:14:30.679 [info] Logged in as mazen91111
2025-05-24 12:14:31.501 [info] Got Copilot token for mazen91111
2025-05-24 12:14:32.915 [info] copilot token chat_enabled: true, sku: free_limited_copilot
2025-05-24 12:14:33.048 [info] BYOK: Copilot Chat known models list fetched successfully.
2025-05-24 12:44:35.906 [info] Logged in as mazen91111
2025-05-24 12:44:36.809 [info] Got Copilot token for mazen91111
2025-05-24 12:44:38.232 [info] copilot token chat_enabled: true, sku: free_limited_copilot
2025-05-24 12:44:38.356 [info] BYOK: Copilot Chat known models list fetched successfully.
