2025-05-24 11:44:18.720 [info] [main] Log level: Info
2025-05-24 11:44:18.720 [info] [main] Validating found git in: "git"
2025-05-24 11:44:18.720 [info] [main] Using git "2.47.2" from "git"
2025-05-24 11:44:18.720 [info] [Model][doInitialScan] Initial repository scan started
2025-05-24 11:44:18.721 [info] > git rev-parse --show-toplevel [3ms]
2025-05-24 11:44:18.721 [info] > git rev-parse --git-dir --git-common-dir [277ms]
2025-05-24 11:44:18.721 [info] [Model][openRepository] Opened repository (path): /home/<USER>/workspace
2025-05-24 11:44:18.721 [info] [Model][openRepository] Opened repository (real path): /home/<USER>/workspace
2025-05-24 11:44:18.721 [info] > git config --get commit.template [10ms]
2025-05-24 11:44:18.721 [info] > git fetch [26ms]
2025-05-24 11:44:18.721 [info] > git rev-parse --show-toplevel [14ms]
2025-05-24 11:44:18.721 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 11:44:18.722 [info] > git config --get commit.template [15ms]
2025-05-24 11:44:18.722 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 11:44:18.722 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [756ms]
2025-05-24 11:44:18.722 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [58ms]
2025-05-24 11:44:18.722 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [830ms]
2025-05-24 11:44:18.722 [info] > git rev-parse --show-toplevel [845ms]
2025-05-24 11:44:18.722 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [15ms]
2025-05-24 11:44:18.722 [info] > git config --get --local branch.main.vscode-merge-base [33ms]
2025-05-24 11:44:18.722 [warning] [Git][config] git config failed: Failed to execute git
2025-05-24 11:44:18.722 [info] > git config --get commit.template [57ms]
2025-05-24 11:44:18.722 [info] > git config --get --local branch.main.vscode-merge-base [10ms]
2025-05-24 11:44:18.722 [warning] [Git][config] git config failed: Failed to execute git
2025-05-24 11:44:18.722 [info] > git rev-parse --show-toplevel [35ms]
2025-05-24 11:44:18.723 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 11:44:18.723 [info] > git reflog main --grep-reflog=branch: Created from *. [24ms]
2025-05-24 11:44:18.723 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [20ms]
2025-05-24 11:44:18.723 [info] > git reflog main --grep-reflog=branch: Created from *. [44ms]
2025-05-24 11:44:18.723 [info] > git status -z -uall [10ms]
2025-05-24 11:44:18.723 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-05-24 11:44:18.723 [info] > git rev-parse --show-toplevel [41ms]
2025-05-24 11:44:18.723 [info] > git rev-parse --show-toplevel [4ms]
2025-05-24 11:44:18.723 [info] > git rev-parse --show-toplevel [1ms]
2025-05-24 11:44:18.723 [info] > git rev-parse --show-toplevel [368ms]
2025-05-24 11:44:18.723 [info] > git rev-parse --show-toplevel [17ms]
2025-05-24 11:44:18.723 [info] > git rev-parse --show-toplevel [65ms]
2025-05-24 11:44:18.732 [info] > git rev-parse --show-toplevel [20ms]
2025-05-24 11:44:18.737 [info] [Model][doInitialScan] Initial repository scan completed - repositories (1), closed repositories (0), parent repositories (0), unsafe repositories (0)
2025-05-24 11:44:18.922 [info] > git show --textconv :shared/schema.ts [124ms]
2025-05-24 11:44:18.922 [info] > git check-ignore -v -z --stdin [6ms]
2025-05-24 11:44:18.923 [info] > git config --get commit.template [22ms]
2025-05-24 11:44:18.924 [info] > git ls-files --stage -- shared/schema.ts [113ms]
2025-05-24 11:44:18.970 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 11:44:18.972 [info] > git cat-file -s d32f4ffe71fcf6e84526cd8ee980575cb739b036 [38ms]
2025-05-24 11:44:18.991 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [21ms]
2025-05-24 11:44:19.030 [info] > git status -z -uall [20ms]
2025-05-24 11:44:19.031 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-05-24 11:44:19.787 [info] > git blame --root --incremental 950dc2c924e9cc2598727254b321b92999218d81 -- shared/schema.ts [9ms]
2025-05-24 11:48:57.031 [info] > git fetch [12ms]
2025-05-24 11:48:57.052 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 11:48:57.054 [info] > git config --get commit.template [22ms]
2025-05-24 11:48:57.054 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 11:48:57.056 [info] > git config --get commit.template [13ms]
2025-05-24 11:48:57.066 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 11:48:57.068 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 11:48:57.089 [info] > git status -z -uall [11ms]
2025-05-24 11:48:57.090 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 11:48:57.108 [info] > git blame --root --incremental 63dc5bf3c394b7b74ba063fd015aa649de876bb8 -- shared/schema.ts [5ms]
2025-05-24 11:48:57.208 [info] > git ls-files --stage -- shared/schema.ts [1ms]
2025-05-24 11:48:57.218 [info] > git cat-file -s d32f4ffe71fcf6e84526cd8ee980575cb739b036 [2ms]
2025-05-24 11:48:57.422 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [17ms]
2025-05-24 11:48:57.424 [info] > git show --textconv :shared/schema.ts [2ms]
2025-05-24 11:48:57.424 [info] > git show -s --decorate=full --shortstat --format=%H%n%aN%n%aE%n%at%n%ct%n%P%n%D%n%B -z 63dc5bf3c394b7b74ba063fd015aa649de876bb8 -- [10ms]
2025-05-24 11:48:57.441 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [9ms]
2025-05-24 11:48:57.452 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [13ms]
2025-05-24 11:48:57.474 [info] > git config --get --local branch.main.vscode-merge-base [24ms]
2025-05-24 11:48:57.474 [warning] [Git][config] git config failed: Failed to execute git
2025-05-24 11:48:57.483 [info] > git show --textconv 63dc5bf3c394b7b74ba063fd015aa649de876bb8:package.json [10ms]
2025-05-24 11:48:57.488 [info] > git reflog main --grep-reflog=branch: Created from *. [6ms]
2025-05-24 11:48:57.502 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 11:48:57.511 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 11:48:57.519 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 11:48:57.682 [info] > git diff 63dc5bf3c394b7b74ba063fd015aa649de876bb8 -- package.json [2ms]
2025-05-24 11:48:57.728 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 11:48:57.740 [info] > git show -s --decorate=full --shortstat --format=%H%n%aN%n%aE%n%at%n%ct%n%P%n%D%n%B -z 63dc5bf3c394b7b74ba063fd015aa649de876bb8 -- [3ms]
2025-05-24 11:48:57.751 [info] > git show -s --decorate=full --shortstat --format=%H%n%aN%n%aE%n%at%n%ct%n%P%n%D%n%B -z 63dc5bf3c394b7b74ba063fd015aa649de876bb8 -- [3ms]
2025-05-24 11:48:59.303 [info] > git ls-files --stage -- shared/schema.ts [1ms]
2025-05-24 11:48:59.315 [info] > git cat-file -s d32f4ffe71fcf6e84526cd8ee980575cb739b036 [2ms]
2025-05-24 11:48:59.528 [info] > git show --textconv :shared/schema.ts [4ms]
2025-05-24 11:49:02.075 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 11:49:02.076 [info] > git config --get commit.template [8ms]
2025-05-24 11:49:02.076 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 11:49:02.093 [info] > git status -z -uall [10ms]
2025-05-24 11:49:02.094 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 11:49:02.415 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 11:56:46.035 [info] > git fetch [22ms]
2025-05-24 11:56:46.050 [info] > git config --get commit.template [16ms]
2025-05-24 11:56:46.062 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 11:56:46.063 [info] > git config --get commit.template [14ms]
2025-05-24 11:56:46.076 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [14ms]
2025-05-24 11:56:46.078 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 11:56:46.078 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 11:56:46.103 [info] > git status -z -uall [12ms]
2025-05-24 11:56:46.104 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 11:56:46.127 [info] > git blame --root --incremental ac10dde1d079a50eed8574fc6fbc8a5697095273 -- shared/schema.ts [6ms]
2025-05-24 11:56:46.229 [info] > git ls-files --stage -- shared/schema.ts [1ms]
2025-05-24 11:56:46.244 [info] > git cat-file -s d32f4ffe71fcf6e84526cd8ee980575cb739b036 [2ms]
2025-05-24 11:56:46.446 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [25ms]
2025-05-24 11:56:46.447 [info] > git show -s --decorate=full --shortstat --format=%H%n%aN%n%aE%n%at%n%ct%n%P%n%D%n%B -z ac10dde1d079a50eed8574fc6fbc8a5697095273 -- [12ms]
2025-05-24 11:56:46.476 [info] > git show --textconv :shared/schema.ts [30ms]
2025-05-24 11:56:46.476 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [15ms]
2025-05-24 11:56:46.487 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [14ms]
2025-05-24 11:56:46.491 [info] > git config --get --local branch.main.vscode-merge-base [4ms]
2025-05-24 11:56:46.491 [warning] [Git][config] git config failed: Failed to execute git
2025-05-24 11:56:46.506 [info] > git reflog main --grep-reflog=branch: Created from *. [5ms]
2025-05-24 11:56:46.521 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-05-24 11:56:46.534 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 11:56:46.549 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 11:56:46.638 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 11:56:46.778 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 11:56:46.802 [info] > git show -s --decorate=full --shortstat --format=%H%n%aN%n%aE%n%at%n%ct%n%P%n%D%n%B -z ac10dde1d079a50eed8574fc6fbc8a5697095273 -- [3ms]
2025-05-24 11:56:46.826 [info] > git show -s --decorate=full --shortstat --format=%H%n%aN%n%aE%n%at%n%ct%n%P%n%D%n%B -z ac10dde1d079a50eed8574fc6fbc8a5697095273 -- [4ms]
2025-05-24 11:56:47.612 [info] > git ls-files --stage -- shared/schema.ts [2ms]
2025-05-24 11:56:47.631 [info] > git cat-file -s d32f4ffe71fcf6e84526cd8ee980575cb739b036 [1ms]
2025-05-24 11:56:47.843 [info] > git show --textconv :shared/schema.ts [2ms]
2025-05-24 11:56:51.119 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 11:56:51.119 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 11:56:51.119 [info] > git config --get commit.template [18ms]
2025-05-24 11:56:51.151 [info] > git status -z -uall [15ms]
2025-05-24 11:56:51.151 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-05-24 11:56:51.471 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 11:56:56.202 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 11:56:56.203 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 11:56:56.203 [info] > git config --get commit.template [23ms]
2025-05-24 11:56:56.253 [info] > git status -z -uall [27ms]
2025-05-24 11:56:56.253 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-05-24 11:56:56.575 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-05-24 11:57:01.293 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 11:57:01.296 [info] > git config --get commit.template [22ms]
2025-05-24 11:57:01.296 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-05-24 11:57:01.334 [info] > git status -z -uall [21ms]
2025-05-24 11:57:01.335 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 11:57:01.655 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-05-24 11:57:06.377 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 11:57:06.378 [info] > git config --get commit.template [16ms]
2025-05-24 11:57:06.379 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 11:57:06.412 [info] > git status -z -uall [21ms]
2025-05-24 11:57:06.413 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 11:57:06.778 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 11:57:11.441 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 11:57:11.442 [info] > git config --get commit.template [12ms]
2025-05-24 11:57:11.443 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 11:57:11.472 [info] > git status -z -uall [16ms]
2025-05-24 11:57:11.473 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 11:57:11.792 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:14:36.497 [info] > git fetch [30ms]
2025-05-24 12:14:36.525 [info] > git config --get commit.template [29ms]
2025-05-24 12:14:36.555 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:14:36.556 [info] > git config --get commit.template [33ms]
2025-05-24 12:14:36.557 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:14:36.581 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:14:36.582 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:14:36.633 [info] > git status -z -uall [23ms]
2025-05-24 12:14:36.634 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 12:14:36.686 [info] > git blame --root --incremental 8d1a5f0b0b487f0e778b9c59a76faedc1543648a -- shared/schema.ts [23ms]
2025-05-24 12:14:36.686 [info] > git ls-files --stage -- shared/schema.ts [1ms]
2025-05-24 12:14:36.711 [info] > git cat-file -s d32f4ffe71fcf6e84526cd8ee980575cb739b036 [2ms]
2025-05-24 12:14:36.915 [info] > git show --textconv :shared/schema.ts [2ms]
2025-05-24 12:14:36.981 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [21ms]
2025-05-24 12:14:37.000 [info] > git show -s --decorate=full --shortstat --format=%H%n%aN%n%aE%n%at%n%ct%n%P%n%D%n%B -z 8d1a5f0b0b487f0e778b9c59a76faedc1543648a -- [20ms]
2025-05-24 12:14:37.052 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [31ms]
2025-05-24 12:14:37.075 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [75ms]
2025-05-24 12:14:37.078 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [31ms]
2025-05-24 12:14:37.078 [info] > git config --get --local branch.main.vscode-merge-base [3ms]
2025-05-24 12:14:37.078 [warning] [Git][config] git config failed: Failed to execute git
2025-05-24 12:14:37.104 [info] > git reflog main --grep-reflog=branch: Created from *. [5ms]
2025-05-24 12:14:37.129 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-05-24 12:14:37.154 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:14:37.179 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 12:14:37.419 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:14:37.445 [info] > git show -s --decorate=full --shortstat --format=%H%n%aN%n%aE%n%at%n%ct%n%P%n%D%n%B -z 8d1a5f0b0b487f0e778b9c59a76faedc1543648a -- [2ms]
2025-05-24 12:14:37.474 [info] > git show -s --decorate=full --shortstat --format=%H%n%aN%n%aE%n%at%n%ct%n%P%n%D%n%B -z 8d1a5f0b0b487f0e778b9c59a76faedc1543648a -- [3ms]
2025-05-24 12:14:38.050 [info] > git ls-files --stage -- shared/schema.ts [2ms]
2025-05-24 12:14:38.074 [info] > git cat-file -s d32f4ffe71fcf6e84526cd8ee980575cb739b036 [1ms]
2025-05-24 12:14:38.288 [info] > git show --textconv :shared/schema.ts [2ms]
2025-05-24 12:14:41.611 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:14:41.611 [info] > git config --get commit.template [25ms]
2025-05-24 12:14:41.612 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:14:41.661 [info] > git status -z -uall [23ms]
2025-05-24 12:14:41.663 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 12:14:41.989 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:14:47.071 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:14:47.072 [info] > git config --get commit.template [26ms]
2025-05-24 12:14:47.073 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:14:47.124 [info] > git status -z -uall [26ms]
2025-05-24 12:14:47.125 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 12:14:47.454 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:14:52.156 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:14:52.256 [info] > git config --get commit.template [100ms]
2025-05-24 12:14:52.257 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [68ms]
2025-05-24 12:14:52.364 [info] > git status -z -uall [38ms]
2025-05-24 12:14:52.365 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-05-24 12:14:52.696 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:14:57.410 [info] > git config --get commit.template [5ms]
2025-05-24 12:14:57.440 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:14:57.442 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:14:57.491 [info] > git status -z -uall [26ms]
2025-05-24 12:14:57.492 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 12:14:57.866 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [49ms]
2025-05-24 12:15:02.518 [info] > git config --get commit.template [1ms]
2025-05-24 12:15:02.541 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:15:02.543 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:15:02.585 [info] > git status -z -uall [22ms]
2025-05-24 12:15:02.586 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 12:15:02.910 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 12:15:07.676 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:15:07.693 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [22ms]
2025-05-24 12:15:07.694 [info] > git config --get commit.template [69ms]
2025-05-24 12:15:07.771 [info] > git status -z -uall [31ms]
2025-05-24 12:15:07.774 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-05-24 12:15:08.114 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [5ms]
2025-05-24 12:15:12.851 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:15:12.924 [info] > git config --get commit.template [118ms]
2025-05-24 12:15:12.925 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [74ms]
2025-05-24 12:15:13.050 [info] > git status -z -uall [63ms]
2025-05-24 12:15:13.061 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [12ms]
2025-05-24 12:15:13.422 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-05-24 12:15:18.129 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:15:18.130 [info] > git config --get commit.template [38ms]
2025-05-24 12:15:18.131 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:15:18.188 [info] > git status -z -uall [28ms]
2025-05-24 12:15:18.190 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-05-24 12:15:18.529 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [0ms]
2025-05-24 12:15:23.242 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:15:23.243 [info] > git config --get commit.template [25ms]
2025-05-24 12:15:23.244 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:15:23.288 [info] > git status -z -uall [22ms]
2025-05-24 12:15:23.288 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 12:15:23.617 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:15:28.347 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:15:28.348 [info] > git config --get commit.template [30ms]
2025-05-24 12:15:28.349 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:15:28.396 [info] > git status -z -uall [22ms]
2025-05-24 12:15:28.397 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 12:15:28.728 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 12:15:33.456 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:15:33.456 [info] > git config --get commit.template [29ms]
2025-05-24 12:15:33.457 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:15:33.510 [info] > git status -z -uall [26ms]
2025-05-24 12:15:33.511 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 12:15:33.939 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [88ms]
2025-05-24 12:15:38.620 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:15:38.623 [info] > git config --get commit.template [54ms]
2025-05-24 12:15:38.627 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [7ms]
2025-05-24 12:15:38.742 [info] > git status -z -uall [79ms]
2025-05-24 12:15:38.742 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-05-24 12:15:39.115 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-05-24 12:15:43.790 [info] > git config --get commit.template [2ms]
2025-05-24 12:15:43.842 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:15:43.845 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:15:43.929 [info] > git status -z -uall [45ms]
2025-05-24 12:15:43.930 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-05-24 12:15:44.274 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [5ms]
2025-05-24 12:15:48.991 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:15:48.992 [info] > git config --get commit.template [31ms]
2025-05-24 12:15:48.993 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:15:49.057 [info] > git status -z -uall [37ms]
2025-05-24 12:15:49.058 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-05-24 12:15:49.388 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:15:54.106 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:15:54.106 [info] > git config --get commit.template [24ms]
2025-05-24 12:15:54.107 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 12:15:54.150 [info] > git status -z -uall [21ms]
2025-05-24 12:15:54.151 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 12:15:54.476 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:15:59.208 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:15:59.208 [info] > git config --get commit.template [27ms]
2025-05-24 12:15:59.209 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:15:59.262 [info] > git status -z -uall [25ms]
2025-05-24 12:15:59.263 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-05-24 12:15:59.597 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:16:04.313 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:16:04.314 [info] > git config --get commit.template [27ms]
2025-05-24 12:16:04.315 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:16:04.373 [info] > git status -z -uall [33ms]
2025-05-24 12:16:04.373 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [7ms]
2025-05-24 12:16:04.701 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 12:16:09.451 [info] > git config --get commit.template [37ms]
2025-05-24 12:16:09.452 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:16:09.453 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:16:09.617 [info] > git status -z -uall [113ms]
2025-05-24 12:16:09.617 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [81ms]
2025-05-24 12:16:09.962 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-05-24 12:16:14.686 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:16:14.686 [info] > git config --get commit.template [27ms]
2025-05-24 12:16:14.687 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:16:14.749 [info] > git status -z -uall [29ms]
2025-05-24 12:16:14.749 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-05-24 12:16:15.091 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 12:16:19.790 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:16:19.829 [info] > git config --get commit.template [40ms]
2025-05-24 12:16:19.830 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:16:19.886 [info] > git status -z -uall [29ms]
2025-05-24 12:16:19.887 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 12:16:20.215 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:16:24.966 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:16:24.967 [info] > git config --get commit.template [34ms]
2025-05-24 12:16:24.968 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:16:25.027 [info] > git status -z -uall [28ms]
2025-05-24 12:16:25.028 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 12:16:25.353 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:16:30.121 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:16:30.121 [info] > git config --get commit.template [38ms]
2025-05-24 12:16:30.123 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:16:30.179 [info] > git status -z -uall [30ms]
2025-05-24 12:16:30.182 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-05-24 12:16:30.522 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-05-24 12:16:35.265 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:16:35.266 [info] > git config --get commit.template [46ms]
2025-05-24 12:16:35.267 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:16:35.332 [info] > git status -z -uall [36ms]
2025-05-24 12:16:35.335 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-05-24 12:16:35.665 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:16:40.384 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:16:40.384 [info] > git config --get commit.template [23ms]
2025-05-24 12:16:40.385 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:16:40.431 [info] > git status -z -uall [22ms]
2025-05-24 12:16:40.432 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 12:16:40.759 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:16:45.467 [info] > git config --get commit.template [1ms]
2025-05-24 12:16:45.491 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:16:45.492 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:16:45.535 [info] > git status -z -uall [21ms]
2025-05-24 12:16:45.536 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 12:16:45.905 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:17:24.371 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:17:24.372 [info] > git config --get commit.template [26ms]
2025-05-24 12:17:24.373 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:17:24.421 [info] > git status -z -uall [24ms]
2025-05-24 12:17:24.422 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 12:17:24.749 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:17:29.545 [info] > git config --get commit.template [98ms]
2025-05-24 12:17:29.568 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:17:29.570 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:17:29.629 [info] > git status -z -uall [32ms]
2025-05-24 12:17:29.629 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [7ms]
2025-05-24 12:17:29.981 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:17:34.686 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:17:34.687 [info] > git config --get commit.template [24ms]
2025-05-24 12:17:34.688 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:17:34.730 [info] > git status -z -uall [21ms]
2025-05-24 12:17:34.731 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 12:17:35.067 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [5ms]
2025-05-24 12:17:36.695 [info] > git fetch [4ms]
2025-05-24 12:17:36.745 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:17:36.746 [info] > git config --get commit.template [23ms]
2025-05-24 12:17:36.747 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 12:17:36.801 [info] > git status -z -uall [28ms]
2025-05-24 12:17:36.802 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-05-24 12:17:37.137 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:17:38.304 [info] > git ls-files --stage -- shared/schema.ts [2ms]
2025-05-24 12:17:38.344 [info] > git cat-file -s d32f4ffe71fcf6e84526cd8ee980575cb739b036 [2ms]
2025-05-24 12:17:38.544 [info] > git show --textconv :shared/schema.ts [1ms]
2025-05-24 12:17:39.784 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:17:39.784 [info] > git config --get commit.template [25ms]
2025-05-24 12:17:39.785 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:17:39.885 [info] > git status -z -uall [78ms]
2025-05-24 12:17:39.885 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [54ms]
2025-05-24 12:17:40.212 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:17:44.924 [info] > git config --get commit.template [2ms]
2025-05-24 12:17:44.946 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:17:44.948 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:17:44.995 [info] > git status -z -uall [24ms]
2025-05-24 12:17:44.996 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 12:17:45.329 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:17:50.055 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:17:50.056 [info] > git config --get commit.template [29ms]
2025-05-24 12:17:50.057 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:17:50.129 [info] > git status -z -uall [35ms]
2025-05-24 12:17:50.129 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-05-24 12:17:50.462 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:17:55.199 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:17:55.199 [info] > git config --get commit.template [33ms]
2025-05-24 12:17:55.200 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 12:17:55.295 [info] > git status -z -uall [43ms]
2025-05-24 12:17:55.298 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-05-24 12:17:55.637 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-05-24 12:18:00.354 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:18:00.355 [info] > git config --get commit.template [25ms]
2025-05-24 12:18:00.356 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:18:00.407 [info] > git status -z -uall [24ms]
2025-05-24 12:18:00.409 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 12:18:00.735 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 12:18:05.479 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:18:05.480 [info] > git config --get commit.template [35ms]
2025-05-24 12:18:05.485 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [6ms]
2025-05-24 12:18:05.542 [info] > git status -z -uall [28ms]
2025-05-24 12:18:05.543 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 12:18:05.872 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:18:10.570 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:18:10.596 [info] > git config --get commit.template [27ms]
2025-05-24 12:18:10.598 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:18:10.652 [info] > git status -z -uall [27ms]
2025-05-24 12:18:10.654 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-05-24 12:18:10.978 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:18:15.682 [info] > git config --get commit.template [2ms]
2025-05-24 12:18:15.718 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:18:15.719 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:18:15.770 [info] > git status -z -uall [23ms]
2025-05-24 12:18:15.771 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-05-24 12:18:16.098 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:18:20.824 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:18:20.824 [info] > git config --get commit.template [27ms]
2025-05-24 12:18:20.825 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:18:20.885 [info] > git status -z -uall [24ms]
2025-05-24 12:18:20.885 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-05-24 12:18:21.218 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:18:25.937 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:18:25.938 [info] > git config --get commit.template [26ms]
2025-05-24 12:18:25.938 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 12:18:25.994 [info] > git status -z -uall [31ms]
2025-05-24 12:18:25.995 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 12:18:26.327 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-05-24 12:18:26.544 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [6ms]
2025-05-24 12:18:26.578 [info] > git show --textconv 8d1a5f0b0b487f0e778b9c59a76faedc1543648a:client/src/pages/Checkout.tsx [2ms]
2025-05-24 12:18:26.781 [info] > git diff 8d1a5f0b0b487f0e778b9c59a76faedc1543648a -- client/src/pages/Checkout.tsx [4ms]
2025-05-24 12:18:31.027 [info] > git config --get commit.template [3ms]
2025-05-24 12:18:31.051 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:18:31.052 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:18:31.096 [info] > git status -z -uall [22ms]
2025-05-24 12:18:31.097 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 12:18:31.424 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:18:36.155 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:18:36.156 [info] > git config --get commit.template [29ms]
2025-05-24 12:18:36.157 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:18:36.223 [info] > git status -z -uall [32ms]
2025-05-24 12:18:36.224 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 12:18:36.563 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:18:41.268 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:18:41.268 [info] > git config --get commit.template [20ms]
2025-05-24 12:18:41.269 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:18:41.319 [info] > git status -z -uall [32ms]
2025-05-24 12:18:41.320 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 12:18:41.647 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:18:46.405 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:18:46.407 [info] > git config --get commit.template [37ms]
2025-05-24 12:18:46.407 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:18:46.465 [info] > git status -z -uall [29ms]
2025-05-24 12:18:46.468 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-05-24 12:18:46.797 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:18:51.532 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:18:51.533 [info] > git config --get commit.template [31ms]
2025-05-24 12:18:51.534 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-05-24 12:18:51.623 [info] > git status -z -uall [51ms]
2025-05-24 12:18:51.625 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-05-24 12:18:51.973 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [6ms]
2025-05-24 12:18:56.699 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:18:56.700 [info] > git config --get commit.template [27ms]
2025-05-24 12:18:56.701 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 12:18:56.766 [info] > git status -z -uall [27ms]
2025-05-24 12:18:56.767 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 12:18:57.094 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:19:01.821 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:19:01.823 [info] > git config --get commit.template [25ms]
2025-05-24 12:19:01.823 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:19:01.872 [info] > git status -z -uall [27ms]
2025-05-24 12:19:01.873 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 12:19:02.201 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 12:19:06.929 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:19:06.931 [info] > git config --get commit.template [25ms]
2025-05-24 12:19:06.931 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:19:06.989 [info] > git status -z -uall [33ms]
2025-05-24 12:19:06.990 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [5ms]
2025-05-24 12:19:07.315 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 12:19:12.038 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:19:12.039 [info] > git config --get commit.template [20ms]
2025-05-24 12:19:12.039 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 12:19:12.089 [info] > git status -z -uall [26ms]
2025-05-24 12:19:12.095 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [6ms]
2025-05-24 12:19:12.422 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:19:17.161 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:19:17.163 [info] > git config --get commit.template [39ms]
2025-05-24 12:19:17.163 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:19:17.207 [info] > git status -z -uall [22ms]
2025-05-24 12:19:17.208 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-05-24 12:19:17.535 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:19:22.259 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:19:22.260 [info] > git config --get commit.template [26ms]
2025-05-24 12:19:22.261 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:19:22.365 [info] > git status -z -uall [73ms]
2025-05-24 12:19:22.366 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [51ms]
2025-05-24 12:19:22.699 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-05-24 12:19:27.415 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:19:27.416 [info] > git config --get commit.template [24ms]
2025-05-24 12:19:27.417 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:19:27.468 [info] > git status -z -uall [27ms]
2025-05-24 12:19:27.468 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 12:19:27.798 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-05-24 12:19:32.494 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:19:32.519 [info] > git config --get commit.template [25ms]
2025-05-24 12:19:32.520 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:19:32.567 [info] > git status -z -uall [26ms]
2025-05-24 12:19:32.568 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 12:19:32.936 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [13ms]
2025-05-24 12:19:37.637 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:19:37.638 [info] > git config --get commit.template [38ms]
2025-05-24 12:19:37.639 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:19:37.684 [info] > git status -z -uall [20ms]
2025-05-24 12:19:37.686 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 12:19:38.016 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:19:42.760 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:19:42.761 [info] > git config --get commit.template [35ms]
2025-05-24 12:19:42.761 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 12:19:42.835 [info] > git status -z -uall [34ms]
2025-05-24 12:19:42.837 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 12:19:43.195 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [14ms]
2025-05-24 12:19:53.430 [info] > git config --get commit.template [5ms]
2025-05-24 12:19:53.467 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:19:53.468 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 12:19:53.540 [info] > git status -z -uall [37ms]
2025-05-24 12:19:53.543 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-05-24 12:19:53.881 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 12:20:02.463 [info] > git config --get commit.template [37ms]
2025-05-24 12:20:02.463 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:20:02.466 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-05-24 12:20:02.554 [info] > git status -z -uall [42ms]
2025-05-24 12:20:02.558 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [5ms]
2025-05-24 12:20:02.901 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:20:11.411 [info] > git config --get commit.template [1ms]
2025-05-24 12:20:11.435 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:20:11.437 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 12:20:11.495 [info] > git status -z -uall [36ms]
2025-05-24 12:20:11.495 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-05-24 12:20:11.823 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:20:16.524 [info] > git config --get commit.template [2ms]
2025-05-24 12:20:16.553 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:20:16.554 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 12:20:16.602 [info] > git status -z -uall [24ms]
2025-05-24 12:20:16.603 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-05-24 12:20:16.930 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:20:21.656 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:20:21.657 [info] > git config --get commit.template [28ms]
2025-05-24 12:20:21.659 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-05-24 12:20:21.711 [info] > git status -z -uall [26ms]
2025-05-24 12:20:21.712 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 12:20:22.042 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-05-24 12:25:05.026 [info] > git fetch [36ms]
2025-05-24 12:25:05.027 [info] > git config --get commit.template [2ms]
2025-05-24 12:25:05.084 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:25:05.090 [info] > git config --get commit.template [35ms]
2025-05-24 12:25:05.129 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:25:05.130 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [47ms]
2025-05-24 12:25:05.133 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-05-24 12:25:05.223 [info] > git status -z -uall [49ms]
2025-05-24 12:25:05.223 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [10ms]
2025-05-24 12:25:05.550 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 12:25:06.554 [info] > git ls-files --stage -- shared/schema.ts [1ms]
2025-05-24 12:25:06.579 [info] > git cat-file -s d32f4ffe71fcf6e84526cd8ee980575cb739b036 [2ms]
2025-05-24 12:25:06.803 [info] > git show --textconv :shared/schema.ts [2ms]
2025-05-24 12:25:19.116 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:25:19.116 [info] > git config --get commit.template [22ms]
2025-05-24 12:25:19.117 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:25:19.161 [info] > git status -z -uall [22ms]
2025-05-24 12:25:19.162 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 12:25:19.487 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:25:28.059 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:25:28.060 [info] > git config --get commit.template [22ms]
2025-05-24 12:25:28.061 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:25:28.104 [info] > git status -z -uall [23ms]
2025-05-24 12:25:28.107 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-05-24 12:25:28.439 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:25:34.086 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:25:34.086 [info] > git config --get commit.template [16ms]
2025-05-24 12:25:34.087 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 12:25:34.127 [info] > git status -z -uall [20ms]
2025-05-24 12:25:34.128 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 12:25:34.454 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-05-24 12:25:39.174 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:25:39.175 [info] > git config --get commit.template [22ms]
2025-05-24 12:25:39.176 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:25:39.228 [info] > git status -z -uall [26ms]
2025-05-24 12:25:39.230 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 12:25:39.558 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:25:48.561 [info] > git config --get commit.template [2ms]
2025-05-24 12:25:48.579 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:25:48.581 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:25:48.630 [info] > git status -z -uall [26ms]
2025-05-24 12:25:48.632 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-05-24 12:25:48.957 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 12:25:53.674 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:25:53.675 [info] > git config --get commit.template [19ms]
2025-05-24 12:25:53.675 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 12:25:53.716 [info] > git status -z -uall [20ms]
2025-05-24 12:25:53.717 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-05-24 12:25:54.045 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-05-24 12:25:58.779 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:25:58.780 [info] > git config --get commit.template [31ms]
2025-05-24 12:25:58.780 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:25:58.846 [info] > git status -z -uall [34ms]
2025-05-24 12:25:58.848 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-05-24 12:25:59.176 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:26:03.902 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:26:03.903 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-05-24 12:26:03.903 [info] > git config --get commit.template [30ms]
2025-05-24 12:26:03.964 [info] > git status -z -uall [33ms]
2025-05-24 12:26:03.966 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 12:26:04.291 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:26:32.905 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:26:32.906 [info] > git config --get commit.template [29ms]
2025-05-24 12:26:32.907 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:26:32.965 [info] > git status -z -uall [32ms]
2025-05-24 12:26:32.965 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-05-24 12:26:33.296 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-05-24 12:26:38.007 [info] > git config --get commit.template [0ms]
2025-05-24 12:26:38.041 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:26:38.042 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 12:26:38.138 [info] > git status -z -uall [56ms]
2025-05-24 12:26:38.140 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-05-24 12:26:38.474 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:26:43.204 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:26:43.205 [info] > git config --get commit.template [34ms]
2025-05-24 12:26:43.206 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-05-24 12:26:43.274 [info] > git status -z -uall [33ms]
2025-05-24 12:26:43.275 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-05-24 12:26:43.606 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-05-24 12:26:48.336 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:26:48.337 [info] > git config --get commit.template [32ms]
2025-05-24 12:26:48.338 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 12:26:48.382 [info] > git status -z -uall [23ms]
2025-05-24 12:26:48.383 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 12:26:48.724 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:26:56.048 [info] > git config --get commit.template [2ms]
2025-05-24 12:26:56.090 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:26:56.092 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:26:56.161 [info] > git status -z -uall [35ms]
2025-05-24 12:26:56.162 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 12:26:56.493 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-05-24 12:27:01.224 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:27:01.224 [info] > git config --get commit.template [37ms]
2025-05-24 12:27:01.225 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 12:27:01.278 [info] > git status -z -uall [27ms]
2025-05-24 12:27:01.279 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 12:27:01.607 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:27:06.336 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:27:06.337 [info] > git config --get commit.template [33ms]
2025-05-24 12:27:06.338 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:27:06.396 [info] > git status -z -uall [29ms]
2025-05-24 12:27:06.397 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 12:27:06.728 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-05-24 12:27:11.427 [info] > git config --get commit.template [0ms]
2025-05-24 12:27:11.453 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:27:11.455 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:27:11.502 [info] > git status -z -uall [24ms]
2025-05-24 12:27:11.504 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 12:27:11.836 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-05-24 12:27:18.967 [info] > git config --get commit.template [1ms]
2025-05-24 12:27:18.997 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:27:18.999 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:27:19.055 [info] > git status -z -uall [24ms]
2025-05-24 12:27:19.056 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 12:27:19.431 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [48ms]
2025-05-24 12:27:24.113 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:27:24.115 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:27:24.115 [info] > git config --get commit.template [32ms]
2025-05-24 12:27:24.169 [info] > git status -z -uall [27ms]
2025-05-24 12:27:24.170 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 12:27:24.501 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-05-24 12:27:29.230 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:27:29.232 [info] > git config --get commit.template [30ms]
2025-05-24 12:27:29.232 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:27:29.320 [info] > git status -z -uall [45ms]
2025-05-24 12:27:29.322 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-05-24 12:27:29.787 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [93ms]
