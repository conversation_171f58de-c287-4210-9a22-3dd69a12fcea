2025-05-23 22:04:37.810 [info] 'AugmentConfigListener' settings parsed successfully
2025-05-23 22:04:37.810 [info] 'AugmentConfigListener' Config changed from <unset> to {"apiToken":"","completionURL":"","modelName":"","conflictingCodingAssistantCheck":true,"codeInstruction":{},"chat":{"enableEditableHistory":false,"useRichTextHistory":true,"smartPasteUsePrecomputation":true,"experimentalFullFilePaste":false,"modelDisplayNameToId":{"Augment":null},"userGuidelines":""},"agent":{},"autofix":{"enabled":false},"oauth":{"clientID":"augment-vscode-extension","url":"https://auth.augmentcode.com"},"enableUpload":true,"shortcutsDisplayDelayMS":2000,"enableEmptyFileHint":true,"enableDataCollection":false,"enableDebugFeatures":false,"enableReviewerWorkflows":false,"completions":{"enableAutomaticCompletions":true,"disableCompletionsByLanguage":{},"enableQuickSuggestions":true,"timeoutMs":800,"maxWaitMs":1600,"addIntelliSenseSuggestions":true},"openFileManager":{},"nextEdit":{"backgroundEnabled":true,"useCursorDecorations":false,"allowDuringDebugging":false,"useMockResults":false,"noDiffModeUseCodeLens":false,"enableBackgroundSuggestions":true,"enableGlobalBackgroundSuggestions":false,"highlightSuggestionsInTheEditor":false,"showDiffInHover":false,"enableAutoApply":true},"recencySignalManager":{"collectTabSwitchEvents":false},"preferenceCollection":{"enable":false,"enableRetrievalDataCollection":false,"enableRandomizedMode":true},"vcs":{"watcherEnabled":false},"git":{"enableCommitIndexing":false,"maxCommitsToIndex":100},"smartPaste":{},"instructions":{},"integrations":{},"mcpServers":[],"advanced":{}}
2025-05-23 22:04:37.810 [info] 'FeatureFlagManager' feature flags changed from <unset> to {"gitDiff":false,"gitDiffPollingFrequencyMSec":0,"additionalChatModels":"","smallSyncThreshold":15,"bigSyncThreshold":1000,"enableWorkspaceManagerUi":true,"enableInstructions":false,"enableSmartPaste":false,"enableSmartPasteMinVersion":"","enablePromptEnhancer":false,"enableViewTextDocument":false,"bypassLanguageFilter":false,"enableHindsight":false,"maxUploadSizeBytes":131072,"vscodeNextEditBottomPanelMinVersion":"","vscodeNextEditMinVersion":"","vscodeNextEditUx1MaxVersion":"","vscodeNextEditUx2MaxVersion":"","vscodeFlywheelMinVersion":"","vscodeExternalSourcesInChatMinVersion":"","vscodeShareMinVersion":"","maxTrackableFileCount":250000,"maxTrackableFileCountWithoutPermission":150000,"minUploadedPercentageWithoutPermission":90,"memoryClassificationOnFirstToken":false,"vscodeSourcesMinVersion":"","vscodeChatHintDecorationMinVersion":"","nextEditDebounceMs":400,"enableCompletionFileEditEvents":false,"vscodeEnableCpuProfile":false,"verifyFolderIsSourceRepo":false,"refuseToSyncHomeDirectories":false,"enableFileLimitsForSyncingPermission":false,"enableChatMermaidDiagrams":false,"enableSummaryTitles":false,"smartPastePrecomputeMode":"visible-hover","vscodeNewThreadsMenuMinVersion":"","vscodeEditableHistoryMinVersion":"","vscodeEnableChatMermaidDiagramsMinVersion":"","userGuidelinesLengthLimit":2000,"workspaceGuidelinesLengthLimit":2000,"enableGuidelines":false,"useCheckpointManagerContextMinVersion":"","validateCheckpointManagerContext":false,"vscodeDesignSystemRichTextEditorMinVersion":"","allowClientFeatureFlagOverrides":false,"vscodeChatWithToolsMinVersion":"","vscodeChatMultimodalMinVersion":"","vscodeAgentModeMinVersion":"","vscodeAgentModeMinStableVersion":"","vscodeBackgroundAgentsMinVersion":"","vscodeAgentEditTool":"backend_edit_tool","vscodeRichCheckpointInfoMinVersion":"","vscodeDirectApplyMinVersion":"","memoriesParams":{},"eloModelConfiguration":{"highPriorityModels":[],"regularBattleModels":[],"highPriorityThreshold":0.5},"vscodeVirtualizedMessageListMinVersion":"","vscodeChatStablePrefixTruncationMinVersion":"","agentEditToolMinViewSize":0,"agentEditToolSchemaType":"StrReplaceEditorToolDefinitionNested","agentEditToolEnableFuzzyMatching":false,"agentEditToolFuzzyMatchSuccessMessage":"Replacement successful. old_str and new_str were slightly modified to match the original file content.","agentEditToolFuzzyMatchMaxDiff":50,"agentEditToolFuzzyMatchMaxDiffRatio":0.15,"agentEditToolFuzzyMatchMinAllMatchStreakBetweenDiffs":5,"agentEditToolInstructionsReminder":false,"agentEditToolShowResultSnippet":true,"agentSaveFileToolInstructionsReminder":false,"vscodePersonalitiesMinVersion":"","useMemorySnapshotManager":false,"vscodeGenerateCommitMessageMinVersion":"","enableRules":false,"memoriesTextEditorEnabled":false,"enableModelRegistry":false,"openFileManagerV2Enabled":false,"modelRegistry":{},"vscodeTaskListMinVersion":""}
2025-05-23 22:04:37.810 [info] 'AugmentExtension' Retrieving model config
2025-05-23 22:04:38.818 [info] 'AugmentExtension' Retrieved model config
2025-05-23 22:04:38.818 [info] 'AugmentExtension' Returning model config
2025-05-23 22:04:38.879 [info] 'FeatureFlagManager' feature flags changed:
  - additionalChatModels: "" to "{}"
  - enableInstructions: false to true
  - enableSmartPasteMinVersion: "" to "0.267.0"
  - enablePromptEnhancer: false to true
  - enableViewTextDocument: false to true
  - bypassLanguageFilter: false to true
  - enableHindsight: false to true
  - maxUploadSizeBytes: 131072 to 524288
  - vscodeNextEditBottomPanelMinVersion: "" to "0.394.0"
  - vscodeNextEditMinVersion: "" to "0.343.0"
  - vscodeFlywheelMinVersion: "" to "0.282.0"
  - vscodeExternalSourcesInChatMinVersion: "" to "0.243.2"
  - vscodeShareMinVersion: "" to "0.314.0"
  - memoryClassificationOnFirstToken: false to true
  - vscodeChatHintDecorationMinVersion: "" to "0.274.0"
  - enableCompletionFileEditEvents: false to true
  - verifyFolderIsSourceRepo: false to true
  - refuseToSyncHomeDirectories: false to true
  - enableFileLimitsForSyncingPermission: false to true
  - enableSummaryTitles: false to true
  - smartPastePrecomputeMode: "visible-hover" to "visible"
  - vscodeNewThreadsMenuMinVersion: "" to "0.305.0"
  - vscodeEditableHistoryMinVersion: "" to "0.330.0"
  - vscodeEnableChatMermaidDiagramsMinVersion: "" to "0.314.0"
  - userGuidelinesLengthLimit: 2000 to 24576
  - workspaceGuidelinesLengthLimit: 2000 to 24576
  - enableGuidelines: false to true
  - useCheckpointManagerContextMinVersion: "" to "0.323.0"
  - vscodeDesignSystemRichTextEditorMinVersion: "" to "0.363.0"
  - vscodeChatMultimodalMinVersion: "" to "0.384.0"
  - vscodeAgentModeMinVersion: "" to "0.395.0"
  - vscodeAgentModeMinStableVersion: "" to "0.399.1"
  - vscodeAgentEditTool: "backend_edit_tool" to "str_replace_editor_tool"
  - eloModelConfiguration > highPriorityModels: [] to undefined
  - eloModelConfiguration > regularBattleModels: [] to undefined
  - eloModelConfiguration > highPriorityThreshold: 0.5 to undefined
  - vscodeChatStablePrefixTruncationMinVersion: "" to "0.402.0"
  - agentEditToolMinViewSize: 0 to 500
  - agentEditToolSchemaType: "StrReplaceEditorToolDefinitionNested" to "StrReplaceEditorToolDefinitionFlat"
  - agentEditToolEnableFuzzyMatching: false to true
  - agentEditToolInstructionsReminder: false to true
  - agentSaveFileToolInstructionsReminder: false to true
  - useMemorySnapshotManager: false to true
  - openFileManagerV2Enabled: false to true
2025-05-23 22:04:38.879 [info] 'SyncingPermissionTracker' Initial syncing permission: syncing permission granted for workspace. Folders:
    /home/<USER>/workspace (explicit) at 5/21/2025, 11:43:10 PM
2025-05-23 22:04:38.879 [info] 'WorkspaceManager' OpenFileManagerProxy created. V2 enabled: [true]
2025-05-23 22:04:38.879 [info] 'BlobsCheckpointManager' BlobsCheckpointManager created. checkpointThreshold: 1000
2025-05-23 22:04:38.879 [info] 'SyncingPermissionTracker' Permission to sync folder /home/<USER>/workspace granted at 5/21/2025, 11:43:10 PM; type = explicit
2025-05-23 22:04:38.879 [info] 'WorkspaceManager' Adding workspace folder workspace; folderRoot = /home/<USER>/workspace; syncingPermission = granted
2025-05-23 22:04:38.879 [info] 'SyncingPermissionTracker' Updating syncing permission: syncing permission granted for workspace. Folders:
    /home/<USER>/workspace (explicit) at 5/21/2025, 11:43:10 PM
2025-05-23 22:04:38.893 [info] 'RemoteAgentsMessenger' RemoteAgentsMessenger initialized, setting up onDidChangeTextDocument listener
2025-05-23 22:04:38.893 [info] 'HotKeyHints' HotKeyHints initialized
2025-05-23 22:04:38.893 [info] 'ToolsModel' Loaded saved chat mode: AGENT
2025-05-23 22:04:38.901 [info] 'ToolsModel' Tools Mode: AGENT (3 hosts)
2025-05-23 22:04:38.902 [info] 'ToolsModel' Host: localToolHost (9 tools: 154 enabled, 0 disabled})
 + str-replace-editor
 + open-browser
 + diagnostics
 + read-terminal
 + launch-process
 + kill-process
 + read-process
 + write-process
 + list-processes

2025-05-23 22:04:41.270 [info] 'WorkspaceManager[workspace]' Start tracking
2025-05-23 22:04:41.911 [info] 'ToolsModel' Host: remoteToolHost (1 tools: 13 enabled, 0 disabled})
 + web-search

2025-05-23 22:04:41.911 [info] 'ToolsModel' Host: sidecarToolHost (7 tools: 101 enabled, 0 disabled})
 + web-fetch
 + codebase-retrieval
 + remove-files
 + save-file
 + remember
 + render-mermaid
 + view

2025-05-23 22:04:42.141 [info] 'ToolsModel' Tools Mode: AGENT (3 hosts)
2025-05-23 22:04:42.141 [info] 'ToolsModel' Host: localToolHost (9 tools: 154 enabled, 0 disabled})
 + str-replace-editor
 + open-browser
 + diagnostics
 + read-terminal
 + launch-process
 + kill-process
 + read-process
 + write-process
 + list-processes

2025-05-23 22:04:42.141 [info] 'ToolsModel' Host: remoteToolHost (1 tools: 13 enabled, 0 disabled})
 + web-search

2025-05-23 22:04:42.141 [info] 'ToolsModel' Host: sidecarToolHost (7 tools: 101 enabled, 0 disabled})
 + web-fetch
 + codebase-retrieval
 + remove-files
 + save-file
 + remember
 + render-mermaid
 + view

2025-05-23 22:04:42.199 [info] 'PathMap' Opened source folder /home/<USER>/workspace with id 100
2025-05-23 22:04:42.199 [info] 'OpenFileManager' Opened source folder 100
2025-05-23 22:04:42.199 [info] 'MtimeCache[workspace]' reading blob name cache from /home/<USER>/.vscode-server/data/User/workspaceStorage/28d495afc6b3165b645c0d6d358123ef/Augment.vscode-augment/ce248d0eb8d4f662cc202e5206f37824b70c818932a8dbfb169463d063d70aa1/mtime-cache.json
2025-05-23 22:04:42.273 [info] 'MtimeCache[workspace]' read 1048 entries from /home/<USER>/.vscode-server/data/User/workspaceStorage/28d495afc6b3165b645c0d6d358123ef/Augment.vscode-augment/ce248d0eb8d4f662cc202e5206f37824b70c818932a8dbfb169463d063d70aa1/mtime-cache.json
2025-05-23 22:04:46.880 [info] 'StallDetector' Event loop delay: Timer(100 msec) ran 4212 msec late.
2025-05-23 22:04:58.950 [info] 'RemoteAgentsMessenger' Registering RemoteAgentsMessenger with AsyncMsgHandler
2025-05-23 22:05:00.353 [info] 'TaskManager' Setting current root task UUID to 90983f98-bbfb-4fad-b0db-833c902fcd50
2025-05-23 22:05:03.965 [info] 'ToolsWebviewMessageHandler' Received closeAllToolProcesses message
2025-05-23 22:05:04.186 [info] 'TaskManager' Setting current root task UUID to 29307a1c-7596-41be-a662-fef1a5e835c8
2025-05-23 22:05:04.186 [info] 'TaskManager' Setting current root task UUID to 29307a1c-7596-41be-a662-fef1a5e835c8
2025-05-23 22:05:26.660 [info] 'WorkspaceManager[workspace]' Tracking enabled
2025-05-23 22:05:26.661 [info] 'WorkspaceManager[workspace]' Path metrics:
  - directories emitted: 293
  - files emitted: 1715
  - other paths emitted: 4
  - total paths emitted: 2012
  - timing stats:
    - readDir: 76 ms
    - filter: 301 ms
    - yield: 23 ms
    - total: 1105 ms
2025-05-23 22:05:26.661 [info] 'WorkspaceManager[workspace]' File metrics:
  - paths accepted: 1604
  - paths not accessible: 0
  - not plain files: 0
  - large files: 25
  - blob name calculation fails: 0
  - encoding errors: 0
  - mtime cache hits: 1025
  - mtime cache misses: 579
  - probe batches: 14
  - blob names probed: 2387
  - files read: 1248
  - blobs uploaded: 523
  - timing stats:
    - ingestPath: 9 ms
    - probe: 8947 ms
    - stat: 28 ms
    - read: 5198 ms
    - upload: 9135 ms
2025-05-23 22:05:26.661 [info] 'WorkspaceManager[workspace]' Startup metrics:
  - create SourceFolder: 929 ms
  - read MtimeCache: 74 ms
  - pre-populate PathMap: 105 ms
  - create PathFilter: 4912 ms
  - create PathNotifier: 1 ms
  - enumerate paths: 1113 ms
  - purge stale PathMap entries: 1 ms
  - enumerate: 0 ms
  - await DiskFileManager quiesced: 38252 ms
  - enable persist: 3 ms
  - total: 45390 ms
2025-05-23 22:05:26.661 [info] 'WorkspaceManager' Workspace startup complete in 47792 ms
2025-05-23 22:07:52.747 [info] 'AugmentConfigListener' settings parsed successfully
2025-05-23 22:10:46.733 [info] 'AugmentConfigListener' settings parsed successfully
2025-05-23 22:11:16.784 [info] 'WorkspaceManager[workspace]' Directory created: .config/npm
2025-05-23 22:11:16.785 [info] 'WorkspaceManager[workspace]' Directory created: .config/npm/node_global
2025-05-23 22:11:16.786 [info] 'WorkspaceManager[workspace]' Directory created: .config/npm/node_global/lib
2025-05-23 22:16:01.380 [info] 'ToolFileUtils' Reading file: client/src/components/Header.tsx
2025-05-23 22:16:01.916 [info] 'ToolFileUtils' Successfully read file: client/src/components/Header.tsx (4534 bytes)
2025-05-23 22:16:03.871 [info] 'WorkspaceManager[workspace]' Directory created: .config/.vscode-server/data/User/History/-7c5c1d6f
2025-05-23 22:16:04.575 [info] 'ToolFileUtils' Reading file: client/src/components/Header.tsx
2025-05-23 22:16:04.576 [info] 'ToolFileUtils' Successfully read file: client/src/components/Header.tsx (4438 bytes)
2025-05-23 22:16:07.282 [info] 'WorkspaceManager[workspace]' Directory created: .config/.vscode-server/data/User/workspaceStorage/28d495afc6b3165b645c0d6d358123ef/Augment.vscode-augment/augment-user-assets/checkpoint-documents/19b0ac25-acfd-4590-b4a9-8bea57fe3117
2025-05-23 22:16:17.755 [info] 'WorkspaceManager[workspace]' Directory created: .config/.vscode-server/data/User/History/3cfd050
2025-05-23 22:17:14.632 [info] 'ToolFileUtils' Reading file: client/src/components/Header.tsx
2025-05-23 22:17:14.632 [info] 'ToolFileUtils' Successfully read file: client/src/components/Header.tsx (4438 bytes)
2025-05-23 22:17:16.845 [info] 'ToolFileUtils' Reading file: client/src/components/Header.tsx
2025-05-23 22:17:16.845 [info] 'ToolFileUtils' Successfully read file: client/src/components/Header.tsx (4393 bytes)
2025-05-23 22:17:46.646 [info] 'ToolFileUtils' Reading file: client/src/components/Header.tsx
2025-05-23 22:17:46.647 [info] 'ToolFileUtils' Successfully read file: client/src/components/Header.tsx (4393 bytes)
2025-05-23 22:17:48.855 [info] 'ToolFileUtils' Reading file: client/src/components/Header.tsx
2025-05-23 22:17:48.855 [info] 'ToolFileUtils' Successfully read file: client/src/components/Header.tsx (4356 bytes)
2025-05-23 22:18:44.880 [info] 'ViewTool' Tool called with path: server/schema.sql and view_range: undefined
2025-05-23 22:18:49.471 [info] 'ViewTool' Tool called with path: client/src/pages/auth/AuthPage.tsx and view_range: undefined
2025-05-23 22:19:20.406 [info] 'ToolFileUtils' Reading file: server/schema.sql
2025-05-23 22:19:20.406 [info] 'ToolFileUtils' Successfully read file: server/schema.sql (6873 bytes)
2025-05-23 22:19:22.654 [info] 'ToolFileUtils' Reading file: server/schema.sql
2025-05-23 22:19:22.655 [info] 'ToolFileUtils' Successfully read file: server/schema.sql (7977 bytes)
2025-05-23 22:19:38.025 [info] 'ToolFileUtils' Reading file: client/src/pages/auth/AuthPage.tsx
2025-05-23 22:19:38.025 [info] 'ToolFileUtils' Successfully read file: client/src/pages/auth/AuthPage.tsx (12044 bytes)
2025-05-23 22:19:39.990 [info] 'WorkspaceManager[workspace]' Directory created: .config/.vscode-server/data/User/History/40f4963e
2025-05-23 22:19:40.688 [info] 'ToolFileUtils' Reading file: client/src/pages/auth/AuthPage.tsx
2025-05-23 22:19:40.688 [info] 'ToolFileUtils' Successfully read file: client/src/pages/auth/AuthPage.tsx (11580 bytes)
2025-05-23 22:19:57.699 [info] 'ToolFileUtils' Reading file: client/src/pages/auth/AuthPage.tsx
2025-05-23 22:19:57.699 [info] 'ToolFileUtils' Successfully read file: client/src/pages/auth/AuthPage.tsx (11580 bytes)
2025-05-23 22:19:59.911 [info] 'ToolFileUtils' Reading file: client/src/pages/auth/AuthPage.tsx
2025-05-23 22:19:59.911 [info] 'ToolFileUtils' Successfully read file: client/src/pages/auth/AuthPage.tsx (11563 bytes)
2025-05-23 22:20:17.305 [info] 'ToolFileUtils' Reading file: client/src/pages/auth/AuthPage.tsx
2025-05-23 22:20:17.305 [info] 'ToolFileUtils' Successfully read file: client/src/pages/auth/AuthPage.tsx (11563 bytes)
2025-05-23 22:20:19.626 [info] 'ToolFileUtils' Reading file: client/src/pages/auth/AuthPage.tsx
2025-05-23 22:20:19.626 [info] 'ToolFileUtils' Successfully read file: client/src/pages/auth/AuthPage.tsx (11639 bytes)
2025-05-23 22:20:40.046 [info] 'WorkspaceManager[workspace]' Directory created: server/auth
2025-05-23 22:21:14.244 [info] 'ToolFileUtils' Reading file: server/routes.ts
2025-05-23 22:21:14.787 [info] 'ToolFileUtils' Successfully read file: server/routes.ts (3947 bytes)
2025-05-23 22:21:17.609 [info] 'ToolFileUtils' Reading file: server/routes.ts
2025-05-23 22:21:17.623 [info] 'ToolFileUtils' Successfully read file: server/routes.ts (3926 bytes)
2025-05-23 22:21:25.685 [info] 'ToolFileUtils' Reading file: server/routes.ts
2025-05-23 22:21:25.685 [info] 'ToolFileUtils' Successfully read file: server/routes.ts (3926 bytes)
2025-05-23 22:21:27.886 [info] 'ToolFileUtils' Reading file: server/routes.ts
2025-05-23 22:21:27.886 [info] 'ToolFileUtils' Successfully read file: server/routes.ts (3999 bytes)
2025-05-23 22:21:49.848 [info] 'ToolFileUtils' Reading file: shared/schema.ts
2025-05-23 22:21:50.381 [info] 'ToolFileUtils' Successfully read file: shared/schema.ts (4882 bytes)
2025-05-23 22:21:53.078 [info] 'ToolFileUtils' Reading file: shared/schema.ts
2025-05-23 22:21:53.078 [info] 'ToolFileUtils' Successfully read file: shared/schema.ts (5374 bytes)
2025-05-23 22:22:00.409 [info] 'ViewTool' Tool called with path: server/storage.ts and view_range: undefined
2025-05-23 22:22:10.639 [info] 'ToolFileUtils' Reading file: server/storage.ts
2025-05-23 22:22:10.639 [info] 'ToolFileUtils' Successfully read file: server/storage.ts (23236 bytes)
2025-05-23 22:22:12.629 [info] 'WorkspaceManager[workspace]' Directory created: .config/.vscode-server/data/User/History/b26caef
2025-05-23 22:22:13.327 [info] 'ToolFileUtils' Reading file: server/storage.ts
2025-05-23 22:22:13.327 [info] 'ToolFileUtils' Successfully read file: server/storage.ts (23172 bytes)
2025-05-23 22:22:24.831 [info] 'ToolFileUtils' Reading file: server/storage.ts
2025-05-23 22:22:24.831 [info] 'ToolFileUtils' Successfully read file: server/storage.ts (23172 bytes)
2025-05-23 22:22:27.024 [info] 'ToolFileUtils' Reading file: server/storage.ts
2025-05-23 22:22:27.025 [info] 'ToolFileUtils' Successfully read file: server/storage.ts (23567 bytes)
2025-05-23 22:22:48.404 [info] 'ToolFileUtils' Reading file: server/auth/auth-routes.ts
2025-05-23 22:22:48.405 [info] 'ToolFileUtils' Successfully read file: server/auth/auth-routes.ts (1733 bytes)
2025-05-23 22:22:50.173 [info] 'WorkspaceManager[workspace]' Directory created: .config/.vscode-server/data/User/History/-5c690278
2025-05-23 22:22:50.867 [info] 'ToolFileUtils' Reading file: server/auth/auth-routes.ts
2025-05-23 22:22:50.867 [info] 'ToolFileUtils' Successfully read file: server/auth/auth-routes.ts (1787 bytes)
2025-05-23 22:34:34.084 [info] 'AugmentExtension' Retrieving model config
2025-05-23 22:34:34.395 [info] 'AugmentExtension' Retrieved model config
2025-05-23 22:34:34.395 [info] 'AugmentExtension' Returning model config
2025-05-23 22:40:20.069 [info] 'WorkspaceManager[workspace]' Directory created: .config/.vscode-server/data/logs/20250523T220418/exthost1/vscode.markdown-language-features
2025-05-23 23:04:34.083 [info] 'AugmentExtension' Retrieving model config
2025-05-23 23:04:34.387 [info] 'AugmentExtension' Retrieved model config
2025-05-23 23:04:34.387 [info] 'AugmentExtension' Returning model config
2025-05-23 23:25:49.997 [info] 'ToolsWebviewMessageHandler' Received closeAllToolProcesses message
2025-05-23 23:25:50.362 [info] 'TaskManager' Setting current root task UUID to 57803a71-46db-43e4-90b1-d6874920bc14
2025-05-23 23:25:50.362 [info] 'TaskManager' Setting current root task UUID to 57803a71-46db-43e4-90b1-d6874920bc14
2025-05-23 23:34:34.084 [info] 'AugmentExtension' Retrieving model config
2025-05-23 23:34:34.396 [info] 'AugmentExtension' Retrieved model config
2025-05-23 23:34:34.396 [info] 'AugmentExtension' Returning model config
2025-05-23 23:40:51.430 [info] 'ToolFileUtils' Reading file: client/src/context/RestaurantStatusContext.tsx
2025-05-23 23:40:52.737 [info] 'ToolFileUtils' File not found: client/src/context/RestaurantStatusContext.tsx. No similar files found
2025-05-23 23:40:52.737 [error] 'StrReplaceEditorTool' Error in tool call: File not found: client/src/context/RestaurantStatusContext.tsx
2025-05-23 23:41:13.823 [info] 'WorkspaceManager[workspace]' Directory created: .config/.vscode-server/data/User/workspaceStorage/28d495afc6b3165b645c0d6d358123ef/Augment.vscode-augment/augment-user-assets/checkpoint-documents/83848ed4-911c-441e-b270-f1f5a7ae95f9
2025-05-23 23:41:33.307 [info] 'ToolFileUtils' Reading file: client/src/App.tsx
2025-05-23 23:41:33.307 [info] 'ToolFileUtils' Successfully read file: client/src/App.tsx (3210 bytes)
2025-05-23 23:41:35.571 [info] 'ToolFileUtils' Reading file: client/src/App.tsx
2025-05-23 23:41:35.571 [info] 'ToolFileUtils' Successfully read file: client/src/App.tsx (3281 bytes)
2025-05-23 23:41:49.933 [info] 'ToolFileUtils' Reading file: client/src/App.tsx
2025-05-23 23:41:49.934 [info] 'ToolFileUtils' Successfully read file: client/src/App.tsx (3281 bytes)
2025-05-23 23:41:52.148 [info] 'ToolFileUtils' Reading file: client/src/App.tsx
2025-05-23 23:41:52.148 [info] 'ToolFileUtils' Successfully read file: client/src/App.tsx (3362 bytes)
2025-05-23 23:42:22.248 [info] 'ToolFileUtils' Reading file: client/src/components/Header.tsx
2025-05-23 23:42:22.791 [info] 'ToolFileUtils' Successfully read file: client/src/components/Header.tsx (4356 bytes)
2025-05-23 23:42:25.506 [info] 'ToolFileUtils' Reading file: client/src/components/Header.tsx
2025-05-23 23:42:25.507 [info] 'ToolFileUtils' Successfully read file: client/src/components/Header.tsx (4419 bytes)
2025-05-23 23:42:36.820 [info] 'ToolFileUtils' Reading file: client/src/components/Header.tsx
2025-05-23 23:42:36.820 [info] 'ToolFileUtils' Successfully read file: client/src/components/Header.tsx (4419 bytes)
2025-05-23 23:42:38.978 [info] 'ToolFileUtils' Reading file: client/src/components/Header.tsx
2025-05-23 23:42:38.978 [info] 'ToolFileUtils' Successfully read file: client/src/components/Header.tsx (4469 bytes)
2025-05-23 23:42:48.810 [info] 'ToolFileUtils' Reading file: client/src/components/Header.tsx
2025-05-23 23:42:48.811 [info] 'ToolFileUtils' Successfully read file: client/src/components/Header.tsx (4469 bytes)
2025-05-23 23:42:50.951 [info] 'ToolFileUtils' Reading file: client/src/components/Header.tsx
2025-05-23 23:42:50.951 [info] 'ToolFileUtils' Successfully read file: client/src/components/Header.tsx (4477 bytes)
2025-05-23 23:43:13.772 [info] 'ToolFileUtils' Reading file: client/src/components/DishCard.tsx
2025-05-23 23:43:14.306 [info] 'ToolFileUtils' Successfully read file: client/src/components/DishCard.tsx (2020 bytes)
2025-05-23 23:43:16.267 [info] 'WorkspaceManager[workspace]' Directory created: .config/.vscode-server/data/User/History/114370e
2025-05-23 23:43:16.966 [info] 'ToolFileUtils' Reading file: client/src/components/DishCard.tsx
2025-05-23 23:43:16.966 [info] 'ToolFileUtils' Successfully read file: client/src/components/DishCard.tsx (2054 bytes)
2025-05-23 23:43:29.619 [info] 'ToolFileUtils' Reading file: client/src/components/DishCard.tsx
2025-05-23 23:43:29.619 [info] 'ToolFileUtils' Successfully read file: client/src/components/DishCard.tsx (2054 bytes)
2025-05-23 23:43:31.855 [info] 'ToolFileUtils' Reading file: client/src/components/DishCard.tsx
2025-05-23 23:43:31.855 [info] 'ToolFileUtils' Successfully read file: client/src/components/DishCard.tsx (2182 bytes)
2025-05-23 23:43:43.288 [info] 'ToolFileUtils' Reading file: client/src/components/DishCard.tsx
2025-05-23 23:43:43.289 [info] 'ToolFileUtils' Successfully read file: client/src/components/DishCard.tsx (2182 bytes)
2025-05-23 23:43:45.407 [info] 'ToolFileUtils' Reading file: client/src/components/DishCard.tsx
2025-05-23 23:43:45.407 [info] 'ToolFileUtils' Successfully read file: client/src/components/DishCard.tsx (2310 bytes)
2025-05-23 23:44:09.956 [info] 'ToolFileUtils' Reading file: client/src/pages/Checkout.tsx
2025-05-23 23:44:10.679 [info] 'ToolFileUtils' Successfully read file: client/src/pages/Checkout.tsx (81933 bytes)
2025-05-23 23:44:13.026 [info] 'WorkspaceManager[workspace]' Directory created: .config/.vscode-server/data/User/History/1a7842dc
2025-05-23 23:44:13.552 [info] 'ToolFileUtils' Reading file: client/src/pages/Checkout.tsx
2025-05-23 23:44:13.552 [info] 'ToolFileUtils' Successfully read file: client/src/pages/Checkout.tsx (80670 bytes)
2025-05-23 23:44:16.288 [error] 'FuzzySymbolSearcher' Failed to read file tokens for d2af4f1abac4622e8b424978c5a760ddcaeb8cd82dc5756582976205870bb9b2: deleted
2025-05-23 23:44:26.092 [info] 'ToolFileUtils' Reading file: client/src/pages/Checkout.tsx
2025-05-23 23:44:26.092 [info] 'ToolFileUtils' Successfully read file: client/src/pages/Checkout.tsx (80670 bytes)
2025-05-23 23:44:28.299 [info] 'ToolFileUtils' Reading file: client/src/pages/Checkout.tsx
2025-05-23 23:44:28.299 [info] 'ToolFileUtils' Successfully read file: client/src/pages/Checkout.tsx (80714 bytes)
2025-05-23 23:44:40.003 [info] 'ToolFileUtils' Reading file: client/src/pages/Checkout.tsx
2025-05-23 23:44:40.003 [info] 'ToolFileUtils' Successfully read file: client/src/pages/Checkout.tsx (80714 bytes)
2025-05-23 23:44:42.364 [info] 'ToolFileUtils' Reading file: client/src/pages/Checkout.tsx
2025-05-23 23:44:42.364 [info] 'ToolFileUtils' Successfully read file: client/src/pages/Checkout.tsx (80918 bytes)
2025-05-23 23:44:56.487 [info] 'ToolFileUtils' Reading file: client/src/pages/Checkout.tsx
2025-05-23 23:44:56.487 [info] 'ToolFileUtils' Successfully read file: client/src/pages/Checkout.tsx (80918 bytes)
2025-05-23 23:44:59.009 [info] 'ToolFileUtils' Reading file: client/src/pages/Checkout.tsx
2025-05-23 23:44:59.009 [info] 'ToolFileUtils' Successfully read file: client/src/pages/Checkout.tsx (81039 bytes)
2025-05-23 23:45:18.309 [info] 'ToolFileUtils' Reading file: client/src/pages/Checkout.tsx
2025-05-23 23:45:18.309 [info] 'ToolFileUtils' Successfully read file: client/src/pages/Checkout.tsx (81039 bytes)
2025-05-23 23:45:20.610 [info] 'ToolFileUtils' Reading file: client/src/pages/Checkout.tsx
2025-05-23 23:45:20.611 [info] 'ToolFileUtils' Successfully read file: client/src/pages/Checkout.tsx (81583 bytes)
2025-05-23 23:45:49.539 [info] 'ToolFileUtils' Reading file: server/routes.ts
2025-05-23 23:45:50.076 [info] 'ToolFileUtils' Successfully read file: server/routes.ts (3962 bytes)
2025-05-23 23:45:52.915 [info] 'ToolFileUtils' Reading file: server/routes.ts
2025-05-23 23:45:52.916 [info] 'ToolFileUtils' Successfully read file: server/routes.ts (4416 bytes)
2025-05-23 23:46:12.254 [info] 'ToolFileUtils' Reading file: client/src/pages/admin/AdminLayout.tsx
2025-05-23 23:46:12.792 [info] 'ToolFileUtils' Successfully read file: client/src/pages/admin/AdminLayout.tsx (8717 bytes)
2025-05-23 23:46:14.801 [info] 'WorkspaceManager[workspace]' Directory created: .config/.vscode-server/data/User/History/-641f3a47
2025-05-23 23:46:15.483 [info] 'ToolFileUtils' Reading file: client/src/pages/admin/AdminLayout.tsx
2025-05-23 23:46:15.483 [info] 'ToolFileUtils' Successfully read file: client/src/pages/admin/AdminLayout.tsx (8667 bytes)
2025-05-23 23:46:30.210 [info] 'ToolFileUtils' Reading file: client/src/pages/admin/AdminLayout.tsx
2025-05-23 23:46:30.210 [info] 'ToolFileUtils' Successfully read file: client/src/pages/admin/AdminLayout.tsx (8667 bytes)
2025-05-23 23:46:32.418 [info] 'ToolFileUtils' Reading file: client/src/pages/admin/AdminLayout.tsx
2025-05-23 23:46:32.418 [info] 'ToolFileUtils' Successfully read file: client/src/pages/admin/AdminLayout.tsx (8722 bytes)
2025-05-23 23:46:53.976 [info] 'ToolFileUtils' Reading file: client/src/pages/admin/AdminLayout.tsx
2025-05-23 23:46:53.977 [info] 'ToolFileUtils' Successfully read file: client/src/pages/admin/AdminLayout.tsx (8722 bytes)
2025-05-23 23:46:56.181 [info] 'ToolFileUtils' Reading file: client/src/pages/admin/AdminLayout.tsx
2025-05-23 23:46:56.181 [info] 'ToolFileUtils' Successfully read file: client/src/pages/admin/AdminLayout.tsx (8737 bytes)
2025-05-23 23:47:18.957 [info] 'ToolFileUtils' Reading file: client/src/pages/admin/Settings.tsx
2025-05-23 23:47:19.500 [info] 'ToolFileUtils' Successfully read file: client/src/pages/admin/Settings.tsx (20351 bytes)
2025-05-23 23:47:21.526 [info] 'WorkspaceManager[workspace]' Directory created: .config/.vscode-server/data/User/History/-37ef5247
2025-05-23 23:47:22.174 [info] 'ToolFileUtils' Reading file: client/src/pages/admin/Settings.tsx
2025-05-23 23:47:22.175 [info] 'ToolFileUtils' Successfully read file: client/src/pages/admin/Settings.tsx (20114 bytes)
2025-05-23 23:47:33.982 [info] 'ToolFileUtils' Reading file: client/src/pages/admin/Settings.tsx
2025-05-23 23:47:33.982 [info] 'ToolFileUtils' Successfully read file: client/src/pages/admin/Settings.tsx (20114 bytes)
2025-05-23 23:47:36.213 [info] 'ToolFileUtils' Reading file: client/src/pages/admin/Settings.tsx
2025-05-23 23:47:36.214 [info] 'ToolFileUtils' Successfully read file: client/src/pages/admin/Settings.tsx (20188 bytes)
2025-05-23 23:47:55.191 [info] 'ToolFileUtils' Reading file: client/src/pages/admin/Settings.tsx
2025-05-23 23:47:55.191 [info] 'ToolFileUtils' Successfully read file: client/src/pages/admin/Settings.tsx (20188 bytes)
2025-05-23 23:47:57.473 [info] 'ToolFileUtils' Reading file: client/src/pages/admin/Settings.tsx
2025-05-23 23:47:57.473 [info] 'ToolFileUtils' Successfully read file: client/src/pages/admin/Settings.tsx (20307 bytes)
2025-05-23 23:48:10.225 [info] 'ToolFileUtils' Reading file: client/src/pages/admin/Settings.tsx
2025-05-23 23:48:10.225 [info] 'ToolFileUtils' Successfully read file: client/src/pages/admin/Settings.tsx (20307 bytes)
2025-05-23 23:48:12.450 [info] 'ToolFileUtils' Reading file: client/src/pages/admin/Settings.tsx
2025-05-23 23:48:12.450 [info] 'ToolFileUtils' Successfully read file: client/src/pages/admin/Settings.tsx (20384 bytes)
2025-05-23 23:52:35.249 [info] 'ToolFileUtils' Reading file: server/routes.ts
2025-05-23 23:52:35.818 [info] 'ToolFileUtils' Successfully read file: server/routes.ts (4416 bytes)
2025-05-23 23:52:38.593 [info] 'ToolFileUtils' Reading file: server/routes.ts
2025-05-23 23:52:38.594 [info] 'ToolFileUtils' Successfully read file: server/routes.ts (5797 bytes)
2025-05-23 23:52:52.178 [info] 'ToolFileUtils' Reading file: server/storage.ts
2025-05-23 23:52:52.911 [info] 'ToolFileUtils' Successfully read file: server/storage.ts (23567 bytes)
2025-05-23 23:52:55.629 [info] 'ToolFileUtils' Reading file: server/storage.ts
2025-05-23 23:52:55.629 [info] 'ToolFileUtils' Successfully read file: server/storage.ts (23702 bytes)
2025-05-23 23:53:09.489 [info] 'ToolFileUtils' Reading file: server/storage.ts
2025-05-23 23:53:09.489 [info] 'ToolFileUtils' Successfully read file: server/storage.ts (23702 bytes)
2025-05-23 23:53:11.755 [info] 'ToolFileUtils' Reading file: server/storage.ts
2025-05-23 23:53:11.755 [info] 'ToolFileUtils' Successfully read file: server/storage.ts (24628 bytes)
2025-05-23 23:53:27.600 [info] 'ToolFileUtils' Reading file: server/routes.ts
2025-05-23 23:53:27.600 [info] 'ToolFileUtils' Successfully read file: server/routes.ts (5797 bytes)
2025-05-23 23:53:29.913 [info] 'ToolFileUtils' Reading file: server/routes.ts
2025-05-23 23:53:29.913 [info] 'ToolFileUtils' Successfully read file: server/routes.ts (5779 bytes)
2025-05-23 23:53:37.956 [info] 'ToolFileUtils' Reading file: client/src/api/adminApi.ts
2025-05-23 23:53:38.506 [info] 'ToolFileUtils' Successfully read file: client/src/api/adminApi.ts (3816 bytes)
2025-05-23 23:53:40.521 [info] 'WorkspaceManager[workspace]' Directory created: .config/.vscode-server/data/User/History/-6a9c2a93
2025-05-23 23:53:41.145 [info] 'ToolFileUtils' Reading file: client/src/api/adminApi.ts
2025-05-23 23:53:41.145 [info] 'ToolFileUtils' Successfully read file: client/src/api/adminApi.ts (3850 bytes)
2025-05-23 23:53:56.268 [info] 'ToolFileUtils' Reading file: client/src/api/adminApi.ts
2025-05-23 23:53:56.268 [info] 'ToolFileUtils' Successfully read file: client/src/api/adminApi.ts (3850 bytes)
2025-05-23 23:53:58.525 [info] 'ToolFileUtils' Reading file: client/src/api/adminApi.ts
2025-05-23 23:53:58.525 [info] 'ToolFileUtils' Successfully read file: client/src/api/adminApi.ts (3826 bytes)
2025-05-23 23:54:14.082 [info] 'ToolFileUtils' Reading file: client/src/api/adminApi.ts
2025-05-23 23:54:14.082 [info] 'ToolFileUtils' Successfully read file: client/src/api/adminApi.ts (3826 bytes)
2025-05-23 23:54:16.350 [info] 'ToolFileUtils' Reading file: client/src/api/adminApi.ts
2025-05-23 23:54:16.351 [info] 'ToolFileUtils' Successfully read file: client/src/api/adminApi.ts (3802 bytes)
2025-05-23 23:54:37.105 [info] 'ToolFileUtils' Reading file: client/src/pages/admin/Menu.tsx
2025-05-23 23:54:37.649 [info] 'ToolFileUtils' Successfully read file: client/src/pages/admin/Menu.tsx (26593 bytes)
2025-05-23 23:54:39.690 [info] 'WorkspaceManager[workspace]' Directory created: .config/.vscode-server/data/User/History/-4579298b
2025-05-23 23:54:40.295 [info] 'ToolFileUtils' Reading file: client/src/pages/admin/Menu.tsx
2025-05-23 23:54:40.295 [info] 'ToolFileUtils' Successfully read file: client/src/pages/admin/Menu.tsx (25972 bytes)
2025-05-23 23:55:01.853 [info] 'ToolFileUtils' Reading file: client/src/pages/admin/Menu.tsx
2025-05-23 23:55:01.854 [info] 'ToolFileUtils' Successfully read file: client/src/pages/admin/Menu.tsx (25972 bytes)
2025-05-23 23:55:04.362 [info] 'ToolFileUtils' Reading file: client/src/pages/admin/Menu.tsx
2025-05-23 23:55:04.363 [info] 'ToolFileUtils' Successfully read file: client/src/pages/admin/Menu.tsx (25843 bytes)
2025-05-23 23:55:24.945 [info] 'ToolFileUtils' Reading file: client/src/pages/admin/Menu.tsx
2025-05-23 23:55:24.945 [info] 'ToolFileUtils' Successfully read file: client/src/pages/admin/Menu.tsx (25843 bytes)
2025-05-23 23:55:27.325 [info] 'ToolFileUtils' Reading file: client/src/pages/admin/Menu.tsx
2025-05-23 23:55:27.326 [info] 'ToolFileUtils' Successfully read file: client/src/pages/admin/Menu.tsx (26116 bytes)
2025-05-23 23:59:20.074 [info] 'ToolFileUtils' Reading file: client/src/pages/Menu.tsx
2025-05-23 23:59:20.638 [info] 'ToolFileUtils' Successfully read file: client/src/pages/Menu.tsx (23583 bytes)
2025-05-23 23:59:22.709 [info] 'WorkspaceManager[workspace]' Directory created: .config/.vscode-server/data/User/History/-36241c0b
2025-05-23 23:59:23.407 [info] 'ToolFileUtils' Reading file: client/src/pages/Menu.tsx
2025-05-23 23:59:23.408 [info] 'ToolFileUtils' Successfully read file: client/src/pages/Menu.tsx (23444 bytes)
2025-05-23 23:59:38.984 [info] 'ToolFileUtils' Reading file: client/src/pages/Menu.tsx
2025-05-23 23:59:38.984 [info] 'ToolFileUtils' Successfully read file: client/src/pages/Menu.tsx (23444 bytes)
2025-05-23 23:59:49.630 [info] 'ToolFileUtils' Reading file: client/src/pages/Menu.tsx
2025-05-23 23:59:49.631 [info] 'ToolFileUtils' Successfully read file: client/src/pages/Menu.tsx (23444 bytes)
2025-05-23 23:59:51.850 [info] 'ToolFileUtils' Reading file: client/src/pages/Menu.tsx
2025-05-23 23:59:51.850 [info] 'ToolFileUtils' Successfully read file: client/src/pages/Menu.tsx (23606 bytes)
2025-05-24 00:00:02.445 [info] 'ToolFileUtils' Reading file: client/src/pages/Menu.tsx
2025-05-24 00:00:02.445 [info] 'ToolFileUtils' Successfully read file: client/src/pages/Menu.tsx (23606 bytes)
2025-05-24 00:00:04.676 [info] 'ToolFileUtils' Reading file: client/src/pages/Menu.tsx
2025-05-24 00:00:04.676 [info] 'ToolFileUtils' Successfully read file: client/src/pages/Menu.tsx (23444 bytes)
2025-05-24 00:00:27.220 [info] 'ToolFileUtils' Reading file: client/src/pages/Menu.tsx
2025-05-24 00:00:27.221 [info] 'ToolFileUtils' Successfully read file: client/src/pages/Menu.tsx (23444 bytes)
2025-05-24 00:00:32.963 [info] 'ViewTool' Tool called with path: client/src/pages/Menu.tsx and view_range: [100,120]
2025-05-24 00:00:44.591 [info] 'ToolFileUtils' Reading file: client/src/pages/Menu.tsx
2025-05-24 00:00:44.591 [info] 'ToolFileUtils' Successfully read file: client/src/pages/Menu.tsx (23444 bytes)
2025-05-24 00:00:46.888 [info] 'ToolFileUtils' Reading file: client/src/pages/Menu.tsx
2025-05-24 00:00:46.889 [info] 'ToolFileUtils' Successfully read file: client/src/pages/Menu.tsx (23875 bytes)
2025-05-24 00:01:11.120 [info] 'ToolFileUtils' Reading file: client/src/pages/Menu.tsx
2025-05-24 00:01:11.120 [info] 'ToolFileUtils' Successfully read file: client/src/pages/Menu.tsx (23875 bytes)
2025-05-24 00:01:13.512 [info] 'ToolFileUtils' Reading file: client/src/pages/Menu.tsx
2025-05-24 00:01:13.512 [info] 'ToolFileUtils' Successfully read file: client/src/pages/Menu.tsx (24531 bytes)
2025-05-24 00:01:23.119 [info] 'ToolFileUtils' Reading file: client/src/pages/Menu.tsx
2025-05-24 00:01:23.119 [info] 'ToolFileUtils' Successfully read file: client/src/pages/Menu.tsx (24531 bytes)
2025-05-24 00:01:25.387 [info] 'ToolFileUtils' Reading file: client/src/pages/Menu.tsx
2025-05-24 00:01:25.387 [info] 'ToolFileUtils' Successfully read file: client/src/pages/Menu.tsx (24592 bytes)
2025-05-24 00:01:34.034 [info] 'ToolFileUtils' Reading file: client/src/pages/Menu.tsx
2025-05-24 00:01:34.034 [info] 'ToolFileUtils' Successfully read file: client/src/pages/Menu.tsx (24592 bytes)
2025-05-24 00:01:36.293 [info] 'ToolFileUtils' Reading file: client/src/pages/Menu.tsx
2025-05-24 00:01:36.293 [info] 'ToolFileUtils' Successfully read file: client/src/pages/Menu.tsx (24603 bytes)
2025-05-24 00:02:00.091 [info] 'ToolFileUtils' Reading file: client/src/pages/Menu.tsx
2025-05-24 00:02:00.091 [info] 'ToolFileUtils' Successfully read file: client/src/pages/Menu.tsx (24603 bytes)
2025-05-24 00:02:02.260 [info] 'ToolFileUtils' Reading file: client/src/pages/Menu.tsx
2025-05-24 00:02:02.260 [info] 'ToolFileUtils' Successfully read file: client/src/pages/Menu.tsx (24112 bytes)
2025-05-24 00:02:12.860 [info] 'ToolFileUtils' Reading file: client/src/pages/Menu.tsx
2025-05-24 00:02:12.860 [info] 'ToolFileUtils' Successfully read file: client/src/pages/Menu.tsx (24112 bytes)
2025-05-24 00:02:15.072 [info] 'ToolFileUtils' Reading file: client/src/pages/Menu.tsx
2025-05-24 00:02:15.072 [info] 'ToolFileUtils' Successfully read file: client/src/pages/Menu.tsx (24120 bytes)
2025-05-24 00:02:34.984 [info] 'ToolFileUtils' Reading file: client/src/components/MenuItemCard.tsx
2025-05-24 00:02:35.611 [info] 'ToolFileUtils' Successfully read file: client/src/components/MenuItemCard.tsx (8072 bytes)
2025-05-24 00:02:37.549 [info] 'WorkspaceManager[workspace]' Directory created: .config/.vscode-server/data/User/History/38661a06
2025-05-24 00:02:38.235 [info] 'ToolFileUtils' Reading file: client/src/components/MenuItemCard.tsx
2025-05-24 00:02:38.235 [info] 'ToolFileUtils' Successfully read file: client/src/components/MenuItemCard.tsx (8209 bytes)
2025-05-24 00:02:51.191 [info] 'ToolFileUtils' Reading file: client/src/components/MenuItemCard.tsx
2025-05-24 00:02:51.191 [info] 'ToolFileUtils' Successfully read file: client/src/components/MenuItemCard.tsx (8209 bytes)
2025-05-24 00:02:53.507 [info] 'ToolFileUtils' Reading file: client/src/components/MenuItemCard.tsx
2025-05-24 00:02:53.507 [info] 'ToolFileUtils' Successfully read file: client/src/components/MenuItemCard.tsx (8245 bytes)
2025-05-24 00:04:34.084 [info] 'AugmentExtension' Retrieving model config
2025-05-24 00:04:34.412 [info] 'AugmentExtension' Retrieved model config
2025-05-24 00:04:34.412 [info] 'AugmentExtension' Returning model config
2025-05-24 00:06:52.798 [info] 'ToolFileUtils' Reading file: client/src/components/CustomizeModal.tsx
2025-05-24 00:06:53.342 [info] 'ToolFileUtils' Successfully read file: client/src/components/CustomizeModal.tsx (8335 bytes)
2025-05-24 00:06:55.414 [info] 'WorkspaceManager[workspace]' Directory created: .config/.vscode-server/data/User/History/-32d90f52
2025-05-24 00:06:56.068 [info] 'ToolFileUtils' Reading file: client/src/components/CustomizeModal.tsx
2025-05-24 00:06:56.068 [info] 'ToolFileUtils' Successfully read file: client/src/components/CustomizeModal.tsx (8351 bytes)
2025-05-24 00:08:13.845 [info] 'AugmentConfigListener' settings parsed successfully
2025-05-24 00:08:13.846 [info] 'AugmentConfigListener' Config changed from <unset> to {"apiToken":"","completionURL":"","modelName":"","conflictingCodingAssistantCheck":true,"codeInstruction":{},"chat":{"enableEditableHistory":false,"useRichTextHistory":true,"smartPasteUsePrecomputation":true,"experimentalFullFilePaste":false,"modelDisplayNameToId":{"Augment":null},"userGuidelines":""},"agent":{},"autofix":{"enabled":false},"oauth":{"clientID":"augment-vscode-extension","url":"https://auth.augmentcode.com"},"enableUpload":true,"shortcutsDisplayDelayMS":2000,"enableEmptyFileHint":true,"enableDataCollection":false,"enableDebugFeatures":false,"enableReviewerWorkflows":false,"completions":{"enableAutomaticCompletions":true,"disableCompletionsByLanguage":{},"enableQuickSuggestions":true,"timeoutMs":800,"maxWaitMs":1600,"addIntelliSenseSuggestions":true},"openFileManager":{},"nextEdit":{"backgroundEnabled":true,"useCursorDecorations":false,"allowDuringDebugging":false,"useMockResults":false,"noDiffModeUseCodeLens":false,"enableBackgroundSuggestions":true,"enableGlobalBackgroundSuggestions":false,"highlightSuggestionsInTheEditor":false,"showDiffInHover":false,"enableAutoApply":true},"recencySignalManager":{"collectTabSwitchEvents":false},"preferenceCollection":{"enable":false,"enableRetrievalDataCollection":false,"enableRandomizedMode":true},"vcs":{"watcherEnabled":false},"git":{"enableCommitIndexing":false,"maxCommitsToIndex":100},"smartPaste":{},"instructions":{},"integrations":{},"mcpServers":[],"advanced":{}}
2025-05-24 00:08:13.846 [info] 'FeatureFlagManager' feature flags changed from <unset> to {"gitDiff":false,"gitDiffPollingFrequencyMSec":0,"additionalChatModels":"","smallSyncThreshold":15,"bigSyncThreshold":1000,"enableWorkspaceManagerUi":true,"enableInstructions":false,"enableSmartPaste":false,"enableSmartPasteMinVersion":"","enablePromptEnhancer":false,"enableViewTextDocument":false,"bypassLanguageFilter":false,"enableHindsight":false,"maxUploadSizeBytes":131072,"vscodeNextEditBottomPanelMinVersion":"","vscodeNextEditMinVersion":"","vscodeNextEditUx1MaxVersion":"","vscodeNextEditUx2MaxVersion":"","vscodeFlywheelMinVersion":"","vscodeExternalSourcesInChatMinVersion":"","vscodeShareMinVersion":"","maxTrackableFileCount":250000,"maxTrackableFileCountWithoutPermission":150000,"minUploadedPercentageWithoutPermission":90,"memoryClassificationOnFirstToken":false,"vscodeSourcesMinVersion":"","vscodeChatHintDecorationMinVersion":"","nextEditDebounceMs":400,"enableCompletionFileEditEvents":false,"vscodeEnableCpuProfile":false,"verifyFolderIsSourceRepo":false,"refuseToSyncHomeDirectories":false,"enableFileLimitsForSyncingPermission":false,"enableChatMermaidDiagrams":false,"enableSummaryTitles":false,"smartPastePrecomputeMode":"visible-hover","vscodeNewThreadsMenuMinVersion":"","vscodeEditableHistoryMinVersion":"","vscodeEnableChatMermaidDiagramsMinVersion":"","userGuidelinesLengthLimit":2000,"workspaceGuidelinesLengthLimit":2000,"enableGuidelines":false,"useCheckpointManagerContextMinVersion":"","validateCheckpointManagerContext":false,"vscodeDesignSystemRichTextEditorMinVersion":"","allowClientFeatureFlagOverrides":false,"vscodeChatWithToolsMinVersion":"","vscodeChatMultimodalMinVersion":"","vscodeAgentModeMinVersion":"","vscodeAgentModeMinStableVersion":"","vscodeBackgroundAgentsMinVersion":"","vscodeAgentEditTool":"backend_edit_tool","vscodeRichCheckpointInfoMinVersion":"","vscodeDirectApplyMinVersion":"","memoriesParams":{},"eloModelConfiguration":{"highPriorityModels":[],"regularBattleModels":[],"highPriorityThreshold":0.5},"vscodeVirtualizedMessageListMinVersion":"","vscodeChatStablePrefixTruncationMinVersion":"","agentEditToolMinViewSize":0,"agentEditToolSchemaType":"StrReplaceEditorToolDefinitionNested","agentEditToolEnableFuzzyMatching":false,"agentEditToolFuzzyMatchSuccessMessage":"Replacement successful. old_str and new_str were slightly modified to match the original file content.","agentEditToolFuzzyMatchMaxDiff":50,"agentEditToolFuzzyMatchMaxDiffRatio":0.15,"agentEditToolFuzzyMatchMinAllMatchStreakBetweenDiffs":5,"agentEditToolInstructionsReminder":false,"agentEditToolShowResultSnippet":true,"agentSaveFileToolInstructionsReminder":false,"vscodePersonalitiesMinVersion":"","useMemorySnapshotManager":false,"vscodeGenerateCommitMessageMinVersion":"","enableRules":false,"memoriesTextEditorEnabled":false,"enableModelRegistry":false,"openFileManagerV2Enabled":false,"modelRegistry":{},"vscodeTaskListMinVersion":""}
2025-05-24 00:08:16.763 [info] 'AugmentExtension' Retrieving model config
2025-05-24 00:08:18.721 [info] 'AugmentExtension' Retrieved model config
2025-05-24 00:08:18.721 [info] 'AugmentExtension' Returning model config
2025-05-24 00:08:18.819 [info] 'FeatureFlagManager' feature flags changed:
  - additionalChatModels: "" to "{}"
  - enableInstructions: false to true
  - enableSmartPasteMinVersion: "" to "0.267.0"
  - enablePromptEnhancer: false to true
  - enableViewTextDocument: false to true
  - bypassLanguageFilter: false to true
  - enableHindsight: false to true
  - maxUploadSizeBytes: 131072 to 524288
  - vscodeNextEditBottomPanelMinVersion: "" to "0.394.0"
  - vscodeNextEditMinVersion: "" to "0.343.0"
  - vscodeFlywheelMinVersion: "" to "0.282.0"
  - vscodeExternalSourcesInChatMinVersion: "" to "0.243.2"
  - vscodeShareMinVersion: "" to "0.314.0"
  - memoryClassificationOnFirstToken: false to true
  - vscodeChatHintDecorationMinVersion: "" to "0.274.0"
  - enableCompletionFileEditEvents: false to true
  - verifyFolderIsSourceRepo: false to true
  - refuseToSyncHomeDirectories: false to true
  - enableFileLimitsForSyncingPermission: false to true
  - enableSummaryTitles: false to true
  - smartPastePrecomputeMode: "visible-hover" to "visible"
  - vscodeNewThreadsMenuMinVersion: "" to "0.305.0"
  - vscodeEditableHistoryMinVersion: "" to "0.330.0"
  - vscodeEnableChatMermaidDiagramsMinVersion: "" to "0.314.0"
  - userGuidelinesLengthLimit: 2000 to 24576
  - workspaceGuidelinesLengthLimit: 2000 to 24576
  - enableGuidelines: false to true
  - useCheckpointManagerContextMinVersion: "" to "0.323.0"
  - vscodeDesignSystemRichTextEditorMinVersion: "" to "0.363.0"
  - vscodeChatMultimodalMinVersion: "" to "0.384.0"
  - vscodeAgentModeMinVersion: "" to "0.395.0"
  - vscodeAgentModeMinStableVersion: "" to "0.399.1"
  - vscodeAgentEditTool: "backend_edit_tool" to "str_replace_editor_tool"
  - eloModelConfiguration > highPriorityModels: [] to undefined
  - eloModelConfiguration > regularBattleModels: [] to undefined
  - eloModelConfiguration > highPriorityThreshold: 0.5 to undefined
  - vscodeChatStablePrefixTruncationMinVersion: "" to "0.402.0"
  - agentEditToolMinViewSize: 0 to 500
  - agentEditToolSchemaType: "StrReplaceEditorToolDefinitionNested" to "StrReplaceEditorToolDefinitionFlat"
  - agentEditToolEnableFuzzyMatching: false to true
  - agentEditToolInstructionsReminder: false to true
  - agentSaveFileToolInstructionsReminder: false to true
  - useMemorySnapshotManager: false to true
  - openFileManagerV2Enabled: false to true
2025-05-24 00:08:18.834 [info] 'SyncingPermissionTracker' Initial syncing permission: syncing permission granted for workspace. Folders:
    /home/<USER>/workspace (explicit) at 5/21/2025, 11:43:10 PM
2025-05-24 00:08:18.839 [info] 'WorkspaceManager' OpenFileManagerProxy created. V2 enabled: [true]
2025-05-24 00:08:18.839 [info] 'BlobsCheckpointManager' BlobsCheckpointManager created. checkpointThreshold: 1000
2025-05-24 00:08:18.841 [info] 'SyncingPermissionTracker' Permission to sync folder /home/<USER>/workspace granted at 5/21/2025, 11:43:10 PM; type = explicit
2025-05-24 00:08:18.841 [info] 'WorkspaceManager' Adding workspace folder workspace; folderRoot = /home/<USER>/workspace; syncingPermission = granted
2025-05-24 00:08:18.841 [info] 'SyncingPermissionTracker' Updating syncing permission: syncing permission granted for workspace. Folders:
    /home/<USER>/workspace (explicit) at 5/21/2025, 11:43:10 PM
2025-05-24 00:08:18.871 [info] 'RemoteAgentsMessenger' RemoteAgentsMessenger initialized, setting up onDidChangeTextDocument listener
2025-05-24 00:08:18.871 [info] 'RemoteAgentsMessenger' Registering RemoteAgentsMessenger with AsyncMsgHandler
2025-05-24 00:08:18.872 [info] 'HotKeyHints' HotKeyHints initialized
2025-05-24 00:08:18.872 [info] 'ToolsModel' Loaded saved chat mode: AGENT
2025-05-24 00:08:18.888 [info] 'ToolsModel' Tools Mode: AGENT (3 hosts)
2025-05-24 00:08:18.889 [info] 'ToolsModel' Host: localToolHost (9 tools: 154 enabled, 0 disabled})
 + str-replace-editor
 + open-browser
 + diagnostics
 + read-terminal
 + launch-process
 + kill-process
 + read-process
 + write-process
 + list-processes

2025-05-24 00:08:19.459 [info] 'TaskManager' Setting current root task UUID to 57803a71-46db-43e4-90b1-d6874920bc14
2025-05-24 00:08:19.965 [info] 'WorkspaceManager[workspace]' Start tracking
2025-05-24 00:08:20.161 [info] 'PathMap' Opened source folder /home/<USER>/workspace with id 100
2025-05-24 00:08:20.161 [info] 'OpenFileManager' Opened source folder 100
2025-05-24 00:08:20.162 [info] 'MtimeCache[workspace]' reading blob name cache from /home/<USER>/.vscode-server/data/User/workspaceStorage/28d495afc6b3165b645c0d6d358123ef/Augment.vscode-augment/ce248d0eb8d4f662cc202e5206f37824b70c818932a8dbfb169463d063d70aa1/mtime-cache.json
2025-05-24 00:08:20.554 [info] 'ToolsModel' Tools Mode: AGENT (3 hosts)
2025-05-24 00:08:20.559 [info] 'ToolsModel' Host: localToolHost (9 tools: 154 enabled, 0 disabled})
 + str-replace-editor
 + open-browser
 + diagnostics
 + read-terminal
 + launch-process
 + kill-process
 + read-process
 + write-process
 + list-processes

2025-05-24 00:08:20.559 [info] 'ToolsModel' Host: remoteToolHost (1 tools: 13 enabled, 0 disabled})
 + web-search

2025-05-24 00:08:20.559 [info] 'ToolsModel' Host: sidecarToolHost (7 tools: 101 enabled, 0 disabled})
 + web-fetch
 + codebase-retrieval
 + remove-files
 + save-file
 + remember
 + render-mermaid
 + view

2025-05-24 00:08:20.568 [info] 'StallDetector' Recent work: [{"name":"get-subscription-info","durationMs":1090.835322,"timestamp":"2025-05-24T00:08:20.544Z"}]
2025-05-24 00:08:20.924 [info] 'ToolsModel' Host: remoteToolHost (1 tools: 13 enabled, 0 disabled})
 + web-search

2025-05-24 00:08:20.925 [info] 'ToolsModel' Host: sidecarToolHost (7 tools: 101 enabled, 0 disabled})
 + web-fetch
 + codebase-retrieval
 + remove-files
 + save-file
 + remember
 + render-mermaid
 + view

2025-05-24 00:08:21.053 [info] 'MtimeCache[workspace]' read 1594 entries from /home/<USER>/.vscode-server/data/User/workspaceStorage/28d495afc6b3165b645c0d6d358123ef/Augment.vscode-augment/ce248d0eb8d4f662cc202e5206f37824b70c818932a8dbfb169463d063d70aa1/mtime-cache.json
2025-05-24 00:08:22.017 [info] 'StallDetector' Recent work: [{"name":"resolve-file-request","durationMs":2208.350732,"timestamp":"2025-05-24T00:08:21.910Z"}]
2025-05-24 00:08:26.402 [info] 'StallDetector' Event loop delay: Timer(100 msec) ran 2217 msec late.
2025-05-24 00:12:06.233 [info] 'ViewTool' Tool called with path: package.json and view_range: undefined
2025-05-24 00:12:12.849 [info] 'ViewTool' Tool called with path: drizzle.config.ts and view_range: undefined
2025-05-24 00:12:42.371 [info] 'ToolFileUtils' Reading file: server/storage.ts
2025-05-24 00:12:42.918 [info] 'ToolFileUtils' Successfully read file: server/storage.ts (24628 bytes)
2025-05-24 00:12:45.593 [info] 'ToolFileUtils' Reading file: server/storage.ts
2025-05-24 00:12:45.593 [info] 'ToolFileUtils' Successfully read file: server/storage.ts (24853 bytes)
2025-05-24 00:13:13.150 [info] 'ToolFileUtils' Reading file: server/storage.ts
2025-05-24 00:13:13.156 [info] 'ToolFileUtils' Successfully read file: server/storage.ts (24853 bytes)
2025-05-24 00:13:25.576 [info] 'ToolFileUtils' Reading file: server/storage.ts
2025-05-24 00:13:25.576 [info] 'ToolFileUtils' Successfully read file: server/storage.ts (24853 bytes)
2025-05-24 00:13:28.069 [info] 'ToolFileUtils' Reading file: server/storage.ts
2025-05-24 00:13:28.070 [info] 'ToolFileUtils' Successfully read file: server/storage.ts (25241 bytes)
2025-05-24 00:13:50.370 [info] 'ToolFileUtils' Reading file: server/storage.ts
2025-05-24 00:13:50.370 [info] 'ToolFileUtils' Successfully read file: server/storage.ts (25241 bytes)
2025-05-24 00:13:53.594 [info] 'ToolFileUtils' Reading file: server/storage.ts
2025-05-24 00:13:53.594 [info] 'ToolFileUtils' Successfully read file: server/storage.ts (26028 bytes)
2025-05-24 00:14:03.972 [info] 'ViewTool' Tool called with path: server/storage.ts and view_range: [650,670]
2025-05-24 00:14:40.514 [info] 'ToolFileUtils' Reading file: server/storage.ts
2025-05-24 00:14:40.515 [info] 'ToolFileUtils' Successfully read file: server/storage.ts (26028 bytes)
2025-05-24 00:14:43.076 [info] 'ToolFileUtils' Reading file: server/storage.ts
2025-05-24 00:14:43.077 [info] 'ToolFileUtils' Successfully read file: server/storage.ts (32948 bytes)
2025-05-24 00:15:00.689 [info] 'ToolFileUtils' Reading file: server/storage.ts
2025-05-24 00:15:00.689 [info] 'ToolFileUtils' Successfully read file: server/storage.ts (32948 bytes)
2025-05-24 00:15:02.980 [info] 'ToolFileUtils' Reading file: server/storage.ts
2025-05-24 00:15:02.980 [info] 'ToolFileUtils' Successfully read file: server/storage.ts (35260 bytes)
2025-05-24 00:15:22.234 [info] 'ToolFileUtils' Reading file: server/storage.ts
2025-05-24 00:15:22.235 [info] 'ToolFileUtils' Successfully read file: server/storage.ts (35260 bytes)
2025-05-24 00:15:24.637 [info] 'ToolFileUtils' Reading file: server/storage.ts
2025-05-24 00:15:24.637 [info] 'ToolFileUtils' Successfully read file: server/storage.ts (38591 bytes)
2025-05-24 00:15:37.522 [info] 'ToolFileUtils' Reading file: server/storage.ts
2025-05-24 00:15:37.522 [info] 'ToolFileUtils' Successfully read file: server/storage.ts (38591 bytes)
2025-05-24 00:15:39.843 [info] 'ToolFileUtils' Reading file: server/storage.ts
2025-05-24 00:15:39.843 [info] 'ToolFileUtils' Successfully read file: server/storage.ts (40451 bytes)
2025-05-24 00:15:58.964 [info] 'ToolFileUtils' Reading file: server/storage.ts
2025-05-24 00:15:58.964 [info] 'ToolFileUtils' Successfully read file: server/storage.ts (40451 bytes)
2025-05-24 00:16:01.348 [info] 'ToolFileUtils' Reading file: server/storage.ts
2025-05-24 00:16:01.349 [info] 'ToolFileUtils' Successfully read file: server/storage.ts (44372 bytes)
2025-05-24 00:16:28.344 [info] 'ToolFileUtils' Reading file: server/storage.ts
2025-05-24 00:16:28.344 [info] 'ToolFileUtils' Successfully read file: server/storage.ts (44372 bytes)
2025-05-24 00:16:30.714 [info] 'ToolFileUtils' Reading file: server/storage.ts
2025-05-24 00:16:30.714 [info] 'ToolFileUtils' Successfully read file: server/storage.ts (49953 bytes)
2025-05-24 00:16:40.268 [info] 'ToolFileUtils' Reading file: server/storage.ts
2025-05-24 00:16:40.278 [info] 'ToolFileUtils' Successfully read file: server/storage.ts (49953 bytes)
2025-05-24 00:16:45.602 [info] 'ViewTool' Tool called with path: server/storage.ts and view_range: [1380,-1]
2025-05-24 00:16:53.452 [info] 'ToolFileUtils' Reading file: server/storage.ts
2025-05-24 00:16:53.452 [info] 'ToolFileUtils' Successfully read file: server/storage.ts (49953 bytes)
2025-05-24 00:16:55.680 [info] 'ToolFileUtils' Reading file: server/storage.ts
2025-05-24 00:16:55.680 [info] 'ToolFileUtils' Successfully read file: server/storage.ts (50144 bytes)
2025-05-24 00:17:05.843 [info] 'ToolFileUtils' Reading file: server/storage.ts
2025-05-24 00:17:05.843 [info] 'ToolFileUtils' Successfully read file: server/storage.ts (50144 bytes)
2025-05-24 00:17:08.114 [info] 'ToolFileUtils' Reading file: server/storage.ts
2025-05-24 00:17:08.114 [info] 'ToolFileUtils' Successfully read file: server/storage.ts (50097 bytes)
2025-05-24 00:17:15.298 [info] 'ToolFileUtils' Reading file: server/storage.ts
2025-05-24 00:17:15.298 [info] 'ToolFileUtils' Successfully read file: server/storage.ts (50097 bytes)
2025-05-24 00:17:17.752 [info] 'ToolFileUtils' Reading file: server/storage.ts
2025-05-24 00:17:17.752 [info] 'ToolFileUtils' Successfully read file: server/storage.ts (50102 bytes)
2025-05-24 00:17:27.314 [info] 'ToolFileUtils' Reading file: server/storage.ts
2025-05-24 00:17:27.314 [info] 'ToolFileUtils' Successfully read file: server/storage.ts (50102 bytes)
2025-05-24 00:17:29.644 [info] 'ToolFileUtils' Reading file: server/storage.ts
2025-05-24 00:17:29.645 [info] 'ToolFileUtils' Successfully read file: server/storage.ts (50152 bytes)
2025-05-24 00:18:53.122 [info] 'ToolFileUtils' Reading file: server/index.ts
2025-05-24 00:18:53.698 [info] 'ToolFileUtils' Successfully read file: server/index.ts (1919 bytes)
2025-05-24 00:18:56.584 [info] 'ToolFileUtils' Reading file: server/index.ts
2025-05-24 00:18:56.584 [info] 'ToolFileUtils' Successfully read file: server/index.ts (1967 bytes)
2025-05-24 00:19:13.857 [info] 'ToolFileUtils' Reading file: server/index.ts
2025-05-24 00:19:13.857 [info] 'ToolFileUtils' Successfully read file: server/index.ts (1967 bytes)
2025-05-24 00:19:16.392 [info] 'ToolFileUtils' Reading file: server/index.ts
2025-05-24 00:19:16.392 [info] 'ToolFileUtils' Successfully read file: server/index.ts (2296 bytes)
2025-05-24 00:19:28.795 [info] 'ToolFileUtils' Reading file: package.json
2025-05-24 00:19:29.342 [info] 'ToolFileUtils' Successfully read file: package.json (3790 bytes)
2025-05-24 00:19:31.305 [info] 'WorkspaceManager[workspace]' Directory created: .config/.vscode-server/data/User/History/6627711f
2025-05-24 00:19:31.990 [info] 'ToolFileUtils' Reading file: package.json
2025-05-24 00:19:31.990 [info] 'ToolFileUtils' Successfully read file: package.json (3955 bytes)
2025-05-24 00:19:34.943 [error] 'FuzzySymbolSearcher' Failed to read file tokens for 37fd78bbf97f33100cacb8a25439675b47419be8aac66179a77dd79cb8dec529: deleted
2025-05-24 00:20:09.627 [info] 'ToolFileUtils' Reading file: server/admin-api.ts
2025-05-24 00:20:10.712 [info] 'ToolFileUtils' Successfully read file: server/admin-api.ts (6038 bytes)
2025-05-24 00:20:12.891 [info] 'WorkspaceManager[workspace]' Directory created: .config/.vscode-server/data/User/History/2beac7ee
2025-05-24 00:20:13.544 [info] 'ToolFileUtils' Reading file: server/admin-api.ts
2025-05-24 00:20:13.544 [info] 'ToolFileUtils' Successfully read file: server/admin-api.ts (10178 bytes)
2025-05-24 00:20:46.540 [info] 'ToolFileUtils' Reading file: package.json
2025-05-24 00:20:46.540 [info] 'ToolFileUtils' Successfully read file: package.json (3955 bytes)
2025-05-24 00:20:48.924 [info] 'ToolFileUtils' Reading file: package.json
2025-05-24 00:20:48.924 [info] 'ToolFileUtils' Successfully read file: package.json (3995 bytes)
2025-05-24 00:20:51.922 [error] 'FuzzySymbolSearcher' Failed to read file tokens for 37fd78bbf97f33100cacb8a25439675b47419be8aac66179a77dd79cb8dec529: deleted
2025-05-24 00:24:51.346 [info] 'ToolFileUtils' Reading file: server/init-db.ts
2025-05-24 00:24:51.887 [info] 'ToolFileUtils' Successfully read file: server/init-db.ts (8300 bytes)
2025-05-24 00:24:54.202 [info] 'WorkspaceManager[workspace]' Directory created: .config/.vscode-server/data/User/History/-57b474f1
2025-05-24 00:24:54.959 [info] 'ToolFileUtils' Reading file: server/init-db.ts
2025-05-24 00:24:54.959 [info] 'ToolFileUtils' Successfully read file: server/init-db.ts (8301 bytes)
2025-05-24 00:25:06.890 [info] 'ToolFileUtils' Reading file: server/test-db.ts
2025-05-24 00:25:07.463 [info] 'ToolFileUtils' Successfully read file: server/test-db.ts (3306 bytes)
2025-05-24 00:25:09.735 [info] 'WorkspaceManager[workspace]' Directory created: .config/.vscode-server/data/User/History/-28beef
2025-05-24 00:25:10.421 [info] 'ToolFileUtils' Reading file: server/test-db.ts
2025-05-24 00:25:10.421 [info] 'ToolFileUtils' Successfully read file: server/test-db.ts (3272 bytes)
2025-05-24 00:25:21.057 [info] 'ToolFileUtils' Reading file: server/migrate.ts
2025-05-24 00:25:21.592 [info] 'ToolFileUtils' Successfully read file: server/migrate.ts (956 bytes)
2025-05-24 00:25:23.815 [info] 'WorkspaceManager[workspace]' Directory created: .config/.vscode-server/data/User/History/-4bd4d641
2025-05-24 00:25:24.492 [info] 'ToolFileUtils' Reading file: server/migrate.ts
2025-05-24 00:25:24.493 [info] 'ToolFileUtils' Successfully read file: server/migrate.ts (972 bytes)
2025-05-24 00:25:45.816 [info] 'ToolFileUtils' Reading file: server/migrate.ts
2025-05-24 00:25:45.816 [info] 'ToolFileUtils' Successfully read file: server/migrate.ts (972 bytes)
2025-05-24 00:25:48.159 [info] 'ToolFileUtils' Reading file: server/migrate.ts
2025-05-24 00:25:48.159 [info] 'ToolFileUtils' Successfully read file: server/migrate.ts (996 bytes)
2025-05-24 00:25:59.276 [info] 'ToolFileUtils' Reading file: server/migrate.ts
2025-05-24 00:25:59.277 [info] 'ToolFileUtils' Successfully read file: server/migrate.ts (996 bytes)
2025-05-24 00:26:01.824 [info] 'ToolFileUtils' Reading file: server/migrate.ts
2025-05-24 00:26:01.824 [info] 'ToolFileUtils' Successfully read file: server/migrate.ts (1043 bytes)
2025-05-24 00:26:11.427 [info] 'ToolFileUtils' Reading file: server/migrate.ts
2025-05-24 00:26:11.427 [info] 'ToolFileUtils' Successfully read file: server/migrate.ts (1043 bytes)
2025-05-24 00:26:13.737 [info] 'ToolFileUtils' Reading file: server/migrate.ts
2025-05-24 00:26:13.738 [info] 'ToolFileUtils' Successfully read file: server/migrate.ts (1070 bytes)
2025-05-24 00:26:44.054 [info] 'ViewTool' Tool called with path: shared/schema.ts and view_range: [1,50]
2025-05-24 00:27:25.644 [info] 'ToolFileUtils' Reading file: package.json
2025-05-24 00:27:26.257 [info] 'ToolFileUtils' Successfully read file: package.json (3995 bytes)
2025-05-24 00:27:28.985 [info] 'ToolFileUtils' Reading file: package.json
2025-05-24 00:27:28.985 [info] 'ToolFileUtils' Successfully read file: package.json (4050 bytes)
2025-05-24 00:27:31.808 [error] 'FuzzySymbolSearcher' Failed to read file tokens for 37fd78bbf97f33100cacb8a25439675b47419be8aac66179a77dd79cb8dec529: deleted
2025-05-24 00:27:50.736 [info] 'WorkspaceManager[workspace]' Directory created: migrations
2025-05-24 00:27:50.737 [info] 'WorkspaceManager[workspace]' Directory created: migrations/meta
2025-05-24 00:28:06.579 [info] 'ViewTool' Tool called with path: migrations and view_range: undefined
2025-05-24 00:28:06.781 [info] 'ViewTool' Listing directory: migrations (depth: 2, showHidden: false)
2025-05-24 00:28:12.390 [info] 'ViewTool' Tool called with path: drizzle.config.ts and view_range: undefined
2025-05-24 00:28:22.242 [info] 'ToolFileUtils' Reading file: server/migrate.ts
2025-05-24 00:28:22.242 [info] 'ToolFileUtils' Successfully read file: server/migrate.ts (1070 bytes)
2025-05-24 00:28:24.587 [info] 'ToolFileUtils' Reading file: server/migrate.ts
2025-05-24 00:28:24.587 [info] 'ToolFileUtils' Successfully read file: server/migrate.ts (1076 bytes)
2025-05-24 00:28:37.691 [info] 'ToolFileUtils' Reading file: server/storage.ts
2025-05-24 00:28:37.692 [info] 'ToolFileUtils' Successfully read file: server/storage.ts (50152 bytes)
2025-05-24 00:28:40.122 [info] 'ToolFileUtils' Reading file: server/storage.ts
2025-05-24 00:28:40.122 [info] 'ToolFileUtils' Successfully read file: server/storage.ts (50106 bytes)
2025-05-24 00:29:15.166 [info] 'ToolFileUtils' Reading file: package.json
2025-05-24 00:29:15.167 [info] 'ToolFileUtils' Successfully read file: package.json (4050 bytes)
2025-05-24 00:29:17.441 [info] 'ToolFileUtils' Reading file: package.json
2025-05-24 00:29:17.441 [info] 'ToolFileUtils' Successfully read file: package.json (4114 bytes)
2025-05-24 00:29:20.355 [error] 'FuzzySymbolSearcher' Failed to read file tokens for 37fd78bbf97f33100cacb8a25439675b47419be8aac66179a77dd79cb8dec529: deleted
2025-05-24 00:30:23.019 [info] 'ToolFileUtils' Reading file: package.json
2025-05-24 00:30:23.020 [info] 'ToolFileUtils' Successfully read file: package.json (4114 bytes)
2025-05-24 00:30:25.400 [info] 'ToolFileUtils' Reading file: package.json
2025-05-24 00:30:25.400 [info] 'ToolFileUtils' Successfully read file: package.json (4181 bytes)
2025-05-24 00:30:28.291 [error] 'FuzzySymbolSearcher' Failed to read file tokens for 37fd78bbf97f33100cacb8a25439675b47419be8aac66179a77dd79cb8dec529: deleted
2025-05-24 00:38:04.974 [info] 'ToolFileUtils' Reading file: shared/schema.ts
2025-05-24 00:38:04.975 [info] 'ToolFileUtils' Successfully read file: shared/schema.ts (5374 bytes)
2025-05-24 00:38:13.426 [info] 'AugmentExtension' Retrieving model config
2025-05-24 00:38:13.822 [info] 'AugmentExtension' Retrieved model config
2025-05-24 00:38:13.822 [info] 'AugmentExtension' Returning model config
2025-05-24 00:38:14.534 [info] 'ViewTool' Tool called with path: shared/schema.ts and view_range: undefined
2025-05-24 00:38:25.613 [info] 'ToolFileUtils' Reading file: shared/schema.ts
2025-05-24 00:38:25.614 [info] 'ToolFileUtils' Successfully read file: shared/schema.ts (5374 bytes)
2025-05-24 00:38:28.113 [info] 'ToolFileUtils' Reading file: shared/schema.ts
2025-05-24 00:38:28.113 [info] 'ToolFileUtils' Successfully read file: shared/schema.ts (6003 bytes)
2025-05-24 00:38:42.606 [info] 'ToolFileUtils' Reading file: server/storage.ts
2025-05-24 00:38:42.607 [info] 'ToolFileUtils' Successfully read file: server/storage.ts (50106 bytes)
2025-05-24 00:38:45.148 [info] 'ToolFileUtils' Reading file: server/storage.ts
2025-05-24 00:38:45.149 [info] 'ToolFileUtils' Successfully read file: server/storage.ts (50176 bytes)
2025-05-24 00:38:56.740 [info] 'ToolFileUtils' Reading file: server/storage.ts
2025-05-24 00:38:56.740 [info] 'ToolFileUtils' Successfully read file: server/storage.ts (50176 bytes)
2025-05-24 00:39:02.769 [info] 'ViewTool' Tool called with path: server/storage.ts and view_range: [80,95]
2025-05-24 00:39:12.426 [info] 'ToolFileUtils' Reading file: server/storage.ts
2025-05-24 00:39:12.426 [info] 'ToolFileUtils' Successfully read file: server/storage.ts (50176 bytes)
2025-05-24 00:39:14.657 [info] 'ToolFileUtils' Reading file: server/storage.ts
2025-05-24 00:39:14.657 [info] 'ToolFileUtils' Successfully read file: server/storage.ts (50395 bytes)
2025-05-24 00:39:20.820 [info] 'ViewTool' Tool called with path: server/storage.ts and view_range: [1380,1393]
2025-05-24 00:39:34.277 [info] 'ToolFileUtils' Reading file: server/storage.ts
2025-05-24 00:39:34.278 [info] 'ToolFileUtils' Successfully read file: server/storage.ts (50395 bytes)
2025-05-24 00:39:36.758 [info] 'ToolFileUtils' Reading file: server/storage.ts
2025-05-24 00:39:36.758 [info] 'ToolFileUtils' Successfully read file: server/storage.ts (51456 bytes)
2025-05-24 00:40:02.309 [info] 'ToolFileUtils' Reading file: server/admin-api.ts
2025-05-24 00:40:02.851 [info] 'ToolFileUtils' Successfully read file: server/admin-api.ts (10178 bytes)
2025-05-24 00:40:06.344 [info] 'ToolFileUtils' Reading file: server/admin-api.ts
2025-05-24 00:40:06.344 [info] 'ToolFileUtils' Successfully read file: server/admin-api.ts (11402 bytes)
2025-05-24 00:40:33.458 [info] 'ToolFileUtils' Reading file: server/routes.ts
2025-05-24 00:40:33.458 [info] 'ToolFileUtils' Successfully read file: server/routes.ts (5779 bytes)
2025-05-24 00:40:36.458 [info] 'ToolFileUtils' Reading file: server/routes.ts
2025-05-24 00:40:36.461 [info] 'ToolFileUtils' Successfully read file: server/routes.ts (6498 bytes)
2025-05-24 00:40:47.774 [info] 'ToolFileUtils' Reading file: client/src/pages/Checkout.tsx
2025-05-24 00:40:48.375 [info] 'ToolFileUtils' Successfully read file: client/src/pages/Checkout.tsx (81583 bytes)
2025-05-24 00:41:03.026 [info] 'ToolFileUtils' Reading file: client/src/pages/Checkout.tsx
2025-05-24 00:41:03.026 [info] 'ToolFileUtils' Successfully read file: client/src/pages/Checkout.tsx (81583 bytes)
2025-05-24 00:41:05.950 [info] 'ToolFileUtils' Reading file: client/src/pages/Checkout.tsx
2025-05-24 00:41:05.951 [info] 'ToolFileUtils' Successfully read file: client/src/pages/Checkout.tsx (82434 bytes)
2025-05-24 00:41:08.624 [error] 'FuzzySymbolSearcher' Failed to read file tokens for d2af4f1abac4622e8b424978c5a760ddcaeb8cd82dc5756582976205870bb9b2: deleted
2025-05-24 00:41:18.567 [info] 'ToolFileUtils' Reading file: client/src/pages/Checkout.tsx
2025-05-24 00:41:18.567 [info] 'ToolFileUtils' Successfully read file: client/src/pages/Checkout.tsx (82434 bytes)
2025-05-24 00:41:21.014 [info] 'ToolFileUtils' Reading file: client/src/pages/Checkout.tsx
2025-05-24 00:41:21.014 [info] 'ToolFileUtils' Successfully read file: client/src/pages/Checkout.tsx (82440 bytes)
2025-05-24 00:41:32.321 [info] 'ToolFileUtils' Reading file: client/src/pages/Checkout.tsx
2025-05-24 00:41:32.321 [info] 'ToolFileUtils' Successfully read file: client/src/pages/Checkout.tsx (82440 bytes)
2025-05-24 00:41:34.624 [info] 'ToolFileUtils' Reading file: client/src/pages/Checkout.tsx
2025-05-24 00:41:34.624 [info] 'ToolFileUtils' Successfully read file: client/src/pages/Checkout.tsx (82446 bytes)
2025-05-24 00:41:45.496 [info] 'ToolFileUtils' Reading file: client/src/pages/Checkout.tsx
2025-05-24 00:41:45.497 [info] 'ToolFileUtils' Successfully read file: client/src/pages/Checkout.tsx (82446 bytes)
2025-05-24 00:41:47.972 [info] 'ToolFileUtils' Reading file: client/src/pages/Checkout.tsx
2025-05-24 00:41:47.972 [info] 'ToolFileUtils' Successfully read file: client/src/pages/Checkout.tsx (82448 bytes)
2025-05-24 00:41:50.853 [error] 'FuzzySymbolSearcher' Failed to read file tokens for d2af4f1abac4622e8b424978c5a760ddcaeb8cd82dc5756582976205870bb9b2: deleted
2025-05-24 00:41:59.977 [info] 'ToolFileUtils' Reading file: client/src/pages/Cart.tsx
2025-05-24 00:42:00.733 [info] 'ToolFileUtils' Successfully read file: client/src/pages/Cart.tsx (29166 bytes)
2025-05-24 00:42:11.355 [info] 'ToolFileUtils' Reading file: client/src/pages/Cart.tsx
2025-05-24 00:42:11.356 [info] 'ToolFileUtils' Successfully read file: client/src/pages/Cart.tsx (29166 bytes)
2025-05-24 00:42:13.801 [info] 'WorkspaceManager[workspace]' Directory created: .config/.vscode-server/data/User/History/-11c37eca
2025-05-24 00:42:14.358 [info] 'ToolFileUtils' Reading file: client/src/pages/Cart.tsx
2025-05-24 00:42:14.358 [info] 'ToolFileUtils' Successfully read file: client/src/pages/Cart.tsx (29163 bytes)
2025-05-24 00:42:23.552 [info] 'ViewTool' Tool called with path: client/src/pages/Cart.tsx and view_range: [400,450]
2025-05-24 00:42:33.421 [info] 'ToolFileUtils' Reading file: client/src/pages/Cart.tsx
2025-05-24 00:42:33.421 [info] 'ToolFileUtils' Successfully read file: client/src/pages/Cart.tsx (29163 bytes)
2025-05-24 00:42:35.887 [info] 'ToolFileUtils' Reading file: client/src/pages/Cart.tsx
2025-05-24 00:42:35.887 [info] 'ToolFileUtils' Successfully read file: client/src/pages/Cart.tsx (29169 bytes)
2025-05-24 00:48:44.432 [info] 'ToolFileUtils' Reading file: test-db.js
2025-05-24 00:48:44.432 [info] 'ToolFileUtils' Successfully read file: test-db.js (1358 bytes)
2025-05-24 00:48:45.433 [info] 'WorkspaceManager[workspace]' Directory created: .cache/typescript/5.8
2025-05-24 00:48:46.415 [info] 'WorkspaceManager[workspace]' Directory created: .config/.vscode-server/data/User/History/-495e0cf3
2025-05-24 00:48:47.112 [info] 'ToolFileUtils' Reading file: test-db.js
2025-05-24 00:48:47.113 [info] 'ToolFileUtils' Successfully read file: test-db.js (1331 bytes)
2025-05-24 00:49:26.416 [info] 'ToolFileUtils' Reading file: test-db.js
2025-05-24 00:49:26.416 [info] 'ToolFileUtils' Successfully read file: test-db.js (1331 bytes)
2025-05-24 00:49:29.033 [info] 'ToolFileUtils' Reading file: test-db.js
2025-05-24 00:49:29.033 [info] 'ToolFileUtils' Successfully read file: test-db.js (1454 bytes)
2025-05-24 00:50:03.725 [info] 'ViewTool' Tool called with path: package.json and view_range: [1,50]
2025-05-24 00:50:11.368 [info] 'ViewTool' Tool called with path: server/test-db.ts and view_range: undefined
2025-05-24 00:50:24.682 [info] 'WorkspaceManager[workspace]' Tracking enabled
2025-05-24 00:50:24.682 [info] 'WorkspaceManager[workspace]' Path metrics:
  - directories emitted: 315
  - files emitted: 1893
  - other paths emitted: 4
  - total paths emitted: 2212
  - timing stats:
    - readDir: 9 ms
    - filter: 109 ms
    - yield: 14 ms
    - total: 142 ms
2025-05-24 00:50:24.682 [info] 'WorkspaceManager[workspace]' File metrics:
  - paths accepted: 5300
  - paths not accessible: 1
  - not plain files: 0
  - large files: 87
  - blob name calculation fails: 0
  - encoding errors: 0
  - mtime cache hits: 1560
  - mtime cache misses: 3740
  - probe batches: 897
  - blob names probed: 89665
  - files read: 4593
  - blobs uploaded: 697
  - timing stats:
    - ingestPath: 337 ms
    - probe: 457674 ms
    - stat: 151 ms
    - read: 31704 ms
    - upload: 231661 ms
2025-05-24 00:50:24.682 [info] 'WorkspaceManager[workspace]' Startup metrics:
  - create SourceFolder: 197 ms
  - read MtimeCache: 892 ms
  - pre-populate PathMap: 125 ms
  - create PathFilter: 565 ms
  - create PathNotifier: 0 ms
  - enumerate paths: 149 ms
  - purge stale PathMap entries: 1 ms
  - enumerate: 0 ms
  - await DiskFileManager quiesced: 2522778 ms
  - enable persist: 9 ms
  - total: 2524716 ms
2025-05-24 00:50:24.682 [info] 'WorkspaceManager' Workspace startup complete in 2525901 ms
2025-05-24 00:50:25.057 [info] 'ToolFileUtils' Reading file: server/test-db.ts
2025-05-24 00:50:25.057 [info] 'ToolFileUtils' Successfully read file: server/test-db.ts (3272 bytes)
2025-05-24 00:50:27.357 [info] 'ToolFileUtils' Reading file: server/test-db.ts
2025-05-24 00:50:27.358 [info] 'ToolFileUtils' Successfully read file: server/test-db.ts (4256 bytes)
2025-05-24 00:54:00.746 [info] 'ToolFileUtils' Reading file: server/storage.ts
2025-05-24 00:54:00.747 [info] 'ToolFileUtils' Successfully read file: server/storage.ts (51456 bytes)
2025-05-24 00:54:03.303 [info] 'ToolFileUtils' Reading file: server/storage.ts
2025-05-24 00:54:03.303 [info] 'ToolFileUtils' Successfully read file: server/storage.ts (51590 bytes)
2025-05-24 00:59:32.127 [info] 'ToolsWebviewMessageHandler' Received closeAllToolProcesses message
2025-05-24 00:59:32.611 [info] 'TaskManager' Setting current root task UUID to a4bcbb9b-6697-471a-bf78-8826179b54e0
2025-05-24 00:59:32.611 [info] 'TaskManager' Setting current root task UUID to a4bcbb9b-6697-471a-bf78-8826179b54e0
2025-05-24 01:01:18.370 [info] 'ViewTool' Tool called with path: . and view_range: undefined
2025-05-24 01:01:18.545 [info] 'ViewTool' Listing directory: . (depth: 2, showHidden: false)
2025-05-24 01:02:25.429 [info] 'ViewTool' Tool called with path: server and view_range: undefined
2025-05-24 01:02:25.603 [info] 'ViewTool' Listing directory: server (depth: 2, showHidden: false)
2025-05-24 01:02:30.284 [info] 'ViewTool' Tool called with path: scripts and view_range: undefined
2025-05-24 01:02:30.460 [info] 'ViewTool' Listing directory: scripts (depth: 2, showHidden: false)
2025-05-24 01:02:35.210 [info] 'ViewTool' Tool called with path: client/src and view_range: undefined
2025-05-24 01:02:35.386 [info] 'ViewTool' Listing directory: client/src (depth: 2, showHidden: false)
2025-05-24 01:02:42.494 [info] 'ViewTool' Tool called with path: node_modules/bcrypt/test and view_range: undefined
2025-05-24 01:02:42.670 [info] 'ViewTool' Listing directory: node_modules/bcrypt/test (depth: 2, showHidden: false)
2025-05-24 01:02:47.074 [info] 'ViewTool' Tool called with path: attached_assets and view_range: undefined
2025-05-24 01:02:47.249 [info] 'ViewTool' Listing directory: attached_assets (depth: 2, showHidden: false)
2025-05-24 01:02:52.782 [info] 'ViewTool' Tool called with path: server/test-db.ts and view_range: undefined
2025-05-24 01:02:57.520 [info] 'ViewTool' Tool called with path: server/mock-orders.ts and view_range: undefined
2025-05-24 01:03:02.952 [info] 'ViewTool' Tool called with path: server/seed-customizations.ts and view_range: undefined
2025-05-24 01:03:08.246 [info] 'ViewTool' Tool called with path: scripts/run_seed.js and view_range: undefined
2025-05-24 01:03:15.489 [info] 'ViewTool' Tool called with path: attached_assets/Pasted-Generate-Seeder-Script-for-PostgreSQL-Menu-Categories-Settings-Orders-Prompt-csharp-Copy-Edit-1747872099827.txt and view_range: [1,50]
2025-05-24 01:03:30.741 [info] 'ToolFileUtils' Reading file: .
2025-05-24 01:03:31.888 [info] 'ToolFileUtils' File not found: .. No similar files found
2025-05-24 01:03:31.888 [error] 'StrReplaceEditorTool' Error in tool call: File not found: .
2025-05-24 01:03:43.121 [info] 'WorkspaceManager[workspace]' Directory created: .config/.vscode-server/data/User/workspaceStorage/28d495afc6b3165b645c0d6d358123ef/Augment.vscode-augment/augment-user-assets/checkpoint-documents/8cd18cae-af48-4628-881a-aae2b3ddde1c
2025-05-24 01:04:07.000 [info] 'ViewTool' Tool called with path: attached_assets and view_range: undefined
2025-05-24 01:04:07.175 [info] 'ViewTool' Listing directory: attached_assets (depth: 2, showHidden: false)
2025-05-24 01:04:49.605 [info] 'ViewTool' Tool called with path: attached_assets and view_range: undefined
2025-05-24 01:04:49.783 [info] 'ViewTool' Listing directory: attached_assets (depth: 2, showHidden: false)
2025-05-24 01:04:56.559 [info] 'WorkspaceManager[workspace]' Directory removed: attached_assets
2025-05-24 01:05:00.967 [info] 'ViewTool' Tool called with path: scripts and view_range: undefined
2025-05-24 01:05:01.139 [info] 'ViewTool' Listing directory: scripts (depth: 2, showHidden: false)
2025-05-24 01:05:07.252 [info] 'WorkspaceManager[workspace]' Directory removed: scripts
2025-05-24 01:05:24.772 [info] 'ViewTool' Tool called with path: package.json and view_range: undefined
2025-05-24 01:05:34.351 [info] 'ToolFileUtils' Reading file: package.json
2025-05-24 01:05:34.351 [info] 'ToolFileUtils' Successfully read file: package.json (4181 bytes)
2025-05-24 01:05:37.521 [info] 'ToolFileUtils' Reading file: package.json
2025-05-24 01:05:37.522 [info] 'ToolFileUtils' Successfully read file: package.json (4074 bytes)
2025-05-24 01:05:47.032 [info] 'ViewTool' Tool called with path: server/admin-api.ts and view_range: [1,50]
2025-05-24 01:05:59.250 [info] 'ToolFileUtils' Reading file: server/admin-api.ts
2025-05-24 01:05:59.251 [info] 'ToolFileUtils' Successfully read file: server/admin-api.ts (11402 bytes)
2025-05-24 01:06:02.614 [info] 'ToolFileUtils' Reading file: server/admin-api.ts
2025-05-24 01:06:02.615 [info] 'ToolFileUtils' Successfully read file: server/admin-api.ts (11356 bytes)
2025-05-24 01:06:15.483 [info] 'ToolFileUtils' Reading file: server/admin-api.ts
2025-05-24 01:06:15.484 [info] 'ToolFileUtils' Successfully read file: server/admin-api.ts (11356 bytes)
2025-05-24 01:06:18.500 [info] 'ToolFileUtils' Reading file: server/admin-api.ts
2025-05-24 01:06:18.500 [info] 'ToolFileUtils' Successfully read file: server/admin-api.ts (11359 bytes)
2025-05-24 01:06:30.919 [info] 'ToolFileUtils' Reading file: server/admin-api.ts
2025-05-24 01:06:30.919 [info] 'ToolFileUtils' Successfully read file: server/admin-api.ts (11359 bytes)
2025-05-24 01:06:33.827 [info] 'ToolFileUtils' Reading file: server/admin-api.ts
2025-05-24 01:06:33.827 [info] 'ToolFileUtils' Successfully read file: server/admin-api.ts (11256 bytes)
2025-05-24 01:06:53.087 [info] 'ToolFileUtils' Reading file: server/admin-api.ts
2025-05-24 01:06:53.087 [info] 'ToolFileUtils' Successfully read file: server/admin-api.ts (11256 bytes)
2025-05-24 01:06:56.007 [info] 'ToolFileUtils' Reading file: server/admin-api.ts
2025-05-24 01:06:56.007 [info] 'ToolFileUtils' Successfully read file: server/admin-api.ts (11262 bytes)
2025-05-24 01:07:13.603 [info] 'ViewTool' Tool called with path: . and view_range: undefined
2025-05-24 01:07:13.780 [info] 'ViewTool' Listing directory: . (depth: 2, showHidden: false)
2025-05-24 01:08:13.460 [info] 'AugmentExtension' Retrieving model config
2025-05-24 01:08:13.755 [info] 'AugmentExtension' Retrieved model config
2025-05-24 01:08:13.755 [info] 'AugmentExtension' Returning model config
2025-05-24 01:11:05.588 [info] 'WorkspaceManager[workspace]' Directory created: .cache/replit/transfers
2025-05-24 01:11:06.304 [info] 'WorkspaceManager[workspace]' Directory created: attached_assets
2025-05-24 01:16:58.055 [info] 'WorkspaceManager[workspace]' Directory created: client/src/components/admin
2025-05-24 01:22:29.487 [info] 'ViewTool' Tool called with path: client/src/pages/admin/OrderManager.tsx and view_range: [310,320]
2025-05-24 01:22:51.598 [info] 'ViewTool' Tool called with path: server/admin-api.ts and view_range: [135,160]
2025-05-24 01:23:04.945 [info] 'ToolFileUtils' Reading file: shared/schema.ts
2025-05-24 01:23:04.945 [info] 'ToolFileUtils' Successfully read file: shared/schema.ts (6003 bytes)
2025-05-24 01:23:07.172 [info] 'ToolFileUtils' Reading file: shared/schema.ts
2025-05-24 01:23:07.173 [info] 'ToolFileUtils' Successfully read file: shared/schema.ts (6053 bytes)
2025-05-24 01:23:37.823 [info] 'ToolFileUtils' Reading file: client/src/pages/admin/OrderManager.tsx
2025-05-24 01:23:37.823 [info] 'ToolFileUtils' Successfully read file: client/src/pages/admin/OrderManager.tsx (18429 bytes)
2025-05-24 01:23:39.746 [info] 'WorkspaceManager[workspace]' Directory created: .config/.vscode-server/data/User/History/21324295
2025-05-24 01:23:40.478 [info] 'ToolFileUtils' Reading file: client/src/pages/admin/OrderManager.tsx
2025-05-24 01:23:40.478 [info] 'ToolFileUtils' Successfully read file: client/src/pages/admin/OrderManager.tsx (18220 bytes)
2025-05-24 01:23:51.606 [info] 'ToolFileUtils' Reading file: client/src/pages/admin/OrderManager.tsx
2025-05-24 01:23:51.606 [info] 'ToolFileUtils' Successfully read file: client/src/pages/admin/OrderManager.tsx (18220 bytes)
2025-05-24 01:23:53.815 [info] 'ToolFileUtils' Reading file: client/src/pages/admin/OrderManager.tsx
2025-05-24 01:23:53.815 [info] 'ToolFileUtils' Successfully read file: client/src/pages/admin/OrderManager.tsx (18230 bytes)
2025-05-24 01:24:06.287 [info] 'ToolFileUtils' Reading file: client/src/pages/admin/OrderManager.tsx
2025-05-24 01:24:06.287 [info] 'ToolFileUtils' Successfully read file: client/src/pages/admin/OrderManager.tsx (18230 bytes)
2025-05-24 01:24:08.519 [info] 'ToolFileUtils' Reading file: client/src/pages/admin/OrderManager.tsx
2025-05-24 01:24:08.520 [info] 'ToolFileUtils' Successfully read file: client/src/pages/admin/OrderManager.tsx (18347 bytes)
2025-05-24 01:24:19.473 [info] 'ToolFileUtils' Reading file: client/src/pages/admin/OrderManager.tsx
2025-05-24 01:24:19.473 [info] 'ToolFileUtils' Successfully read file: client/src/pages/admin/OrderManager.tsx (18347 bytes)
2025-05-24 01:24:21.740 [info] 'ToolFileUtils' Reading file: client/src/pages/admin/OrderManager.tsx
2025-05-24 01:24:21.740 [info] 'ToolFileUtils' Successfully read file: client/src/pages/admin/OrderManager.tsx (18350 bytes)
2025-05-24 01:24:33.026 [info] 'ToolFileUtils' Reading file: client/src/pages/admin/OrderManager.tsx
2025-05-24 01:24:33.027 [info] 'ToolFileUtils' Successfully read file: client/src/pages/admin/OrderManager.tsx (18350 bytes)
2025-05-24 01:24:35.231 [info] 'ToolFileUtils' Reading file: client/src/pages/admin/OrderManager.tsx
2025-05-24 01:24:35.231 [info] 'ToolFileUtils' Successfully read file: client/src/pages/admin/OrderManager.tsx (18351 bytes)
2025-05-24 01:24:45.535 [info] 'ToolFileUtils' Reading file: client/src/pages/driver/DriverPage.tsx
2025-05-24 01:24:46.076 [info] 'ToolFileUtils' Successfully read file: client/src/pages/driver/DriverPage.tsx (16461 bytes)
2025-05-24 01:24:48.013 [info] 'WorkspaceManager[workspace]' Directory created: .config/.vscode-server/data/User/History/3a5f65fe
2025-05-24 01:24:48.670 [info] 'ToolFileUtils' Reading file: client/src/pages/driver/DriverPage.tsx
2025-05-24 01:24:48.671 [info] 'ToolFileUtils' Successfully read file: client/src/pages/driver/DriverPage.tsx (16196 bytes)
2025-05-24 01:25:01.760 [info] 'ToolFileUtils' Reading file: client/src/pages/driver/DriverPage.tsx
2025-05-24 01:25:01.760 [info] 'ToolFileUtils' Successfully read file: client/src/pages/driver/DriverPage.tsx (16196 bytes)
2025-05-24 01:25:04.111 [info] 'ToolFileUtils' Reading file: client/src/pages/driver/DriverPage.tsx
2025-05-24 01:25:04.111 [info] 'ToolFileUtils' Successfully read file: client/src/pages/driver/DriverPage.tsx (16197 bytes)
2025-05-24 01:25:12.518 [info] 'ViewTool' Tool called with path: client/src/pages/driver/DriverPage.tsx and view_range: [375,385]
2025-05-24 01:25:22.416 [info] 'ToolFileUtils' Reading file: client/src/pages/driver/DriverPage.tsx
2025-05-24 01:25:22.417 [info] 'ToolFileUtils' Successfully read file: client/src/pages/driver/DriverPage.tsx (16197 bytes)
2025-05-24 01:25:24.742 [info] 'ToolFileUtils' Reading file: client/src/pages/driver/DriverPage.tsx
2025-05-24 01:25:24.742 [info] 'ToolFileUtils' Successfully read file: client/src/pages/driver/DriverPage.tsx (16198 bytes)
2025-05-24 01:25:35.593 [info] 'ToolFileUtils' Reading file: client/src/pages/manager/ManagerPage.tsx
2025-05-24 01:25:36.136 [info] 'ToolFileUtils' Successfully read file: client/src/pages/manager/ManagerPage.tsx (25424 bytes)
2025-05-24 01:25:38.103 [info] 'WorkspaceManager[workspace]' Directory created: .config/.vscode-server/data/User/History/5cd34fda
2025-05-24 01:25:38.799 [info] 'ToolFileUtils' Reading file: client/src/pages/manager/ManagerPage.tsx
2025-05-24 01:25:38.799 [info] 'ToolFileUtils' Successfully read file: client/src/pages/manager/ManagerPage.tsx (24937 bytes)
2025-05-24 01:25:50.077 [info] 'ToolFileUtils' Reading file: client/src/pages/manager/ManagerPage.tsx
2025-05-24 01:25:50.077 [info] 'ToolFileUtils' Successfully read file: client/src/pages/manager/ManagerPage.tsx (24937 bytes)
2025-05-24 01:25:52.350 [info] 'ToolFileUtils' Reading file: client/src/pages/manager/ManagerPage.tsx
2025-05-24 01:25:52.350 [info] 'ToolFileUtils' Successfully read file: client/src/pages/manager/ManagerPage.tsx (24938 bytes)
2025-05-24 01:26:11.128 [info] 'ToolFileUtils' Reading file: client/src/pages/manager/ManagerPage.tsx
2025-05-24 01:26:11.128 [info] 'ToolFileUtils' Successfully read file: client/src/pages/manager/ManagerPage.tsx (24938 bytes)
2025-05-24 01:26:13.378 [info] 'ToolFileUtils' Reading file: client/src/pages/manager/ManagerPage.tsx
2025-05-24 01:26:13.378 [info] 'ToolFileUtils' Successfully read file: client/src/pages/manager/ManagerPage.tsx (24939 bytes)
2025-05-24 01:26:25.664 [info] 'ToolFileUtils' Reading file: client/src/pages/manager/ManagerPage.tsx
2025-05-24 01:26:25.664 [info] 'ToolFileUtils' Successfully read file: client/src/pages/manager/ManagerPage.tsx (24939 bytes)
2025-05-24 01:26:27.897 [info] 'ToolFileUtils' Reading file: client/src/pages/manager/ManagerPage.tsx
2025-05-24 01:26:27.897 [info] 'ToolFileUtils' Successfully read file: client/src/pages/manager/ManagerPage.tsx (24941 bytes)
2025-05-24 01:26:39.402 [info] 'ToolFileUtils' Reading file: client/src/pages/manager/ManagerPage.tsx
2025-05-24 01:26:39.402 [info] 'ToolFileUtils' Successfully read file: client/src/pages/manager/ManagerPage.tsx (24941 bytes)
2025-05-24 01:26:42.936 [info] 'ToolFileUtils' Reading file: client/src/pages/manager/ManagerPage.tsx
2025-05-24 01:26:42.936 [info] 'ToolFileUtils' Successfully read file: client/src/pages/manager/ManagerPage.tsx (24943 bytes)
2025-05-24 01:26:54.180 [info] 'ToolFileUtils' Reading file: client/src/pages/manager/ManagerPage.tsx
2025-05-24 01:26:54.180 [info] 'ToolFileUtils' Successfully read file: client/src/pages/manager/ManagerPage.tsx (24943 bytes)
2025-05-24 01:26:57.945 [info] 'ToolFileUtils' Reading file: client/src/pages/manager/ManagerPage.tsx
2025-05-24 01:26:57.945 [info] 'ToolFileUtils' Successfully read file: client/src/pages/manager/ManagerPage.tsx (24944 bytes)
2025-05-24 01:27:17.318 [info] 'ToolFileUtils' Reading file: client/src/pages/admin/OrderManager.tsx
2025-05-24 01:27:17.318 [info] 'ToolFileUtils' Successfully read file: client/src/pages/admin/OrderManager.tsx (18351 bytes)
2025-05-24 01:27:19.729 [info] 'ToolFileUtils' Reading file: client/src/pages/admin/OrderManager.tsx
2025-05-24 01:27:19.730 [info] 'ToolFileUtils' Successfully read file: client/src/pages/admin/OrderManager.tsx (18352 bytes)
2025-05-24 01:32:42.974 [info] 'ViewTool' Tool called with path: client/src/pages/admin and view_range: undefined
2025-05-24 01:32:43.150 [info] 'ViewTool' Listing directory: client/src/pages/admin (depth: 2, showHidden: false)
2025-05-24 01:32:48.711 [info] 'ViewTool' Tool called with path: server/storage.ts and view_range: [20,100]
2025-05-24 01:33:09.239 [info] 'ToolFileUtils' Reading file: server/storage.ts
2025-05-24 01:33:09.239 [info] 'ToolFileUtils' Successfully read file: server/storage.ts (51590 bytes)
2025-05-24 01:33:11.617 [info] 'ToolFileUtils' Reading file: server/storage.ts
2025-05-24 01:33:11.617 [info] 'ToolFileUtils' Successfully read file: server/storage.ts (52779 bytes)
2025-05-24 01:33:23.781 [info] 'ToolFileUtils' Reading file: server/storage.ts
2025-05-24 01:33:23.781 [info] 'ToolFileUtils' Successfully read file: server/storage.ts (52779 bytes)
2025-05-24 01:33:26.016 [info] 'ToolFileUtils' Reading file: server/storage.ts
2025-05-24 01:33:26.016 [info] 'ToolFileUtils' Successfully read file: server/storage.ts (53798 bytes)
2025-05-24 01:33:40.296 [info] 'ToolFileUtils' Reading file: server/storage.ts
2025-05-24 01:33:40.296 [info] 'ToolFileUtils' Successfully read file: server/storage.ts (53798 bytes)
2025-05-24 01:33:42.517 [info] 'ToolFileUtils' Reading file: server/storage.ts
2025-05-24 01:33:42.518 [info] 'ToolFileUtils' Successfully read file: server/storage.ts (54465 bytes)
2025-05-24 01:33:56.946 [info] 'ToolFileUtils' Reading file: server/admin-api.ts
2025-05-24 01:33:57.479 [info] 'ToolFileUtils' Successfully read file: server/admin-api.ts (11262 bytes)
2025-05-24 01:34:00.059 [info] 'ToolFileUtils' Reading file: server/admin-api.ts
2025-05-24 01:34:00.059 [info] 'ToolFileUtils' Successfully read file: server/admin-api.ts (13362 bytes)
2025-05-24 01:34:16.251 [info] 'ToolFileUtils' Reading file: server/admin-api.ts
2025-05-24 01:34:16.252 [info] 'ToolFileUtils' Successfully read file: server/admin-api.ts (13362 bytes)
2025-05-24 01:34:18.453 [info] 'ToolFileUtils' Reading file: server/admin-api.ts
2025-05-24 01:34:18.454 [info] 'ToolFileUtils' Successfully read file: server/admin-api.ts (14861 bytes)
2025-05-24 01:34:35.800 [info] 'ToolFileUtils' Reading file: server/admin-api.ts
2025-05-24 01:34:35.800 [info] 'ToolFileUtils' Successfully read file: server/admin-api.ts (14861 bytes)
2025-05-24 01:34:38.059 [info] 'ToolFileUtils' Reading file: server/admin-api.ts
2025-05-24 01:34:38.059 [info] 'ToolFileUtils' Successfully read file: server/admin-api.ts (16077 bytes)
2025-05-24 01:34:56.383 [info] 'ToolFileUtils' Reading file: server/admin-api.ts
2025-05-24 01:34:56.383 [info] 'ToolFileUtils' Successfully read file: server/admin-api.ts (16077 bytes)
2025-05-24 01:34:58.614 [info] 'ToolFileUtils' Reading file: server/admin-api.ts
2025-05-24 01:34:58.614 [info] 'ToolFileUtils' Successfully read file: server/admin-api.ts (17747 bytes)
2025-05-24 01:36:40.470 [info] 'ToolFileUtils' Reading file: client/src/pages/admin/CustomizationManager.tsx
2025-05-24 01:36:40.470 [info] 'ToolFileUtils' Successfully read file: client/src/pages/admin/CustomizationManager.tsx (12176 bytes)
2025-05-24 01:36:42.242 [info] 'WorkspaceManager[workspace]' Directory created: .config/.vscode-server/data/User/History/415831f0
2025-05-24 01:36:42.895 [info] 'ToolFileUtils' Reading file: client/src/pages/admin/CustomizationManager.tsx
2025-05-24 01:36:42.896 [info] 'ToolFileUtils' Successfully read file: client/src/pages/admin/CustomizationManager.tsx (18197 bytes)
2025-05-24 01:36:58.473 [info] 'ToolFileUtils' Reading file: client/src/pages/admin/AdminLayout.tsx
2025-05-24 01:36:59.017 [info] 'ToolFileUtils' Successfully read file: client/src/pages/admin/AdminLayout.tsx (8737 bytes)
2025-05-24 01:37:01.718 [info] 'ToolFileUtils' Reading file: client/src/pages/admin/AdminLayout.tsx
2025-05-24 01:37:01.718 [info] 'ToolFileUtils' Successfully read file: client/src/pages/admin/AdminLayout.tsx (8746 bytes)
2025-05-24 01:37:15.324 [info] 'ToolFileUtils' Reading file: client/src/pages/admin/AdminLayout.tsx
2025-05-24 01:37:15.324 [info] 'ToolFileUtils' Successfully read file: client/src/pages/admin/AdminLayout.tsx (8746 bytes)
2025-05-24 01:37:17.616 [info] 'ToolFileUtils' Reading file: client/src/pages/admin/AdminLayout.tsx
2025-05-24 01:37:17.617 [info] 'ToolFileUtils' Successfully read file: client/src/pages/admin/AdminLayout.tsx (8925 bytes)
2025-05-24 01:37:26.826 [info] 'ViewTool' Tool called with path: client/src/App.tsx and view_range: [1,100]
2025-05-24 01:37:36.856 [info] 'ToolFileUtils' Reading file: client/src/App.tsx
2025-05-24 01:37:36.856 [info] 'ToolFileUtils' Successfully read file: client/src/App.tsx (3362 bytes)
2025-05-24 01:37:40.314 [info] 'ToolFileUtils' Reading file: client/src/App.tsx
2025-05-24 01:37:40.315 [info] 'ToolFileUtils' Successfully read file: client/src/App.tsx (3433 bytes)
2025-05-24 01:37:50.689 [info] 'ToolFileUtils' Reading file: client/src/App.tsx
2025-05-24 01:37:50.689 [info] 'ToolFileUtils' Successfully read file: client/src/App.tsx (3433 bytes)
2025-05-24 01:37:53.146 [info] 'ToolFileUtils' Reading file: client/src/App.tsx
2025-05-24 01:37:53.146 [info] 'ToolFileUtils' Successfully read file: client/src/App.tsx (3515 bytes)
2025-05-24 01:38:09.939 [info] 'ToolFileUtils' Reading file: server/routes.ts
2025-05-24 01:38:09.940 [info] 'ToolFileUtils' Successfully read file: server/routes.ts (6498 bytes)
2025-05-24 01:38:12.308 [info] 'ToolFileUtils' Reading file: server/routes.ts
2025-05-24 01:38:12.308 [info] 'ToolFileUtils' Successfully read file: server/routes.ts (6882 bytes)
2025-05-24 01:38:13.567 [info] 'AugmentExtension' Retrieving model config
2025-05-24 01:38:13.995 [info] 'AugmentExtension' Retrieved model config
2025-05-24 01:38:13.995 [info] 'AugmentExtension' Returning model config
2025-05-24 01:38:22.823 [info] 'ToolFileUtils' Reading file: client/src/components/CustomizeModal.tsx
2025-05-24 01:38:22.823 [info] 'ToolFileUtils' Successfully read file: client/src/components/CustomizeModal.tsx (8351 bytes)
2025-05-24 01:38:25.168 [info] 'ToolFileUtils' Reading file: client/src/components/CustomizeModal.tsx
2025-05-24 01:38:25.168 [info] 'ToolFileUtils' Successfully read file: client/src/components/CustomizeModal.tsx (8153 bytes)
2025-05-24 01:38:42.723 [info] 'ToolFileUtils' Reading file: client/src/components/CustomizeModal.tsx
2025-05-24 01:38:42.724 [info] 'ToolFileUtils' Successfully read file: client/src/components/CustomizeModal.tsx (8153 bytes)
2025-05-24 01:38:48.819 [info] 'ViewTool' Tool called with path: client/src/components/CustomizeModal.tsx and view_range: [10,80]
2025-05-24 01:39:09.687 [info] 'ToolFileUtils' Reading file: client/src/components/CustomizeModal.tsx
2025-05-24 01:39:09.688 [info] 'ToolFileUtils' Successfully read file: client/src/components/CustomizeModal.tsx (8153 bytes)
2025-05-24 01:39:12.304 [info] 'ToolFileUtils' Reading file: client/src/components/CustomizeModal.tsx
2025-05-24 01:39:12.304 [info] 'ToolFileUtils' Successfully read file: client/src/components/CustomizeModal.tsx (7524 bytes)
2025-05-24 01:39:25.095 [info] 'ToolFileUtils' Reading file: client/src/components/CustomizeModal.tsx
2025-05-24 01:39:25.096 [info] 'ToolFileUtils' Successfully read file: client/src/components/CustomizeModal.tsx (7524 bytes)
2025-05-24 01:39:27.353 [info] 'ToolFileUtils' Reading file: client/src/components/CustomizeModal.tsx
2025-05-24 01:39:27.353 [info] 'ToolFileUtils' Successfully read file: client/src/components/CustomizeModal.tsx (7524 bytes)
2025-05-24 01:39:51.271 [info] 'ToolFileUtils' Reading file: client/src/components/CustomizeModal.tsx
2025-05-24 01:39:51.271 [info] 'ToolFileUtils' Successfully read file: client/src/components/CustomizeModal.tsx (7524 bytes)
2025-05-24 01:39:53.638 [info] 'ToolFileUtils' Reading file: client/src/components/CustomizeModal.tsx
2025-05-24 01:39:53.639 [info] 'ToolFileUtils' Successfully read file: client/src/components/CustomizeModal.tsx (8451 bytes)
2025-05-24 01:40:58.766 [info] 'ViewTool' Tool called with path: server/routes.ts and view_range: [230,250]
2025-05-24 01:41:03.809 [info] 'ViewTool' Tool called with path: server/auth.ts and view_range: [1,50]
2025-05-24 01:41:15.722 [info] 'ToolFileUtils' Reading file: server/auth.ts
2025-05-24 01:41:15.722 [info] 'ToolFileUtils' Successfully read file: server/auth.ts (6708 bytes)
2025-05-24 01:41:17.779 [info] 'WorkspaceManager[workspace]' Directory created: .config/.vscode-server/data/User/History/-69ca4e78
2025-05-24 01:41:18.471 [info] 'ToolFileUtils' Reading file: server/auth.ts
2025-05-24 01:41:18.471 [info] 'ToolFileUtils' Successfully read file: server/auth.ts (7082 bytes)
2025-05-24 01:41:26.141 [info] 'AugmentConfigListener' settings parsed successfully
2025-05-24 01:41:26.218 [info] 'WorkspaceManager[workspace]' Directory created: .config/.vscode-server/extensions/coderabbit.coderabbit-vscode-0.7.8
2025-05-24 01:41:28.837 [info] 'ToolFileUtils' Reading file: server/routes.ts
2025-05-24 01:41:28.838 [info] 'ToolFileUtils' Successfully read file: server/routes.ts (6882 bytes)
2025-05-24 01:41:31.249 [info] 'ToolFileUtils' Reading file: server/routes.ts
2025-05-24 01:41:31.250 [info] 'ToolFileUtils' Successfully read file: server/routes.ts (6891 bytes)
2025-05-24 01:41:39.533 [info] 'ToolFileUtils' Reading file: server/routes.ts
2025-05-24 01:41:39.533 [info] 'ToolFileUtils' Successfully read file: server/routes.ts (6891 bytes)
2025-05-24 01:41:41.804 [info] 'ToolFileUtils' Reading file: server/routes.ts
2025-05-24 01:41:41.804 [info] 'ToolFileUtils' Successfully read file: server/routes.ts (6922 bytes)
2025-05-24 01:41:47.859 [info] 'ViewTool' Tool called with path: server/db.ts and view_range: [200,300]
2025-05-24 01:41:53.218 [info] 'ViewTool' Tool called with path: server and view_range: undefined
2025-05-24 01:41:53.398 [info] 'ViewTool' Listing directory: server (depth: 2, showHidden: false)
2025-05-24 01:41:59.011 [info] 'ViewTool' Tool called with path: server/init-db.ts and view_range: [1,100]
2025-05-24 01:43:49.075 [info] 'ToolFileUtils' Reading file: test-customizations.js
2025-05-24 01:43:49.075 [info] 'ToolFileUtils' Successfully read file: test-customizations.js (1876 bytes)
2025-05-24 01:43:50.707 [info] 'WorkspaceManager[workspace]' Directory created: .config/.vscode-server/data/User/History/-7f9dd8b5
2025-05-24 01:43:51.369 [info] 'ToolFileUtils' Reading file: test-customizations.js
2025-05-24 01:43:51.370 [info] 'ToolFileUtils' Successfully read file: test-customizations.js (1855 bytes)
2025-05-24 01:44:16.173 [info] 'ToolFileUtils' Reading file: test-customizations.js
2025-05-24 01:44:16.173 [info] 'ToolFileUtils' Successfully read file: test-customizations.js (1855 bytes)
2025-05-24 01:44:20.257 [info] 'StallDetector' Recent work: [{"name":"get-subscription-info","durationMs":1012.838397,"timestamp":"2025-05-24T01:44:20.207Z"}]
2025-05-24 01:44:21.012 [info] 'ToolFileUtils' Reading file: test-customizations.js
2025-05-24 01:44:21.012 [info] 'ToolFileUtils' Successfully read file: test-customizations.js (1861 bytes)
2025-05-24 01:47:31.301 [info] 'ViewTool' Tool called with path: client/src/api/customizationApi.ts and view_range: [1,30]
2025-05-24 01:47:43.603 [info] 'ToolFileUtils' Reading file: client/src/api/customizationApi.ts
2025-05-24 01:47:43.604 [info] 'ToolFileUtils' Successfully read file: client/src/api/customizationApi.ts (4547 bytes)
2025-05-24 01:47:45.754 [info] 'WorkspaceManager[workspace]' Directory created: .config/.vscode-server/data/User/History/15106bf1
2025-05-24 01:47:46.558 [info] 'ToolFileUtils' Reading file: client/src/api/customizationApi.ts
2025-05-24 01:47:46.558 [info] 'ToolFileUtils' Successfully read file: client/src/api/customizationApi.ts (4565 bytes)
2025-05-24 01:47:59.643 [info] 'ToolFileUtils' Reading file: client/src/api/customizationApi.ts
2025-05-24 01:47:59.644 [info] 'ToolFileUtils' Successfully read file: client/src/api/customizationApi.ts (4565 bytes)
2025-05-24 01:48:02.427 [info] 'ToolFileUtils' Reading file: client/src/api/customizationApi.ts
2025-05-24 01:48:02.428 [info] 'ToolFileUtils' Successfully read file: client/src/api/customizationApi.ts (4593 bytes)
2025-05-24 01:48:15.197 [info] 'ToolFileUtils' Reading file: client/src/api/customizationApi.ts
2025-05-24 01:48:15.197 [info] 'ToolFileUtils' Successfully read file: client/src/api/customizationApi.ts (4593 bytes)
2025-05-24 01:48:17.531 [info] 'ToolFileUtils' Reading file: client/src/api/customizationApi.ts
2025-05-24 01:48:17.531 [info] 'ToolFileUtils' Successfully read file: client/src/api/customizationApi.ts (4621 bytes)
2025-05-24 01:48:29.047 [info] 'ToolFileUtils' Reading file: client/src/api/customizationApi.ts
2025-05-24 01:48:29.047 [info] 'ToolFileUtils' Successfully read file: client/src/api/customizationApi.ts (4621 bytes)
2025-05-24 01:48:31.432 [info] 'ToolFileUtils' Reading file: client/src/api/customizationApi.ts
2025-05-24 01:48:31.432 [info] 'ToolFileUtils' Successfully read file: client/src/api/customizationApi.ts (4649 bytes)
2025-05-24 01:48:42.553 [info] 'ToolFileUtils' Reading file: client/src/api/customizationApi.ts
2025-05-24 01:48:42.553 [info] 'ToolFileUtils' Successfully read file: client/src/api/customizationApi.ts (4649 bytes)
2025-05-24 01:48:44.763 [info] 'ToolFileUtils' Reading file: client/src/api/customizationApi.ts
2025-05-24 01:48:44.763 [info] 'ToolFileUtils' Successfully read file: client/src/api/customizationApi.ts (4683 bytes)
2025-05-24 01:49:01.689 [info] 'ToolFileUtils' Reading file: client/src/api/customizationApi.ts
2025-05-24 01:49:01.689 [info] 'ToolFileUtils' Successfully read file: client/src/api/customizationApi.ts (4683 bytes)
2025-05-24 01:49:04.138 [info] 'ToolFileUtils' Reading file: client/src/api/customizationApi.ts
2025-05-24 01:49:04.139 [info] 'ToolFileUtils' Successfully read file: client/src/api/customizationApi.ts (4745 bytes)
2025-05-24 01:49:19.349 [info] 'ToolFileUtils' Reading file: client/src/api/customizationApi.ts
2025-05-24 01:49:19.349 [info] 'ToolFileUtils' Successfully read file: client/src/api/customizationApi.ts (4745 bytes)
2025-05-24 01:49:21.637 [info] 'ToolFileUtils' Reading file: client/src/api/customizationApi.ts
2025-05-24 01:49:21.637 [info] 'ToolFileUtils' Successfully read file: client/src/api/customizationApi.ts (4801 bytes)
2025-05-24 01:49:40.417 [info] 'ToolFileUtils' Reading file: client/src/api/customizationApi.ts
2025-05-24 01:49:40.418 [info] 'ToolFileUtils' Successfully read file: client/src/api/customizationApi.ts (4801 bytes)
2025-05-24 01:49:42.849 [info] 'ToolFileUtils' Reading file: client/src/api/customizationApi.ts
2025-05-24 01:49:42.849 [info] 'ToolFileUtils' Successfully read file: client/src/api/customizationApi.ts (4891 bytes)
2025-05-24 02:08:13.568 [info] 'AugmentExtension' Retrieving model config
2025-05-24 02:08:13.868 [info] 'AugmentExtension' Retrieved model config
2025-05-24 02:08:13.868 [info] 'AugmentExtension' Returning model config
2025-05-24 02:38:13.587 [info] 'AugmentExtension' Retrieving model config
2025-05-24 02:38:13.909 [info] 'AugmentExtension' Retrieved model config
2025-05-24 02:38:13.909 [info] 'AugmentExtension' Returning model config
2025-05-24 03:08:13.588 [info] 'AugmentExtension' Retrieving model config
2025-05-24 03:08:13.936 [info] 'AugmentExtension' Retrieved model config
2025-05-24 03:08:13.936 [info] 'AugmentExtension' Returning model config
2025-05-24 03:14:24.126 [error] 'AugmentExtension' API request 825ba1ee-0dc6-4262-94a9-f443d5803cd5 to https://i1.api.augmentcode.com/find-missing response 503: Service Unavailable
2025-05-24 03:14:24.445 [info] 'DiskFileManager[workspace]' Operation failed with error Error: HTTP error: 503 Service Unavailable, retrying in 100 ms; retries = 0
2025-05-24 03:14:25.033 [error] 'AugmentExtension' API request 8734c12c-a213-4427-915f-916f4752c0cb to https://i1.api.augmentcode.com/find-missing response 503: Service Unavailable
2025-05-24 03:14:25.318 [info] 'DiskFileManager[workspace]' Operation failed with error Error: HTTP error: 503 Service Unavailable, retrying in 200 ms; retries = 1
2025-05-24 03:14:26.007 [info] 'DiskFileManager[workspace]' Operation succeeded after 2 transient failures
