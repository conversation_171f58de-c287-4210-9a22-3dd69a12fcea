{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "package.json"}, "originalCode": "{\n  \"name\": \"rest-express\",\n  \"version\": \"1.0.0\",\n  \"type\": \"module\",\n  \"license\": \"MIT\",\n  \"scripts\": {\n    \"dev\": \"NODE_ENV=development tsx server/index.ts\",\n    \"build\": \"vite build && esbuild server/index.ts --platform=node --packages=external --bundle --format=esm --outdir=dist\",\n    \"start\": \"NODE_ENV=production node dist/index.js\",\n    \"check\": \"tsc\",\n    \"db:push\": \"drizzle-kit push\",\n    \"db:generate\": \"drizzle-kit generate\",\n    \"db:migrate\": \"tsx server/migrate.ts\",\n    \"db:init\": \"tsx server/init-db.ts\",\n    \"db:studio\": \"drizzle-kit studio\",\n    \"db:test\": \"tsx server/test-db.ts\",\n    \"db:create-tables\": \"tsx server/create-tables.ts\",\n    \"db:create-missing\": \"tsx server/create-missing-tables.ts\",\n    \"db:seed-customizations\": \"tsx server/seed-customizations.ts\"\n  },\n  \"dependencies\": {\n    \"@hookform/resolvers\": \"^3.10.0\",\n    \"@jridgewell/trace-mapping\": \"^0.3.25\",\n    \"@neondatabase/serverless\": \"^0.10.4\",\n    \"@radix-ui/react-accordion\": \"^1.2.4\",\n    \"@radix-ui/react-alert-dialog\": \"^1.1.7\",\n    \"@radix-ui/react-aspect-ratio\": \"^1.1.3\",\n    \"@radix-ui/react-avatar\": \"^1.1.4\",\n    \"@radix-ui/react-checkbox\": \"^1.1.5\",\n    \"@radix-ui/react-collapsible\": \"^1.1.4\",\n    \"@radix-ui/react-context-menu\": \"^2.2.7\",\n    \"@radix-ui/react-dialog\": \"^1.1.7\",\n    \"@radix-ui/react-dropdown-menu\": \"^2.1.7\",\n    \"@radix-ui/react-hover-card\": \"^1.1.7\",\n    \"@radix-ui/react-label\": \"^2.1.3\",\n    \"@radix-ui/react-menubar\": \"^1.1.7\",\n    \"@radix-ui/react-navigation-menu\": \"^1.2.6\",\n    \"@radix-ui/react-popover\": \"^1.1.7\",\n    \"@radix-ui/react-progress\": \"^1.1.3\",\n    \"@radix-ui/react-radio-group\": \"^1.2.4\",\n    \"@radix-ui/react-scroll-area\": \"^1.2.4\",\n    \"@radix-ui/react-select\": \"^2.1.7\",\n    \"@radix-ui/react-separator\": \"^1.1.3\",\n    \"@radix-ui/react-slider\": \"^1.2.4\",\n    \"@radix-ui/react-slot\": \"^1.2.0\",\n    \"@radix-ui/react-switch\": \"^1.1.4\",\n    \"@radix-ui/react-tabs\": \"^1.1.4\",\n    \"@radix-ui/react-toast\": \"^1.2.7\",\n    \"@radix-ui/react-toggle\": \"^1.1.3\",\n    \"@radix-ui/react-toggle-group\": \"^1.1.3\",\n    \"@radix-ui/react-tooltip\": \"^1.2.0\",\n    \"@tanstack/react-query\": \"^5.76.1\",\n    \"bcrypt\": \"^6.0.0\",\n    \"class-variance-authority\": \"^0.7.1\",\n    \"clsx\": \"^2.1.1\",\n    \"cmdk\": \"^1.1.1\",\n    \"connect-pg-simple\": \"^10.0.0\",\n    \"cors\": \"^2.8.5\",\n    \"date-fns\": \"^3.6.0\",\n    \"dotenv\": \"^16.5.0\",\n    \"drizzle-orm\": \"^0.39.1\",\n    \"drizzle-zod\": \"^0.7.0\",\n    \"embla-carousel-react\": \"^8.6.0\",\n    \"express\": \"^4.21.2\",\n    \"express-session\": \"^1.18.1\",\n    \"framer-motion\": \"^11.13.1\",\n    \"input-otp\": \"^1.4.2\",\n    \"lottie-react\": \"^2.4.1\",\n    \"lucide-react\": \"^0.453.0\",\n    \"memorystore\": \"^1.6.7\",\n    \"next-themes\": \"^0.4.6\",\n    \"passport\": \"^0.7.0\",\n    \"passport-local\": \"^1.0.0\",\n    \"pg\": \"^8.16.0\",\n    \"pg-promise\": \"^11.13.0\",\n    \"react\": \"^18.3.1\",\n    \"react-day-picker\": \"^8.10.1\",\n    \"react-dom\": \"^18.3.1\",\n    \"react-hook-form\": \"^7.56.4\",\n    \"react-icons\": \"^5.5.0\",\n    \"react-intersection-observer\": \"^9.16.0\",\n    \"react-resizable-panels\": \"^2.1.7\",\n    \"recharts\": \"^2.15.2\",\n    \"swiper\": \"^11.2.7\",\n    \"tailwind-merge\": \"^2.6.0\",\n    \"tailwindcss-animate\": \"^1.0.7\",\n    \"tw-animate-css\": \"^1.2.5\",\n    \"vaul\": \"^1.1.2\",\n    \"wouter\": \"^3.7.0\",\n    \"ws\": \"^8.18.0\",\n    \"zod\": \"^3.25.20\",\n    \"zod-validation-error\": \"^3.4.0\"\n  },\n  \"devDependencies\": {\n    \"@replit/vite-plugin-cartographer\": \"^0.2.4\",\n    \"@replit/vite-plugin-runtime-error-modal\": \"^0.0.3\",\n    \"@tailwindcss/typography\": \"^0.5.15\",\n    \"@tailwindcss/vite\": \"^4.1.3\",\n    \"@types/bcrypt\": \"^5.0.2\",\n    \"@types/connect-pg-simple\": \"^7.0.3\",\n    \"@types/express\": \"4.17.21\",\n    \"@types/express-session\": \"^1.18.0\",\n    \"@types/node\": \"20.16.11\",\n    \"@types/passport\": \"^1.0.16\",\n    \"@types/passport-local\": \"^1.0.38\",\n    \"@types/react\": \"^18.3.11\",\n    \"@types/react-dom\": \"^18.3.1\",\n    \"@types/ws\": \"^8.5.13\",\n    \"@vitejs/plugin-react\": \"^4.3.2\",\n    \"autoprefixer\": \"^10.4.20\",\n    \"drizzle-kit\": \"^0.30.4\",\n    \"esbuild\": \"^0.25.0\",\n    \"postcss\": \"^8.4.47\",\n    \"tailwindcss\": \"^3.4.17\",\n    \"tsx\": \"^4.19.1\",\n    \"typescript\": \"5.6.3\",\n    \"vite\": \"^5.4.14\"\n  },\n  \"optionalDependencies\": {\n    \"bufferutil\": \"^4.0.8\"\n  }\n}\n", "modifiedCode": "{\n  \"name\": \"rest-express\",\n  \"version\": \"1.0.0\",\n  \"type\": \"module\",\n  \"license\": \"MIT\",\n  \"scripts\": {\n    \"dev\": \"NODE_ENV=development tsx server/index.ts\",\n    \"build\": \"vite build && esbuild server/index.ts --platform=node --packages=external --bundle --format=esm --outdir=dist\",\n    \"start\": \"NODE_ENV=production node dist/index.js\",\n    \"check\": \"tsc\",\n    \"db:push\": \"drizzle-kit push\",\n    \"db:generate\": \"drizzle-kit generate\",\n    \"db:migrate\": \"tsx server/migrate.ts\",\n    \"db:init\": \"tsx server/init-db.ts\",\n    \"db:studio\": \"drizzle-kit studio\",\n    \"db:test\": \"tsx server/test-db.ts\",\n    \"db:create-tables\": \"tsx server/create-tables.ts\",\n    \"db:create-missing\": \"tsx server/create-missing-tables.ts\",\n    \"db:seed-customizations\": \"tsx server/seed-customizations.ts\"\n  },\n  \"dependencies\": {\n    \"@hookform/resolvers\": \"^3.10.0\",\n    \"@jridgewell/trace-mapping\": \"^0.3.25\",\n    \"@neondatabase/serverless\": \"^0.10.4\",\n    \"@radix-ui/react-accordion\": \"^1.2.4\",\n    \"@radix-ui/react-alert-dialog\": \"^1.1.7\",\n    \"@radix-ui/react-aspect-ratio\": \"^1.1.3\",\n    \"@radix-ui/react-avatar\": \"^1.1.4\",\n    \"@radix-ui/react-checkbox\": \"^1.1.5\",\n    \"@radix-ui/react-collapsible\": \"^1.1.4\",\n    \"@radix-ui/react-context-menu\": \"^2.2.7\",\n    \"@radix-ui/react-dialog\": \"^1.1.7\",\n    \"@radix-ui/react-dropdown-menu\": \"^2.1.7\",\n    \"@radix-ui/react-hover-card\": \"^1.1.7\",\n    \"@radix-ui/react-label\": \"^2.1.3\",\n    \"@radix-ui/react-menubar\": \"^1.1.7\",\n    \"@radix-ui/react-navigation-menu\": \"^1.2.6\",\n    \"@radix-ui/react-popover\": \"^1.1.7\",\n    \"@radix-ui/react-progress\": \"^1.1.3\",\n    \"@radix-ui/react-radio-group\": \"^1.2.4\",\n    \"@radix-ui/react-scroll-area\": \"^1.2.4\",\n    \"@radix-ui/react-select\": \"^2.1.7\",\n    \"@radix-ui/react-separator\": \"^1.1.3\",\n    \"@radix-ui/react-slider\": \"^1.2.4\",\n    \"@radix-ui/react-slot\": \"^1.2.0\",\n    \"@radix-ui/react-switch\": \"^1.1.4\",\n    \"@radix-ui/react-tabs\": \"^1.1.4\",\n    \"@radix-ui/react-toast\": \"^1.2.7\",\n    \"@radix-ui/react-toggle\": \"^1.1.3\",\n    \"@radix-ui/react-toggle-group\": \"^1.1.3\",\n    \"@radix-ui/react-tooltip\": \"^1.2.0\",\n    \"@tanstack/react-query\": \"^5.76.1\",\n    \"bcrypt\": \"^6.0.0\",\n    \"class-variance-authority\": \"^0.7.1\",\n    \"clsx\": \"^2.1.1\",\n    \"cmdk\": \"^1.1.1\",\n    \"connect-pg-simple\": \"^10.0.0\",\n    \"cors\": \"^2.8.5\",\n    \"date-fns\": \"^3.6.0\",\n    \"dotenv\": \"^16.5.0\",\n    \"drizzle-orm\": \"^0.39.1\",\n    \"drizzle-zod\": \"^0.7.0\",\n    \"embla-carousel-react\": \"^8.6.0\",\n    \"express\": \"^4.21.2\",\n    \"express-session\": \"^1.18.1\",\n    \"framer-motion\": \"^11.13.1\",\n    \"input-otp\": \"^1.4.2\",\n    \"lottie-react\": \"^2.4.1\",\n    \"lucide-react\": \"^0.453.0\",\n    \"memorystore\": \"^1.6.7\",\n    \"next-themes\": \"^0.4.6\",\n    \"passport\": \"^0.7.0\",\n    \"passport-local\": \"^1.0.0\",\n    \"pg\": \"^8.16.0\",\n    \"pg-promise\": \"^11.13.0\",\n    \"react\": \"^18.3.1\",\n    \"react-day-picker\": \"^8.10.1\",\n    \"react-dom\": \"^18.3.1\",\n    \"react-hook-form\": \"^7.56.4\",\n    \"react-icons\": \"^5.5.0\",\n    \"react-intersection-observer\": \"^9.16.0\",\n    \"react-resizable-panels\": \"^2.1.7\",\n    \"recharts\": \"^2.15.2\",\n    \"swiper\": \"^11.2.7\",\n    \"tailwind-merge\": \"^2.6.0\",\n    \"tailwindcss-animate\": \"^1.0.7\",\n    \"tw-animate-css\": \"^1.2.5\",\n    \"vaul\": \"^1.1.2\",\n    \"wouter\": \"^3.7.0\",\n    \"ws\": \"^8.18.0\",\n    \"zod\": \"^3.25.20\",\n    \"zod-validation-error\": \"^3.4.0\"\n  },\n  \"devDependencies\": {\n    \"@replit/vite-plugin-cartographer\": \"^0.2.4\",\n    \"@replit/vite-plugin-runtime-error-modal\": \"^0.0.3\",\n    \"@tailwindcss/typography\": \"^0.5.15\",\n    \"@tailwindcss/vite\": \"^4.1.3\",\n    \"@types/bcrypt\": \"^5.0.2\",\n    \"@types/connect-pg-simple\": \"^7.0.3\",\n    \"@types/express\": \"4.17.21\",\n    \"@types/express-session\": \"^1.18.0\",\n    \"@types/node\": \"20.16.11\",\n    \"@types/passport\": \"^1.0.16\",\n    \"@types/passport-local\": \"^1.0.38\",\n    \"@types/react\": \"^18.3.11\",\n    \"@types/react-dom\": \"^18.3.1\",\n    \"@types/ws\": \"^8.5.13\",\n    \"@vitejs/plugin-react\": \"^4.3.2\",\n    \"autoprefixer\": \"^10.4.20\",\n    \"drizzle-kit\": \"^0.30.4\",\n    \"esbuild\": \"^0.25.0\",\n    \"postcss\": \"^8.4.47\",\n    \"tailwindcss\": \"^3.4.17\",\n    \"tsx\": \"^4.19.1\",\n    \"typescript\": \"5.6.3\",\n    \"vite\": \"^5.4.14\"\n  },\n  \"optionalDependencies\": {\n    \"bufferutil\": \"^4.0.8\"\n  }\n}\n"}