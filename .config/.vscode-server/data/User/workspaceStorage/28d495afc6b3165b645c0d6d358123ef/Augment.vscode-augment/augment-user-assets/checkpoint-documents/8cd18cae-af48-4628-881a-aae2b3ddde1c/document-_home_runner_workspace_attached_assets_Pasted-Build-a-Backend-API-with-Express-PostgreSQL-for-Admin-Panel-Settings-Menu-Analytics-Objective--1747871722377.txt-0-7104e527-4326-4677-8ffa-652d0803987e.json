{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "attached_assets/Pasted-Build-a-Backend-API-with-Express-PostgreSQL-for-Admin-Panel-Settings-Menu-Analytics-Objective--1747871722377.txt"}, "originalCode": "Build a Backend API with Express + PostgreSQL for Admin Panel (Settings, Menu, Analytics)\nObjective:\n\nDevelop a robust RESTful API using Node.js + Express + PostgreSQL to serve the admin panel functionalities of Barbecuez Restaurant. This includes:\n\nRestaurant availability + business settings\n\nMenu (categories + items) management\n\nFinancial analytics (revenue, earnings breakdown)\n\nNo live order tracking is required\n\n🗂️ Project Structure (Suggested)\nbash\nCopy\nEdit\n/backend\n  /routes\n    adminSettings.js\n    categories.js\n    menuItems.js\n    analytics.js\n  /controllers\n    adminController.js\n    categoryController.js\n    itemController.js\n    analyticsController.js\n  /models\n    db.js (PostgreSQL connection)\n    queries.js\n  /data\n    seed.sql\n  .env\n  server.js\n🔌 API Endpoints Specification\n🔧 Admin Settings\nbash\nCopy\nEdit\nGET /api/admin/settings\n→ Return current settings:\n{\n  \"restaurantOpen\": true,\n  \"businessHours\": {\n    \"mon\": { \"open\": \"10:00\", \"close\": \"22:00\", \"delivery\": true },\n    ...\n  },\n  \"deliveryFee\": 49,\n  \"estimatedTime\": \"25–35 min\"\n}\n\nPUT /api/admin/settings\n→ Update business hours, deliveryFee, estimatedTime, and open/close state.\nPayload:\n{\n  \"restaurantOpen\": false,\n  \"businessHours\": { ... },\n  \"deliveryFee\": 49,\n  \"estimatedTime\": \"25–35 min\"\n}\n🧾 Categories\nbash\nCopy\nEdit\nGET /api/categories\n→ List all categories\n\nPOST /api/categories\n→ Add new category:\n{\n  \"name\": \"BurgerZ\",\n  \"imageUrl\": \"/uploads/burger.jpg\"\n}\n\nDELETE /api/categories/:id\n→ Delete a category\n🍔 Menu Items\nbash\nCopy\nEdit\nGET /api/items\n→ Return all items with category joined\n\nPOST /api/items\n→ Add new menu item:\n{\n  \"name\": \"BBQ Burger\",\n  \"description\": \"...\",\n  \"price\": 189,\n  \"categoryId\": 1,\n  \"imageUrl\": \"/uploads/bbq-burger.jpg\"\n}\n\nPUT /api/items/:id\n→ Edit item details\n\nDELETE /api/items/:id\n→ Remove menu item\n📊 Analytics\ncss\nCopy\nEdit\nGET /api/analytics/summary\n→ Return:\n{\n  \"today\": 1450,\n  \"week\": 10120,\n  \"month\": 39210,\n  \"orderCount\": 52\n}\n\nGET /api/analytics/daily\n→ Return revenue per day:\n[\n  { \"date\": \"2025-05-17\", \"total\": 850 },\n  { \"date\": \"2025-05-18\", \"total\": 920 },\n  ...\n]\n\nGET /api/analytics/by-category\n→ Return revenue by category:\n[\n  { \"category\": \"BurgerZ\", \"total\": 12200 },\n  ...\n]\n🧱 PostgreSQL Schema\nTable: restaurant_settings\nsql\nCopy\nEdit\nCREATE TABLE restaurant_settings (\n  id SERIAL PRIMARY KEY,\n  restaurant_open BOOLEAN DEFAULT TRUE,\n  business_hours JSONB,\n  delivery_fee INTEGER,\n  estimated_time TEXT\n);\nTable: categories\nsql\nCopy\nEdit\nCREATE TABLE categories (\n  id SERIAL PRIMARY KEY,\n  name TEXT NOT NULL,\n  image_url TEXT\n);\nTable: menu_items\nsql\nCopy\nEdit\nCREATE TABLE menu_items (\n  id SERIAL PRIMARY KEY,\n  name TEXT,\n  description TEXT,\n  price INTEGER,\n  image_url TEXT,\n  category_id INTEGER REFERENCES categories(id)\n);\nTable: orders (for revenue analytics)\nsql\nCopy\nEdit\nCREATE TABLE orders (\n  id SERIAL PRIMARY KEY,\n  total INTEGER,\n  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP\n);\nTable: order_items (for item-level insights – optional)\nsql\nCopy\nEdit\nCREATE TABLE order_items (\n  id SERIAL PRIMARY KEY,\n  order_id INTEGER REFERENCES orders(id),\n  item_id INTEGER REFERENCES menu_items(id),\n  quantity INTEGER\n);\n🔐 Optional Enhancements\nMiddleware: Admin token auth (JWT or session-based)\n\nUpload support for images via Multer (/uploads)\n\nEnvironment variables for DB connection via .env\n\n✅ Output\nFully integrated, scalable backend API ready for:\n\nAdmin Dashboard control\n\nMenu management with image support\n\nLive business hours & availability logic\n\nRevenue and sales data aggregation\n\nSupports frontend plug-and-play with Axios", "modifiedCode": "Build a Backend API with Express + PostgreSQL for Admin Panel (Settings, Menu, Analytics)\nObjective:\n\nDevelop a robust RESTful API using Node.js + Express + PostgreSQL to serve the admin panel functionalities of Barbecuez Restaurant. This includes:\n\nRestaurant availability + business settings\n\nMenu (categories + items) management\n\nFinancial analytics (revenue, earnings breakdown)\n\nNo live order tracking is required\n\n🗂️ Project Structure (Suggested)\nbash\nCopy\nEdit\n/backend\n  /routes\n    adminSettings.js\n    categories.js\n    menuItems.js\n    analytics.js\n  /controllers\n    adminController.js\n    categoryController.js\n    itemController.js\n    analyticsController.js\n  /models\n    db.js (PostgreSQL connection)\n    queries.js\n  /data\n    seed.sql\n  .env\n  server.js\n🔌 API Endpoints Specification\n🔧 Admin Settings\nbash\nCopy\nEdit\nGET /api/admin/settings\n→ Return current settings:\n{\n  \"restaurantOpen\": true,\n  \"businessHours\": {\n    \"mon\": { \"open\": \"10:00\", \"close\": \"22:00\", \"delivery\": true },\n    ...\n  },\n  \"deliveryFee\": 49,\n  \"estimatedTime\": \"25–35 min\"\n}\n\nPUT /api/admin/settings\n→ Update business hours, deliveryFee, estimatedTime, and open/close state.\nPayload:\n{\n  \"restaurantOpen\": false,\n  \"businessHours\": { ... },\n  \"deliveryFee\": 49,\n  \"estimatedTime\": \"25–35 min\"\n}\n🧾 Categories\nbash\nCopy\nEdit\nGET /api/categories\n→ List all categories\n\nPOST /api/categories\n→ Add new category:\n{\n  \"name\": \"BurgerZ\",\n  \"imageUrl\": \"/uploads/burger.jpg\"\n}\n\nDELETE /api/categories/:id\n→ Delete a category\n🍔 Menu Items\nbash\nCopy\nEdit\nGET /api/items\n→ Return all items with category joined\n\nPOST /api/items\n→ Add new menu item:\n{\n  \"name\": \"BBQ Burger\",\n  \"description\": \"...\",\n  \"price\": 189,\n  \"categoryId\": 1,\n  \"imageUrl\": \"/uploads/bbq-burger.jpg\"\n}\n\nPUT /api/items/:id\n→ Edit item details\n\nDELETE /api/items/:id\n→ Remove menu item\n📊 Analytics\ncss\nCopy\nEdit\nGET /api/analytics/summary\n→ Return:\n{\n  \"today\": 1450,\n  \"week\": 10120,\n  \"month\": 39210,\n  \"orderCount\": 52\n}\n\nGET /api/analytics/daily\n→ Return revenue per day:\n[\n  { \"date\": \"2025-05-17\", \"total\": 850 },\n  { \"date\": \"2025-05-18\", \"total\": 920 },\n  ...\n]\n\nGET /api/analytics/by-category\n→ Return revenue by category:\n[\n  { \"category\": \"BurgerZ\", \"total\": 12200 },\n  ...\n]\n🧱 PostgreSQL Schema\nTable: restaurant_settings\nsql\nCopy\nEdit\nCREATE TABLE restaurant_settings (\n  id SERIAL PRIMARY KEY,\n  restaurant_open BOOLEAN DEFAULT TRUE,\n  business_hours JSONB,\n  delivery_fee INTEGER,\n  estimated_time TEXT\n);\nTable: categories\nsql\nCopy\nEdit\nCREATE TABLE categories (\n  id SERIAL PRIMARY KEY,\n  name TEXT NOT NULL,\n  image_url TEXT\n);\nTable: menu_items\nsql\nCopy\nEdit\nCREATE TABLE menu_items (\n  id SERIAL PRIMARY KEY,\n  name TEXT,\n  description TEXT,\n  price INTEGER,\n  image_url TEXT,\n  category_id INTEGER REFERENCES categories(id)\n);\nTable: orders (for revenue analytics)\nsql\nCopy\nEdit\nCREATE TABLE orders (\n  id SERIAL PRIMARY KEY,\n  total INTEGER,\n  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP\n);\nTable: order_items (for item-level insights – optional)\nsql\nCopy\nEdit\nCREATE TABLE order_items (\n  id SERIAL PRIMARY KEY,\n  order_id INTEGER REFERENCES orders(id),\n  item_id INTEGER REFERENCES menu_items(id),\n  quantity INTEGER\n);\n🔐 Optional Enhancements\nMiddleware: Admin token auth (JWT or session-based)\n\nUpload support for images via Multer (/uploads)\n\nEnvironment variables for DB connection via .env\n\n✅ Output\nFully integrated, scalable backend API ready for:\n\nAdmin Dashboard control\n\nMenu management with image support\n\nLive business hours & availability logic\n\nRevenue and sales data aggregation\n\nSupports frontend plug-and-play with Axios"}