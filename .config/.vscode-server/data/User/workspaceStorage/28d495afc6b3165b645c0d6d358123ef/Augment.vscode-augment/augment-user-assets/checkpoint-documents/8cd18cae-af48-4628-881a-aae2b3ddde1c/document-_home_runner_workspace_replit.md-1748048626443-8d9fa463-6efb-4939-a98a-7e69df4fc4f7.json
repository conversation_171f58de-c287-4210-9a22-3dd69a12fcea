{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "replit.md"}, "originalCode": "# Barbecuez Restaurant Web Application\n\n## Overview\n\nThis is a modern web application for \"Barbecuez Restaurant\", a premium barbecue restaurant. The application features a responsive design with neon-themed UI elements, a comprehensive menu system, cart functionality, checkout process, and contact form. The application is built with a React frontend and an Express backend, using Drizzle ORM for database operations.\n\n## User Preferences\n\nPreferred communication style: Simple, everyday language.\n\n## System Architecture\n\n### Frontend Architecture\n\n- **Technology**: React with TypeScript\n- **State Management**:\n  - React Context API for cart state management\n  - React Query for server state\n- **Routing**: Wouter for lightweight client-side routing\n- **Styling**:\n  - Tailwind CSS for utility-first styling\n  - Custom neon-themed design system\n  - Shadcn UI components (based on Radix UI primitives)\n- **Form Handling**: React Hook Form with Zod validation\n\nThe frontend is organized into pages, components, and UI elements. The application uses a context provider for cart management, allowing items to be added, removed, and quantities to be updated across the application.\n\n### Backend Architecture\n\n- **Technology**: Express.js with TypeScript\n- **API Layer**: RESTful API endpoints for dishes, orders, and contact messages\n- **Database Access**: Drizzle ORM for type-safe database operations\n- **Storage Implementation**: \n  - Memory-based storage for development\n  - Database connectivity for production\n\nThe backend serves both the API and the static assets. All API routes are prefixed with `/api` and include logging for debugging purposes. The server uses middleware to parse JSON and URL-encoded requests.\n\n### Data Storage\n\n- **ORM**: Drizzle ORM\n- **Schema**: Defined in `shared/schema.ts` for dishes, orders, and contact messages\n- **Database**: PostgreSQL (configured but not fully implemented yet)\n\n## Key Components\n\n### Frontend Components\n\n1. **Pages**:\n   - Home: Landing page with hero section\n   - Menu: Displays available dishes by category\n   - Cart: Shows selected items with quantity controls\n   - Checkout: Order form with customer and payment information\n   - Contact: Contact form for customer inquiries\n\n2. **UI Components**:\n   - DishCard: Displays individual dish with image, price, and description\n   - Header: Navigation and cart summary\n   - Footer: Restaurant information and links\n   - Button: Custom styled button component with multiple variants\n   - Loader: Loading indicator for asynchronous operations\n\n3. **Context Providers**:\n   - CartProvider: Manages cart state and operations\n\n### Backend Components\n\n1. **API Routes**:\n   - `/api/dishes`: Get all dishes or a specific dish\n   - `/api/categories`: Get available dish categories\n   - `/api/orders`: Create and manage orders\n   - `/api/contact`: Submit contact messages\n\n2. **Storage**:\n   - `IStorage` interface defining CRUD operations\n   - `MemStorage` implementation for development\n\n## Data Flow\n\n### Adding Items to Cart\n\n1. User browses the menu page\n2. User clicks \"Add to Cart\" on a dish\n3. Frontend calls `addToCart` function from CartContext\n4. Item is added to cart state and persisted to localStorage\n5. Cart count updates in the header\n\n### Checkout Process\n\n1. User reviews items in cart page\n2. User proceeds to checkout\n3. User completes the form with personal and payment information\n4. Frontend validates the form using Zod schemas\n5. On submission, frontend calls `/api/orders` endpoint\n6. Backend creates the order in the database\n7. User is shown a confirmation\n\n### Contact Form Submission\n\n1. User fills out the contact form\n2. Frontend validates input using Zod schema\n3. Frontend calls `/api/contact` endpoint\n4. Backend stores the message in the database\n5. User receives confirmation of submission\n\n## External Dependencies\n\n### Frontend Dependencies\n\n- **UI Libraries**:\n  - Radix UI (various components)\n  - Tailwind CSS\n  - Shadcn UI component system\n- **State Management**:\n  - @tanstack/react-query for server state\n- **Form Handling**:\n  - react-hook-form\n  - @hookform/resolvers/zod for validation\n- **Routing**:\n  - wouter for lightweight routing\n\n### Backend Dependencies\n\n- **Web Framework**:\n  - express\n- **Database**:\n  - @neondatabase/serverless for database connectivity\n  - drizzle-orm for ORM\n  - drizzle-zod for schema validation\n- **Development**:\n  - tsx for TypeScript execution\n  - vite for frontend bundling\n\n## Deployment Strategy\n\nThe application is configured for deployment on Replit with the following considerations:\n\n1. **Build Process**:\n   - Frontend: Vite builds static assets to `dist/public`\n   - Backend: ESBuild bundles server code to `dist/index.js`\n\n2. **Runtime**:\n   - Production: `node dist/index.js`\n   - Development: `tsx server/index.ts`\n\n3. **Database**:\n   - Uses the DATABASE_URL environment variable for connection\n   - Drizzle ORM for database operations\n   - Migrations managed through drizzle-kit\n\n4. **Environment Variables**:\n   - DATABASE_URL: Database connection string\n   - NODE_ENV: Environment mode (development/production)\n\nThe application is designed to run as a full-stack application where the backend serves both the API and the static frontend assets.\n\n## Database Schema\n\n1. **Dishes Table**:\n   - id: Serial primary key\n   - name: Text\n   - description: Text\n   - price: Integer (in NOK)\n   - imageUrl: Text\n   - category: Text\n   - available: Boolean\n   - rating: Integer\n   - reviews: Integer\n\n2. **Orders Table**:\n   - id: Serial primary key\n   - customer: JSON\n   - items: JSON\n   - subtotal: Integer\n   - deliveryFee: Integer\n   - total: Integer\n   - status: Text\n   - paymentMethod: Text\n   - notes: Text\n   - createdAt: Timestamp\n\n3. **Contact Messages Table**:\n   - id: Serial primary key\n   - name: Text\n   - email: Text\n   - subject: Text\n   - message: Text\n   - createdAt: Timestamp"}