{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/pages/admin/AdminLayout.tsx"}, "originalCode": "import { ReactNode, useState } from \"react\";\nimport { useLocation, Link } from \"wouter\";\nimport { motion } from \"framer-motion\";\nimport { useRestaurantStatus } from \"@/context/RestaurantStatusContext\";\n\n// Icons\nimport {\n  Settings, Menu, BarChart3, ChevronRight, LogOut,\n  Home, X, Menu as MenuIcon, ShoppingBag\n} from \"lucide-react\";\n\ninterface AdminLayoutProps {\n  children: ReactNode;\n}\n\nconst AdminLayout = ({ children }: AdminLayoutProps) => {\n  const [location] = useLocation();\n  const [isSidebarOpen, setIsSidebarOpen] = useState(true);\n  const [isMobileSidebarOpen, setIsMobileSidebarOpen] = useState(false);\n  const { isOpen, isLoading } = useRestaurantStatus();\n\n  const navItems = [\n    {\n      label: \"Settings\",\n      icon: <Settings className=\"w-5 h-5\" />,\n      href: \"/admin/settings\",\n      isActive: location === \"/admin/settings\"\n    },\n    {\n      label: \"Menu Management\",\n      icon: <Menu className=\"w-5 h-5\" />,\n      href: \"/admin/menu\",\n      isActive: location === \"/admin/menu\"\n    },\n    {\n      label: \"Order Manager\",\n      icon: <ShoppingBag className=\"w-5 h-5\" />,\n      href: \"/admin/orders\",\n      isActive: location === \"/admin/orders\"\n    },\n    {\n      label: \"Analytics\",\n      icon: <BarChart3 className=\"w-5 h-5\" />,\n      href: \"/admin/analytics\",\n      isActive: location === \"/admin/analytics\"\n    }\n  ];\n\n  return (\n    <div className=\"min-h-screen bg-black text-white flex\">\n      {/* Sidebar - Desktop */}\n      <motion.aside\n        className={`bg-gray-900/80 border-r border-gray-800 h-screen hidden md:flex flex-col fixed transition-all\n                    backdrop-blur-md z-20 ${isSidebarOpen ? 'w-64' : 'w-20'}`}\n        initial={{ x: -20, opacity: 0 }}\n        animate={{ x: 0, opacity: 1 }}\n        transition={{ duration: 0.3 }}\n      >\n        <div className=\"p-4 border-b border-gray-800 flex items-center justify-between\">\n          <div className={`flex items-center ${!isSidebarOpen && 'justify-center w-full'}`}>\n            {isSidebarOpen ? (\n              <h1 className=\"text-xl font-bold bg-gradient-to-r from-purple-400 via-cyan-400 to-purple-400 text-transparent bg-clip-text\">\n                Barbecuez Admin\n              </h1>\n            ) : (\n              <span className=\"text-xl font-bold text-cyan-400\">B</span>\n            )}\n          </div>\n\n          <button\n            onClick={() => setIsSidebarOpen(!isSidebarOpen)}\n            className=\"text-gray-400 hover:text-white transition-colors\"\n          >\n            {isSidebarOpen ? <ChevronLeft className=\"w-5 h-5\" /> : <ChevronRight className=\"w-5 h-5\" />}\n          </button>\n        </div>\n\n        <nav className=\"flex-1 overflow-y-auto py-4\">\n          <ul className=\"space-y-1 px-2\">\n            {navItems.map((item) => (\n              <li key={item.href}>\n                <Link href={item.href}>\n                  <div\n                    className={`flex items-center px-3 py-3 rounded-lg transition-all cursor-pointer\n                             ${item.isActive\n                                ? 'bg-cyan-900/40 text-cyan-300 border border-cyan-500/20 shadow-[0_0_10px_rgba(0,255,255,0.15)]'\n                                : 'text-gray-400 hover:text-white hover:bg-gray-800/50'}`}>\n                    <span className=\"flex-shrink-0\">{item.icon}</span>\n                    {isSidebarOpen && <span className=\"ml-3\">{item.label}</span>}\n                  </div>\n                </Link>\n              </li>\n            ))}\n          </ul>\n        </nav>\n\n        <div className=\"border-t border-gray-800 p-4\">\n          <Link href=\"/\">\n            <div className=\"flex items-center px-3 py-2 text-gray-400 hover:text-white hover:bg-gray-800/50 rounded-lg cursor-pointer\">\n              <Home className=\"w-5 h-5\" />\n              {isSidebarOpen && <span className=\"ml-3\">Back to Website</span>}\n            </div>\n          </Link>\n\n          <Link href=\"/logout\">\n            <div className=\"flex items-center px-3 py-2 text-gray-400 hover:text-white hover:bg-red-900/30 rounded-lg mt-2 cursor-pointer\">\n              <LogOut className=\"w-5 h-5 text-red-400\" />\n              {isSidebarOpen && <span className=\"ml-3\">Logout</span>}\n            </div>\n          </Link>\n        </div>\n      </motion.aside>\n\n      {/* Mobile Sidebar Toggle */}\n      <div className=\"fixed top-4 left-4 md:hidden z-30\">\n        <button\n          onClick={() => setIsMobileSidebarOpen(true)}\n          className=\"bg-gray-900/90 text-white p-2 rounded-lg border border-gray-700\"\n        >\n          <MenuIcon className=\"w-6 h-6\" />\n        </button>\n      </div>\n\n      {/* Mobile Sidebar */}\n      {isMobileSidebarOpen && (\n        <div\n          className=\"fixed inset-0 bg-black/70 backdrop-blur-sm z-40 md:hidden\"\n          onClick={() => setIsMobileSidebarOpen(false)}\n        >\n          <motion.div\n            className=\"bg-gray-900/95 h-full w-64 absolute left-0 top-0 border-r border-gray-800\"\n            initial={{ x: -300 }}\n            animate={{ x: 0 }}\n            transition={{ duration: 0.3 }}\n            onClick={(e) => e.stopPropagation()}\n          >\n            <div className=\"p-4 border-b border-gray-800 flex items-center justify-between\">\n              <h1 className=\"text-xl font-bold bg-gradient-to-r from-purple-400 via-cyan-400 to-purple-400 text-transparent bg-clip-text\">\n                Barbecuez Admin\n              </h1>\n\n              <button\n                onClick={() => setIsMobileSidebarOpen(false)}\n                className=\"text-gray-400 hover:text-white transition-colors\"\n              >\n                <X className=\"w-5 h-5\" />\n              </button>\n            </div>\n\n            <nav className=\"py-4\">\n              <ul className=\"space-y-1 px-2\">\n                {navItems.map((item) => (\n                  <li key={item.href}>\n                    <Link href={item.href}>\n                      <a\n                        className={`flex items-center px-3 py-3 rounded-lg transition-all\n                                   ${item.isActive\n                                      ? 'bg-cyan-900/40 text-cyan-300 border border-cyan-500/20 shadow-[0_0_10px_rgba(0,255,255,0.15)]'\n                                      : 'text-gray-400 hover:text-white hover:bg-gray-800/50'}`}\n                        onClick={() => setIsMobileSidebarOpen(false)}\n                      >\n                        <span className=\"flex-shrink-0\">{item.icon}</span>\n                        <span className=\"ml-3\">{item.label}</span>\n                      </a>\n                    </Link>\n                  </li>\n                ))}\n              </ul>\n            </nav>\n\n            <div className=\"border-t border-gray-800 p-4\">\n              <Link href=\"/\">\n                <div\n                  className=\"flex items-center px-3 py-2 text-gray-400 hover:text-white hover:bg-gray-800/50 rounded-lg cursor-pointer\"\n                  onClick={() => setIsMobileSidebarOpen(false)}\n                >\n                  <Home className=\"w-5 h-5\" />\n                  <span className=\"ml-3\">Back to Website</span>\n                </div>\n              </Link>\n\n              <Link href=\"/logout\">\n                <div\n                  className=\"flex items-center px-3 py-2 text-gray-400 hover:text-white hover:bg-red-900/30 rounded-lg mt-2 cursor-pointer\"\n                  onClick={() => setIsMobileSidebarOpen(false)}\n                >\n                  <LogOut className=\"w-5 h-5 text-red-400\" />\n                  <span className=\"ml-3\">Logout</span>\n                </div>\n              </Link>\n            </div>\n          </motion.div>\n        </div>\n      )}\n\n      {/* Main Content */}\n      <main className={`flex-1 relative ${isSidebarOpen ? 'md:ml-64' : 'md:ml-20'}`}>\n        {/* Restaurant Status Banner */}\n        {!isLoading && !isOpen && (\n          <div className=\"bg-red-900/80 text-white py-2 px-4 text-center border-b border-red-700\">\n            <p className=\"flex items-center justify-center\">\n              <span className=\"inline-block w-2 h-2 bg-red-500 rounded-full mr-2 animate-pulse\"></span>\n              Restaurant is currently set to CLOSED - Customers cannot place orders\n            </p>\n          </div>\n        )}\n\n        <div className=\"container mx-auto p-4 md:p-6\">\n          {children}\n        </div>\n      </main>\n    </div>\n  );\n};\n\nconst ChevronLeft = ({ className }: { className?: string }) => {\n  return (\n    <svg\n      xmlns=\"http://www.w3.org/2000/svg\"\n      viewBox=\"0 0 24 24\"\n      fill=\"none\"\n      stroke=\"currentColor\"\n      strokeWidth=\"2\"\n      strokeLinecap=\"round\"\n      strokeLinejoin=\"round\"\n      className={className}\n    >\n      <polyline points=\"15 18 9 12 15 6\"></polyline>\n    </svg>\n  );\n};\n\nexport default AdminLayout;", "modifiedCode": "import { ReactNode, useState } from \"react\";\nimport { useLocation, Link } from \"wouter\";\nimport { motion } from \"framer-motion\";\nimport { useRestaurantStatus } from \"@/context/RestaurantStatusContext\";\n\n// Icons\nimport {\n  Settings, Menu, BarChart3, ChevronRight, LogOut,\n  Home, X, Menu as MenuIcon, ShoppingBag\n} from \"lucide-react\";\n\ninterface AdminLayoutProps {\n  children: ReactNode;\n}\n\nconst AdminLayout = ({ children }: AdminLayoutProps) => {\n  const [location] = useLocation();\n  const [isSidebarOpen, setIsSidebarOpen] = useState(true);\n  const [isMobileSidebarOpen, setIsMobileSidebarOpen] = useState(false);\n  const { isOpen, isLoading } = useRestaurantStatus();\n\n  const navItems = [\n    {\n      label: \"Settings\",\n      icon: <Settings className=\"w-5 h-5\" />,\n      href: \"/admin/settings\",\n      isActive: location === \"/admin/settings\"\n    },\n    {\n      label: \"Menu Management\",\n      icon: <Menu className=\"w-5 h-5\" />,\n      href: \"/admin/menu\",\n      isActive: location === \"/admin/menu\"\n    },\n    {\n      label: \"Order Manager\",\n      icon: <ShoppingBag className=\"w-5 h-5\" />,\n      href: \"/admin/orders\",\n      isActive: location === \"/admin/orders\"\n    },\n    {\n      label: \"Analytics\",\n      icon: <BarChart3 className=\"w-5 h-5\" />,\n      href: \"/admin/analytics\",\n      isActive: location === \"/admin/analytics\"\n    }\n  ];\n\n  return (\n    <div className=\"min-h-screen bg-black text-white flex\">\n      {/* Sidebar - Desktop */}\n      <motion.aside\n        className={`bg-gray-900/80 border-r border-gray-800 h-screen hidden md:flex flex-col fixed transition-all\n                    backdrop-blur-md z-20 ${isSidebarOpen ? 'w-64' : 'w-20'}`}\n        initial={{ x: -20, opacity: 0 }}\n        animate={{ x: 0, opacity: 1 }}\n        transition={{ duration: 0.3 }}\n      >\n        <div className=\"p-4 border-b border-gray-800 flex items-center justify-between\">\n          <div className={`flex items-center ${!isSidebarOpen && 'justify-center w-full'}`}>\n            {isSidebarOpen ? (\n              <h1 className=\"text-xl font-bold bg-gradient-to-r from-purple-400 via-cyan-400 to-purple-400 text-transparent bg-clip-text\">\n                Barbecuez Admin\n              </h1>\n            ) : (\n              <span className=\"text-xl font-bold text-cyan-400\">B</span>\n            )}\n          </div>\n\n          <button\n            onClick={() => setIsSidebarOpen(!isSidebarOpen)}\n            className=\"text-gray-400 hover:text-white transition-colors\"\n          >\n            {isSidebarOpen ? <ChevronLeft className=\"w-5 h-5\" /> : <ChevronRight className=\"w-5 h-5\" />}\n          </button>\n        </div>\n\n        <nav className=\"flex-1 overflow-y-auto py-4\">\n          <ul className=\"space-y-1 px-2\">\n            {navItems.map((item) => (\n              <li key={item.href}>\n                <Link href={item.href}>\n                  <div\n                    className={`flex items-center px-3 py-3 rounded-lg transition-all cursor-pointer\n                             ${item.isActive\n                                ? 'bg-cyan-900/40 text-cyan-300 border border-cyan-500/20 shadow-[0_0_10px_rgba(0,255,255,0.15)]'\n                                : 'text-gray-400 hover:text-white hover:bg-gray-800/50'}`}>\n                    <span className=\"flex-shrink-0\">{item.icon}</span>\n                    {isSidebarOpen && <span className=\"ml-3\">{item.label}</span>}\n                  </div>\n                </Link>\n              </li>\n            ))}\n          </ul>\n        </nav>\n\n        <div className=\"border-t border-gray-800 p-4\">\n          <Link href=\"/\">\n            <div className=\"flex items-center px-3 py-2 text-gray-400 hover:text-white hover:bg-gray-800/50 rounded-lg cursor-pointer\">\n              <Home className=\"w-5 h-5\" />\n              {isSidebarOpen && <span className=\"ml-3\">Back to Website</span>}\n            </div>\n          </Link>\n\n          <Link href=\"/logout\">\n            <div className=\"flex items-center px-3 py-2 text-gray-400 hover:text-white hover:bg-red-900/30 rounded-lg mt-2 cursor-pointer\">\n              <LogOut className=\"w-5 h-5 text-red-400\" />\n              {isSidebarOpen && <span className=\"ml-3\">Logout</span>}\n            </div>\n          </Link>\n        </div>\n      </motion.aside>\n\n      {/* Mobile Sidebar Toggle */}\n      <div className=\"fixed top-4 left-4 md:hidden z-30\">\n        <button\n          onClick={() => setIsMobileSidebarOpen(true)}\n          className=\"bg-gray-900/90 text-white p-2 rounded-lg border border-gray-700\"\n        >\n          <MenuIcon className=\"w-6 h-6\" />\n        </button>\n      </div>\n\n      {/* Mobile Sidebar */}\n      {isMobileSidebarOpen && (\n        <div\n          className=\"fixed inset-0 bg-black/70 backdrop-blur-sm z-40 md:hidden\"\n          onClick={() => setIsMobileSidebarOpen(false)}\n        >\n          <motion.div\n            className=\"bg-gray-900/95 h-full w-64 absolute left-0 top-0 border-r border-gray-800\"\n            initial={{ x: -300 }}\n            animate={{ x: 0 }}\n            transition={{ duration: 0.3 }}\n            onClick={(e) => e.stopPropagation()}\n          >\n            <div className=\"p-4 border-b border-gray-800 flex items-center justify-between\">\n              <h1 className=\"text-xl font-bold bg-gradient-to-r from-purple-400 via-cyan-400 to-purple-400 text-transparent bg-clip-text\">\n                Barbecuez Admin\n              </h1>\n\n              <button\n                onClick={() => setIsMobileSidebarOpen(false)}\n                className=\"text-gray-400 hover:text-white transition-colors\"\n              >\n                <X className=\"w-5 h-5\" />\n              </button>\n            </div>\n\n            <nav className=\"py-4\">\n              <ul className=\"space-y-1 px-2\">\n                {navItems.map((item) => (\n                  <li key={item.href}>\n                    <Link href={item.href}>\n                      <a\n                        className={`flex items-center px-3 py-3 rounded-lg transition-all\n                                   ${item.isActive\n                                      ? 'bg-cyan-900/40 text-cyan-300 border border-cyan-500/20 shadow-[0_0_10px_rgba(0,255,255,0.15)]'\n                                      : 'text-gray-400 hover:text-white hover:bg-gray-800/50'}`}\n                        onClick={() => setIsMobileSidebarOpen(false)}\n                      >\n                        <span className=\"flex-shrink-0\">{item.icon}</span>\n                        <span className=\"ml-3\">{item.label}</span>\n                      </a>\n                    </Link>\n                  </li>\n                ))}\n              </ul>\n            </nav>\n\n            <div className=\"border-t border-gray-800 p-4\">\n              <Link href=\"/\">\n                <div\n                  className=\"flex items-center px-3 py-2 text-gray-400 hover:text-white hover:bg-gray-800/50 rounded-lg cursor-pointer\"\n                  onClick={() => setIsMobileSidebarOpen(false)}\n                >\n                  <Home className=\"w-5 h-5\" />\n                  <span className=\"ml-3\">Back to Website</span>\n                </div>\n              </Link>\n\n              <Link href=\"/logout\">\n                <div\n                  className=\"flex items-center px-3 py-2 text-gray-400 hover:text-white hover:bg-red-900/30 rounded-lg mt-2 cursor-pointer\"\n                  onClick={() => setIsMobileSidebarOpen(false)}\n                >\n                  <LogOut className=\"w-5 h-5 text-red-400\" />\n                  <span className=\"ml-3\">Logout</span>\n                </div>\n              </Link>\n            </div>\n          </motion.div>\n        </div>\n      )}\n\n      {/* Main Content */}\n      <main className={`flex-1 relative ${isSidebarOpen ? 'md:ml-64' : 'md:ml-20'}`}>\n        {/* Restaurant Status Banner */}\n        {!isLoading && !isOpen && (\n          <div className=\"bg-red-900/80 text-white py-2 px-4 text-center border-b border-red-700\">\n            <p className=\"flex items-center justify-center\">\n              <span className=\"inline-block w-2 h-2 bg-red-500 rounded-full mr-2 animate-pulse\"></span>\n              Restaurant is currently set to CLOSED - Customers cannot place orders\n            </p>\n          </div>\n        )}\n\n        <div className=\"container mx-auto p-4 md:p-6\">\n          {children}\n        </div>\n      </main>\n    </div>\n  );\n};\n\nconst ChevronLeft = ({ className }: { className?: string }) => {\n  return (\n    <svg\n      xmlns=\"http://www.w3.org/2000/svg\"\n      viewBox=\"0 0 24 24\"\n      fill=\"none\"\n      stroke=\"currentColor\"\n      strokeWidth=\"2\"\n      strokeLinecap=\"round\"\n      strokeLinejoin=\"round\"\n      className={className}\n    >\n      <polyline points=\"15 18 9 12 15 6\"></polyline>\n    </svg>\n  );\n};\n\nexport default AdminLayout;"}