{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "server/test-db.ts"}, "modifiedCode": "import { storage } from \"./storage\";\n\nasync function testDatabaseOperations() {\n  console.log(\"Testing database operations...\");\n\n  try {\n    // Test category operations\n    console.log(\"\\n=== Testing Category Operations ===\");\n    \n    // Get all categories\n    const categories = await storage.getAllMenuCategories();\n    console.log(`Found ${categories.length} categories`);\n    \n    if (categories.length > 0) {\n      console.log(\"First category:\", categories[0]);\n      \n      // Test updating a category\n      const updatedCategory = await storage.updateMenuCategory(categories[0].id, {\n        name: categories[0].name + \" (Updated)\"\n      });\n      console.log(\"Updated category:\", updatedCategory);\n      \n      // Revert the update\n      await storage.updateMenuCategory(categories[0].id, {\n        name: categories[0].name.replace(\" (Updated)\", \"\")\n      });\n    }\n\n    // Test menu item operations\n    console.log(\"\\n=== Testing Menu Item Operations ===\");\n    \n    // Get all menu items\n    const menuItems = await storage.getAllMenuItems();\n    console.log(`Found ${menuItems.length} menu items`);\n    \n    if (menuItems.length > 0) {\n      console.log(\"First menu item:\", menuItems[0]);\n      \n      // Test getting items by category\n      if (categories.length > 0) {\n        const itemsByCategory = await storage.getMenuItemsByCategory(categories[0].id);\n        console.log(`Found ${itemsByCategory.length} items in category \"${categories[0].name}\"`);\n      }\n    }\n\n    // Test creating and deleting a test category\n    console.log(\"\\n=== Testing Create/Delete Operations ===\");\n    \n    const testCategory = await storage.createMenuCategory({\n      name: \"Test Category\",\n      imageUrl: \"https://example.com/test.jpg\"\n    });\n    console.log(\"Created test category:\", testCategory);\n    \n    const deleted = await storage.deleteMenuCategory(testCategory.id);\n    console.log(\"Deleted test category:\", deleted);\n\n    // Test order operations\n    console.log(\"\\n=== Testing Order Operations ===\");\n    \n    const orders = await storage.getAllOrders();\n    console.log(`Found ${orders.length} orders`);\n\n    // Test contact message operations\n    console.log(\"\\n=== Testing Contact Message Operations ===\");\n    \n    const contactMessages = await storage.getAllContactMessages();\n    console.log(`Found ${contactMessages.length} contact messages`);\n\n    // Test customization operations\n    console.log(\"\\n=== Testing Customization Operations ===\");\n    \n    const customizationGroups = await storage.getAllCustomizationGroups();\n    console.log(`Found ${customizationGroups.length} customization groups`);\n    \n    const customizationOptions = await storage.getAllCustomizationOptions();\n    console.log(`Found ${customizationOptions.length} customization options`);\n\n    console.log(\"\\n✅ All database operations completed successfully!\");\n\n  } catch (error) {\n    console.error(\"❌ Database test failed:\", error);\n    throw error;\n  }\n}\n\n// Run the test if this file is executed directly\nif (require.main === module) {\n  testDatabaseOperations()\n    .then(() => {\n      console.log(\"Database test completed!\");\n      process.exit(0);\n    })\n    .catch((error) => {\n      console.error(\"Database test failed:\", error);\n      process.exit(1);\n    });\n}\n\nexport { testDatabaseOperations };\n"}