{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "attached_assets/Pasted-Create-a-scalable-REST-API-using-Node-js-Express-and-PostgreSQL-for-a-restaurant-website-This-API-1747869135315.txt"}, "originalCode": "Create a scalable REST API using Node.js, Express, and PostgreSQL for a restaurant website. This API will manage the menu system with categories, menu items, and customizable options.\r\n\r\n### 📦 Tech Stack\r\n- Node.js with Express.js\r\n- PostgreSQL (via pg or Prisma)\r\n- Organized in a modular folder structure (routes, controllers, models/services)\r\n- Use async/await with proper error handling\r\n- Base URL: `http://localhost:5000/api/`\r\n\r\n---\r\n\r\n### 🗂️ API Structure Overview:\r\n\r\n1. **GET /categories**\r\n   - Return all menu categories.\r\n   - Response:\r\n     ```json\r\n     [\r\n       { \"id\": 1, \"name\": \"BurgerZ\", \"imageUrl\": \"/images/burger.jpg\" },\r\n       ...\r\n     ]\r\n     ```\r\n\r\n2. **GET /items?categoryId=1**\r\n   - Return all menu items under a specific category.\r\n   - Response:\r\n     ```json\r\n     [\r\n       {\r\n         \"id\": 101,\r\n         \"name\": \"BBQ Burger\",\r\n         \"description\": \"BBQ saus, Burger dressing, <PERSON><PERSON><PERSON><PERSON><PERSON>, Biff patty, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>\",\r\n         \"price\": 169,\r\n         \"imageUrl\": \"/images/bbq-burger.jpg\"\r\n       },\r\n       ...\r\n     ]\r\n     ```\r\n\r\n3. **GET /items/:id/customization**\r\n   - Get all customization options for a specific menu item, grouped by category.\r\n   - Response:\r\n     ```json\r\n     {\r\n       \"itemId\": 101,\r\n       \"customization\": [\r\n         {\r\n           \"group\": \"Saus & Topping\",\r\n           \"options\": [\r\n             { \"id\": 1, \"name\": \"BBQ saus\", \"extraPrice\": 1, \"imageUrl\": \"/icons/bbq.png\" },\r\n             ...\r\n           ]\r\n         },\r\n         {\r\n           \"group\": \"Grønnsaker\",\r\n           \"options\": [\r\n             { \"id\": 7, \"name\": \"Tomat\", \"extraPrice\": 0, \"imageUrl\": \"/icons/tomato.png\" }\r\n           ]\r\n         }\r\n       ]\r\n     }\r\n     ```\r\n\r\n4. **POST /cart**\r\n   - Accept a cart payload with selected item, quantity, and selected customizations.\r\n   - Payload example:\r\n     ```json\r\n     {\r\n       \"itemId\": 101,\r\n       \"quantity\": 2,\r\n       \"customizations\": [1, 4, 7]\r\n     }\r\n     ```\r\n\r\n---\r\n\r\n### 🛠️ PostgreSQL Schema:\r\n\r\n1. `categories`\r\n   - id (PK)\r\n   - name (e.g., BurgerZ)\r\n   - image_url\r\n\r\n2. `menu_items`\r\n   - id (PK)\r\n   - name\r\n   - description\r\n   - price\r\n   - image_url\r\n   - category_id (FK)\r\n\r\n3. `customization_groups`\r\n   - id (PK)\r\n   - title (e.g., \"Saus & Topping\")\r\n\r\n4. `customization_options`\r\n   - id (PK)\r\n   - name\r\n   - extra_price\r\n   - image_url\r\n   - group_id (FK)\r\n\r\n5. `item_customization_map`\r\n   - id (PK)\r\n   - item_id (FK)\r\n   - option_id (FK)\r\n\r\n---\r\n\r\n### 🧪 Optional:\r\n- Use CORS middleware to allow requests from React frontend.\r\n- Seed database with sample data for categories, items, and customizations.\r\n- Use `.env` file for DB connection string and port.\r\n\r\n---\r\n\r\n### ✅ Output:\r\n- A complete working Express API that:\r\n  - Serves categories and menu items\r\n  - Returns dynamic customization data per item\r\n  - Is ready for integration with a PostgreSQL database\r\n  - Accepts POST requests for order/cart submission\r\n📁 Suggested Backend Folder Structure:\r\nbash\r\nCopy\r\nEdit\r\n/backend\r\n  /routes\r\n    categories.js\r\n    items.js\r\n    cart.js\r\n  /controllers\r\n    categoriesController.js\r\n    itemsController.js\r\n    cartController.js\r\n  /models\r\n    db.js (DB connection)\r\n    queries.js\r\n  /data\r\n    seed.sql\r\n  server.js\r\n  .env\r\n", "modifiedCode": "Create a scalable REST API using Node.js, Express, and PostgreSQL for a restaurant website. This API will manage the menu system with categories, menu items, and customizable options.\r\n\r\n### 📦 Tech Stack\r\n- Node.js with Express.js\r\n- PostgreSQL (via pg or Prisma)\r\n- Organized in a modular folder structure (routes, controllers, models/services)\r\n- Use async/await with proper error handling\r\n- Base URL: `http://localhost:5000/api/`\r\n\r\n---\r\n\r\n### 🗂️ API Structure Overview:\r\n\r\n1. **GET /categories**\r\n   - Return all menu categories.\r\n   - Response:\r\n     ```json\r\n     [\r\n       { \"id\": 1, \"name\": \"BurgerZ\", \"imageUrl\": \"/images/burger.jpg\" },\r\n       ...\r\n     ]\r\n     ```\r\n\r\n2. **GET /items?categoryId=1**\r\n   - Return all menu items under a specific category.\r\n   - Response:\r\n     ```json\r\n     [\r\n       {\r\n         \"id\": 101,\r\n         \"name\": \"BBQ Burger\",\r\n         \"description\": \"BBQ saus, Burger dressing, <PERSON><PERSON><PERSON><PERSON><PERSON>, Biff patty, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>\",\r\n         \"price\": 169,\r\n         \"imageUrl\": \"/images/bbq-burger.jpg\"\r\n       },\r\n       ...\r\n     ]\r\n     ```\r\n\r\n3. **GET /items/:id/customization**\r\n   - Get all customization options for a specific menu item, grouped by category.\r\n   - Response:\r\n     ```json\r\n     {\r\n       \"itemId\": 101,\r\n       \"customization\": [\r\n         {\r\n           \"group\": \"Saus & Topping\",\r\n           \"options\": [\r\n             { \"id\": 1, \"name\": \"BBQ saus\", \"extraPrice\": 1, \"imageUrl\": \"/icons/bbq.png\" },\r\n             ...\r\n           ]\r\n         },\r\n         {\r\n           \"group\": \"Grønnsaker\",\r\n           \"options\": [\r\n             { \"id\": 7, \"name\": \"Tomat\", \"extraPrice\": 0, \"imageUrl\": \"/icons/tomato.png\" }\r\n           ]\r\n         }\r\n       ]\r\n     }\r\n     ```\r\n\r\n4. **POST /cart**\r\n   - Accept a cart payload with selected item, quantity, and selected customizations.\r\n   - Payload example:\r\n     ```json\r\n     {\r\n       \"itemId\": 101,\r\n       \"quantity\": 2,\r\n       \"customizations\": [1, 4, 7]\r\n     }\r\n     ```\r\n\r\n---\r\n\r\n### 🛠️ PostgreSQL Schema:\r\n\r\n1. `categories`\r\n   - id (PK)\r\n   - name (e.g., BurgerZ)\r\n   - image_url\r\n\r\n2. `menu_items`\r\n   - id (PK)\r\n   - name\r\n   - description\r\n   - price\r\n   - image_url\r\n   - category_id (FK)\r\n\r\n3. `customization_groups`\r\n   - id (PK)\r\n   - title (e.g., \"Saus & Topping\")\r\n\r\n4. `customization_options`\r\n   - id (PK)\r\n   - name\r\n   - extra_price\r\n   - image_url\r\n   - group_id (FK)\r\n\r\n5. `item_customization_map`\r\n   - id (PK)\r\n   - item_id (FK)\r\n   - option_id (FK)\r\n\r\n---\r\n\r\n### 🧪 Optional:\r\n- Use CORS middleware to allow requests from React frontend.\r\n- Seed database with sample data for categories, items, and customizations.\r\n- Use `.env` file for DB connection string and port.\r\n\r\n---\r\n\r\n### ✅ Output:\r\n- A complete working Express API that:\r\n  - Serves categories and menu items\r\n  - Returns dynamic customization data per item\r\n  - Is ready for integration with a PostgreSQL database\r\n  - Accepts POST requests for order/cart submission\r\n📁 Suggested Backend Folder Structure:\r\nbash\r\nCopy\r\nEdit\r\n/backend\r\n  /routes\r\n    categories.js\r\n    items.js\r\n    cart.js\r\n  /controllers\r\n    categoriesController.js\r\n    itemsController.js\r\n    cartController.js\r\n  /models\r\n    db.js (DB connection)\r\n    queries.js\r\n  /data\r\n    seed.sql\r\n  server.js\r\n  .env\r\n"}