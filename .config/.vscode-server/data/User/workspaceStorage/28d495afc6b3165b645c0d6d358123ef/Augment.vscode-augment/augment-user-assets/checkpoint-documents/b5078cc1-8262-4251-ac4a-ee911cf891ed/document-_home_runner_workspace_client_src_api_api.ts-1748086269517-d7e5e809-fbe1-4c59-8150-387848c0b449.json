{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/api/api.ts"}, "originalCode": "import { apiRequest } from \"@/lib/queryClient\";\nimport { Dish, ContactMessage, Order } from \"@shared/schema\";\n\n// Fetch all dishes\nexport const getDishes = async (): Promise<Dish[]> => {\n  const response = await apiRequest('GET', '/api/dishes', undefined);\n  return response.json();\n};\n\n// Fetch a single dish by ID\nexport const getDishById = async (id: number): Promise<Dish> => {\n  const response = await apiRequest('GET', `/api/dishes/${id}`, undefined);\n  return response.json();\n};\n\n// Create a new order\nexport const createOrder = async (orderData: Order): Promise<Order> => {\n  const response = await apiRequest('POST', '/api/orders', orderData);\n  return response.json();\n};\n\n// Send a contact message\nexport const sendContactMessage = async (messageData: ContactMessage): Promise<{ success: boolean }> => {\n  const response = await apiRequest('POST', '/api/contact', messageData);\n  return response.json();\n};\n\n// Fetch menu categories\nexport const getCategories = async (): Promise<string[]> => {\n  const response = await apiRequest('GET', '/api/categories', undefined);\n  return response.json();\n};\n\n// Add more API functions as needed\n", "modifiedCode": "import { apiRequest } from \"@/lib/queryClient\";\nimport { Dish, ContactMessage, Order } from \"@shared/schema\";\n\n// Fetch all dishes\nexport const getDishes = async (): Promise<Dish[]> => {\n  const response = await apiRequest('GET', '/api/items', undefined);\n  return response.json();\n};\n\n// Fetch a single dish by ID\nexport const getDishById = async (id: number): Promise<Dish> => {\n  const response = await apiRequest('GET', `/api/dishes/${id}`, undefined);\n  return response.json();\n};\n\n// Create a new order\nexport const createOrder = async (orderData: Order): Promise<Order> => {\n  const response = await apiRequest('POST', '/api/orders', orderData);\n  return response.json();\n};\n\n// Send a contact message\nexport const sendContactMessage = async (messageData: ContactMessage): Promise<{ success: boolean }> => {\n  const response = await apiRequest('POST', '/api/contact', messageData);\n  return response.json();\n};\n\n// Fetch menu categories\nexport const getCategories = async (): Promise<string[]> => {\n  const response = await apiRequest('GET', '/api/categories', undefined);\n  return response.json();\n};\n\n// Add more API functions as needed\n"}