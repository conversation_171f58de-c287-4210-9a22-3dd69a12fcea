{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "server/storage.ts"}, "originalCode": "import {\n  User, InsertUser,\n  Dish, InsertDish,\n  Order, InsertOrder,\n  ContactMessage, InsertContactMessage,\n  Category, InsertCategory,\n  MenuItem, InsertMenuItem,\n  CustomizationGroup, InsertCustomizationGroup,\n  CustomizationOption, InsertCustomizationOption,\n  ItemCustomizationMap, InsertItemCustomizationMap,\n  users, categories, menuItems, orders, contactMessages,\n  customizationGroups, customizationOptions, itemCustomizationMap\n} from \"@shared/schema\";\nimport { db } from \"./db\";\nimport { eq, desc } from \"drizzle-orm\";\nimport * as crypto from \"crypto\";\n\n// modify the interface with any CRUD methods\n// you might need\nexport interface IStorage {\n  // User operations\n  getUser(id: number): Promise<User | undefined>;\n  getUserByUsername(username: string): Promise<User | undefined>;\n  createUser(user: InsertUser): Promise<User>;\n  updateUser(id: number, user: Partial<InsertUser>): Promise<User | undefined>;\n  deleteUser(id: number): Promise<boolean>;\n  verifyUserCredentials(username: string, password: string): Promise<User | null>;\n\n  // Dish/MenuItem operations\n  getAllDishes(): Promise<Dish[]>;\n  getDishById(id: number): Promise<Dish | undefined>;\n  createDish(dish: InsertDish): Promise<Dish>;\n  updateDish(id: number, dish: Partial<InsertDish>): Promise<Dish | undefined>;\n  deleteDish(id: number): Promise<boolean>;\n\n  // Enhanced Menu Item operations\n  getAllMenuItems(): Promise<MenuItem[]>;\n  getMenuItemsByCategory(categoryId: number): Promise<MenuItem[]>;\n  getMenuItemById(id: number): Promise<MenuItem | undefined>;\n  createMenuItem(item: InsertMenuItem): Promise<MenuItem>;\n  updateMenuItem(id: number, item: Partial<InsertMenuItem>): Promise<MenuItem | undefined>;\n  deleteMenuItem(id: number): Promise<boolean>;\n\n  // Category operations\n  getCategories(): Promise<string[]>; // Original method for backwards compatibility\n  getAllMenuCategories(): Promise<Category[]>;\n  getMenuCategoryById(id: number): Promise<Category | undefined>;\n  createMenuCategory(category: InsertCategory): Promise<Category>;\n  updateMenuCategory(id: number, category: Partial<InsertCategory>): Promise<Category | undefined>;\n  deleteMenuCategory(id: number): Promise<boolean>;\n\n  // Customization operations\n  getAllCustomizationGroups(): Promise<CustomizationGroup[]>;\n  getCustomizationGroupById(id: number): Promise<CustomizationGroup | undefined>;\n  createCustomizationGroup(group: InsertCustomizationGroup): Promise<CustomizationGroup>;\n  updateCustomizationGroup(id: number, group: Partial<InsertCustomizationGroup>): Promise<CustomizationGroup | undefined>;\n  deleteCustomizationGroup(id: number): Promise<boolean>;\n\n  getAllCustomizationOptions(): Promise<CustomizationOption[]>;\n  getCustomizationOptionsByGroup(groupId: number): Promise<CustomizationOption[]>;\n  getCustomizationOptionById(id: number): Promise<CustomizationOption | undefined>;\n  createCustomizationOption(option: InsertCustomizationOption): Promise<CustomizationOption>;\n  updateCustomizationOption(id: number, option: Partial<InsertCustomizationOption>): Promise<CustomizationOption | undefined>;\n  deleteCustomizationOption(id: number): Promise<boolean>;\n\n  // Item Customization Map operations\n  getCustomizationOptionsForMenuItem(itemId: number): Promise<{\n    group: CustomizationGroup;\n    options: CustomizationOption[];\n  }[]>;\n  mapCustomizationOptionToMenuItem(itemId: number, optionId: number): Promise<ItemCustomizationMap>;\n  unmapCustomizationOptionFromMenuItem(itemId: number, optionId: number): Promise<boolean>;\n\n  // Order operations\n  getOrderById(id: number): Promise<Order | undefined>;\n  getAllOrders(): Promise<Order[]>;\n  createOrder(order: InsertOrder): Promise<Order>;\n  updateOrder(id: number, order: Partial<InsertOrder>): Promise<Order | undefined>;\n  deleteOrder(id: number): Promise<boolean>;\n\n  // Cart operations\n  createCart(cart: { itemId: number, quantity: number, customizations: number[] }): Promise<{ success: boolean, id: number }>;\n\n  // Contact operations\n  createContactMessage(message: InsertContactMessage): Promise<ContactMessage>;\n  getAllContactMessages(): Promise<ContactMessage[]>;\n  getContactMessageById(id: number): Promise<ContactMessage | undefined>;\n  deleteContactMessage(id: number): Promise<boolean>;\n}\n\nexport class MemStorage implements IStorage {\n  private users: Map<number, User>;\n  private dishes: Map<number, Dish>;\n  private orders: Map<number, Order>;\n  private contactMessages: Map<number, ContactMessage>;\n\n  // New storage for enhanced menu system\n  private menuCategories: Map<number, Category>;\n  private menuItems: Map<number, MenuItem>;\n  private customizationGroups: Map<number, CustomizationGroup>;\n  private customizationOptions: Map<number, CustomizationOption>;\n  private itemCustomizationMaps: Map<number, ItemCustomizationMap>;\n\n  // ID counters\n  currentUserId: number;\n  currentDishId: number;\n  currentOrderId: number;\n  currentContactId: number;\n  currentCategoryId: number;\n  currentMenuItemId: number;\n  currentCustomizationGroupId: number;\n  currentCustomizationOptionId: number;\n  currentItemCustomizationMapId: number;\n\n  constructor() {\n    // Initialize maps\n    this.users = new Map();\n    this.dishes = new Map();\n    this.orders = new Map();\n    this.contactMessages = new Map();\n    this.menuCategories = new Map();\n    this.menuItems = new Map();\n    this.customizationGroups = new Map();\n    this.customizationOptions = new Map();\n    this.itemCustomizationMaps = new Map();\n\n    // Initialize counters\n    this.currentUserId = 1;\n    this.currentDishId = 1;\n    this.currentOrderId = 1;\n    this.currentContactId = 1;\n    this.currentCategoryId = 1;\n    this.currentMenuItemId = 1;\n    this.currentCustomizationGroupId = 1;\n    this.currentCustomizationOptionId = 1;\n    this.currentItemCustomizationMapId = 1;\n\n    // Initialize sample data\n    this.initializeSampleData();\n  }\n\n  // User methods\n  async getUser(id: number): Promise<User | undefined> {\n    return this.users.get(id);\n  }\n\n  async getUserByUsername(username: string): Promise<User | undefined> {\n    return Array.from(this.users.values()).find(\n      (user) => user.username === username,\n    );\n  }\n\n  async createUser(insertUser: InsertUser): Promise<User> {\n    const id = this.currentUserId++;\n    const user: User = { ...insertUser, id };\n    this.users.set(id, user);\n    return user;\n  }\n\n  async verifyUserCredentials(username: string, password: string): Promise<User | null> {\n    const user = await this.getUserByUsername(username);\n\n    if (!user) {\n      return null;\n    }\n\n    // In a real implementation, you would use bcrypt.compare\n    // For this demo, we'll just do a direct comparison\n    if (user.password === password) {\n      return user;\n    }\n\n    return null;\n  }\n\n  // Original Dish methods (for backwards compatibility)\n  async getAllDishes(): Promise<Dish[]> {\n    return Array.from(this.dishes.values());\n  }\n\n  async getDishById(id: number): Promise<Dish | undefined> {\n    return this.dishes.get(id);\n  }\n\n  async createDish(insertDish: InsertDish): Promise<Dish> {\n    const id = this.currentDishId++;\n    const dish: Dish = { ...insertDish, id };\n    this.dishes.set(id, dish);\n    return dish;\n  }\n\n  // Enhanced Menu Item methods\n  async getAllMenuItems(): Promise<MenuItem[]> {\n    return Array.from(this.menuItems.values());\n  }\n\n  async getMenuItemsByCategory(categoryId: number): Promise<MenuItem[]> {\n    return Array.from(this.menuItems.values()).filter(\n      (item) => item.categoryId === categoryId\n    );\n  }\n\n  async getMenuItemById(id: number): Promise<MenuItem | undefined> {\n    return this.menuItems.get(id);\n  }\n\n  async createMenuItem(item: InsertMenuItem): Promise<MenuItem> {\n    const id = this.currentMenuItemId++;\n    const menuItem: MenuItem = { ...item, id };\n    this.menuItems.set(id, menuItem);\n\n    // Also add to dishes for backward compatibility\n    const category = this.menuCategories.get(item.categoryId)?.name || \"Uncategorized\";\n    this.dishes.set(id, {\n      ...menuItem,\n      category\n    } as Dish);\n\n    return menuItem;\n  }\n\n  // Enhanced Category methods\n  async getAllMenuCategories(): Promise<Category[]> {\n    try {\n      // Make sure we actually return the full category objects\n      return Array.from(this.menuCategories.values());\n    } catch (error) {\n      console.error(\"Error getting menu categories:\", error);\n      return [];\n    }\n  }\n\n  async getMenuCategoryById(id: number): Promise<Category | undefined> {\n    return this.menuCategories.get(id);\n  }\n\n  async createMenuCategory(category: InsertCategory): Promise<Category> {\n    const id = this.currentCategoryId++;\n    const newCategory: Category = { ...category, id };\n    this.menuCategories.set(id, newCategory);\n    return newCategory;\n  }\n\n  async updateMenuCategory(id: number, category: Partial<Category>): Promise<Category> {\n    const existingCategory = this.menuCategories.get(id);\n    if (!existingCategory) {\n      throw new Error(`Category with id ${id} not found`);\n    }\n\n    const updatedCategory = { ...existingCategory, ...category };\n    this.menuCategories.set(id, updatedCategory);\n    return updatedCategory;\n  }\n\n  async deleteMenuCategory(id: number): Promise<Category> {\n    const category = this.menuCategories.get(id);\n    if (!category) {\n      throw new Error(`Category with id ${id} not found`);\n    }\n\n    this.menuCategories.delete(id);\n\n    // Update any menu items that were in this category\n    for (const [itemId, item] of this.menuItems.entries()) {\n      if (item.category_id === id) {\n        const updatedItem = { ...item, category_id: 0 };\n        this.menuItems.set(itemId, updatedItem);\n      }\n    }\n\n    return category;\n  }\n\n  // Customization Group methods\n  async getAllCustomizationGroups(): Promise<CustomizationGroup[]> {\n    return Array.from(this.customizationGroups.values());\n  }\n\n  async getCustomizationGroupById(id: number): Promise<CustomizationGroup | undefined> {\n    return this.customizationGroups.get(id);\n  }\n\n  async createCustomizationGroup(group: InsertCustomizationGroup): Promise<CustomizationGroup> {\n    const id = this.currentCustomizationGroupId++;\n    const newGroup: CustomizationGroup = { ...group, id };\n    this.customizationGroups.set(id, newGroup);\n    return newGroup;\n  }\n\n  // Customization Option methods\n  async getAllCustomizationOptions(): Promise<CustomizationOption[]> {\n    return Array.from(this.customizationOptions.values());\n  }\n\n  async getCustomizationOptionsByGroup(groupId: number): Promise<CustomizationOption[]> {\n    return Array.from(this.customizationOptions.values()).filter(\n      (option) => option.groupId === groupId\n    );\n  }\n\n  async getCustomizationOptionById(id: number): Promise<CustomizationOption | undefined> {\n    return this.customizationOptions.get(id);\n  }\n\n  async createCustomizationOption(option: InsertCustomizationOption): Promise<CustomizationOption> {\n    const id = this.currentCustomizationOptionId++;\n    const newOption: CustomizationOption = { ...option, id };\n    this.customizationOptions.set(id, newOption);\n    return newOption;\n  }\n\n  // Item Customization Map methods\n  async getCustomizationOptionsForMenuItem(itemId: number): Promise<{\n    group: CustomizationGroup;\n    options: CustomizationOption[];\n  }[]> {\n    // Get all mapping entries for this item\n    const mappings = Array.from(this.itemCustomizationMaps.values()).filter(\n      (map) => map.itemId === itemId\n    );\n\n    // Get all option IDs for this item\n    const optionIds = mappings.map((map) => map.optionId);\n\n    // Get all options for these IDs\n    const options = optionIds.map((id) => this.customizationOptions.get(id)!).filter(Boolean);\n\n    // Group options by group ID\n    const groupedOptions = new Map<number, CustomizationOption[]>();\n    options.forEach((option) => {\n      if (!groupedOptions.has(option.groupId)) {\n        groupedOptions.set(option.groupId, []);\n      }\n      groupedOptions.get(option.groupId)!.push(option);\n    });\n\n    // Format the result with group objects\n    const result: { group: CustomizationGroup; options: CustomizationOption[] }[] = [];\n    groupedOptions.forEach((options, groupId) => {\n      const group = this.customizationGroups.get(groupId);\n      if (group) {\n        result.push({ group, options });\n      }\n    });\n\n    return result;\n  }\n\n  async mapCustomizationOptionToMenuItem(itemId: number, optionId: number): Promise<ItemCustomizationMap> {\n    // Check if the menu item and option exist\n    const menuItem = await this.getMenuItemById(itemId);\n    const option = await this.getCustomizationOptionById(optionId);\n\n    if (!menuItem || !option) {\n      throw new Error(\"Menu item or customization option not found\");\n    }\n\n    // Create the mapping\n    const id = this.currentItemCustomizationMapId++;\n    const mapping: ItemCustomizationMap = { id, itemId, optionId };\n    this.itemCustomizationMaps.set(id, mapping);\n\n    return mapping;\n  }\n\n  // Cart operations\n  async createCart(cart: { itemId: number, quantity: number, customizations: number[] }): Promise<{ success: boolean, id: number }> {\n    // Validate that the item exists\n    const menuItem = await this.getMenuItemById(cart.itemId);\n    if (!menuItem) {\n      throw new Error(\"Menu item not found\");\n    }\n\n    // Validate that all customization options exist\n    for (const optionId of cart.customizations) {\n      const option = await this.getCustomizationOptionById(optionId);\n      if (!option) {\n        throw new Error(`Customization option ${optionId} not found`);\n      }\n    }\n\n    // Since we don't have a cart table yet, we'll just return success\n    return { success: true, id: Date.now() };\n  }\n\n  // Order methods\n  async getOrderById(id: number): Promise<Order | undefined> {\n    return this.orders.get(id);\n  }\n\n  async getAllOrders(): Promise<Order[]> {\n    return Array.from(this.orders.values());\n  }\n\n  async createOrder(insertOrder: InsertOrder): Promise<Order> {\n    const id = this.currentOrderId++;\n    const now = new Date();\n    const order: Order = { ...insertOrder, id, createdAt: now };\n    this.orders.set(id, order);\n    return order;\n  }\n\n  // Contact methods\n  async createContactMessage(insertMessage: InsertContactMessage): Promise<ContactMessage> {\n    const id = this.currentContactId++;\n    const now = new Date();\n    const message: ContactMessage = { ...insertMessage, id, createdAt: now };\n    this.contactMessages.set(id, message);\n    return message;\n  }\n\n  // Category methods (for backwards compatibility)\n  async getCategories(): Promise<string[]> {\n    // Get all category names from the categories table\n    return Array.from(this.menuCategories.values()).map(category => category.name);\n  }\n\n  // Initialize sample data for the menu system\n  private initializeSampleData() {\n    // 1. Initialize categories\n    const sampleCategories: InsertCategory[] = [\n      {\n        name: \"Signature BBQ\",\n        imageUrl: \"https://images.unsplash.com/photo-1529193591184-b1d58069ecdd?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&h=400\",\n      },\n      {\n        name: \"Starters\",\n        imageUrl: \"https://images.unsplash.com/photo-1527477396000-e27163b481c2?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&h=400\",\n      },\n      {\n        name: \"Main Course\",\n        imageUrl: \"https://images.unsplash.com/photo-1519708227418-c8fd9a32b7a2?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&h=400\",\n      },\n      {\n        name: \"Desserts\",\n        imageUrl: \"https://images.unsplash.com/photo-1624353365286-3f8d62daad51?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&h=400\",\n      },\n      {\n        name: \"BurgerZ\",\n        imageUrl: \"https://images.unsplash.com/photo-1568901346375-23c9450c58cd?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&h=400\",\n      },\n      {\n        name: \"SandwichZ\",\n        imageUrl: \"https://images.unsplash.com/photo-1621996346565-e3dbc646d9a9?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&h=400\",\n      }\n    ];\n\n    // Insert categories and create a map for reference when creating menu items\n    const categoryMap: Record<string, number> = {};\n    sampleCategories.forEach(category => {\n      const id = this.currentCategoryId++;\n      this.menuCategories.set(id, { ...category, id });\n      categoryMap[category.name] = id;\n    });\n\n    // 2. Initialize menu items\n    const sampleMenuItems: Array<InsertMenuItem & { categoryName: string }> = [\n      {\n        name: \"Smoked Beef Brisket\",\n        description: \"24-hour smoked premium beef brisket with our signature spice rub, served with homemade BBQ sauce and pickled vegetables.\",\n        price: 329,\n        imageUrl: \"https://images.unsplash.com/photo-1529193591184-b1d58069ecdd?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&h=400\",\n        categoryId: 0, // Will be replaced with actual ID\n        categoryName: \"Signature BBQ\",\n        available: true,\n        rating: 49,\n        reviews: 120\n      },\n      {\n        name: \"BBQ Pork Ribs\",\n        description: \"Slow-cooked St. Louis style ribs with our house dry rub, glazed with maple bourbon sauce and finished over open flame.\",\n        price: 289,\n        imageUrl: \"https://images.unsplash.com/photo-1544025162-d76694265947?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&h=400\",\n        categoryId: 0,\n        categoryName: \"Signature BBQ\",\n        available: true,\n        rating: 48,\n        reviews: 98\n      },\n      {\n        name: \"Pulled Pork Sandwich\",\n        description: \"12-hour smoked pork shoulder, hand-pulled and tossed in Carolina vinegar sauce, served on a brioche bun with coleslaw.\",\n        price: 219,\n        imageUrl: \"https://images.unsplash.com/photo-1621996346565-e3dbc646d9a9?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&h=400\",\n        categoryId: 0,\n        categoryName: \"SandwichZ\",\n        available: true,\n        rating: 47,\n        reviews: 86\n      },\n      {\n        name: \"Smoked Chicken Wings\",\n        description: \"Applewood smoked wings finished on the grill, tossed in your choice of sauce: Classic Buffalo, Honey Chipotle, or Garlic Parmesan.\",\n        price: 189,\n        imageUrl: \"https://images.unsplash.com/photo-1527477396000-e27163b481c2?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&h=400\",\n        categoryId: 0,\n        categoryName: \"Starters\",\n        available: true,\n        rating: 46,\n        reviews: 112\n      },\n      {\n        name: \"Cedar Plank Salmon\",\n        description: \"Norwegian salmon fillet grilled on a cedar plank with maple glaze, served with grilled lemon and seasonal vegetables.\",\n        price: 299,\n        imageUrl: \"https://images.unsplash.com/photo-1519708227418-c8fd9a32b7a2?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&h=400\",\n        categoryId: 0,\n        categoryName: \"Main Course\",\n        available: true,\n        rating: 49,\n        reviews: 74\n      },\n      {\n        name: \"Smokehouse Burger\",\n        description: \"House-ground prime beef patty with smoked cheddar, bacon jam, caramelized onions, and bourbon BBQ sauce on a toasted brioche bun.\",\n        price: 249,\n        imageUrl: \"https://images.unsplash.com/photo-1568901346375-23c9450c58cd?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&h=400\",\n        categoryId: 0,\n        categoryName: \"BurgerZ\",\n        available: true,\n        rating: 48,\n        reviews: 103\n      },\n      {\n        name: \"BBQ Sampler Platter\",\n        description: \"A selection of our signature meats including brisket, ribs, pulled pork, and smoked sausage, served with two sides of your choice.\",\n        price: 399,\n        imageUrl: \"https://images.unsplash.com/photo-1508615263227-c5d58c1e5821?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&h=400\",\n        categoryId: 0,\n        categoryName: \"Signature BBQ\",\n        available: true,\n        rating: 50,\n        reviews: 135\n      },\n      {\n        name: \"Grilled Vegetable Skewers\",\n        description: \"Seasonal vegetables marinated in herbs and olive oil, grilled to perfection and served with chimichurri sauce.\",\n        price: 179,\n        imageUrl: \"https://images.unsplash.com/photo-1625944525533-473f1a3d54a7?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&h=400\",\n        categoryId: 0,\n        categoryName: \"Starters\",\n        available: true,\n        rating: 45,\n        reviews: 62\n      },\n      {\n        name: \"Chocolate Lava Cake\",\n        description: \"Warm chocolate cake with a molten center, served with vanilla bean ice cream and fresh berries.\",\n        price: 149,\n        imageUrl: \"https://images.unsplash.com/photo-1624353365286-3f8d62daad51?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&h=400\",\n        categoryId: 0,\n        categoryName: \"Desserts\",\n        available: true,\n        rating: 48,\n        reviews: 89\n      }\n    ];\n\n    // Insert menu items with the correct category IDs\n    sampleMenuItems.forEach(menuItem => {\n      const { categoryName, ...item } = menuItem;\n      const categoryId = categoryMap[categoryName];\n      const id = this.currentMenuItemId++;\n      const dishToInsert = { ...item, categoryId, id };\n\n      // Add to both menu items and dishes (for backward compatibility)\n      this.menuItems.set(id, dishToInsert);\n      this.dishes.set(id, {\n        ...dishToInsert,\n        category: categoryName // For backward compatibility\n      } as Dish);\n    });\n\n    // 3. Initialize customization groups\n    const customizationGroups: InsertCustomizationGroup[] = [\n      { title: \"Saus & Topping\" },\n      { title: \"Ost\" },\n      { title: \"Ekstra Produkter\" },\n      { title: \"Grønnsaker\" }\n    ];\n\n    // Add customization groups and track IDs\n    const groupMap: Record<string, number> = {};\n    customizationGroups.forEach(group => {\n      const id = this.currentCustomizationGroupId++;\n      this.customizationGroups.set(id, { ...group, id });\n      groupMap[group.title] = id;\n    });\n\n    // 4. Initialize customization options\n    const customizationOptions: Array<InsertCustomizationOption & { groupTitle: string }> = [\n      { name: \"BBQ saus\", extraPrice: 10, imageUrl: \"/icons/bbq.png\", groupId: 0, groupTitle: \"Saus & Topping\" },\n      { name: \"Hot Sauce\", extraPrice: 10, imageUrl: \"/icons/hot.png\", groupId: 0, groupTitle: \"Saus & Topping\" },\n      { name: \"Mayo\", extraPrice: 10, imageUrl: \"/icons/mayo.png\", groupId: 0, groupTitle: \"Saus & Topping\" },\n      { name: \"Cheddar\", extraPrice: 15, imageUrl: \"/icons/cheddar.png\", groupId: 0, groupTitle: \"Ost\" },\n      { name: \"Blue Cheese\", extraPrice: 20, imageUrl: \"/icons/blue.png\", groupId: 0, groupTitle: \"Ost\" },\n      { name: \"Bacon\", extraPrice: 25, imageUrl: \"/icons/bacon.png\", groupId: 0, groupTitle: \"Ekstra Produkter\" },\n      { name: \"Double Meat\", extraPrice: 40, imageUrl: \"/icons/meat.png\", groupId: 0, groupTitle: \"Ekstra Produkter\" },\n      { name: \"Lettuce\", extraPrice: 0, imageUrl: \"/icons/lettuce.png\", groupId: 0, groupTitle: \"Grønnsaker\" },\n      { name: \"Tomato\", extraPrice: 0, imageUrl: \"/icons/tomato.png\", groupId: 0, groupTitle: \"Grønnsaker\" },\n      { name: \"Onion\", extraPrice: 0, imageUrl: \"/icons/onion.png\", groupId: 0, groupTitle: \"Grønnsaker\" },\n      { name: \"Avocado\", extraPrice: 20, imageUrl: \"/icons/avocado.png\", groupId: 0, groupTitle: \"Grønnsaker\" }\n    ];\n\n    // Add customization options with correct group IDs\n    const optionMap: Record<string, number> = {};\n    customizationOptions.forEach(option => {\n      const { groupTitle, ...optionData } = option;\n      const groupId = groupMap[groupTitle];\n      const id = this.currentCustomizationOptionId++;\n      this.customizationOptions.set(id, { ...optionData, groupId, id });\n      optionMap[option.name] = id;\n    });\n\n    // 5. Map customization options to menu items\n    // For each burger, add some customization options\n    const burgerItems = Array.from(this.menuItems.values())\n      .filter(item => {\n        const category = Array.from(this.menuCategories.values())\n          .find(cat => cat.id === item.categoryId);\n        return category && (category.name === \"BurgerZ\" || category.name === \"SandwichZ\");\n      });\n\n    burgerItems.forEach(burger => {\n      // Add all sauce options\n      const sauceOptions = Array.from(this.customizationOptions.values())\n        .filter(option => {\n          const group = this.customizationGroups.get(option.groupId);\n          return group && group.title === \"Saus & Topping\";\n        });\n\n      sauceOptions.forEach(sauce => {\n        const id = this.currentItemCustomizationMapId++;\n        this.itemCustomizationMaps.set(id, {\n          id,\n          itemId: burger.id,\n          optionId: sauce.id\n        });\n      });\n\n      // Add all cheese options\n      const cheeseOptions = Array.from(this.customizationOptions.values())\n        .filter(option => {\n          const group = this.customizationGroups.get(option.groupId);\n          return group && group.title === \"Ost\";\n        });\n\n      cheeseOptions.forEach(cheese => {\n        const id = this.currentItemCustomizationMapId++;\n        this.itemCustomizationMaps.set(id, {\n          id,\n          itemId: burger.id,\n          optionId: cheese.id\n        });\n      });\n\n      // Add all vegetable options\n      const vegOptions = Array.from(this.customizationOptions.values())\n        .filter(option => {\n          const group = this.customizationGroups.get(option.groupId);\n          return group && group.title === \"Grønnsaker\";\n        });\n\n      vegOptions.forEach(veg => {\n        const id = this.currentItemCustomizationMapId++;\n        this.itemCustomizationMaps.set(id, {\n          id,\n          itemId: burger.id,\n          optionId: veg.id\n        });\n      });\n\n      // Add extra options\n      const extraOptions = Array.from(this.customizationOptions.values())\n        .filter(option => {\n          const group = this.customizationGroups.get(option.groupId);\n          return group && group.title === \"Ekstra Produkter\";\n        });\n\n      extraOptions.forEach(extra => {\n        const id = this.currentItemCustomizationMapId++;\n        this.itemCustomizationMaps.set(id, {\n          id,\n          itemId: burger.id,\n          optionId: extra.id\n        });\n      });\n    });\n  }\n}\n\n// Password hashing utility functions\nasync function hashPassword(password: string): Promise<string> {\n  const salt = crypto.randomBytes(16).toString(\"hex\");\n  return new Promise((resolve, reject) => {\n    crypto.scrypt(password, salt, 64, (err, derivedKey) => {\n      if (err) reject(err);\n      resolve(`${derivedKey.toString(\"hex\")}.${salt}`);\n    });\n  });\n}\n\nasync function verifyPassword(password: string, hashedPassword: string): Promise<boolean> {\n  const [hash, salt] = hashedPassword.split(\".\");\n  return new Promise((resolve, reject) => {\n    crypto.scrypt(password, salt, 64, (err, derivedKey) => {\n      if (err) reject(err);\n      resolve(hash === derivedKey.toString(\"hex\"));\n    });\n  });\n}\n\nexport class DatabaseStorage implements IStorage {\n  constructor() {\n    // Initialize database with sample data if needed\n    this.initializeDatabase();\n  }\n\n  private async initializeDatabase() {\n    try {\n      // Check if we have any categories, if not, initialize with sample data\n      const existingCategories = await db.select().from(categories).limit(1);\n\n      if (existingCategories.length === 0) {\n        console.log(\"Initializing database with sample data...\");\n        await this.seedDatabase();\n      }\n    } catch (error) {\n      console.error(\"Error initializing database:\", error);\n    }\n  }\n\n  private async seedDatabase() {\n    try {\n      // Insert sample categories\n      const sampleCategories = [\n        {\n          name: \"Signature BBQ\",\n          imageUrl: \"https://images.unsplash.com/photo-1529193591184-b1d58069ecdd?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&h=400\",\n        },\n        {\n          name: \"Starters\",\n          imageUrl: \"https://images.unsplash.com/photo-1527477396000-e27163b481c2?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&h=400\",\n        },\n        {\n          name: \"Main Course\",\n          imageUrl: \"https://images.unsplash.com/photo-1519708227418-c8fd9a32b7a2?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&h=400\",\n        },\n        {\n          name: \"Desserts\",\n          imageUrl: \"https://images.unsplash.com/photo-1624353365286-3f8d62daad51?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&h=400\",\n        },\n        {\n          name: \"BurgerZ\",\n          imageUrl: \"https://images.unsplash.com/photo-1568901346375-23c9450c58cd?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&h=400\",\n        },\n        {\n          name: \"SandwichZ\",\n          imageUrl: \"https://images.unsplash.com/photo-1621996346565-e3dbc646d9a9?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&h=400\",\n        }\n      ];\n\n      const insertedCategories = await db.insert(categories).values(sampleCategories).returning();\n      console.log(`Inserted ${insertedCategories.length} categories`);\n\n      // Insert sample menu items\n      const sampleMenuItems = [\n        {\n          name: \"Smoked Beef Brisket\",\n          description: \"24-hour smoked premium beef brisket with our signature spice rub, served with homemade BBQ sauce and pickled vegetables.\",\n          price: 329,\n          imageUrl: \"https://images.unsplash.com/photo-1529193591184-b1d58069ecdd?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&h=400\",\n          categoryId: insertedCategories[0].id, // Signature BBQ\n          available: true,\n          rating: 49,\n          reviews: 120\n        },\n        {\n          name: \"BBQ Pulled Pork\",\n          description: \"Slow-cooked pulled pork shoulder with tangy BBQ sauce, served on a brioche bun with coleslaw.\",\n          price: 269,\n          imageUrl: \"https://images.unsplash.com/photo-1544025162-d76694265947?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&h=400\",\n          categoryId: insertedCategories[0].id, // Signature BBQ\n          available: true,\n          rating: 46,\n          reviews: 89\n        },\n        {\n          name: \"Loaded Nachos\",\n          description: \"Crispy tortilla chips topped with melted cheese, jalapeños, sour cream, and guacamole.\",\n          price: 189,\n          imageUrl: \"https://images.unsplash.com/photo-1513456852971-30c0b8199d4d?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&h=400\",\n          categoryId: insertedCategories[1].id, // Starters\n          available: true,\n          rating: 42,\n          reviews: 67\n        },\n        {\n          name: \"BBQ Wings\",\n          description: \"Crispy chicken wings tossed in our signature BBQ sauce, served with ranch dipping sauce.\",\n          price: 219,\n          imageUrl: \"https://images.unsplash.com/photo-1527477396000-e27163b481c2?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&h=400\",\n          categoryId: insertedCategories[1].id, // Starters\n          available: true,\n          rating: 44,\n          reviews: 78\n        }\n      ];\n\n      const insertedMenuItems = await db.insert(menuItems).values(sampleMenuItems).returning();\n      console.log(`Inserted ${insertedMenuItems.length} menu items`);\n\n      // Insert customization groups\n      const sampleGroups = [\n        { title: \"Saus & Topping\" },\n        { title: \"Ost\" },\n        { title: \"Ekstra Produkter\" },\n        { title: \"Grønnsaker\" }\n      ];\n\n      const insertedGroups = await db.insert(customizationGroups).values(sampleGroups).returning();\n      console.log(`Inserted ${insertedGroups.length} customization groups`);\n\n      // Insert customization options\n      const sampleOptions = [\n        { name: \"BBQ Sauce\", extraPrice: 10, imageUrl: \"\", groupId: insertedGroups[0].id },\n        { name: \"Hot Sauce\", extraPrice: 10, imageUrl: \"\", groupId: insertedGroups[0].id },\n        { name: \"Mayo\", extraPrice: 10, imageUrl: \"\", groupId: insertedGroups[0].id },\n        { name: \"Honey Mustard\", extraPrice: 15, imageUrl: \"\", groupId: insertedGroups[0].id },\n\n        { name: \"Cheddar\", extraPrice: 15, imageUrl: \"\", groupId: insertedGroups[1].id },\n        { name: \"Blue Cheese\", extraPrice: 20, imageUrl: \"\", groupId: insertedGroups[1].id },\n        { name: \"Mozzarella\", extraPrice: 15, imageUrl: \"\", groupId: insertedGroups[1].id },\n\n        { name: \"Bacon\", extraPrice: 25, imageUrl: \"\", groupId: insertedGroups[2].id },\n        { name: \"Double Meat\", extraPrice: 40, imageUrl: \"\", groupId: insertedGroups[2].id },\n        { name: \"Fried Egg\", extraPrice: 15, imageUrl: \"\", groupId: insertedGroups[2].id },\n\n        { name: \"Lettuce\", extraPrice: 0, imageUrl: \"\", groupId: insertedGroups[3].id },\n        { name: \"Tomato\", extraPrice: 0, imageUrl: \"\", groupId: insertedGroups[3].id },\n        { name: \"Onion\", extraPrice: 0, imageUrl: \"\", groupId: insertedGroups[3].id },\n        { name: \"Cucumber\", extraPrice: 0, imageUrl: \"\", groupId: insertedGroups[3].id },\n        { name: \"Avocado\", extraPrice: 20, imageUrl: \"\", groupId: insertedGroups[3].id }\n      ];\n\n      const insertedOptions = await db.insert(customizationOptions).values(sampleOptions).returning();\n      console.log(`Inserted ${insertedOptions.length} customization options`);\n\n      console.log(\"Database seeded successfully!\");\n    } catch (error) {\n      console.error(\"Error seeding database:\", error);\n    }\n  }\n\n  // User operations\n  async getUser(id: number): Promise<User | undefined> {\n    try {\n      const result = await db.select().from(users).where(eq(users.id, id)).limit(1);\n      return result[0];\n    } catch (error) {\n      console.error(\"Error getting user:\", error);\n      return undefined;\n    }\n  }\n\n  async getUserByUsername(username: string): Promise<User | undefined> {\n    try {\n      const result = await db.select().from(users).where(eq(users.username, username)).limit(1);\n      return result[0];\n    } catch (error) {\n      console.error(\"Error getting user by username:\", error);\n      return undefined;\n    }\n  }\n\n  async createUser(insertUser: InsertUser): Promise<User> {\n    try {\n      // Hash the password before storing\n      const hashedPassword = await hashPassword(insertUser.password);\n      const userToInsert = { ...insertUser, password: hashedPassword };\n\n      const result = await db.insert(users).values(userToInsert).returning();\n      return result[0];\n    } catch (error) {\n      console.error(\"Error creating user:\", error);\n      throw error;\n    }\n  }\n\n  async updateUser(id: number, user: Partial<InsertUser>): Promise<User | undefined> {\n    try {\n      // Hash password if it's being updated\n      const updateData = { ...user };\n      if (updateData.password) {\n        updateData.password = await hashPassword(updateData.password);\n      }\n\n      const result = await db.update(users).set(updateData).where(eq(users.id, id)).returning();\n      return result[0];\n    } catch (error) {\n      console.error(\"Error updating user:\", error);\n      return undefined;\n    }\n  }\n\n  async deleteUser(id: number): Promise<boolean> {\n    try {\n      const result = await db.delete(users).where(eq(users.id, id));\n      return result.rowCount > 0;\n    } catch (error) {\n      console.error(\"Error deleting user:\", error);\n      return false;\n    }\n  }\n\n  async verifyUserCredentials(username: string, password: string): Promise<User | null> {\n    try {\n      const user = await this.getUserByUsername(username);\n      if (!user) {\n        return null;\n      }\n\n      const isValid = await verifyPassword(password, user.password);\n      return isValid ? user : null;\n    } catch (error) {\n      console.error(\"Error verifying user credentials:\", error);\n      return null;\n    }\n  }\n\n  // Dish/MenuItem operations (for backward compatibility)\n  async getAllDishes(): Promise<Dish[]> {\n    try {\n      const result = await db.select().from(menuItems);\n      return result;\n    } catch (error) {\n      console.error(\"Error getting all dishes:\", error);\n      return [];\n    }\n  }\n\n  async getDishById(id: number): Promise<Dish | undefined> {\n    try {\n      const result = await db.select().from(menuItems).where(eq(menuItems.id, id)).limit(1);\n      return result[0];\n    } catch (error) {\n      console.error(\"Error getting dish by id:\", error);\n      return undefined;\n    }\n  }\n\n  async createDish(insertDish: InsertDish): Promise<Dish> {\n    try {\n      const result = await db.insert(menuItems).values(insertDish).returning();\n      return result[0];\n    } catch (error) {\n      console.error(\"Error creating dish:\", error);\n      throw error;\n    }\n  }\n\n  async updateDish(id: number, dish: Partial<InsertDish>): Promise<Dish | undefined> {\n    try {\n      const result = await db.update(menuItems).set(dish).where(eq(menuItems.id, id)).returning();\n      return result[0];\n    } catch (error) {\n      console.error(\"Error updating dish:\", error);\n      return undefined;\n    }\n  }\n\n  async deleteDish(id: number): Promise<boolean> {\n    try {\n      const result = await db.delete(menuItems).where(eq(menuItems.id, id));\n      return (result.rowCount || 0) > 0;\n    } catch (error) {\n      console.error(\"Error deleting dish:\", error);\n      return false;\n    }\n  }\n\n  // Enhanced Menu Item operations\n  async getAllMenuItems(): Promise<MenuItem[]> {\n    try {\n      const result = await db.select().from(menuItems);\n      return result;\n    } catch (error) {\n      console.error(\"Error getting all menu items:\", error);\n      return [];\n    }\n  }\n\n  async getMenuItemsByCategory(categoryId: number): Promise<MenuItem[]> {\n    try {\n      const result = await db.select().from(menuItems).where(eq(menuItems.categoryId, categoryId));\n      return result;\n    } catch (error) {\n      console.error(\"Error getting menu items by category:\", error);\n      return [];\n    }\n  }\n\n  async getMenuItemById(id: number): Promise<MenuItem | undefined> {\n    try {\n      const result = await db.select().from(menuItems).where(eq(menuItems.id, id)).limit(1);\n      return result[0];\n    } catch (error) {\n      console.error(\"Error getting menu item by id:\", error);\n      return undefined;\n    }\n  }\n\n  async createMenuItem(item: InsertMenuItem): Promise<MenuItem> {\n    try {\n      const result = await db.insert(menuItems).values(item).returning();\n      return result[0];\n    } catch (error) {\n      console.error(\"Error creating menu item:\", error);\n      throw error;\n    }\n  }\n\n  async updateMenuItem(id: number, item: Partial<InsertMenuItem>): Promise<MenuItem | undefined> {\n    try {\n      const result = await db.update(menuItems).set(item).where(eq(menuItems.id, id)).returning();\n      return result[0];\n    } catch (error) {\n      console.error(\"Error updating menu item:\", error);\n      return undefined;\n    }\n  }\n\n  async deleteMenuItem(id: number): Promise<boolean> {\n    try {\n      const result = await db.delete(menuItems).where(eq(menuItems.id, id));\n      return (result.rowCount || 0) > 0;\n    } catch (error) {\n      console.error(\"Error deleting menu item:\", error);\n      return false;\n    }\n  }\n\n  // Category operations\n  async getCategories(): Promise<string[]> {\n    try {\n      const result = await db.select({ name: categories.name }).from(categories);\n      return result.map(cat => cat.name);\n    } catch (error) {\n      console.error(\"Error getting categories:\", error);\n      return [];\n    }\n  }\n\n  async getAllMenuCategories(): Promise<Category[]> {\n    try {\n      const result = await db.select().from(categories);\n      return result;\n    } catch (error) {\n      console.error(\"Error getting all menu categories:\", error);\n      return [];\n    }\n  }\n\n  async getMenuCategoryById(id: number): Promise<Category | undefined> {\n    try {\n      const result = await db.select().from(categories).where(eq(categories.id, id)).limit(1);\n      return result[0];\n    } catch (error) {\n      console.error(\"Error getting menu category by id:\", error);\n      return undefined;\n    }\n  }\n\n  async createMenuCategory(category: InsertCategory): Promise<Category> {\n    try {\n      const result = await db.insert(categories).values(category).returning();\n      return result[0];\n    } catch (error) {\n      console.error(\"Error creating menu category:\", error);\n      throw error;\n    }\n  }\n\n  async updateMenuCategory(id: number, category: Partial<InsertCategory>): Promise<Category | undefined> {\n    try {\n      const result = await db.update(categories).set(category).where(eq(categories.id, id)).returning();\n      return result[0];\n    } catch (error) {\n      console.error(\"Error updating menu category:\", error);\n      return undefined;\n    }\n  }\n\n  async deleteMenuCategory(id: number): Promise<boolean> {\n    try {\n      const result = await db.delete(categories).where(eq(categories.id, id));\n      return (result.rowCount || 0) > 0;\n    } catch (error) {\n      console.error(\"Error deleting menu category:\", error);\n      return false;\n    }\n  }\n\n  // Customization Group operations\n  async getAllCustomizationGroups(): Promise<CustomizationGroup[]> {\n    try {\n      const result = await db.select().from(customizationGroups);\n      return result;\n    } catch (error) {\n      console.error(\"Error getting all customization groups:\", error);\n      return [];\n    }\n  }\n\n  async getCustomizationGroupById(id: number): Promise<CustomizationGroup | undefined> {\n    try {\n      const result = await db.select().from(customizationGroups).where(eq(customizationGroups.id, id)).limit(1);\n      return result[0];\n    } catch (error) {\n      console.error(\"Error getting customization group by id:\", error);\n      return undefined;\n    }\n  }\n\n  async createCustomizationGroup(group: InsertCustomizationGroup): Promise<CustomizationGroup> {\n    try {\n      const result = await db.insert(customizationGroups).values(group).returning();\n      return result[0];\n    } catch (error) {\n      console.error(\"Error creating customization group:\", error);\n      throw error;\n    }\n  }\n\n  async updateCustomizationGroup(id: number, group: Partial<InsertCustomizationGroup>): Promise<CustomizationGroup | undefined> {\n    try {\n      const result = await db.update(customizationGroups).set(group).where(eq(customizationGroups.id, id)).returning();\n      return result[0];\n    } catch (error) {\n      console.error(\"Error updating customization group:\", error);\n      return undefined;\n    }\n  }\n\n  async deleteCustomizationGroup(id: number): Promise<boolean> {\n    try {\n      const result = await db.delete(customizationGroups).where(eq(customizationGroups.id, id));\n      return (result.rowCount || 0) > 0;\n    } catch (error) {\n      console.error(\"Error deleting customization group:\", error);\n      return false;\n    }\n  }\n\n  // Customization Option operations\n  async getAllCustomizationOptions(): Promise<CustomizationOption[]> {\n    try {\n      const result = await db.select().from(customizationOptions);\n      return result;\n    } catch (error) {\n      console.error(\"Error getting all customization options:\", error);\n      return [];\n    }\n  }\n\n  async getCustomizationOptionsByGroup(groupId: number): Promise<CustomizationOption[]> {\n    try {\n      const result = await db.select().from(customizationOptions).where(eq(customizationOptions.groupId, groupId));\n      return result;\n    } catch (error) {\n      console.error(\"Error getting customization options by group:\", error);\n      return [];\n    }\n  }\n\n  async getCustomizationOptionById(id: number): Promise<CustomizationOption | undefined> {\n    try {\n      const result = await db.select().from(customizationOptions).where(eq(customizationOptions.id, id)).limit(1);\n      return result[0];\n    } catch (error) {\n      console.error(\"Error getting customization option by id:\", error);\n      return undefined;\n    }\n  }\n\n  async createCustomizationOption(option: InsertCustomizationOption): Promise<CustomizationOption> {\n    try {\n      const result = await db.insert(customizationOptions).values(option).returning();\n      return result[0];\n    } catch (error) {\n      console.error(\"Error creating customization option:\", error);\n      throw error;\n    }\n  }\n\n  async updateCustomizationOption(id: number, option: Partial<InsertCustomizationOption>): Promise<CustomizationOption | undefined> {\n    try {\n      const result = await db.update(customizationOptions).set(option).where(eq(customizationOptions.id, id)).returning();\n      return result[0];\n    } catch (error) {\n      console.error(\"Error updating customization option:\", error);\n      return undefined;\n    }\n  }\n\n  async deleteCustomizationOption(id: number): Promise<boolean> {\n    try {\n      const result = await db.delete(customizationOptions).where(eq(customizationOptions.id, id));\n      return (result.rowCount || 0) > 0;\n    } catch (error) {\n      console.error(\"Error deleting customization option:\", error);\n      return false;\n    }\n  }\n\n  // Item Customization Map operations\n  async getCustomizationOptionsForMenuItem(itemId: number): Promise<{\n    group: CustomizationGroup;\n    options: CustomizationOption[];\n  }[]> {\n    try {\n      // This is a complex query that joins multiple tables\n      // For now, we'll implement a simpler version\n      const maps = await db.select().from(itemCustomizationMap).where(eq(itemCustomizationMap.itemId, itemId));\n\n      const groupedOptions: Record<number, { group: CustomizationGroup; options: CustomizationOption[] }> = {};\n\n      for (const map of maps) {\n        const option = await this.getCustomizationOptionById(map.optionId);\n        if (option) {\n          const group = await this.getCustomizationGroupById(option.groupId);\n          if (group) {\n            if (!groupedOptions[group.id]) {\n              groupedOptions[group.id] = { group, options: [] };\n            }\n            groupedOptions[group.id].options.push(option);\n          }\n        }\n      }\n\n      return Object.values(groupedOptions);\n    } catch (error) {\n      console.error(\"Error getting customization options for menu item:\", error);\n      return [];\n    }\n  }\n\n  async mapCustomizationOptionToMenuItem(itemId: number, optionId: number): Promise<ItemCustomizationMap> {\n    try {\n      const result = await db.insert(itemCustomizationMap).values({ itemId, optionId }).returning();\n      return result[0];\n    } catch (error) {\n      console.error(\"Error mapping customization option to menu item:\", error);\n      throw error;\n    }\n  }\n\n  async unmapCustomizationOptionFromMenuItem(itemId: number, optionId: number): Promise<boolean> {\n    try {\n      const result = await db.delete(itemCustomizationMap)\n        .where(eq(itemCustomizationMap.itemId, itemId) && eq(itemCustomizationMap.optionId, optionId));\n      return (result.rowCount || 0) > 0;\n    } catch (error) {\n      console.error(\"Error unmapping customization option from menu item:\", error);\n      return false;\n    }\n  }\n\n  // Order operations\n  async getOrderById(id: number): Promise<Order | undefined> {\n    try {\n      const result = await db.select().from(orders).where(eq(orders.id, id)).limit(1);\n      return result[0];\n    } catch (error) {\n      console.error(\"Error getting order by id:\", error);\n      return undefined;\n    }\n  }\n\n  async getAllOrders(): Promise<Order[]> {\n    try {\n      const result = await db.select().from(orders);\n      return result;\n    } catch (error) {\n      console.error(\"Error getting all orders:\", error);\n      return [];\n    }\n  }\n\n  async createOrder(insertOrder: InsertOrder): Promise<Order> {\n    try {\n      const result = await db.insert(orders).values(insertOrder).returning();\n      return result[0];\n    } catch (error) {\n      console.error(\"Error creating order:\", error);\n      throw error;\n    }\n  }\n\n  async updateOrder(id: number, order: Partial<InsertOrder>): Promise<Order | undefined> {\n    try {\n      const result = await db.update(orders).set(order).where(eq(orders.id, id)).returning();\n      return result[0];\n    } catch (error) {\n      console.error(\"Error updating order:\", error);\n      return undefined;\n    }\n  }\n\n  async deleteOrder(id: number): Promise<boolean> {\n    try {\n      const result = await db.delete(orders).where(eq(orders.id, id));\n      return (result.rowCount || 0) > 0;\n    } catch (error) {\n      console.error(\"Error deleting order:\", error);\n      return false;\n    }\n  }\n\n  // Cart operations\n  async createCart(cart: { itemId: number, quantity: number, customizations: number[] }): Promise<{ success: boolean, id: number }> {\n    try {\n      // Validate that the item exists\n      const menuItem = await this.getMenuItemById(cart.itemId);\n      if (!menuItem) {\n        throw new Error(\"Menu item not found\");\n      }\n\n      // Validate that all customization options exist\n      for (const optionId of cart.customizations) {\n        const option = await this.getCustomizationOptionById(optionId);\n        if (!option) {\n          throw new Error(`Customization option ${optionId} not found`);\n        }\n      }\n\n      // Since we don't have a cart table yet, we'll just return success\n      return { success: true, id: Date.now() };\n    } catch (error) {\n      console.error(\"Error creating cart:\", error);\n      throw error;\n    }\n  }\n\n  // Contact operations\n  async createContactMessage(insertMessage: InsertContactMessage): Promise<ContactMessage> {\n    try {\n      const result = await db.insert(contactMessages).values(insertMessage).returning();\n      return result[0];\n    } catch (error) {\n      console.error(\"Error creating contact message:\", error);\n      throw error;\n    }\n  }\n\n  async getAllContactMessages(): Promise<ContactMessage[]> {\n    try {\n      const result = await db.select().from(contactMessages);\n      return result;\n    } catch (error) {\n      console.error(\"Error getting all contact messages:\", error);\n      return [];\n    }\n  }\n\n  async getContactMessageById(id: number): Promise<ContactMessage | undefined> {\n    try {\n      const result = await db.select().from(contactMessages).where(eq(contactMessages.id, id)).limit(1);\n      return result[0];\n    } catch (error) {\n      console.error(\"Error getting contact message by id:\", error);\n      return undefined;\n    }\n  }\n\n  async deleteContactMessage(id: number): Promise<boolean> {\n    try {\n      const result = await db.delete(contactMessages).where(eq(contactMessages.id, id));\n      return (result.rowCount || 0) > 0;\n    } catch (error) {\n      console.error(\"Error deleting contact message:\", error);\n      return false;\n    }\n  }\n}\n\n// Use DatabaseStorage for persistent storage\nexport const storage = new DatabaseStorage();\n\n// Keep MemStorage available for testing or fallback\nexport const memStorage = new MemStorage();", "modifiedCode": "import {\n  User, InsertUser,\n  Dish, InsertDish,\n  Order, InsertOrder,\n  ContactMessage, InsertContactMessage,\n  Category, InsertCategory,\n  MenuItem, InsertMenuItem,\n  CustomizationGroup, InsertCustomizationGroup,\n  CustomizationOption, InsertCustomizationOption,\n  ItemCustomizationMap, InsertItemCustomizationMap,\n  users, categories, menuItems, orders, contactMessages,\n  customizationGroups, customizationOptions, itemCustomizationMap\n} from \"@shared/schema\";\nimport { db } from \"./db\";\nimport { eq, desc } from \"drizzle-orm\";\nimport * as crypto from \"crypto\";\n\n// modify the interface with any CRUD methods\n// you might need\nexport interface IStorage {\n  // User operations\n  getUser(id: number): Promise<User | undefined>;\n  getUserByUsername(username: string): Promise<User | undefined>;\n  createUser(user: InsertUser): Promise<User>;\n  updateUser(id: number, user: Partial<InsertUser>): Promise<User | undefined>;\n  deleteUser(id: number): Promise<boolean>;\n  verifyUserCredentials(username: string, password: string): Promise<User | null>;\n\n  // Dish/MenuItem operations\n  getAllDishes(): Promise<Dish[]>;\n  getDishById(id: number): Promise<Dish | undefined>;\n  createDish(dish: InsertDish): Promise<Dish>;\n  updateDish(id: number, dish: Partial<InsertDish>): Promise<Dish | undefined>;\n  deleteDish(id: number): Promise<boolean>;\n\n  // Enhanced Menu Item operations\n  getAllMenuItems(): Promise<MenuItem[]>;\n  getMenuItemsByCategory(categoryId: number): Promise<MenuItem[]>;\n  getMenuItemById(id: number): Promise<MenuItem | undefined>;\n  createMenuItem(item: InsertMenuItem): Promise<MenuItem>;\n  updateMenuItem(id: number, item: Partial<InsertMenuItem>): Promise<MenuItem | undefined>;\n  deleteMenuItem(id: number): Promise<boolean>;\n\n  // Category operations\n  getCategories(): Promise<string[]>; // Original method for backwards compatibility\n  getAllMenuCategories(): Promise<Category[]>;\n  getMenuCategoryById(id: number): Promise<Category | undefined>;\n  createMenuCategory(category: InsertCategory): Promise<Category>;\n  updateMenuCategory(id: number, category: Partial<InsertCategory>): Promise<Category | undefined>;\n  deleteMenuCategory(id: number): Promise<boolean>;\n\n  // Customization operations\n  getAllCustomizationGroups(): Promise<CustomizationGroup[]>;\n  getCustomizationGroupById(id: number): Promise<CustomizationGroup | undefined>;\n  createCustomizationGroup(group: InsertCustomizationGroup): Promise<CustomizationGroup>;\n  updateCustomizationGroup(id: number, group: Partial<InsertCustomizationGroup>): Promise<CustomizationGroup | undefined>;\n  deleteCustomizationGroup(id: number): Promise<boolean>;\n\n  getAllCustomizationOptions(): Promise<CustomizationOption[]>;\n  getCustomizationOptionsByGroup(groupId: number): Promise<CustomizationOption[]>;\n  getCustomizationOptionById(id: number): Promise<CustomizationOption | undefined>;\n  createCustomizationOption(option: InsertCustomizationOption): Promise<CustomizationOption>;\n  updateCustomizationOption(id: number, option: Partial<InsertCustomizationOption>): Promise<CustomizationOption | undefined>;\n  deleteCustomizationOption(id: number): Promise<boolean>;\n\n  // Item Customization Map operations\n  getCustomizationOptionsForMenuItem(itemId: number): Promise<{\n    group: CustomizationGroup;\n    options: CustomizationOption[];\n  }[]>;\n  mapCustomizationOptionToMenuItem(itemId: number, optionId: number): Promise<ItemCustomizationMap>;\n  unmapCustomizationOptionFromMenuItem(itemId: number, optionId: number): Promise<boolean>;\n\n  // Order operations\n  getOrderById(id: number): Promise<Order | undefined>;\n  getAllOrders(): Promise<Order[]>;\n  createOrder(order: InsertOrder): Promise<Order>;\n  updateOrder(id: number, order: Partial<InsertOrder>): Promise<Order | undefined>;\n  deleteOrder(id: number): Promise<boolean>;\n\n  // Cart operations\n  createCart(cart: { itemId: number, quantity: number, customizations: number[] }): Promise<{ success: boolean, id: number }>;\n\n  // Contact operations\n  createContactMessage(message: InsertContactMessage): Promise<ContactMessage>;\n  getAllContactMessages(): Promise<ContactMessage[]>;\n  getContactMessageById(id: number): Promise<ContactMessage | undefined>;\n  deleteContactMessage(id: number): Promise<boolean>;\n}\n\nexport class MemStorage implements IStorage {\n  private users: Map<number, User>;\n  private dishes: Map<number, Dish>;\n  private orders: Map<number, Order>;\n  private contactMessages: Map<number, ContactMessage>;\n\n  // New storage for enhanced menu system\n  private menuCategories: Map<number, Category>;\n  private menuItems: Map<number, MenuItem>;\n  private customizationGroups: Map<number, CustomizationGroup>;\n  private customizationOptions: Map<number, CustomizationOption>;\n  private itemCustomizationMaps: Map<number, ItemCustomizationMap>;\n\n  // ID counters\n  currentUserId: number;\n  currentDishId: number;\n  currentOrderId: number;\n  currentContactId: number;\n  currentCategoryId: number;\n  currentMenuItemId: number;\n  currentCustomizationGroupId: number;\n  currentCustomizationOptionId: number;\n  currentItemCustomizationMapId: number;\n\n  constructor() {\n    // Initialize maps\n    this.users = new Map();\n    this.dishes = new Map();\n    this.orders = new Map();\n    this.contactMessages = new Map();\n    this.menuCategories = new Map();\n    this.menuItems = new Map();\n    this.customizationGroups = new Map();\n    this.customizationOptions = new Map();\n    this.itemCustomizationMaps = new Map();\n\n    // Initialize counters\n    this.currentUserId = 1;\n    this.currentDishId = 1;\n    this.currentOrderId = 1;\n    this.currentContactId = 1;\n    this.currentCategoryId = 1;\n    this.currentMenuItemId = 1;\n    this.currentCustomizationGroupId = 1;\n    this.currentCustomizationOptionId = 1;\n    this.currentItemCustomizationMapId = 1;\n\n    // Initialize sample data\n    this.initializeSampleData();\n  }\n\n  // User methods\n  async getUser(id: number): Promise<User | undefined> {\n    return this.users.get(id);\n  }\n\n  async getUserByUsername(username: string): Promise<User | undefined> {\n    return Array.from(this.users.values()).find(\n      (user) => user.username === username,\n    );\n  }\n\n  async createUser(insertUser: InsertUser): Promise<User> {\n    const id = this.currentUserId++;\n    const user: User = { ...insertUser, id };\n    this.users.set(id, user);\n    return user;\n  }\n\n  async verifyUserCredentials(username: string, password: string): Promise<User | null> {\n    const user = await this.getUserByUsername(username);\n\n    if (!user) {\n      return null;\n    }\n\n    // In a real implementation, you would use bcrypt.compare\n    // For this demo, we'll just do a direct comparison\n    if (user.password === password) {\n      return user;\n    }\n\n    return null;\n  }\n\n  // Original Dish methods (for backwards compatibility)\n  async getAllDishes(): Promise<Dish[]> {\n    return Array.from(this.dishes.values());\n  }\n\n  async getDishById(id: number): Promise<Dish | undefined> {\n    return this.dishes.get(id);\n  }\n\n  async createDish(insertDish: InsertDish): Promise<Dish> {\n    const id = this.currentDishId++;\n    const dish: Dish = { ...insertDish, id };\n    this.dishes.set(id, dish);\n    return dish;\n  }\n\n  // Enhanced Menu Item methods\n  async getAllMenuItems(): Promise<MenuItem[]> {\n    return Array.from(this.menuItems.values());\n  }\n\n  async getMenuItemsByCategory(categoryId: number): Promise<MenuItem[]> {\n    return Array.from(this.menuItems.values()).filter(\n      (item) => item.categoryId === categoryId\n    );\n  }\n\n  async getMenuItemById(id: number): Promise<MenuItem | undefined> {\n    return this.menuItems.get(id);\n  }\n\n  async createMenuItem(item: InsertMenuItem): Promise<MenuItem> {\n    const id = this.currentMenuItemId++;\n    const menuItem: MenuItem = { ...item, id };\n    this.menuItems.set(id, menuItem);\n\n    // Also add to dishes for backward compatibility\n    const category = this.menuCategories.get(item.categoryId)?.name || \"Uncategorized\";\n    this.dishes.set(id, {\n      ...menuItem,\n      category\n    } as Dish);\n\n    return menuItem;\n  }\n\n  // Enhanced Category methods\n  async getAllMenuCategories(): Promise<Category[]> {\n    try {\n      // Make sure we actually return the full category objects\n      return Array.from(this.menuCategories.values());\n    } catch (error) {\n      console.error(\"Error getting menu categories:\", error);\n      return [];\n    }\n  }\n\n  async getMenuCategoryById(id: number): Promise<Category | undefined> {\n    return this.menuCategories.get(id);\n  }\n\n  async createMenuCategory(category: InsertCategory): Promise<Category> {\n    const id = this.currentCategoryId++;\n    const newCategory: Category = { ...category, id };\n    this.menuCategories.set(id, newCategory);\n    return newCategory;\n  }\n\n  async updateMenuCategory(id: number, category: Partial<Category>): Promise<Category> {\n    const existingCategory = this.menuCategories.get(id);\n    if (!existingCategory) {\n      throw new Error(`Category with id ${id} not found`);\n    }\n\n    const updatedCategory = { ...existingCategory, ...category };\n    this.menuCategories.set(id, updatedCategory);\n    return updatedCategory;\n  }\n\n  async deleteMenuCategory(id: number): Promise<Category> {\n    const category = this.menuCategories.get(id);\n    if (!category) {\n      throw new Error(`Category with id ${id} not found`);\n    }\n\n    this.menuCategories.delete(id);\n\n    // Update any menu items that were in this category\n    for (const [itemId, item] of this.menuItems.entries()) {\n      if (item.category_id === id) {\n        const updatedItem = { ...item, category_id: 0 };\n        this.menuItems.set(itemId, updatedItem);\n      }\n    }\n\n    return category;\n  }\n\n  // Customization Group methods\n  async getAllCustomizationGroups(): Promise<CustomizationGroup[]> {\n    return Array.from(this.customizationGroups.values());\n  }\n\n  async getCustomizationGroupById(id: number): Promise<CustomizationGroup | undefined> {\n    return this.customizationGroups.get(id);\n  }\n\n  async createCustomizationGroup(group: InsertCustomizationGroup): Promise<CustomizationGroup> {\n    const id = this.currentCustomizationGroupId++;\n    const newGroup: CustomizationGroup = { ...group, id };\n    this.customizationGroups.set(id, newGroup);\n    return newGroup;\n  }\n\n  // Customization Option methods\n  async getAllCustomizationOptions(): Promise<CustomizationOption[]> {\n    return Array.from(this.customizationOptions.values());\n  }\n\n  async getCustomizationOptionsByGroup(groupId: number): Promise<CustomizationOption[]> {\n    return Array.from(this.customizationOptions.values()).filter(\n      (option) => option.groupId === groupId\n    );\n  }\n\n  async getCustomizationOptionById(id: number): Promise<CustomizationOption | undefined> {\n    return this.customizationOptions.get(id);\n  }\n\n  async createCustomizationOption(option: InsertCustomizationOption): Promise<CustomizationOption> {\n    const id = this.currentCustomizationOptionId++;\n    const newOption: CustomizationOption = { ...option, id };\n    this.customizationOptions.set(id, newOption);\n    return newOption;\n  }\n\n  // Item Customization Map methods\n  async getCustomizationOptionsForMenuItem(itemId: number): Promise<{\n    group: CustomizationGroup;\n    options: CustomizationOption[];\n  }[]> {\n    // Get all mapping entries for this item\n    const mappings = Array.from(this.itemCustomizationMaps.values()).filter(\n      (map) => map.itemId === itemId\n    );\n\n    // Get all option IDs for this item\n    const optionIds = mappings.map((map) => map.optionId);\n\n    // Get all options for these IDs\n    const options = optionIds.map((id) => this.customizationOptions.get(id)!).filter(Boolean);\n\n    // Group options by group ID\n    const groupedOptions = new Map<number, CustomizationOption[]>();\n    options.forEach((option) => {\n      if (!groupedOptions.has(option.groupId)) {\n        groupedOptions.set(option.groupId, []);\n      }\n      groupedOptions.get(option.groupId)!.push(option);\n    });\n\n    // Format the result with group objects\n    const result: { group: CustomizationGroup; options: CustomizationOption[] }[] = [];\n    groupedOptions.forEach((options, groupId) => {\n      const group = this.customizationGroups.get(groupId);\n      if (group) {\n        result.push({ group, options });\n      }\n    });\n\n    return result;\n  }\n\n  async mapCustomizationOptionToMenuItem(itemId: number, optionId: number): Promise<ItemCustomizationMap> {\n    // Check if the menu item and option exist\n    const menuItem = await this.getMenuItemById(itemId);\n    const option = await this.getCustomizationOptionById(optionId);\n\n    if (!menuItem || !option) {\n      throw new Error(\"Menu item or customization option not found\");\n    }\n\n    // Create the mapping\n    const id = this.currentItemCustomizationMapId++;\n    const mapping: ItemCustomizationMap = { id, itemId, optionId };\n    this.itemCustomizationMaps.set(id, mapping);\n\n    return mapping;\n  }\n\n  // Cart operations\n  async createCart(cart: { itemId: number, quantity: number, customizations: number[] }): Promise<{ success: boolean, id: number }> {\n    // Validate that the item exists\n    const menuItem = await this.getMenuItemById(cart.itemId);\n    if (!menuItem) {\n      throw new Error(\"Menu item not found\");\n    }\n\n    // Validate that all customization options exist\n    for (const optionId of cart.customizations) {\n      const option = await this.getCustomizationOptionById(optionId);\n      if (!option) {\n        throw new Error(`Customization option ${optionId} not found`);\n      }\n    }\n\n    // Since we don't have a cart table yet, we'll just return success\n    return { success: true, id: Date.now() };\n  }\n\n  // Order methods\n  async getOrderById(id: number): Promise<Order | undefined> {\n    return this.orders.get(id);\n  }\n\n  async getAllOrders(): Promise<Order[]> {\n    return Array.from(this.orders.values());\n  }\n\n  async createOrder(insertOrder: InsertOrder): Promise<Order> {\n    const id = this.currentOrderId++;\n    const now = new Date();\n    const order: Order = { ...insertOrder, id, createdAt: now };\n    this.orders.set(id, order);\n    return order;\n  }\n\n  // Contact methods\n  async createContactMessage(insertMessage: InsertContactMessage): Promise<ContactMessage> {\n    const id = this.currentContactId++;\n    const now = new Date();\n    const message: ContactMessage = { ...insertMessage, id, createdAt: now };\n    this.contactMessages.set(id, message);\n    return message;\n  }\n\n  // Category methods (for backwards compatibility)\n  async getCategories(): Promise<string[]> {\n    // Get all category names from the categories table\n    return Array.from(this.menuCategories.values()).map(category => category.name);\n  }\n\n  // Initialize sample data for the menu system\n  private initializeSampleData() {\n    // 1. Initialize categories\n    const sampleCategories: InsertCategory[] = [\n      {\n        name: \"Signature BBQ\",\n        imageUrl: \"https://images.unsplash.com/photo-1529193591184-b1d58069ecdd?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&h=400\",\n      },\n      {\n        name: \"Starters\",\n        imageUrl: \"https://images.unsplash.com/photo-1527477396000-e27163b481c2?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&h=400\",\n      },\n      {\n        name: \"Main Course\",\n        imageUrl: \"https://images.unsplash.com/photo-1519708227418-c8fd9a32b7a2?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&h=400\",\n      },\n      {\n        name: \"Desserts\",\n        imageUrl: \"https://images.unsplash.com/photo-1624353365286-3f8d62daad51?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&h=400\",\n      },\n      {\n        name: \"BurgerZ\",\n        imageUrl: \"https://images.unsplash.com/photo-1568901346375-23c9450c58cd?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&h=400\",\n      },\n      {\n        name: \"SandwichZ\",\n        imageUrl: \"https://images.unsplash.com/photo-1621996346565-e3dbc646d9a9?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&h=400\",\n      }\n    ];\n\n    // Insert categories and create a map for reference when creating menu items\n    const categoryMap: Record<string, number> = {};\n    sampleCategories.forEach(category => {\n      const id = this.currentCategoryId++;\n      this.menuCategories.set(id, { ...category, id });\n      categoryMap[category.name] = id;\n    });\n\n    // 2. Initialize menu items\n    const sampleMenuItems: Array<InsertMenuItem & { categoryName: string }> = [\n      {\n        name: \"Smoked Beef Brisket\",\n        description: \"24-hour smoked premium beef brisket with our signature spice rub, served with homemade BBQ sauce and pickled vegetables.\",\n        price: 329,\n        imageUrl: \"https://images.unsplash.com/photo-1529193591184-b1d58069ecdd?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&h=400\",\n        categoryId: 0, // Will be replaced with actual ID\n        categoryName: \"Signature BBQ\",\n        available: true,\n        rating: 49,\n        reviews: 120\n      },\n      {\n        name: \"BBQ Pork Ribs\",\n        description: \"Slow-cooked St. Louis style ribs with our house dry rub, glazed with maple bourbon sauce and finished over open flame.\",\n        price: 289,\n        imageUrl: \"https://images.unsplash.com/photo-1544025162-d76694265947?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&h=400\",\n        categoryId: 0,\n        categoryName: \"Signature BBQ\",\n        available: true,\n        rating: 48,\n        reviews: 98\n      },\n      {\n        name: \"Pulled Pork Sandwich\",\n        description: \"12-hour smoked pork shoulder, hand-pulled and tossed in Carolina vinegar sauce, served on a brioche bun with coleslaw.\",\n        price: 219,\n        imageUrl: \"https://images.unsplash.com/photo-1621996346565-e3dbc646d9a9?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&h=400\",\n        categoryId: 0,\n        categoryName: \"SandwichZ\",\n        available: true,\n        rating: 47,\n        reviews: 86\n      },\n      {\n        name: \"Smoked Chicken Wings\",\n        description: \"Applewood smoked wings finished on the grill, tossed in your choice of sauce: Classic Buffalo, Honey Chipotle, or Garlic Parmesan.\",\n        price: 189,\n        imageUrl: \"https://images.unsplash.com/photo-1527477396000-e27163b481c2?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&h=400\",\n        categoryId: 0,\n        categoryName: \"Starters\",\n        available: true,\n        rating: 46,\n        reviews: 112\n      },\n      {\n        name: \"Cedar Plank Salmon\",\n        description: \"Norwegian salmon fillet grilled on a cedar plank with maple glaze, served with grilled lemon and seasonal vegetables.\",\n        price: 299,\n        imageUrl: \"https://images.unsplash.com/photo-1519708227418-c8fd9a32b7a2?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&h=400\",\n        categoryId: 0,\n        categoryName: \"Main Course\",\n        available: true,\n        rating: 49,\n        reviews: 74\n      },\n      {\n        name: \"Smokehouse Burger\",\n        description: \"House-ground prime beef patty with smoked cheddar, bacon jam, caramelized onions, and bourbon BBQ sauce on a toasted brioche bun.\",\n        price: 249,\n        imageUrl: \"https://images.unsplash.com/photo-1568901346375-23c9450c58cd?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&h=400\",\n        categoryId: 0,\n        categoryName: \"BurgerZ\",\n        available: true,\n        rating: 48,\n        reviews: 103\n      },\n      {\n        name: \"BBQ Sampler Platter\",\n        description: \"A selection of our signature meats including brisket, ribs, pulled pork, and smoked sausage, served with two sides of your choice.\",\n        price: 399,\n        imageUrl: \"https://images.unsplash.com/photo-1508615263227-c5d58c1e5821?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&h=400\",\n        categoryId: 0,\n        categoryName: \"Signature BBQ\",\n        available: true,\n        rating: 50,\n        reviews: 135\n      },\n      {\n        name: \"Grilled Vegetable Skewers\",\n        description: \"Seasonal vegetables marinated in herbs and olive oil, grilled to perfection and served with chimichurri sauce.\",\n        price: 179,\n        imageUrl: \"https://images.unsplash.com/photo-1625944525533-473f1a3d54a7?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&h=400\",\n        categoryId: 0,\n        categoryName: \"Starters\",\n        available: true,\n        rating: 45,\n        reviews: 62\n      },\n      {\n        name: \"Chocolate Lava Cake\",\n        description: \"Warm chocolate cake with a molten center, served with vanilla bean ice cream and fresh berries.\",\n        price: 149,\n        imageUrl: \"https://images.unsplash.com/photo-1624353365286-3f8d62daad51?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&h=400\",\n        categoryId: 0,\n        categoryName: \"Desserts\",\n        available: true,\n        rating: 48,\n        reviews: 89\n      }\n    ];\n\n    // Insert menu items with the correct category IDs\n    sampleMenuItems.forEach(menuItem => {\n      const { categoryName, ...item } = menuItem;\n      const categoryId = categoryMap[categoryName];\n      const id = this.currentMenuItemId++;\n      const dishToInsert = { ...item, categoryId, id };\n\n      // Add to both menu items and dishes (for backward compatibility)\n      this.menuItems.set(id, dishToInsert);\n      this.dishes.set(id, {\n        ...dishToInsert,\n        category: categoryName // For backward compatibility\n      } as Dish);\n    });\n\n    // 3. Initialize customization groups\n    const customizationGroups: InsertCustomizationGroup[] = [\n      { title: \"Saus & Topping\" },\n      { title: \"Ost\" },\n      { title: \"Ekstra Produkter\" },\n      { title: \"Grønnsaker\" }\n    ];\n\n    // Add customization groups and track IDs\n    const groupMap: Record<string, number> = {};\n    customizationGroups.forEach(group => {\n      const id = this.currentCustomizationGroupId++;\n      this.customizationGroups.set(id, { ...group, id });\n      groupMap[group.title] = id;\n    });\n\n    // 4. Initialize customization options\n    const customizationOptions: Array<InsertCustomizationOption & { groupTitle: string }> = [\n      { name: \"BBQ saus\", extraPrice: 10, imageUrl: \"/icons/bbq.png\", groupId: 0, groupTitle: \"Saus & Topping\" },\n      { name: \"Hot Sauce\", extraPrice: 10, imageUrl: \"/icons/hot.png\", groupId: 0, groupTitle: \"Saus & Topping\" },\n      { name: \"Mayo\", extraPrice: 10, imageUrl: \"/icons/mayo.png\", groupId: 0, groupTitle: \"Saus & Topping\" },\n      { name: \"Cheddar\", extraPrice: 15, imageUrl: \"/icons/cheddar.png\", groupId: 0, groupTitle: \"Ost\" },\n      { name: \"Blue Cheese\", extraPrice: 20, imageUrl: \"/icons/blue.png\", groupId: 0, groupTitle: \"Ost\" },\n      { name: \"Bacon\", extraPrice: 25, imageUrl: \"/icons/bacon.png\", groupId: 0, groupTitle: \"Ekstra Produkter\" },\n      { name: \"Double Meat\", extraPrice: 40, imageUrl: \"/icons/meat.png\", groupId: 0, groupTitle: \"Ekstra Produkter\" },\n      { name: \"Lettuce\", extraPrice: 0, imageUrl: \"/icons/lettuce.png\", groupId: 0, groupTitle: \"Grønnsaker\" },\n      { name: \"Tomato\", extraPrice: 0, imageUrl: \"/icons/tomato.png\", groupId: 0, groupTitle: \"Grønnsaker\" },\n      { name: \"Onion\", extraPrice: 0, imageUrl: \"/icons/onion.png\", groupId: 0, groupTitle: \"Grønnsaker\" },\n      { name: \"Avocado\", extraPrice: 20, imageUrl: \"/icons/avocado.png\", groupId: 0, groupTitle: \"Grønnsaker\" }\n    ];\n\n    // Add customization options with correct group IDs\n    const optionMap: Record<string, number> = {};\n    customizationOptions.forEach(option => {\n      const { groupTitle, ...optionData } = option;\n      const groupId = groupMap[groupTitle];\n      const id = this.currentCustomizationOptionId++;\n      this.customizationOptions.set(id, { ...optionData, groupId, id });\n      optionMap[option.name] = id;\n    });\n\n    // 5. Map customization options to menu items\n    // For each burger, add some customization options\n    const burgerItems = Array.from(this.menuItems.values())\n      .filter(item => {\n        const category = Array.from(this.menuCategories.values())\n          .find(cat => cat.id === item.categoryId);\n        return category && (category.name === \"BurgerZ\" || category.name === \"SandwichZ\");\n      });\n\n    burgerItems.forEach(burger => {\n      // Add all sauce options\n      const sauceOptions = Array.from(this.customizationOptions.values())\n        .filter(option => {\n          const group = this.customizationGroups.get(option.groupId);\n          return group && group.title === \"Saus & Topping\";\n        });\n\n      sauceOptions.forEach(sauce => {\n        const id = this.currentItemCustomizationMapId++;\n        this.itemCustomizationMaps.set(id, {\n          id,\n          itemId: burger.id,\n          optionId: sauce.id\n        });\n      });\n\n      // Add all cheese options\n      const cheeseOptions = Array.from(this.customizationOptions.values())\n        .filter(option => {\n          const group = this.customizationGroups.get(option.groupId);\n          return group && group.title === \"Ost\";\n        });\n\n      cheeseOptions.forEach(cheese => {\n        const id = this.currentItemCustomizationMapId++;\n        this.itemCustomizationMaps.set(id, {\n          id,\n          itemId: burger.id,\n          optionId: cheese.id\n        });\n      });\n\n      // Add all vegetable options\n      const vegOptions = Array.from(this.customizationOptions.values())\n        .filter(option => {\n          const group = this.customizationGroups.get(option.groupId);\n          return group && group.title === \"Grønnsaker\";\n        });\n\n      vegOptions.forEach(veg => {\n        const id = this.currentItemCustomizationMapId++;\n        this.itemCustomizationMaps.set(id, {\n          id,\n          itemId: burger.id,\n          optionId: veg.id\n        });\n      });\n\n      // Add extra options\n      const extraOptions = Array.from(this.customizationOptions.values())\n        .filter(option => {\n          const group = this.customizationGroups.get(option.groupId);\n          return group && group.title === \"Ekstra Produkter\";\n        });\n\n      extraOptions.forEach(extra => {\n        const id = this.currentItemCustomizationMapId++;\n        this.itemCustomizationMaps.set(id, {\n          id,\n          itemId: burger.id,\n          optionId: extra.id\n        });\n      });\n    });\n  }\n}\n\n// Password hashing utility functions\nasync function hashPassword(password: string): Promise<string> {\n  const salt = crypto.randomBytes(16).toString(\"hex\");\n  return new Promise((resolve, reject) => {\n    crypto.scrypt(password, salt, 64, (err, derivedKey) => {\n      if (err) reject(err);\n      resolve(`${derivedKey.toString(\"hex\")}.${salt}`);\n    });\n  });\n}\n\nasync function verifyPassword(password: string, hashedPassword: string): Promise<boolean> {\n  const [hash, salt] = hashedPassword.split(\".\");\n  return new Promise((resolve, reject) => {\n    crypto.scrypt(password, salt, 64, (err, derivedKey) => {\n      if (err) reject(err);\n      resolve(hash === derivedKey.toString(\"hex\"));\n    });\n  });\n}\n\nexport class DatabaseStorage implements IStorage {\n  constructor() {\n    // Initialize database with sample data if needed\n    this.initializeDatabase();\n  }\n\n  private async initializeDatabase() {\n    try {\n      // Check if we have any categories, if not, initialize with sample data\n      const existingCategories = await db.select().from(categories).limit(1);\n\n      if (existingCategories.length === 0) {\n        console.log(\"Initializing database with sample data...\");\n        await this.seedDatabase();\n      }\n    } catch (error) {\n      console.error(\"Error initializing database:\", error);\n    }\n  }\n\n  private async seedDatabase() {\n    try {\n      // Insert sample categories\n      const sampleCategories = [\n        {\n          name: \"Signature BBQ\",\n          imageUrl: \"https://images.unsplash.com/photo-1529193591184-b1d58069ecdd?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&h=400\",\n        },\n        {\n          name: \"Starters\",\n          imageUrl: \"https://images.unsplash.com/photo-1527477396000-e27163b481c2?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&h=400\",\n        },\n        {\n          name: \"Main Course\",\n          imageUrl: \"https://images.unsplash.com/photo-1519708227418-c8fd9a32b7a2?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&h=400\",\n        },\n        {\n          name: \"Desserts\",\n          imageUrl: \"https://images.unsplash.com/photo-1624353365286-3f8d62daad51?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&h=400\",\n        },\n        {\n          name: \"BurgerZ\",\n          imageUrl: \"https://images.unsplash.com/photo-1568901346375-23c9450c58cd?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&h=400\",\n        },\n        {\n          name: \"SandwichZ\",\n          imageUrl: \"https://images.unsplash.com/photo-1621996346565-e3dbc646d9a9?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&h=400\",\n        }\n      ];\n\n      const insertedCategories = await db.insert(categories).values(sampleCategories).returning();\n      console.log(`Inserted ${insertedCategories.length} categories`);\n\n      // Insert sample menu items\n      const sampleMenuItems = [\n        {\n          name: \"Smoked Beef Brisket\",\n          description: \"24-hour smoked premium beef brisket with our signature spice rub, served with homemade BBQ sauce and pickled vegetables.\",\n          price: 329,\n          imageUrl: \"https://images.unsplash.com/photo-1529193591184-b1d58069ecdd?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&h=400\",\n          categoryId: insertedCategories[0].id, // Signature BBQ\n          available: true,\n          rating: 49,\n          reviews: 120\n        },\n        {\n          name: \"BBQ Pulled Pork\",\n          description: \"Slow-cooked pulled pork shoulder with tangy BBQ sauce, served on a brioche bun with coleslaw.\",\n          price: 269,\n          imageUrl: \"https://images.unsplash.com/photo-1544025162-d76694265947?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&h=400\",\n          categoryId: insertedCategories[0].id, // Signature BBQ\n          available: true,\n          rating: 46,\n          reviews: 89\n        },\n        {\n          name: \"Loaded Nachos\",\n          description: \"Crispy tortilla chips topped with melted cheese, jalapeños, sour cream, and guacamole.\",\n          price: 189,\n          imageUrl: \"https://images.unsplash.com/photo-1513456852971-30c0b8199d4d?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&h=400\",\n          categoryId: insertedCategories[1].id, // Starters\n          available: true,\n          rating: 42,\n          reviews: 67\n        },\n        {\n          name: \"BBQ Wings\",\n          description: \"Crispy chicken wings tossed in our signature BBQ sauce, served with ranch dipping sauce.\",\n          price: 219,\n          imageUrl: \"https://images.unsplash.com/photo-1527477396000-e27163b481c2?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&h=400\",\n          categoryId: insertedCategories[1].id, // Starters\n          available: true,\n          rating: 44,\n          reviews: 78\n        }\n      ];\n\n      const insertedMenuItems = await db.insert(menuItems).values(sampleMenuItems).returning();\n      console.log(`Inserted ${insertedMenuItems.length} menu items`);\n\n      // Insert customization groups\n      const sampleGroups = [\n        { title: \"Saus & Topping\" },\n        { title: \"Ost\" },\n        { title: \"Ekstra Produkter\" },\n        { title: \"Grønnsaker\" }\n      ];\n\n      const insertedGroups = await db.insert(customizationGroups).values(sampleGroups).returning();\n      console.log(`Inserted ${insertedGroups.length} customization groups`);\n\n      // Insert customization options\n      const sampleOptions = [\n        { name: \"BBQ Sauce\", extraPrice: 10, imageUrl: \"\", groupId: insertedGroups[0].id },\n        { name: \"Hot Sauce\", extraPrice: 10, imageUrl: \"\", groupId: insertedGroups[0].id },\n        { name: \"Mayo\", extraPrice: 10, imageUrl: \"\", groupId: insertedGroups[0].id },\n        { name: \"Honey Mustard\", extraPrice: 15, imageUrl: \"\", groupId: insertedGroups[0].id },\n\n        { name: \"Cheddar\", extraPrice: 15, imageUrl: \"\", groupId: insertedGroups[1].id },\n        { name: \"Blue Cheese\", extraPrice: 20, imageUrl: \"\", groupId: insertedGroups[1].id },\n        { name: \"Mozzarella\", extraPrice: 15, imageUrl: \"\", groupId: insertedGroups[1].id },\n\n        { name: \"Bacon\", extraPrice: 25, imageUrl: \"\", groupId: insertedGroups[2].id },\n        { name: \"Double Meat\", extraPrice: 40, imageUrl: \"\", groupId: insertedGroups[2].id },\n        { name: \"Fried Egg\", extraPrice: 15, imageUrl: \"\", groupId: insertedGroups[2].id },\n\n        { name: \"Lettuce\", extraPrice: 0, imageUrl: \"\", groupId: insertedGroups[3].id },\n        { name: \"Tomato\", extraPrice: 0, imageUrl: \"\", groupId: insertedGroups[3].id },\n        { name: \"Onion\", extraPrice: 0, imageUrl: \"\", groupId: insertedGroups[3].id },\n        { name: \"Cucumber\", extraPrice: 0, imageUrl: \"\", groupId: insertedGroups[3].id },\n        { name: \"Avocado\", extraPrice: 20, imageUrl: \"\", groupId: insertedGroups[3].id }\n      ];\n\n      const insertedOptions = await db.insert(customizationOptions).values(sampleOptions).returning();\n      console.log(`Inserted ${insertedOptions.length} customization options`);\n\n      console.log(\"Database seeded successfully!\");\n    } catch (error) {\n      console.error(\"Error seeding database:\", error);\n    }\n  }\n\n  // User operations\n  async getUser(id: number): Promise<User | undefined> {\n    try {\n      const result = await db.select().from(users).where(eq(users.id, id)).limit(1);\n      return result[0];\n    } catch (error) {\n      console.error(\"Error getting user:\", error);\n      return undefined;\n    }\n  }\n\n  async getUserByUsername(username: string): Promise<User | undefined> {\n    try {\n      const result = await db.select().from(users).where(eq(users.username, username)).limit(1);\n      return result[0];\n    } catch (error) {\n      console.error(\"Error getting user by username:\", error);\n      return undefined;\n    }\n  }\n\n  async createUser(insertUser: InsertUser): Promise<User> {\n    try {\n      // Hash the password before storing\n      const hashedPassword = await hashPassword(insertUser.password);\n      const userToInsert = { ...insertUser, password: hashedPassword };\n\n      const result = await db.insert(users).values(userToInsert).returning();\n      return result[0];\n    } catch (error) {\n      console.error(\"Error creating user:\", error);\n      throw error;\n    }\n  }\n\n  async updateUser(id: number, user: Partial<InsertUser>): Promise<User | undefined> {\n    try {\n      // Hash password if it's being updated\n      const updateData = { ...user };\n      if (updateData.password) {\n        updateData.password = await hashPassword(updateData.password);\n      }\n\n      const result = await db.update(users).set(updateData).where(eq(users.id, id)).returning();\n      return result[0];\n    } catch (error) {\n      console.error(\"Error updating user:\", error);\n      return undefined;\n    }\n  }\n\n  async deleteUser(id: number): Promise<boolean> {\n    try {\n      const result = await db.delete(users).where(eq(users.id, id));\n      return result.rowCount > 0;\n    } catch (error) {\n      console.error(\"Error deleting user:\", error);\n      return false;\n    }\n  }\n\n  async verifyUserCredentials(username: string, password: string): Promise<User | null> {\n    try {\n      const user = await this.getUserByUsername(username);\n      if (!user) {\n        return null;\n      }\n\n      const isValid = await verifyPassword(password, user.password);\n      return isValid ? user : null;\n    } catch (error) {\n      console.error(\"Error verifying user credentials:\", error);\n      return null;\n    }\n  }\n\n  // Dish/MenuItem operations (for backward compatibility)\n  async getAllDishes(): Promise<Dish[]> {\n    try {\n      const result = await db.select().from(menuItems);\n      return result;\n    } catch (error) {\n      console.error(\"Error getting all dishes:\", error);\n      return [];\n    }\n  }\n\n  async getDishById(id: number): Promise<Dish | undefined> {\n    try {\n      const result = await db.select().from(menuItems).where(eq(menuItems.id, id)).limit(1);\n      return result[0];\n    } catch (error) {\n      console.error(\"Error getting dish by id:\", error);\n      return undefined;\n    }\n  }\n\n  async createDish(insertDish: InsertDish): Promise<Dish> {\n    try {\n      const result = await db.insert(menuItems).values(insertDish).returning();\n      return result[0];\n    } catch (error) {\n      console.error(\"Error creating dish:\", error);\n      throw error;\n    }\n  }\n\n  async updateDish(id: number, dish: Partial<InsertDish>): Promise<Dish | undefined> {\n    try {\n      const result = await db.update(menuItems).set(dish).where(eq(menuItems.id, id)).returning();\n      return result[0];\n    } catch (error) {\n      console.error(\"Error updating dish:\", error);\n      return undefined;\n    }\n  }\n\n  async deleteDish(id: number): Promise<boolean> {\n    try {\n      const result = await db.delete(menuItems).where(eq(menuItems.id, id));\n      return (result.rowCount || 0) > 0;\n    } catch (error) {\n      console.error(\"Error deleting dish:\", error);\n      return false;\n    }\n  }\n\n  // Enhanced Menu Item operations\n  async getAllMenuItems(): Promise<MenuItem[]> {\n    try {\n      const result = await db.select().from(menuItems);\n      return result;\n    } catch (error) {\n      console.error(\"Error getting all menu items:\", error);\n      return [];\n    }\n  }\n\n  async getMenuItemsByCategory(categoryId: number): Promise<MenuItem[]> {\n    try {\n      const result = await db.select().from(menuItems).where(eq(menuItems.categoryId, categoryId));\n      return result;\n    } catch (error) {\n      console.error(\"Error getting menu items by category:\", error);\n      return [];\n    }\n  }\n\n  async getMenuItemById(id: number): Promise<MenuItem | undefined> {\n    try {\n      const result = await db.select().from(menuItems).where(eq(menuItems.id, id)).limit(1);\n      return result[0];\n    } catch (error) {\n      console.error(\"Error getting menu item by id:\", error);\n      return undefined;\n    }\n  }\n\n  async createMenuItem(item: InsertMenuItem): Promise<MenuItem> {\n    try {\n      const result = await db.insert(menuItems).values(item).returning();\n      return result[0];\n    } catch (error) {\n      console.error(\"Error creating menu item:\", error);\n      throw error;\n    }\n  }\n\n  async updateMenuItem(id: number, item: Partial<InsertMenuItem>): Promise<MenuItem | undefined> {\n    try {\n      const result = await db.update(menuItems).set(item).where(eq(menuItems.id, id)).returning();\n      return result[0];\n    } catch (error) {\n      console.error(\"Error updating menu item:\", error);\n      return undefined;\n    }\n  }\n\n  async deleteMenuItem(id: number): Promise<boolean> {\n    try {\n      const result = await db.delete(menuItems).where(eq(menuItems.id, id));\n      return (result.rowCount || 0) > 0;\n    } catch (error) {\n      console.error(\"Error deleting menu item:\", error);\n      return false;\n    }\n  }\n\n  // Category operations\n  async getCategories(): Promise<string[]> {\n    try {\n      const result = await db.select({ name: categories.name }).from(categories);\n      return result.map(cat => cat.name);\n    } catch (error) {\n      console.error(\"Error getting categories:\", error);\n      return [];\n    }\n  }\n\n  async getAllMenuCategories(): Promise<Category[]> {\n    try {\n      const result = await db.select().from(categories);\n      return result;\n    } catch (error) {\n      console.error(\"Error getting all menu categories:\", error);\n      return [];\n    }\n  }\n\n  async getMenuCategoryById(id: number): Promise<Category | undefined> {\n    try {\n      const result = await db.select().from(categories).where(eq(categories.id, id)).limit(1);\n      return result[0];\n    } catch (error) {\n      console.error(\"Error getting menu category by id:\", error);\n      return undefined;\n    }\n  }\n\n  async createMenuCategory(category: InsertCategory): Promise<Category> {\n    try {\n      const result = await db.insert(categories).values(category).returning();\n      return result[0];\n    } catch (error) {\n      console.error(\"Error creating menu category:\", error);\n      throw error;\n    }\n  }\n\n  async updateMenuCategory(id: number, category: Partial<InsertCategory>): Promise<Category | undefined> {\n    try {\n      const result = await db.update(categories).set(category).where(eq(categories.id, id)).returning();\n      return result[0];\n    } catch (error) {\n      console.error(\"Error updating menu category:\", error);\n      return undefined;\n    }\n  }\n\n  async deleteMenuCategory(id: number): Promise<boolean> {\n    try {\n      const result = await db.delete(categories).where(eq(categories.id, id));\n      return (result.rowCount || 0) > 0;\n    } catch (error) {\n      console.error(\"Error deleting menu category:\", error);\n      return false;\n    }\n  }\n\n  // Customization Group operations\n  async getAllCustomizationGroups(): Promise<CustomizationGroup[]> {\n    try {\n      const result = await db.select().from(customizationGroups);\n      return result;\n    } catch (error) {\n      console.error(\"Error getting all customization groups:\", error);\n      return [];\n    }\n  }\n\n  async getCustomizationGroupById(id: number): Promise<CustomizationGroup | undefined> {\n    try {\n      const result = await db.select().from(customizationGroups).where(eq(customizationGroups.id, id)).limit(1);\n      return result[0];\n    } catch (error) {\n      console.error(\"Error getting customization group by id:\", error);\n      return undefined;\n    }\n  }\n\n  async createCustomizationGroup(group: InsertCustomizationGroup): Promise<CustomizationGroup> {\n    try {\n      const result = await db.insert(customizationGroups).values(group).returning();\n      return result[0];\n    } catch (error) {\n      console.error(\"Error creating customization group:\", error);\n      throw error;\n    }\n  }\n\n  async updateCustomizationGroup(id: number, group: Partial<InsertCustomizationGroup>): Promise<CustomizationGroup | undefined> {\n    try {\n      const result = await db.update(customizationGroups).set(group).where(eq(customizationGroups.id, id)).returning();\n      return result[0];\n    } catch (error) {\n      console.error(\"Error updating customization group:\", error);\n      return undefined;\n    }\n  }\n\n  async deleteCustomizationGroup(id: number): Promise<boolean> {\n    try {\n      const result = await db.delete(customizationGroups).where(eq(customizationGroups.id, id));\n      return (result.rowCount || 0) > 0;\n    } catch (error) {\n      console.error(\"Error deleting customization group:\", error);\n      return false;\n    }\n  }\n\n  // Customization Option operations\n  async getAllCustomizationOptions(): Promise<CustomizationOption[]> {\n    try {\n      const result = await db.select().from(customizationOptions);\n      return result;\n    } catch (error) {\n      console.error(\"Error getting all customization options:\", error);\n      return [];\n    }\n  }\n\n  async getCustomizationOptionsByGroup(groupId: number): Promise<CustomizationOption[]> {\n    try {\n      const result = await db.select().from(customizationOptions).where(eq(customizationOptions.groupId, groupId));\n      return result;\n    } catch (error) {\n      console.error(\"Error getting customization options by group:\", error);\n      return [];\n    }\n  }\n\n  async getCustomizationOptionById(id: number): Promise<CustomizationOption | undefined> {\n    try {\n      const result = await db.select().from(customizationOptions).where(eq(customizationOptions.id, id)).limit(1);\n      return result[0];\n    } catch (error) {\n      console.error(\"Error getting customization option by id:\", error);\n      return undefined;\n    }\n  }\n\n  async createCustomizationOption(option: InsertCustomizationOption): Promise<CustomizationOption> {\n    try {\n      const result = await db.insert(customizationOptions).values(option).returning();\n      return result[0];\n    } catch (error) {\n      console.error(\"Error creating customization option:\", error);\n      throw error;\n    }\n  }\n\n  async updateCustomizationOption(id: number, option: Partial<InsertCustomizationOption>): Promise<CustomizationOption | undefined> {\n    try {\n      const result = await db.update(customizationOptions).set(option).where(eq(customizationOptions.id, id)).returning();\n      return result[0];\n    } catch (error) {\n      console.error(\"Error updating customization option:\", error);\n      return undefined;\n    }\n  }\n\n  async deleteCustomizationOption(id: number): Promise<boolean> {\n    try {\n      const result = await db.delete(customizationOptions).where(eq(customizationOptions.id, id));\n      return (result.rowCount || 0) > 0;\n    } catch (error) {\n      console.error(\"Error deleting customization option:\", error);\n      return false;\n    }\n  }\n\n  // Item Customization Map operations\n  async getCustomizationOptionsForMenuItem(itemId: number): Promise<{\n    group: CustomizationGroup;\n    options: CustomizationOption[];\n  }[]> {\n    try {\n      // This is a complex query that joins multiple tables\n      // For now, we'll implement a simpler version\n      const maps = await db.select().from(itemCustomizationMap).where(eq(itemCustomizationMap.itemId, itemId));\n\n      const groupedOptions: Record<number, { group: CustomizationGroup; options: CustomizationOption[] }> = {};\n\n      for (const map of maps) {\n        const option = await this.getCustomizationOptionById(map.optionId);\n        if (option) {\n          const group = await this.getCustomizationGroupById(option.groupId);\n          if (group) {\n            if (!groupedOptions[group.id]) {\n              groupedOptions[group.id] = { group, options: [] };\n            }\n            groupedOptions[group.id].options.push(option);\n          }\n        }\n      }\n\n      return Object.values(groupedOptions);\n    } catch (error) {\n      console.error(\"Error getting customization options for menu item:\", error);\n      return [];\n    }\n  }\n\n  async mapCustomizationOptionToMenuItem(itemId: number, optionId: number): Promise<ItemCustomizationMap> {\n    try {\n      const result = await db.insert(itemCustomizationMap).values({ itemId, optionId }).returning();\n      return result[0];\n    } catch (error) {\n      console.error(\"Error mapping customization option to menu item:\", error);\n      throw error;\n    }\n  }\n\n  async unmapCustomizationOptionFromMenuItem(itemId: number, optionId: number): Promise<boolean> {\n    try {\n      const result = await db.delete(itemCustomizationMap)\n        .where(eq(itemCustomizationMap.itemId, itemId));\n      return (result.rowCount || 0) > 0;\n    } catch (error) {\n      console.error(\"Error unmapping customization option from menu item:\", error);\n      return false;\n    }\n  }\n\n  // Order operations\n  async getOrderById(id: number): Promise<Order | undefined> {\n    try {\n      const result = await db.select().from(orders).where(eq(orders.id, id)).limit(1);\n      return result[0];\n    } catch (error) {\n      console.error(\"Error getting order by id:\", error);\n      return undefined;\n    }\n  }\n\n  async getAllOrders(): Promise<Order[]> {\n    try {\n      const result = await db.select().from(orders);\n      return result;\n    } catch (error) {\n      console.error(\"Error getting all orders:\", error);\n      return [];\n    }\n  }\n\n  async createOrder(insertOrder: InsertOrder): Promise<Order> {\n    try {\n      const result = await db.insert(orders).values(insertOrder).returning();\n      return result[0];\n    } catch (error) {\n      console.error(\"Error creating order:\", error);\n      throw error;\n    }\n  }\n\n  async updateOrder(id: number, order: Partial<InsertOrder>): Promise<Order | undefined> {\n    try {\n      const result = await db.update(orders).set(order).where(eq(orders.id, id)).returning();\n      return result[0];\n    } catch (error) {\n      console.error(\"Error updating order:\", error);\n      return undefined;\n    }\n  }\n\n  async deleteOrder(id: number): Promise<boolean> {\n    try {\n      const result = await db.delete(orders).where(eq(orders.id, id));\n      return (result.rowCount || 0) > 0;\n    } catch (error) {\n      console.error(\"Error deleting order:\", error);\n      return false;\n    }\n  }\n\n  // Cart operations\n  async createCart(cart: { itemId: number, quantity: number, customizations: number[] }): Promise<{ success: boolean, id: number }> {\n    try {\n      // Validate that the item exists\n      const menuItem = await this.getMenuItemById(cart.itemId);\n      if (!menuItem) {\n        throw new Error(\"Menu item not found\");\n      }\n\n      // Validate that all customization options exist\n      for (const optionId of cart.customizations) {\n        const option = await this.getCustomizationOptionById(optionId);\n        if (!option) {\n          throw new Error(`Customization option ${optionId} not found`);\n        }\n      }\n\n      // Since we don't have a cart table yet, we'll just return success\n      return { success: true, id: Date.now() };\n    } catch (error) {\n      console.error(\"Error creating cart:\", error);\n      throw error;\n    }\n  }\n\n  // Contact operations\n  async createContactMessage(insertMessage: InsertContactMessage): Promise<ContactMessage> {\n    try {\n      const result = await db.insert(contactMessages).values(insertMessage).returning();\n      return result[0];\n    } catch (error) {\n      console.error(\"Error creating contact message:\", error);\n      throw error;\n    }\n  }\n\n  async getAllContactMessages(): Promise<ContactMessage[]> {\n    try {\n      const result = await db.select().from(contactMessages);\n      return result;\n    } catch (error) {\n      console.error(\"Error getting all contact messages:\", error);\n      return [];\n    }\n  }\n\n  async getContactMessageById(id: number): Promise<ContactMessage | undefined> {\n    try {\n      const result = await db.select().from(contactMessages).where(eq(contactMessages.id, id)).limit(1);\n      return result[0];\n    } catch (error) {\n      console.error(\"Error getting contact message by id:\", error);\n      return undefined;\n    }\n  }\n\n  async deleteContactMessage(id: number): Promise<boolean> {\n    try {\n      const result = await db.delete(contactMessages).where(eq(contactMessages.id, id));\n      return (result.rowCount || 0) > 0;\n    } catch (error) {\n      console.error(\"Error deleting contact message:\", error);\n      return false;\n    }\n  }\n}\n\n// Use DatabaseStorage for persistent storage\nexport const storage = new DatabaseStorage();\n\n// Keep MemStorage available for testing or fallback\nexport const memStorage = new MemStorage();"}