{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "DATABASE.md"}, "originalCode": "# Database Implementation\n\nThis document describes the persistent database storage implementation for the restaurant management system.\n\n## Overview\n\nThe application has been upgraded from in-memory storage to persistent PostgreSQL database storage using Drizzle ORM. All CRUD operations for categories, menu items, orders, contact messages, users, and customization options are now persisted to the database.\n\n## Database Schema\n\nThe database uses the following main tables:\n\n### Core Tables\n- **users** - User accounts and authentication data\n- **categories** - Menu categories (e.g., \"BBQ\", \"Starters\", \"Desserts\")\n- **menuItems** - Individual menu items with prices, descriptions, and images\n- **orders** - Customer orders with items and status\n- **contactMessages** - Contact form submissions\n\n### Customization Tables\n- **customizationGroups** - Groups of customization options (e.g., \"Sauces\", \"Toppings\")\n- **customizationOptions** - Individual customization choices with prices\n- **itemCustomizationMap** - Links menu items to available customizations\n\n## Key Features\n\n### 1. Persistent Storage\n- All data is stored in PostgreSQL database\n- Data survives server restarts and page refreshes\n- Automatic database initialization with sample data\n\n### 2. Full CRUD Operations\n- **Create**: Add new categories, menu items, orders, etc.\n- **Read**: Fetch all data with proper relationships\n- **Update**: Modify existing records\n- **Delete**: Remove records with proper cleanup\n\n### 3. Database Initialization\n- Automatic seeding with sample data on first run\n- Default admin user creation (username: \"admin\", password: \"admin123\")\n- Sample categories, menu items, and customization options\n\n### 4. Error Handling\n- Comprehensive error logging\n- Graceful fallbacks for database failures\n- Transaction support for data integrity\n\n## File Structure\n\n```\nserver/\n├── storage.ts          # Main storage interface and DatabaseStorage class\n├── db.ts              # Database connection setup\n├── init-db.ts         # Database initialization and seeding\n├── migrate.ts         # Database migration runner\n├── test-db.ts         # Database operation testing\n└── admin-api.ts       # Admin API routes using database storage\n```\n\n## Usage\n\n### Starting the Application\nThe database is automatically initialized when the server starts:\n\n```bash\nnpm run dev\n```\n\n### Manual Database Operations\n\n```bash\n# Initialize database with sample data\nnpm run db:init\n\n# Push schema changes to database\nnpm run db:push\n\n# Generate migration files\nnpm run db:generate\n\n# Run migrations\nnpm run db:migrate\n\n# Test database operations\nnpm run db:test\n\n# Open Drizzle Studio (database GUI)\nnpm run db:studio\n```\n\n### Environment Variables\nMake sure to set the `DATABASE_URL` environment variable:\n\n```bash\nDATABASE_URL=postgresql://username:password@host:port/database\n```\n\n## API Endpoints\n\n### Categories\n- `GET /api/categories` - List all categories\n- `POST /api/admin/categories` - Create new category\n- `PUT /api/admin/categories/:id` - Update category\n- `DELETE /api/admin/categories/:id` - Delete category\n\n### Menu Items\n- `GET /api/dishes` - List all menu items\n- `POST /api/admin/items` - Create new menu item\n- `PUT /api/admin/items/:id` - Update menu item\n- `DELETE /api/admin/items/:id` - Delete menu item\n\n### Orders\n- `GET /api/admin/orders` - List all orders\n- `POST /api/orders` - Create new order\n- `PUT /api/admin/orders/:id/status` - Update order status\n\n## Data Persistence Testing\n\nTo verify that data persists correctly:\n\n1. Start the application: `npm run dev`\n2. Add a new category through the admin interface\n3. Add a new menu item to that category\n4. Restart the server\n5. Verify that the category and menu item are still there\n\n## Migration from In-Memory Storage\n\nThe system maintains backward compatibility:\n- Old `MemStorage` class is still available as `memStorage`\n- New `DatabaseStorage` class is used by default as `storage`\n- All existing API endpoints work without changes\n- Data structure remains the same\n\n## Troubleshooting\n\n### Database Connection Issues\n- Verify `DATABASE_URL` is set correctly\n- Check database server is running\n- Ensure database exists and is accessible\n\n### Data Not Persisting\n- Check server logs for database errors\n- Verify database connection is established\n- Run `npm run db:test` to test operations\n\n### Schema Issues\n- Run `npm run db:push` to sync schema\n- Check for migration conflicts\n- Use `npm run db:studio` to inspect database\n\n## Security Considerations\n\n- Passwords are hashed using scrypt with salt\n- SQL injection protection through Drizzle ORM\n- Input validation on all API endpoints\n- Error messages don't expose sensitive information\n\n## Performance\n\n- Database queries are optimized with proper indexing\n- Connection pooling for efficient resource usage\n- Lazy loading for large datasets\n- Caching strategies for frequently accessed data\n"}