{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "server/create-tables.ts"}, "modifiedCode": "import { db } from \"./db\";\nimport { \n  categories, menuItems, orders, contactMessages, users,\n  customizationGroups, customizationOptions, itemCustomizationMap\n} from \"@shared/schema\";\n\nasync function createMissingTables() {\n  try {\n    console.log(\"Creating missing database tables...\");\n\n    // Check if tables exist by trying to query them\n    const tablesToCheck = [\n      { name: 'contact_messages', table: contactMessages },\n      { name: 'customization_groups', table: customizationGroups },\n      { name: 'customization_options', table: customizationOptions },\n      { name: 'item_customization_map', table: itemCustomizationMap },\n      { name: 'menu_items', table: menuItems },\n      { name: 'categories', table: categories },\n      { name: 'orders', table: orders },\n      { name: 'users', table: users }\n    ];\n\n    for (const { name, table } of tablesToCheck) {\n      try {\n        await db.select().from(table).limit(1);\n        console.log(`✓ Table ${name} exists`);\n      } catch (error) {\n        console.log(`✗ Table ${name} does not exist or has schema issues`);\n        console.log(`Error: ${error.message}`);\n      }\n    }\n\n    // Try to create missing columns in menu_items table\n    try {\n      console.log(\"\\nAttempting to add missing columns to menu_items...\");\n      \n      // Check if rating column exists\n      try {\n        await db.execute(`ALTER TABLE menu_items ADD COLUMN IF NOT EXISTS rating integer DEFAULT 0`);\n        console.log(\"✓ Added rating column to menu_items\");\n      } catch (error) {\n        console.log(`Rating column: ${error.message}`);\n      }\n\n      // Check if reviews column exists\n      try {\n        await db.execute(`ALTER TABLE menu_items ADD COLUMN IF NOT EXISTS reviews integer DEFAULT 0`);\n        console.log(\"✓ Added reviews column to menu_items\");\n      } catch (error) {\n        console.log(`Reviews column: ${error.message}`);\n      }\n\n    } catch (error) {\n      console.log(`Error modifying menu_items table: ${error.message}`);\n    }\n\n    console.log(\"\\nTable creation check completed!\");\n\n  } catch (error) {\n    console.error(\"Error creating tables:\", error);\n    throw error;\n  }\n}\n\n// Run if this file is executed directly\nif (import.meta.url === `file://${process.argv[1]}`) {\n  createMissingTables()\n    .then(() => {\n      console.log(\"Table creation completed!\");\n      process.exit(0);\n    })\n    .catch((error) => {\n      console.error(\"Table creation failed:\", error);\n      process.exit(1);\n    });\n}\n\nexport { createMissingTables };\n"}