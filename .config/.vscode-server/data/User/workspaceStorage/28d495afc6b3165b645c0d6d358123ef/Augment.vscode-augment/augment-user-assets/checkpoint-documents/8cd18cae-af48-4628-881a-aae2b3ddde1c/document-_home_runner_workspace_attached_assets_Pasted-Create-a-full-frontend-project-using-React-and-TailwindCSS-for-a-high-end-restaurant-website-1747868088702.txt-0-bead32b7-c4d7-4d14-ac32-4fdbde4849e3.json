{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "attached_assets/Pasted-Create-a-full-frontend-project-using-React-and-TailwindCSS-for-a-high-end-restaurant-website-1747868088702.txt"}, "originalCode": "Create a full frontend project using **React** and **TailwindCSS** for a high-end restaurant website named **\"Barbecuez Restaurant\"**.\r\n\r\n### 🎨 Design & UI Requirements:\r\n- The overall design should be **minimalistic** and **luxurious**.\r\n- Use a **black background** (`#000000` or dark gray tones).\r\n- Incorporate **neon-colored interactive elements** for buttons, links, and highlights:\r\n  - Electric Blue: `#00FFFF`\r\n  - Neon Pink: `#FF00FF`\r\n  - Lime Green: `#39FF14`\r\n- Apply a subtle glowing effect using CSS (`box-shadow`, `text-shadow`, or Tailwind's utilities).\r\n- Typography should be clean and elegant. Consider using fonts like `'Playfair Display'` for headings and `'Poppins'` or `'Inter'` for body text.\r\n- Ensure the design is responsive and mobile-friendly.\r\n\r\n### 🧱 Pages to Include:\r\n1. **Home** (`/`)\r\n   - Hero section with restaurant branding, tagline, and a background image or video.\r\n   - Brief introduction about the restaurant and delivery service.\r\n   - Call-to-action button (e.g., “Explore Menu”).\r\n\r\n2. **Menu** (`/menu`)\r\n   - Grid/list of dishes fetched from the backend API.\r\n   - Each item should display: name, description, image, and price in **Norwegian Krone (NOK)**.\r\n   - Add to cart button on each dish.\r\n\r\n3. **Cart** (`/cart`)\r\n   - List of items added by the user.\r\n   - Options to update quantity or remove items.\r\n   - Display subtotal in NOK.\r\n\r\n4. **Orders / Checkout** (`/checkout`)\r\n   - Form to enter customer details (name, address, phone number).\r\n   - Summary of order and total cost.\r\n   - Button to place order (calls backend API).\r\n\r\n5. **Contact** (`/contact`) *(optional placeholder)*\r\n   - A simple contact form or message stating \"Coming Soon\".\r\n\r\n### 🧩 Technical Requirements:\r\n- Use **React Router** for page navigation.\r\n- Use **Axios** or **Fetch API** to connect to a backend (assumed to be running on `http://localhost:5000`).\r\n- All pages should be built as **functional components** using React Hooks.\r\n- Create reusable components such as:\r\n  - Button (with neon styles)\r\n  - Dish Card\r\n  - Header / Navbar (sticky)\r\n  - Footer\r\n  - Loader (for loading states)\r\n\r\n### 📦 Project Structure Suggestion:\r\n/src\r\n/components\r\nButton.jsx\r\nHeader.jsx\r\nFooter.jsx\r\nDishCard.jsx\r\n/pages\r\nHome.jsx\r\nMenu.jsx\r\nCart.jsx\r\nCheckout.jsx\r\nContact.jsx\r\n/api\r\napi.js\r\nApp.jsx\r\nindex.jsx\r\ntailwind.config.js\r\n\r\nmarkdown\r\nCopy\r\nEdit\r\n\r\n### ✅ Output:\r\n- A fully functional, visually luxurious React project ready to be connected to an Express.js backend and PostgreSQL database.\r\n- All placeholder API requests should be pre-configured to allow easy plug-and-play integration.\r\n- Include brief loading states, error handling, and fallbacks where applicable.\r\n- Make sure styles follow the neon + black aesthetic consistently across the app.\r\n", "modifiedCode": "Create a full frontend project using **React** and **TailwindCSS** for a high-end restaurant website named **\"Barbecuez Restaurant\"**.\r\n\r\n### 🎨 Design & UI Requirements:\r\n- The overall design should be **minimalistic** and **luxurious**.\r\n- Use a **black background** (`#000000` or dark gray tones).\r\n- Incorporate **neon-colored interactive elements** for buttons, links, and highlights:\r\n  - Electric Blue: `#00FFFF`\r\n  - Neon Pink: `#FF00FF`\r\n  - Lime Green: `#39FF14`\r\n- Apply a subtle glowing effect using CSS (`box-shadow`, `text-shadow`, or Tailwind's utilities).\r\n- Typography should be clean and elegant. Consider using fonts like `'Playfair Display'` for headings and `'Poppins'` or `'Inter'` for body text.\r\n- Ensure the design is responsive and mobile-friendly.\r\n\r\n### 🧱 Pages to Include:\r\n1. **Home** (`/`)\r\n   - Hero section with restaurant branding, tagline, and a background image or video.\r\n   - Brief introduction about the restaurant and delivery service.\r\n   - Call-to-action button (e.g., “Explore Menu”).\r\n\r\n2. **Menu** (`/menu`)\r\n   - Grid/list of dishes fetched from the backend API.\r\n   - Each item should display: name, description, image, and price in **Norwegian Krone (NOK)**.\r\n   - Add to cart button on each dish.\r\n\r\n3. **Cart** (`/cart`)\r\n   - List of items added by the user.\r\n   - Options to update quantity or remove items.\r\n   - Display subtotal in NOK.\r\n\r\n4. **Orders / Checkout** (`/checkout`)\r\n   - Form to enter customer details (name, address, phone number).\r\n   - Summary of order and total cost.\r\n   - Button to place order (calls backend API).\r\n\r\n5. **Contact** (`/contact`) *(optional placeholder)*\r\n   - A simple contact form or message stating \"Coming Soon\".\r\n\r\n### 🧩 Technical Requirements:\r\n- Use **React Router** for page navigation.\r\n- Use **Axios** or **Fetch API** to connect to a backend (assumed to be running on `http://localhost:5000`).\r\n- All pages should be built as **functional components** using React Hooks.\r\n- Create reusable components such as:\r\n  - Button (with neon styles)\r\n  - Dish Card\r\n  - Header / Navbar (sticky)\r\n  - Footer\r\n  - Loader (for loading states)\r\n\r\n### 📦 Project Structure Suggestion:\r\n/src\r\n/components\r\nButton.jsx\r\nHeader.jsx\r\nFooter.jsx\r\nDishCard.jsx\r\n/pages\r\nHome.jsx\r\nMenu.jsx\r\nCart.jsx\r\nCheckout.jsx\r\nContact.jsx\r\n/api\r\napi.js\r\nApp.jsx\r\nindex.jsx\r\ntailwind.config.js\r\n\r\nmarkdown\r\nCopy\r\nEdit\r\n\r\n### ✅ Output:\r\n- A fully functional, visually luxurious React project ready to be connected to an Express.js backend and PostgreSQL database.\r\n- All placeholder API requests should be pre-configured to allow easy plug-and-play integration.\r\n- Include brief loading states, error handling, and fallbacks where applicable.\r\n- Make sure styles follow the neon + black aesthetic consistently across the app.\r\n"}