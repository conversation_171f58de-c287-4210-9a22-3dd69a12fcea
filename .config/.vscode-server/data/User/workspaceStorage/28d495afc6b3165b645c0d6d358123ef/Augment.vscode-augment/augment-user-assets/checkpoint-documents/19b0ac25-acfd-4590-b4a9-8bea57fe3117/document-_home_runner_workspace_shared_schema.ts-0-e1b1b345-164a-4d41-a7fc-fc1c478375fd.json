{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "shared/schema.ts"}, "originalCode": "import { pgTable, text, serial, integer, boolean, jsonb, timestamp, foreignKey } from \"drizzle-orm/pg-core\";\nimport { createInsertSchema } from \"drizzle-zod\";\nimport { z } from \"zod\";\n\n// Categories Schema\nexport const categories = pgTable(\"categories\", {\n  id: serial(\"id\").primaryKey(),\n  name: text(\"name\").notNull(),\n  imageUrl: text(\"image_url\").notNull(),\n});\n\nexport const insertCategorySchema = createInsertSchema(categories).omit({ id: true });\nexport type InsertCategory = z.infer<typeof insertCategorySchema>;\nexport type Category = typeof categories.$inferSelect;\n\n// Dish Schema (renamed to menu_items to match the new API structure)\nexport const menuItems = pgTable(\"menu_items\", {\n  id: serial(\"id\").primaryKey(),\n  name: text(\"name\").notNull(),\n  description: text(\"description\").notNull(),\n  price: integer(\"price\").notNull(), // Price in NOK\n  imageUrl: text(\"image_url\").notNull(),\n  categoryId: integer(\"category_id\").notNull().references(() => categories.id),\n  available: boolean(\"available\").default(true),\n  rating: integer(\"rating\").default(0),\n  reviews: integer(\"reviews\").default(0),\n});\n\nexport const insertMenuItemSchema = createInsertSchema(menuItems).omit({ id: true });\nexport type InsertMenuItem = z.infer<typeof insertMenuItemSchema>;\nexport type MenuItem = typeof menuItems.$inferSelect;\n\n// Customization Groups Schema\nexport const customizationGroups = pgTable(\"customization_groups\", {\n  id: serial(\"id\").primaryKey(),\n  title: text(\"title\").notNull(),\n});\n\nexport const insertCustomizationGroupSchema = createInsertSchema(customizationGroups).omit({ id: true });\nexport type InsertCustomizationGroup = z.infer<typeof insertCustomizationGroupSchema>;\nexport type CustomizationGroup = typeof customizationGroups.$inferSelect;\n\n// Customization Options Schema\nexport const customizationOptions = pgTable(\"customization_options\", {\n  id: serial(\"id\").primaryKey(),\n  name: text(\"name\").notNull(),\n  extraPrice: integer(\"extra_price\").default(0),\n  imageUrl: text(\"image_url\").notNull().default(\"\"),\n  groupId: integer(\"group_id\").notNull().references(() => customizationGroups.id),\n});\n\nexport const insertCustomizationOptionSchema = createInsertSchema(customizationOptions).omit({ id: true });\nexport type InsertCustomizationOption = z.infer<typeof insertCustomizationOptionSchema>;\nexport type CustomizationOption = typeof customizationOptions.$inferSelect;\n\n// Item Customization Map (many-to-many relationship)\nexport const itemCustomizationMap = pgTable(\"item_customization_map\", {\n  id: serial(\"id\").primaryKey(),\n  itemId: integer(\"item_id\").notNull().references(() => menuItems.id),\n  optionId: integer(\"option_id\").notNull().references(() => customizationOptions.id),\n});\n\nexport const insertItemCustomizationMapSchema = createInsertSchema(itemCustomizationMap).omit({ id: true });\nexport type InsertItemCustomizationMap = z.infer<typeof insertItemCustomizationMapSchema>;\nexport type ItemCustomizationMap = typeof itemCustomizationMap.$inferSelect;\n\n// For backwards compatibility with existing code\nexport const dishes = menuItems;\nexport const insertDishSchema = insertMenuItemSchema;\nexport type InsertDish = InsertMenuItem;\nexport type Dish = MenuItem;\n\n// Order Schema\nexport const orders = pgTable(\"orders\", {\n  id: serial(\"id\").primaryKey(),\n  customer: jsonb(\"customer\").notNull(),\n  items: jsonb(\"items\").notNull(),\n  subtotal: integer(\"subtotal\").notNull(),\n  deliveryFee: integer(\"delivery_fee\").notNull(),\n  total: integer(\"total\").notNull(),\n  status: text(\"status\").notNull().default(\"pending\"),\n  paymentMethod: text(\"payment_method\").notNull(),\n  notes: text(\"notes\"),\n  createdAt: timestamp(\"created_at\").defaultNow(),\n});\n\nexport const insertOrderSchema = createInsertSchema(orders).omit({ \n  id: true, \n  createdAt: true \n});\nexport type InsertOrder = z.infer<typeof insertOrderSchema>;\nexport type Order = typeof orders.$inferSelect;\n\n// Contact Message Schema\nexport const contactMessages = pgTable(\"contact_messages\", {\n  id: serial(\"id\").primaryKey(),\n  name: text(\"name\").notNull(),\n  email: text(\"email\").notNull(),\n  subject: text(\"subject\").notNull(),\n  message: text(\"message\").notNull(),\n  createdAt: timestamp(\"created_at\").defaultNow(),\n});\n\nexport const insertContactSchema = createInsertSchema(contactMessages).omit({ \n  id: true, \n  createdAt: true \n});\nexport type InsertContactMessage = z.infer<typeof insertContactSchema>;\nexport type ContactMessage = typeof contactMessages.$inferSelect;\n\n// User Schema (from original)\nexport const users = pgTable(\"users\", {\n  id: serial(\"id\").primaryKey(),\n  username: text(\"username\").notNull().unique(),\n  password: text(\"password\").notNull(),\n});\n\nexport const insertUserSchema = createInsertSchema(users).pick({\n  username: true,\n  password: true,\n});\n\nexport type InsertUser = z.infer<typeof insertUserSchema>;\nexport type User = typeof users.$inferSelect;\n", "modifiedCode": "import { pgTable, text, serial, integer, boolean, jsonb, timestamp, foreignKey } from \"drizzle-orm/pg-core\";\nimport { createInsertSchema } from \"drizzle-zod\";\nimport { z } from \"zod\";\n\n// Categories Schema\nexport const categories = pgTable(\"categories\", {\n  id: serial(\"id\").primaryKey(),\n  name: text(\"name\").notNull(),\n  imageUrl: text(\"image_url\").notNull(),\n});\n\nexport const insertCategorySchema = createInsertSchema(categories).omit({ id: true });\nexport type InsertCategory = z.infer<typeof insertCategorySchema>;\nexport type Category = typeof categories.$inferSelect;\n\n// Dish Schema (renamed to menu_items to match the new API structure)\nexport const menuItems = pgTable(\"menu_items\", {\n  id: serial(\"id\").primaryKey(),\n  name: text(\"name\").notNull(),\n  description: text(\"description\").notNull(),\n  price: integer(\"price\").notNull(), // Price in NOK\n  imageUrl: text(\"image_url\").notNull(),\n  categoryId: integer(\"category_id\").notNull().references(() => categories.id),\n  available: boolean(\"available\").default(true),\n  rating: integer(\"rating\").default(0),\n  reviews: integer(\"reviews\").default(0),\n});\n\nexport const insertMenuItemSchema = createInsertSchema(menuItems).omit({ id: true });\nexport type InsertMenuItem = z.infer<typeof insertMenuItemSchema>;\nexport type MenuItem = typeof menuItems.$inferSelect;\n\n// Customization Groups Schema\nexport const customizationGroups = pgTable(\"customization_groups\", {\n  id: serial(\"id\").primaryKey(),\n  title: text(\"title\").notNull(),\n});\n\nexport const insertCustomizationGroupSchema = createInsertSchema(customizationGroups).omit({ id: true });\nexport type InsertCustomizationGroup = z.infer<typeof insertCustomizationGroupSchema>;\nexport type CustomizationGroup = typeof customizationGroups.$inferSelect;\n\n// Customization Options Schema\nexport const customizationOptions = pgTable(\"customization_options\", {\n  id: serial(\"id\").primaryKey(),\n  name: text(\"name\").notNull(),\n  extraPrice: integer(\"extra_price\").default(0),\n  imageUrl: text(\"image_url\").notNull().default(\"\"),\n  groupId: integer(\"group_id\").notNull().references(() => customizationGroups.id),\n});\n\nexport const insertCustomizationOptionSchema = createInsertSchema(customizationOptions).omit({ id: true });\nexport type InsertCustomizationOption = z.infer<typeof insertCustomizationOptionSchema>;\nexport type CustomizationOption = typeof customizationOptions.$inferSelect;\n\n// Item Customization Map (many-to-many relationship)\nexport const itemCustomizationMap = pgTable(\"item_customization_map\", {\n  id: serial(\"id\").primaryKey(),\n  itemId: integer(\"item_id\").notNull().references(() => menuItems.id),\n  optionId: integer(\"option_id\").notNull().references(() => customizationOptions.id),\n});\n\nexport const insertItemCustomizationMapSchema = createInsertSchema(itemCustomizationMap).omit({ id: true });\nexport type InsertItemCustomizationMap = z.infer<typeof insertItemCustomizationMapSchema>;\nexport type ItemCustomizationMap = typeof itemCustomizationMap.$inferSelect;\n\n// For backwards compatibility with existing code\nexport const dishes = menuItems;\nexport const insertDishSchema = insertMenuItemSchema;\nexport type InsertDish = InsertMenuItem;\nexport type Dish = MenuItem;\n\n// Order Schema\nexport const orders = pgTable(\"orders\", {\n  id: serial(\"id\").primaryKey(),\n  customer: jsonb(\"customer\").notNull(),\n  items: jsonb(\"items\").notNull(),\n  subtotal: integer(\"subtotal\").notNull(),\n  deliveryFee: integer(\"delivery_fee\").notNull(),\n  total: integer(\"total\").notNull(),\n  status: text(\"status\").notNull().default(\"pending\"),\n  paymentMethod: text(\"payment_method\").notNull(),\n  notes: text(\"notes\"),\n  createdAt: timestamp(\"created_at\").defaultNow(),\n});\n\nexport const insertOrderSchema = createInsertSchema(orders).omit({ \n  id: true, \n  createdAt: true \n});\nexport type InsertOrder = z.infer<typeof insertOrderSchema>;\nexport type Order = typeof orders.$inferSelect;\n\n// Contact Message Schema\nexport const contactMessages = pgTable(\"contact_messages\", {\n  id: serial(\"id\").primaryKey(),\n  name: text(\"name\").notNull(),\n  email: text(\"email\").notNull(),\n  subject: text(\"subject\").notNull(),\n  message: text(\"message\").notNull(),\n  createdAt: timestamp(\"created_at\").defaultNow(),\n});\n\nexport const insertContactSchema = createInsertSchema(contactMessages).omit({ \n  id: true, \n  createdAt: true \n});\nexport type InsertContactMessage = z.infer<typeof insertContactSchema>;\nexport type ContactMessage = typeof contactMessages.$inferSelect;\n\n// User Schema (from original)\nexport const users = pgTable(\"users\", {\n  id: serial(\"id\").primaryKey(),\n  username: text(\"username\").notNull().unique(),\n  password: text(\"password\").notNull(),\n});\n\nexport const insertUserSchema = createInsertSchema(users).pick({\n  username: true,\n  password: true,\n});\n\nexport type InsertUser = z.infer<typeof insertUserSchema>;\nexport type User = typeof users.$inferSelect;\n"}