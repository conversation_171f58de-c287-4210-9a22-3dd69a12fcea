{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/pages/Cart.tsx"}, "originalCode": "import { useState, useEffect } from \"react\";\nimport { <PERSON> } from \"wouter\";\nimport { useCart } from \"@/context/CartContext\";\nimport Button from \"@/components/Button\";\nimport { formatCurrency } from \"@/lib/utils\";\nimport { motion, AnimatePresence } from \"framer-motion\";\nimport { useInView } from \"react-intersection-observer\";\n\nconst Cart = () => {\n  const { cartItems, updateQuantity, removeFromCart } = useCart();\n  const [isUpdating, setIsUpdating] = useState<number | null>(null);\n  const [animatedTotal, setAnimatedTotal] = useState(0);\n  const [estimatedTime, setEstimatedTime] = useState(\"15\");\n\n  // Animation reference for sections\n  const [headerRef, headerInView] = useInView({\n    triggerOnce: true,\n    threshold: 0.1,\n  });\n\n  const [summaryRef, summaryInView] = useInView({\n    triggerOnce: true,\n    threshold: 0.2,\n  });\n\n  // Animation variants\n  const containerVariants = {\n    hidden: { opacity: 0 },\n    visible: {\n      opacity: 1,\n      transition: {\n        staggerChildren: 0.1\n      }\n    }\n  };\n\n  const itemVariants = {\n    hidden: { y: 20, opacity: 0 },\n    visible: {\n      y: 0,\n      opacity: 1,\n      transition: {\n        type: \"spring\",\n        stiffness: 100,\n        damping: 12\n      }\n    },\n    exit: {\n      opacity: 0,\n      x: 20,\n      transition: { duration: 0.3 }\n    }\n  };\n\n  // Calculate order totals\n  const subtotal = cartItems.reduce((total, item) => total + (item.price * item.quantity), 0);\n  const deliveryFee = cartItems.length > 0 ? 89 : 0;\n  const total = subtotal + deliveryFee;\n\n  // Update animated total with smooth animation\n  useEffect(() => {\n    const duration = 1000; // Animation duration in ms\n    const steps = 20; // Number of steps\n    const stepDuration = duration / steps;\n    const increment = (total - animatedTotal) / steps;\n\n    if (Math.abs(total - animatedTotal) < 1) {\n      setAnimatedTotal(total);\n      return;\n    }\n\n    const timer = setTimeout(() => {\n      setAnimatedTotal(prev => {\n        const next = prev + increment;\n        // If we're close enough, just set the exact value\n        if (Math.abs(next - total) < 1) return total;\n        return next;\n      });\n    }, stepDuration);\n\n    return () => clearTimeout(timer);\n  }, [total, animatedTotal]);\n\n  // Calculate estimated time based on cart items\n  useEffect(() => {\n    if (cartItems.length === 0) {\n      setEstimatedTime(\"0\");\n    } else {\n      // Base time + additional time for each item\n      const baseTime = 10;\n      const itemTime = cartItems.reduce((time, item) => time + (item.quantity * 2), 0);\n      setEstimatedTime(Math.min(baseTime + itemTime, 45).toString());\n    }\n  }, [cartItems]);\n\n  const handleQuantityChange = (id: number, newQuantity: number) => {\n    if (newQuantity < 1) return;\n\n    setIsUpdating(id);\n\n    // Simulate a small delay for better UX\n    setTimeout(() => {\n      updateQuantity(id, newQuantity);\n      setIsUpdating(null);\n    }, 300);\n  };\n\n  const handleRemoveItem = (id: number) => {\n    setIsUpdating(id);\n\n    // Simulate a small delay for better UX\n    setTimeout(() => {\n      removeFromCart(id);\n      setIsUpdating(null);\n    }, 300);\n  };\n\n  return (\n    <section className=\"min-h-screen py-32 bg-black relative overflow-hidden\">\n      {/* Animated Gradient Background */}\n      <div\n        className=\"absolute inset-0 z-0\"\n        style={{\n          background: \"radial-gradient(circle at 20% 80%, rgba(0, 255, 255, 0.03), transparent 33%), \" +\n                    \"radial-gradient(circle at 80% 20%, rgba(255, 0, 255, 0.03), transparent 33%), \" +\n                    \"radial-gradient(circle at 50% 50%, rgba(57, 255, 20, 0.03), rgba(0, 0, 0, 1) 100%)\",\n        }}\n      ></div>\n\n      {/* Neon Grid Overlay */}\n      <div className=\"absolute inset-0 z-0 opacity-[0.02]\"\n           style={{\n             backgroundImage: \"linear-gradient(to right, #FF00FF 1px, transparent 1px), linear-gradient(to bottom, #FF00FF 1px, transparent 1px)\",\n             backgroundSize: \"40px 40px\"\n           }}>\n      </div>\n\n      <div className=\"container mx-auto px-4 relative z-10\">\n        {/* Cart Header - Animated */}\n        <motion.div\n          ref={headerRef}\n          initial={{ opacity: 0, y: -20 }}\n          animate={headerInView ? { opacity: 1, y: 0 } : {}}\n          transition={{ duration: 0.8 }}\n          className=\"text-center mb-12\"\n        >\n          <motion.h2\n            className=\"font-playfair text-4xl md:text-5xl font-bold mb-3 relative inline-block\"\n            initial={{ opacity: 0 }}\n            animate={headerInView ? { opacity: 1 } : {}}\n            transition={{ duration: 0.5, delay: 0.2 }}\n          >\n            <span className=\"text-white\">Your </span>\n            <motion.span\n              className=\"text-transparent bg-clip-text bg-gradient-to-r from-cyan-400 to-fuchsia-500 relative z-10\"\n              animate={{\n                textShadow: [\n                  \"0 0 5px rgba(0, 255, 255, 0.7), 0 0 10px rgba(0, 255, 255, 0.5), 0 0 15px rgba(0, 255, 255, 0.3)\",\n                  \"0 0 5px rgba(255, 0, 255, 0.7), 0 0 10px rgba(255, 0, 255, 0.5), 0 0 15px rgba(255, 0, 255, 0.3)\",\n                  \"0 0 5px rgba(0, 255, 255, 0.7), 0 0 10px rgba(0, 255, 255, 0.5), 0 0 15px rgba(0, 255, 255, 0.3)\",\n                ]\n              }}\n              transition={{ duration: 3, repeat: Infinity }}\n            >\n              Cart\n            </motion.span>\n          </motion.h2>\n\n          {cartItems.length > 0 && (\n            <motion.div\n              initial={{ opacity: 0, y: 10 }}\n              animate={headerInView ? { opacity: 1, y: 0 } : {}}\n              transition={{ duration: 0.5, delay: 0.5 }}\n              className=\"flex items-center justify-center gap-2 mb-2\"\n            >\n              <motion.span\n                className=\"inline-flex items-center px-3 py-1 rounded-full\n                           bg-black/40 backdrop-blur-sm border border-fuchsia-800/50\"\n              >\n                <span className=\"text-fuchsia-400 text-sm font-medium mr-1.5\">{cartItems.length}</span>\n                <span className=\"text-gray-300 text-sm\">items</span>\n              </motion.span>\n\n              <span className=\"text-gray-600\">•</span>\n\n              <motion.span\n                className=\"inline-flex items-center px-3 py-1 rounded-full\n                           bg-black/40 backdrop-blur-sm border border-cyan-800/50\"\n              >\n                <svg\n                  className=\"w-4 h-4 text-cyan-400 mr-1\"\n                  fill=\"none\"\n                  viewBox=\"0 0 24 24\"\n                  stroke=\"currentColor\"\n                >\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z\" />\n                </svg>\n                <span className=\"text-gray-300 text-sm\">~{estimatedTime} min</span>\n              </motion.span>\n            </motion.div>\n          )}\n\n          <motion.p\n            className=\"font-poppins text-gray-400 max-w-2xl mx-auto\"\n            initial={{ opacity: 0, y: 20 }}\n            animate={headerInView ? { opacity: 1, y: 0 } : {}}\n            transition={{ duration: 0.6, delay: 0.4 }}\n          >\n            {cartItems.length > 0\n              ? 'Your culinary selections are almost ready for checkout'\n              : 'Your cart is waiting to be filled with delicious BBQ treats'}\n          </motion.p>\n        </motion.div>\n\n        {/* Cart Content - Responsive Card Layout */}\n        <div className=\"max-w-7xl mx-auto lg:flex lg:gap-8\">\n          {/* Cart Items Column */}\n          <div className=\"lg:flex-1\">\n            {cartItems.length > 0 ? (\n              <motion.div\n                variants={containerVariants}\n                initial=\"hidden\"\n                animate=\"visible\"\n                className=\"space-y-4\"\n              >\n                <AnimatePresence>\n                  {cartItems.map((item) => (\n                    <motion.div\n                      key={item.id}\n                      variants={itemVariants}\n                      exit=\"exit\"\n                      layout\n                      className=\"bg-black/20 backdrop-blur-sm rounded-xl p-5 border border-gray-800\n                               overflow-hidden group hover:shadow-[0_0_25px_rgba(0,255,255,0.1)]\n                               hover:border-cyan-900/50 transition-all duration-500\"\n                    >\n                      <div className=\"flex flex-col md:flex-row md:items-center\">\n                        {/* Image Container */}\n                        <div className=\"relative w-full md:w-24 h-24 md:h-24 mb-4 md:mb-0 md:mr-6 overflow-hidden rounded-lg\">\n                          <motion.div\n                            className=\"absolute inset-0 bg-gradient-to-tr from-fuchsia-600/20 to-cyan-600/20 opacity-0\n                                      transition-opacity duration-300 group-hover:opacity-100 z-10\"\n                          />\n                          <motion.img\n                            src={item.imageUrl}\n                            alt={item.name}\n                            className=\"w-full h-full object-cover\"\n                            whileHover={{ scale: 1.05 }}\n                            transition={{ duration: 0.4 }}\n                          />\n                        </div>\n\n                        {/* Item Details */}\n                        <div className=\"flex-1\">\n                          <h3 className=\"font-playfair text-xl font-bold text-white mb-1\">{item.name}</h3>\n                          <p className=\"font-poppins text-gray-400 text-sm mb-2\">\n                            {item.description.substring(0, 50)}...\n                          </p>\n                          <motion.span\n                            className=\"font-medium text-transparent bg-clip-text bg-gradient-to-r from-cyan-400 to-blue-500\"\n                            animate={{\n                              textShadow: ['0 0 4px rgba(0, 255, 255, 0.3)', '0 0 8px rgba(0, 255, 255, 0.5)', '0 0 4px rgba(0, 255, 255, 0.3)']\n                            }}\n                            transition={{ duration: 2, repeat: Infinity }}\n                          >\n                            {formatCurrency(item.price)}\n                          </motion.span>\n                        </div>\n\n                        {/* Quantity Controls & Total */}\n                        <div className=\"flex flex-col items-end mt-4 md:mt-0\">\n                          {/* Item Total */}\n                          <motion.div\n                            className=\"text-right mb-3 font-bold\"\n                            animate={isUpdating === item.id ? { opacity: [1, 0.5, 1] } : {}}\n                            transition={{ duration: 0.5, repeat: isUpdating === item.id ? Infinity : 0 }}\n                          >\n                            <span className=\"text-xs text-gray-500 block mb-1\">Item Total</span>\n                            <span className=\"text-fuchsia-400\">\n                              {formatCurrency(item.price * item.quantity)}\n                            </span>\n                          </motion.div>\n\n                          {/* Controls */}\n                          <div className=\"flex items-center\">\n                            {/* Minus Button */}\n                            <motion.button\n                              className=\"w-8 h-8 flex items-center justify-center bg-black/50 text-white rounded-full\n                                        border border-gray-700 hover:border-fuchsia-500 hover:shadow-[0_0_10px_rgba(255,0,255,0.3)]\n                                        transition-all\"\n                              onClick={() => handleQuantityChange(item.id, item.quantity - 1)}\n                              disabled={isUpdating === item.id}\n                              whileHover={{ scale: 1.05 }}\n                              whileTap={{ scale: 0.95 }}\n                            >\n                              <svg className=\"w-3.5 h-3.5\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M20 12H4\" />\n                              </svg>\n                            </motion.button>\n\n                            {/* Quantity Display */}\n                            <motion.span\n                              className=\"font-poppins font-medium text-white mx-4 w-5 text-center\"\n                              animate={\n                                isUpdating === item.id\n                                  ? { opacity: [1, 0.5, 1], scale: [1, 0.9, 1] }\n                                  : {}\n                              }\n                              transition={{ duration: 0.5, repeat: isUpdating === item.id ? Infinity : 0 }}\n                            >\n                              {isUpdating === item.id ? (\n                                <svg className=\"w-4 h-4 mx-auto animate-spin\" fill=\"none\" viewBox=\"0 0 24 24\">\n                                  <circle className=\"opacity-25\" cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\" strokeWidth=\"4\"></circle>\n                                  <path className=\"opacity-75\" fill=\"currentColor\" d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"></path>\n                                </svg>\n                              ) : (\n                                item.quantity\n                              )}\n                            </motion.span>\n\n                            {/* Plus Button */}\n                            <motion.button\n                              className=\"w-8 h-8 flex items-center justify-center bg-black/50 text-white rounded-full\n                                        border border-gray-700 hover:border-cyan-500 hover:shadow-[0_0_10px_rgba(0,255,255,0.3)]\n                                        transition-all\"\n                              onClick={() => handleQuantityChange(item.id, item.quantity + 1)}\n                              disabled={isUpdating === item.id}\n                              whileHover={{ scale: 1.05 }}\n                              whileTap={{ scale: 0.95 }}\n                            >\n                              <svg className=\"w-3.5 h-3.5\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 6v6m0 0v6m0-6h6m-6 0H6\" />\n                              </svg>\n                            </motion.button>\n\n                            {/* Remove Button */}\n                            <motion.button\n                              className=\"ml-6 bg-black/30 text-gray-400 w-8 h-8 rounded-full flex items-center justify-center\n                                        hover:text-red-400 hover:border-red-500 border border-transparent\n                                        hover:shadow-[0_0_10px_rgba(248,113,113,0.4)] transition-all\"\n                              onClick={() => handleRemoveItem(item.id)}\n                              disabled={isUpdating === item.id}\n                              whileHover={{ scale: 1.05 }}\n                              whileTap={{ scale: 0.95 }}\n                            >\n                              <svg className=\"w-4 h-4\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16\" />\n                              </svg>\n                            </motion.button>\n                          </div>\n                        </div>\n                      </div>\n                    </motion.div>\n                  ))}\n                </AnimatePresence>\n              </motion.div>\n            ) : (\n              <motion.div\n                initial={{ opacity: 0 }}\n                animate={{ opacity: 1 }}\n                transition={{ duration: 0.8 }}\n                className=\"text-center py-16 backdrop-blur-sm bg-black/20 rounded-xl border border-gray-800\"\n              >\n                <motion.div\n                  className=\"text-gray-600 text-7xl mb-6 mx-auto w-20 h-20 relative\"\n                  animate={{ rotate: [0, -10, 10, -5, 5, 0] }}\n                  transition={{ duration: 4, repeat: Infinity, repeatType: \"reverse\" }}\n                >\n                  <svg className=\"w-full h-full\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={1.5} d=\"M3 3h2l.4 2M7 13h10l4-8H5.4M7 13L5.4 5M7 13l-2.293 2.293c-.63.63-.184 1.707.707 1.707H17m0 0a2 2 0 100 4 2 2 0 000-4zm-8 2a2 2 0 11-4 0 2 2 0 014 0z\" />\n                  </svg>\n                  <motion.div\n                    className=\"absolute -top-2 -right-2 w-6 h-6 rounded-full bg-gradient-to-r from-cyan-500 to-fuchsia-500\"\n                    animate={{\n                      boxShadow: [\n                        \"0 0 5px rgba(0, 255, 255, 0.5), 0 0 10px rgba(0, 255, 255, 0.3)\",\n                        \"0 0 5px rgba(255, 0, 255, 0.5), 0 0 10px rgba(255, 0, 255, 0.3)\",\n                        \"0 0 5px rgba(0, 255, 255, 0.5), 0 0 10px rgba(0, 255, 255, 0.3)\"\n                      ]\n                    }}\n                    transition={{ duration: 2, repeat: Infinity }}\n                  />\n                </motion.div>\n                <motion.h3\n                  className=\"font-playfair text-2xl font-bold text-white mb-3\"\n                  initial={{ opacity: 0, y: 20 }}\n                  animate={{ opacity: 1, y: 0 }}\n                  transition={{ delay: 0.3, duration: 0.5 }}\n                >\n                  Your cart is empty\n                </motion.h3>\n                <motion.p\n                  className=\"font-poppins text-gray-400 mb-8 max-w-md mx-auto\"\n                  initial={{ opacity: 0 }}\n                  animate={{ opacity: 1 }}\n                  transition={{ delay: 0.5, duration: 0.5 }}\n                >\n                  Your culinary journey awaits! Explore our menu to discover premium BBQ dishes crafted to perfection.\n                </motion.p>\n                <motion.div\n                  initial={{ opacity: 0, y: 20 }}\n                  animate={{ opacity: 1, y: 0 }}\n                  transition={{ delay: 0.7, duration: 0.5 }}\n                >\n                  <Link href=\"/menu\">\n                    <motion.button\n                      className=\"relative overflow-hidden rounded-md px-8 py-3 bg-transparent\"\n                      whileHover={{ scale: 1.03 }}\n                      whileTap={{ scale: 0.97 }}\n                    >\n                      {/* Button Background */}\n                      <span className=\"absolute inset-0 w-full h-full bg-gradient-to-r from-cyan-800/70 to-cyan-600/70\"></span>\n\n                      {/* Button Glow Effect */}\n                      <span className=\"absolute inset-0 w-full h-full transition-all duration-300\n                                      bg-gradient-to-r from-cyan-600 via-blue-600 to-cyan-600\n                                      opacity-0 hover:opacity-100 hover:blur-md\"></span>\n\n                      {/* Button Border */}\n                      <span className=\"absolute inset-0 w-full h-full border border-cyan-500 rounded-md\"></span>\n\n                      {/* Button Text */}\n                      <span className=\"relative z-10 flex items-center font-medium text-white\">\n                        <svg className=\"w-5 h-5 mr-2\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M4 6h16M4 12h16M4 18h7\" />\n                        </svg>\n                        Browse Menu\n                      </span>\n                    </motion.button>\n                  </Link>\n                </motion.div>\n              </motion.div>\n            )}\n          </div>\n\n          {/* Cart Summary Panel - Side */}\n          {cartItems.length > 0 && (\n            <motion.div\n              ref={summaryRef}\n              variants={itemVariants}\n              initial=\"hidden\"\n              animate={summaryInView ? \"visible\" : \"hidden\"}\n              className=\"lg:w-96 mt-8 lg:mt-0 sticky top-8\"\n            >\n              <div className=\"bg-black/30 backdrop-blur-sm rounded-xl border border-gray-800\n                             shadow-[0_0_25px_rgba(57,255,20,0.05)]\n                             hover:shadow-[0_0_30px_rgba(57,255,20,0.1)]\n                             transition-all duration-500\">\n                {/* Glowing top border */}\n                <div className=\"h-1 w-full bg-gradient-to-r from-cyan-500 via-fuchsia-500 to-lime-500 rounded-t-xl\"></div>\n\n                <div className=\"p-8\">\n                  <motion.h3\n                    className=\"font-playfair text-2xl font-bold text-white mb-8 flex items-center\"\n                    animate={{\n                      textShadow: ['0 0 0px rgba(255, 255, 255, 0)', '0 0 2px rgba(255, 255, 255, 0.5)', '0 0 0px rgba(255, 255, 255, 0)']\n                    }}\n                    transition={{ duration: 2, repeat: Infinity }}\n                  >\n                    <svg\n                      className=\"w-6 h-6 mr-2 text-lime-400\"\n                      fill=\"none\"\n                      viewBox=\"0 0 24 24\"\n                      stroke=\"currentColor\"\n                    >\n                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01\" />\n                    </svg>\n                    Order Summary\n                  </motion.h3>\n\n                  <div className=\"space-y-6 mb-8\">\n                    {/* Subtotal */}\n                    <motion.div\n                      className=\"flex justify-between items-center\"\n                      initial={{ opacity: 0, y: 10 }}\n                      animate={summaryInView ? { opacity: 1, y: 0 } : {}}\n                      transition={{ delay: 0.2, duration: 0.4 }}\n                    >\n                      <span className=\"font-poppins text-gray-300\">Subtotal</span>\n                      <motion.span\n                        className=\"font-poppins font-medium text-white\"\n                        animate={\n                          isUpdating !== null\n                            ? { opacity: [1, 0.7, 1], scale: [1, 0.98, 1] }\n                            : {}\n                        }\n                        transition={{ duration: 0.5, repeat: isUpdating !== null ? 3 : 0 }}\n                      >\n                        {formatCurrency(subtotal)}\n                      </motion.span>\n                    </motion.div>\n\n                    {/* Delivery Fee */}\n                    <motion.div\n                      className=\"flex justify-between items-center\"\n                      initial={{ opacity: 0, y: 10 }}\n                      animate={summaryInView ? { opacity: 1, y: 0 } : {}}\n                      transition={{ delay: 0.3, duration: 0.4 }}\n                    >\n                      <span className=\"font-poppins text-gray-300\">Delivery Fee</span>\n                      <span className=\"font-poppins font-medium text-white\">{formatCurrency(deliveryFee)}</span>\n                    </motion.div>\n\n                    {/* VAT */}\n                    <motion.div\n                      className=\"flex justify-between items-center\"\n                      initial={{ opacity: 0, y: 10 }}\n                      animate={summaryInView ? { opacity: 1, y: 0 } : {}}\n                      transition={{ delay: 0.4, duration: 0.4 }}\n                    >\n                      <span className=\"font-poppins text-gray-300\">VAT (25%)</span>\n                      <span className=\"font-poppins font-medium text-white\">{formatCurrency(subtotal * 0.25)}</span>\n                    </motion.div>\n\n                    {/* Total Amount */}\n                    <motion.div\n                      className=\"flex justify-between items-center border-t border-gray-700 pt-6\"\n                      initial={{ opacity: 0, y: 10 }}\n                      animate={summaryInView ? { opacity: 1, y: 0 } : {}}\n                      transition={{ delay: 0.5, duration: 0.4 }}\n                    >\n                      <span className=\"font-poppins font-medium text-white text-lg\">Total</span>\n                      <motion.span\n                        className=\"font-poppins font-bold text-transparent bg-clip-text bg-gradient-to-r from-lime-400 to-green-500 text-2xl\"\n                        animate={{\n                          textShadow: ['0 0 4px rgba(57, 255, 20, 0.4)', '0 0 8px rgba(57, 255, 20, 0.6)', '0 0 4px rgba(57, 255, 20, 0.4)']\n                        }}\n                        transition={{ duration: 2, repeat: Infinity }}\n                      >\n                        {formatCurrency(Math.round(animatedTotal * 1.25))}\n                      </motion.span>\n                    </motion.div>\n                  </div>\n\n                  {/* Checkout Button */}\n                  <motion.div\n                    initial={{ opacity: 0, y: 20 }}\n                    animate={summaryInView ? { opacity: 1, y: 0 } : {}}\n                    transition={{ delay: 0.6, duration: 0.5 }}\n                  >\n                    <Link href=\"/checkout\">\n                      <motion.button\n                        className=\"relative w-full overflow-hidden rounded-lg p-4 flex items-center justify-center\"\n                        whileHover={{ scale: 1.02 }}\n                        whileTap={{ scale: 0.98 }}\n                      >\n                        {/* Button Background */}\n                        <span className=\"absolute inset-0 w-full h-full bg-gradient-to-r from-lime-600 to-green-700\"></span>\n\n                        {/* Animated Glow Effect */}\n                        <motion.span\n                          className=\"absolute inset-0 w-full h-full bg-gradient-to-r from-lime-500 to-green-600 opacity-0\"\n                          animate={{\n                            opacity: [0, 0.5, 0],\n                            scale: [1, 1.05, 1],\n                            boxShadow: [\n                              \"0 0 0 rgba(57, 255, 20, 0)\",\n                              \"0 0 20px rgba(57, 255, 20, 0.5)\",\n                              \"0 0 0 rgba(57, 255, 20, 0)\"\n                            ]\n                          }}\n                          transition={{ duration: 2.5, repeat: Infinity }}\n                        />\n\n                        {/* Button Text */}\n                        <span className=\"relative z-10 font-bold text-white tracking-wide flex items-center\">\n                          Proceed to Checkout\n                          <motion.svg\n                            className=\"w-5 h-5 ml-2\"\n                            fill=\"none\"\n                            viewBox=\"0 0 24 24\"\n                            stroke=\"currentColor\"\n                            animate={{ x: [0, 4, 0] }}\n                            transition={{ duration: 1.5, repeat: Infinity }}\n                          >\n                            <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M14 5l7 7m0 0l-7 7m7-7H3\" />\n                          </motion.svg>\n                        </span>\n                      </motion.button>\n                    </Link>\n                  </motion.div>\n\n                  {/* Security Note */}\n                  <motion.p\n                    className=\"text-gray-500 text-xs mt-4 text-center\"\n                    initial={{ opacity: 0 }}\n                    animate={summaryInView ? { opacity: 1 } : {}}\n                    transition={{ delay: 0.7, duration: 0.4 }}\n                  >\n                    <svg className=\"w-3 h-3 inline-block mr-1\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z\" />\n                    </svg>\n                    Secure checkout - we protect your payment information\n                  </motion.p>\n                </div>\n              </div>\n            </motion.div>\n          )}\n        </div>\n      </div>\n    </section>\n  );\n};\n\nexport default Cart;\n", "modifiedCode": "import { useState, useEffect } from \"react\";\nimport { <PERSON> } from \"wouter\";\nimport { useCart } from \"@/context/CartContext\";\nimport Button from \"@/components/Button\";\nimport { formatCurrency } from \"@/lib/utils\";\nimport { motion, AnimatePresence } from \"framer-motion\";\nimport { useInView } from \"react-intersection-observer\";\n\nconst Cart = () => {\n  const { cartItems, updateQuantity, removeFromCart } = useCart();\n  const [isUpdating, setIsUpdating] = useState<number | null>(null);\n  const [animatedTotal, setAnimatedTotal] = useState(0);\n  const [estimatedTime, setEstimatedTime] = useState(\"15\");\n\n  // Animation reference for sections\n  const [headerRef, headerInView] = useInView({\n    triggerOnce: true,\n    threshold: 0.1,\n  });\n\n  const [summaryRef, summaryInView] = useInView({\n    triggerOnce: true,\n    threshold: 0.2,\n  });\n\n  // Animation variants\n  const containerVariants = {\n    hidden: { opacity: 0 },\n    visible: {\n      opacity: 1,\n      transition: {\n        staggerChildren: 0.1\n      }\n    }\n  };\n\n  const itemVariants = {\n    hidden: { y: 20, opacity: 0 },\n    visible: {\n      y: 0,\n      opacity: 1,\n      transition: {\n        type: \"spring\",\n        stiffness: 100,\n        damping: 12\n      }\n    },\n    exit: {\n      opacity: 0,\n      x: 20,\n      transition: { duration: 0.3 }\n    }\n  };\n\n  // State for delivery fee\n  const [deliveryFee, setDeliveryFee] = useState(89); // Default fallback\n\n  // Fetch delivery fee from API\n  useEffect(() => {\n    const fetchSettings = async () => {\n      try {\n        const response = await fetch('/api/settings');\n        if (response.ok) {\n          const settings = await response.json();\n          setDeliveryFee(settings.delivery_fee);\n        }\n      } catch (error) {\n        console.error('Failed to fetch settings:', error);\n        // Keep default value on error\n      }\n    };\n\n    fetchSettings();\n  }, []);\n\n  // Calculate order totals\n  const subtotal = cartItems.reduce((total, item) => total + (item.price * item.quantity), 0);\n  const actualDeliveryFee = cartItems.length > 0 ? deliveryFee : 0;\n  const total = subtotal + actualDeliveryFee;\n\n  // Update animated total with smooth animation\n  useEffect(() => {\n    const duration = 1000; // Animation duration in ms\n    const steps = 20; // Number of steps\n    const stepDuration = duration / steps;\n    const increment = (total - animatedTotal) / steps;\n\n    if (Math.abs(total - animatedTotal) < 1) {\n      setAnimatedTotal(total);\n      return;\n    }\n\n    const timer = setTimeout(() => {\n      setAnimatedTotal(prev => {\n        const next = prev + increment;\n        // If we're close enough, just set the exact value\n        if (Math.abs(next - total) < 1) return total;\n        return next;\n      });\n    }, stepDuration);\n\n    return () => clearTimeout(timer);\n  }, [total, animatedTotal]);\n\n  // Calculate estimated time based on cart items\n  useEffect(() => {\n    if (cartItems.length === 0) {\n      setEstimatedTime(\"0\");\n    } else {\n      // Base time + additional time for each item\n      const baseTime = 10;\n      const itemTime = cartItems.reduce((time, item) => time + (item.quantity * 2), 0);\n      setEstimatedTime(Math.min(baseTime + itemTime, 45).toString());\n    }\n  }, [cartItems]);\n\n  const handleQuantityChange = (id: number, newQuantity: number) => {\n    if (newQuantity < 1) return;\n\n    setIsUpdating(id);\n\n    // Simulate a small delay for better UX\n    setTimeout(() => {\n      updateQuantity(id, newQuantity);\n      setIsUpdating(null);\n    }, 300);\n  };\n\n  const handleRemoveItem = (id: number) => {\n    setIsUpdating(id);\n\n    // Simulate a small delay for better UX\n    setTimeout(() => {\n      removeFromCart(id);\n      setIsUpdating(null);\n    }, 300);\n  };\n\n  return (\n    <section className=\"min-h-screen py-32 bg-black relative overflow-hidden\">\n      {/* Animated Gradient Background */}\n      <div\n        className=\"absolute inset-0 z-0\"\n        style={{\n          background: \"radial-gradient(circle at 20% 80%, rgba(0, 255, 255, 0.03), transparent 33%), \" +\n                    \"radial-gradient(circle at 80% 20%, rgba(255, 0, 255, 0.03), transparent 33%), \" +\n                    \"radial-gradient(circle at 50% 50%, rgba(57, 255, 20, 0.03), rgba(0, 0, 0, 1) 100%)\",\n        }}\n      ></div>\n\n      {/* Neon Grid Overlay */}\n      <div className=\"absolute inset-0 z-0 opacity-[0.02]\"\n           style={{\n             backgroundImage: \"linear-gradient(to right, #FF00FF 1px, transparent 1px), linear-gradient(to bottom, #FF00FF 1px, transparent 1px)\",\n             backgroundSize: \"40px 40px\"\n           }}>\n      </div>\n\n      <div className=\"container mx-auto px-4 relative z-10\">\n        {/* Cart Header - Animated */}\n        <motion.div\n          ref={headerRef}\n          initial={{ opacity: 0, y: -20 }}\n          animate={headerInView ? { opacity: 1, y: 0 } : {}}\n          transition={{ duration: 0.8 }}\n          className=\"text-center mb-12\"\n        >\n          <motion.h2\n            className=\"font-playfair text-4xl md:text-5xl font-bold mb-3 relative inline-block\"\n            initial={{ opacity: 0 }}\n            animate={headerInView ? { opacity: 1 } : {}}\n            transition={{ duration: 0.5, delay: 0.2 }}\n          >\n            <span className=\"text-white\">Your </span>\n            <motion.span\n              className=\"text-transparent bg-clip-text bg-gradient-to-r from-cyan-400 to-fuchsia-500 relative z-10\"\n              animate={{\n                textShadow: [\n                  \"0 0 5px rgba(0, 255, 255, 0.7), 0 0 10px rgba(0, 255, 255, 0.5), 0 0 15px rgba(0, 255, 255, 0.3)\",\n                  \"0 0 5px rgba(255, 0, 255, 0.7), 0 0 10px rgba(255, 0, 255, 0.5), 0 0 15px rgba(255, 0, 255, 0.3)\",\n                  \"0 0 5px rgba(0, 255, 255, 0.7), 0 0 10px rgba(0, 255, 255, 0.5), 0 0 15px rgba(0, 255, 255, 0.3)\",\n                ]\n              }}\n              transition={{ duration: 3, repeat: Infinity }}\n            >\n              Cart\n            </motion.span>\n          </motion.h2>\n\n          {cartItems.length > 0 && (\n            <motion.div\n              initial={{ opacity: 0, y: 10 }}\n              animate={headerInView ? { opacity: 1, y: 0 } : {}}\n              transition={{ duration: 0.5, delay: 0.5 }}\n              className=\"flex items-center justify-center gap-2 mb-2\"\n            >\n              <motion.span\n                className=\"inline-flex items-center px-3 py-1 rounded-full\n                           bg-black/40 backdrop-blur-sm border border-fuchsia-800/50\"\n              >\n                <span className=\"text-fuchsia-400 text-sm font-medium mr-1.5\">{cartItems.length}</span>\n                <span className=\"text-gray-300 text-sm\">items</span>\n              </motion.span>\n\n              <span className=\"text-gray-600\">•</span>\n\n              <motion.span\n                className=\"inline-flex items-center px-3 py-1 rounded-full\n                           bg-black/40 backdrop-blur-sm border border-cyan-800/50\"\n              >\n                <svg\n                  className=\"w-4 h-4 text-cyan-400 mr-1\"\n                  fill=\"none\"\n                  viewBox=\"0 0 24 24\"\n                  stroke=\"currentColor\"\n                >\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z\" />\n                </svg>\n                <span className=\"text-gray-300 text-sm\">~{estimatedTime} min</span>\n              </motion.span>\n            </motion.div>\n          )}\n\n          <motion.p\n            className=\"font-poppins text-gray-400 max-w-2xl mx-auto\"\n            initial={{ opacity: 0, y: 20 }}\n            animate={headerInView ? { opacity: 1, y: 0 } : {}}\n            transition={{ duration: 0.6, delay: 0.4 }}\n          >\n            {cartItems.length > 0\n              ? 'Your culinary selections are almost ready for checkout'\n              : 'Your cart is waiting to be filled with delicious BBQ treats'}\n          </motion.p>\n        </motion.div>\n\n        {/* Cart Content - Responsive Card Layout */}\n        <div className=\"max-w-7xl mx-auto lg:flex lg:gap-8\">\n          {/* Cart Items Column */}\n          <div className=\"lg:flex-1\">\n            {cartItems.length > 0 ? (\n              <motion.div\n                variants={containerVariants}\n                initial=\"hidden\"\n                animate=\"visible\"\n                className=\"space-y-4\"\n              >\n                <AnimatePresence>\n                  {cartItems.map((item) => (\n                    <motion.div\n                      key={item.id}\n                      variants={itemVariants}\n                      exit=\"exit\"\n                      layout\n                      className=\"bg-black/20 backdrop-blur-sm rounded-xl p-5 border border-gray-800\n                               overflow-hidden group hover:shadow-[0_0_25px_rgba(0,255,255,0.1)]\n                               hover:border-cyan-900/50 transition-all duration-500\"\n                    >\n                      <div className=\"flex flex-col md:flex-row md:items-center\">\n                        {/* Image Container */}\n                        <div className=\"relative w-full md:w-24 h-24 md:h-24 mb-4 md:mb-0 md:mr-6 overflow-hidden rounded-lg\">\n                          <motion.div\n                            className=\"absolute inset-0 bg-gradient-to-tr from-fuchsia-600/20 to-cyan-600/20 opacity-0\n                                      transition-opacity duration-300 group-hover:opacity-100 z-10\"\n                          />\n                          <motion.img\n                            src={item.imageUrl}\n                            alt={item.name}\n                            className=\"w-full h-full object-cover\"\n                            whileHover={{ scale: 1.05 }}\n                            transition={{ duration: 0.4 }}\n                          />\n                        </div>\n\n                        {/* Item Details */}\n                        <div className=\"flex-1\">\n                          <h3 className=\"font-playfair text-xl font-bold text-white mb-1\">{item.name}</h3>\n                          <p className=\"font-poppins text-gray-400 text-sm mb-2\">\n                            {item.description.substring(0, 50)}...\n                          </p>\n                          <motion.span\n                            className=\"font-medium text-transparent bg-clip-text bg-gradient-to-r from-cyan-400 to-blue-500\"\n                            animate={{\n                              textShadow: ['0 0 4px rgba(0, 255, 255, 0.3)', '0 0 8px rgba(0, 255, 255, 0.5)', '0 0 4px rgba(0, 255, 255, 0.3)']\n                            }}\n                            transition={{ duration: 2, repeat: Infinity }}\n                          >\n                            {formatCurrency(item.price)}\n                          </motion.span>\n                        </div>\n\n                        {/* Quantity Controls & Total */}\n                        <div className=\"flex flex-col items-end mt-4 md:mt-0\">\n                          {/* Item Total */}\n                          <motion.div\n                            className=\"text-right mb-3 font-bold\"\n                            animate={isUpdating === item.id ? { opacity: [1, 0.5, 1] } : {}}\n                            transition={{ duration: 0.5, repeat: isUpdating === item.id ? Infinity : 0 }}\n                          >\n                            <span className=\"text-xs text-gray-500 block mb-1\">Item Total</span>\n                            <span className=\"text-fuchsia-400\">\n                              {formatCurrency(item.price * item.quantity)}\n                            </span>\n                          </motion.div>\n\n                          {/* Controls */}\n                          <div className=\"flex items-center\">\n                            {/* Minus Button */}\n                            <motion.button\n                              className=\"w-8 h-8 flex items-center justify-center bg-black/50 text-white rounded-full\n                                        border border-gray-700 hover:border-fuchsia-500 hover:shadow-[0_0_10px_rgba(255,0,255,0.3)]\n                                        transition-all\"\n                              onClick={() => handleQuantityChange(item.id, item.quantity - 1)}\n                              disabled={isUpdating === item.id}\n                              whileHover={{ scale: 1.05 }}\n                              whileTap={{ scale: 0.95 }}\n                            >\n                              <svg className=\"w-3.5 h-3.5\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M20 12H4\" />\n                              </svg>\n                            </motion.button>\n\n                            {/* Quantity Display */}\n                            <motion.span\n                              className=\"font-poppins font-medium text-white mx-4 w-5 text-center\"\n                              animate={\n                                isUpdating === item.id\n                                  ? { opacity: [1, 0.5, 1], scale: [1, 0.9, 1] }\n                                  : {}\n                              }\n                              transition={{ duration: 0.5, repeat: isUpdating === item.id ? Infinity : 0 }}\n                            >\n                              {isUpdating === item.id ? (\n                                <svg className=\"w-4 h-4 mx-auto animate-spin\" fill=\"none\" viewBox=\"0 0 24 24\">\n                                  <circle className=\"opacity-25\" cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\" strokeWidth=\"4\"></circle>\n                                  <path className=\"opacity-75\" fill=\"currentColor\" d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"></path>\n                                </svg>\n                              ) : (\n                                item.quantity\n                              )}\n                            </motion.span>\n\n                            {/* Plus Button */}\n                            <motion.button\n                              className=\"w-8 h-8 flex items-center justify-center bg-black/50 text-white rounded-full\n                                        border border-gray-700 hover:border-cyan-500 hover:shadow-[0_0_10px_rgba(0,255,255,0.3)]\n                                        transition-all\"\n                              onClick={() => handleQuantityChange(item.id, item.quantity + 1)}\n                              disabled={isUpdating === item.id}\n                              whileHover={{ scale: 1.05 }}\n                              whileTap={{ scale: 0.95 }}\n                            >\n                              <svg className=\"w-3.5 h-3.5\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 6v6m0 0v6m0-6h6m-6 0H6\" />\n                              </svg>\n                            </motion.button>\n\n                            {/* Remove Button */}\n                            <motion.button\n                              className=\"ml-6 bg-black/30 text-gray-400 w-8 h-8 rounded-full flex items-center justify-center\n                                        hover:text-red-400 hover:border-red-500 border border-transparent\n                                        hover:shadow-[0_0_10px_rgba(248,113,113,0.4)] transition-all\"\n                              onClick={() => handleRemoveItem(item.id)}\n                              disabled={isUpdating === item.id}\n                              whileHover={{ scale: 1.05 }}\n                              whileTap={{ scale: 0.95 }}\n                            >\n                              <svg className=\"w-4 h-4\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16\" />\n                              </svg>\n                            </motion.button>\n                          </div>\n                        </div>\n                      </div>\n                    </motion.div>\n                  ))}\n                </AnimatePresence>\n              </motion.div>\n            ) : (\n              <motion.div\n                initial={{ opacity: 0 }}\n                animate={{ opacity: 1 }}\n                transition={{ duration: 0.8 }}\n                className=\"text-center py-16 backdrop-blur-sm bg-black/20 rounded-xl border border-gray-800\"\n              >\n                <motion.div\n                  className=\"text-gray-600 text-7xl mb-6 mx-auto w-20 h-20 relative\"\n                  animate={{ rotate: [0, -10, 10, -5, 5, 0] }}\n                  transition={{ duration: 4, repeat: Infinity, repeatType: \"reverse\" }}\n                >\n                  <svg className=\"w-full h-full\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={1.5} d=\"M3 3h2l.4 2M7 13h10l4-8H5.4M7 13L5.4 5M7 13l-2.293 2.293c-.63.63-.184 1.707.707 1.707H17m0 0a2 2 0 100 4 2 2 0 000-4zm-8 2a2 2 0 11-4 0 2 2 0 014 0z\" />\n                  </svg>\n                  <motion.div\n                    className=\"absolute -top-2 -right-2 w-6 h-6 rounded-full bg-gradient-to-r from-cyan-500 to-fuchsia-500\"\n                    animate={{\n                      boxShadow: [\n                        \"0 0 5px rgba(0, 255, 255, 0.5), 0 0 10px rgba(0, 255, 255, 0.3)\",\n                        \"0 0 5px rgba(255, 0, 255, 0.5), 0 0 10px rgba(255, 0, 255, 0.3)\",\n                        \"0 0 5px rgba(0, 255, 255, 0.5), 0 0 10px rgba(0, 255, 255, 0.3)\"\n                      ]\n                    }}\n                    transition={{ duration: 2, repeat: Infinity }}\n                  />\n                </motion.div>\n                <motion.h3\n                  className=\"font-playfair text-2xl font-bold text-white mb-3\"\n                  initial={{ opacity: 0, y: 20 }}\n                  animate={{ opacity: 1, y: 0 }}\n                  transition={{ delay: 0.3, duration: 0.5 }}\n                >\n                  Your cart is empty\n                </motion.h3>\n                <motion.p\n                  className=\"font-poppins text-gray-400 mb-8 max-w-md mx-auto\"\n                  initial={{ opacity: 0 }}\n                  animate={{ opacity: 1 }}\n                  transition={{ delay: 0.5, duration: 0.5 }}\n                >\n                  Your culinary journey awaits! Explore our menu to discover premium BBQ dishes crafted to perfection.\n                </motion.p>\n                <motion.div\n                  initial={{ opacity: 0, y: 20 }}\n                  animate={{ opacity: 1, y: 0 }}\n                  transition={{ delay: 0.7, duration: 0.5 }}\n                >\n                  <Link href=\"/menu\">\n                    <motion.button\n                      className=\"relative overflow-hidden rounded-md px-8 py-3 bg-transparent\"\n                      whileHover={{ scale: 1.03 }}\n                      whileTap={{ scale: 0.97 }}\n                    >\n                      {/* Button Background */}\n                      <span className=\"absolute inset-0 w-full h-full bg-gradient-to-r from-cyan-800/70 to-cyan-600/70\"></span>\n\n                      {/* Button Glow Effect */}\n                      <span className=\"absolute inset-0 w-full h-full transition-all duration-300\n                                      bg-gradient-to-r from-cyan-600 via-blue-600 to-cyan-600\n                                      opacity-0 hover:opacity-100 hover:blur-md\"></span>\n\n                      {/* Button Border */}\n                      <span className=\"absolute inset-0 w-full h-full border border-cyan-500 rounded-md\"></span>\n\n                      {/* Button Text */}\n                      <span className=\"relative z-10 flex items-center font-medium text-white\">\n                        <svg className=\"w-5 h-5 mr-2\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M4 6h16M4 12h16M4 18h7\" />\n                        </svg>\n                        Browse Menu\n                      </span>\n                    </motion.button>\n                  </Link>\n                </motion.div>\n              </motion.div>\n            )}\n          </div>\n\n          {/* Cart Summary Panel - Side */}\n          {cartItems.length > 0 && (\n            <motion.div\n              ref={summaryRef}\n              variants={itemVariants}\n              initial=\"hidden\"\n              animate={summaryInView ? \"visible\" : \"hidden\"}\n              className=\"lg:w-96 mt-8 lg:mt-0 sticky top-8\"\n            >\n              <div className=\"bg-black/30 backdrop-blur-sm rounded-xl border border-gray-800\n                             shadow-[0_0_25px_rgba(57,255,20,0.05)]\n                             hover:shadow-[0_0_30px_rgba(57,255,20,0.1)]\n                             transition-all duration-500\">\n                {/* Glowing top border */}\n                <div className=\"h-1 w-full bg-gradient-to-r from-cyan-500 via-fuchsia-500 to-lime-500 rounded-t-xl\"></div>\n\n                <div className=\"p-8\">\n                  <motion.h3\n                    className=\"font-playfair text-2xl font-bold text-white mb-8 flex items-center\"\n                    animate={{\n                      textShadow: ['0 0 0px rgba(255, 255, 255, 0)', '0 0 2px rgba(255, 255, 255, 0.5)', '0 0 0px rgba(255, 255, 255, 0)']\n                    }}\n                    transition={{ duration: 2, repeat: Infinity }}\n                  >\n                    <svg\n                      className=\"w-6 h-6 mr-2 text-lime-400\"\n                      fill=\"none\"\n                      viewBox=\"0 0 24 24\"\n                      stroke=\"currentColor\"\n                    >\n                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01\" />\n                    </svg>\n                    Order Summary\n                  </motion.h3>\n\n                  <div className=\"space-y-6 mb-8\">\n                    {/* Subtotal */}\n                    <motion.div\n                      className=\"flex justify-between items-center\"\n                      initial={{ opacity: 0, y: 10 }}\n                      animate={summaryInView ? { opacity: 1, y: 0 } : {}}\n                      transition={{ delay: 0.2, duration: 0.4 }}\n                    >\n                      <span className=\"font-poppins text-gray-300\">Subtotal</span>\n                      <motion.span\n                        className=\"font-poppins font-medium text-white\"\n                        animate={\n                          isUpdating !== null\n                            ? { opacity: [1, 0.7, 1], scale: [1, 0.98, 1] }\n                            : {}\n                        }\n                        transition={{ duration: 0.5, repeat: isUpdating !== null ? 3 : 0 }}\n                      >\n                        {formatCurrency(subtotal)}\n                      </motion.span>\n                    </motion.div>\n\n                    {/* Delivery Fee */}\n                    <motion.div\n                      className=\"flex justify-between items-center\"\n                      initial={{ opacity: 0, y: 10 }}\n                      animate={summaryInView ? { opacity: 1, y: 0 } : {}}\n                      transition={{ delay: 0.3, duration: 0.4 }}\n                    >\n                      <span className=\"font-poppins text-gray-300\">Delivery Fee</span>\n                      <span className=\"font-poppins font-medium text-white\">{formatCurrency(deliveryFee)}</span>\n                    </motion.div>\n\n                    {/* VAT */}\n                    <motion.div\n                      className=\"flex justify-between items-center\"\n                      initial={{ opacity: 0, y: 10 }}\n                      animate={summaryInView ? { opacity: 1, y: 0 } : {}}\n                      transition={{ delay: 0.4, duration: 0.4 }}\n                    >\n                      <span className=\"font-poppins text-gray-300\">VAT (25%)</span>\n                      <span className=\"font-poppins font-medium text-white\">{formatCurrency(subtotal * 0.25)}</span>\n                    </motion.div>\n\n                    {/* Total Amount */}\n                    <motion.div\n                      className=\"flex justify-between items-center border-t border-gray-700 pt-6\"\n                      initial={{ opacity: 0, y: 10 }}\n                      animate={summaryInView ? { opacity: 1, y: 0 } : {}}\n                      transition={{ delay: 0.5, duration: 0.4 }}\n                    >\n                      <span className=\"font-poppins font-medium text-white text-lg\">Total</span>\n                      <motion.span\n                        className=\"font-poppins font-bold text-transparent bg-clip-text bg-gradient-to-r from-lime-400 to-green-500 text-2xl\"\n                        animate={{\n                          textShadow: ['0 0 4px rgba(57, 255, 20, 0.4)', '0 0 8px rgba(57, 255, 20, 0.6)', '0 0 4px rgba(57, 255, 20, 0.4)']\n                        }}\n                        transition={{ duration: 2, repeat: Infinity }}\n                      >\n                        {formatCurrency(Math.round(animatedTotal * 1.25))}\n                      </motion.span>\n                    </motion.div>\n                  </div>\n\n                  {/* Checkout Button */}\n                  <motion.div\n                    initial={{ opacity: 0, y: 20 }}\n                    animate={summaryInView ? { opacity: 1, y: 0 } : {}}\n                    transition={{ delay: 0.6, duration: 0.5 }}\n                  >\n                    <Link href=\"/checkout\">\n                      <motion.button\n                        className=\"relative w-full overflow-hidden rounded-lg p-4 flex items-center justify-center\"\n                        whileHover={{ scale: 1.02 }}\n                        whileTap={{ scale: 0.98 }}\n                      >\n                        {/* Button Background */}\n                        <span className=\"absolute inset-0 w-full h-full bg-gradient-to-r from-lime-600 to-green-700\"></span>\n\n                        {/* Animated Glow Effect */}\n                        <motion.span\n                          className=\"absolute inset-0 w-full h-full bg-gradient-to-r from-lime-500 to-green-600 opacity-0\"\n                          animate={{\n                            opacity: [0, 0.5, 0],\n                            scale: [1, 1.05, 1],\n                            boxShadow: [\n                              \"0 0 0 rgba(57, 255, 20, 0)\",\n                              \"0 0 20px rgba(57, 255, 20, 0.5)\",\n                              \"0 0 0 rgba(57, 255, 20, 0)\"\n                            ]\n                          }}\n                          transition={{ duration: 2.5, repeat: Infinity }}\n                        />\n\n                        {/* Button Text */}\n                        <span className=\"relative z-10 font-bold text-white tracking-wide flex items-center\">\n                          Proceed to Checkout\n                          <motion.svg\n                            className=\"w-5 h-5 ml-2\"\n                            fill=\"none\"\n                            viewBox=\"0 0 24 24\"\n                            stroke=\"currentColor\"\n                            animate={{ x: [0, 4, 0] }}\n                            transition={{ duration: 1.5, repeat: Infinity }}\n                          >\n                            <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M14 5l7 7m0 0l-7 7m7-7H3\" />\n                          </motion.svg>\n                        </span>\n                      </motion.button>\n                    </Link>\n                  </motion.div>\n\n                  {/* Security Note */}\n                  <motion.p\n                    className=\"text-gray-500 text-xs mt-4 text-center\"\n                    initial={{ opacity: 0 }}\n                    animate={summaryInView ? { opacity: 1 } : {}}\n                    transition={{ delay: 0.7, duration: 0.4 }}\n                  >\n                    <svg className=\"w-3 h-3 inline-block mr-1\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z\" />\n                    </svg>\n                    Secure checkout - we protect your payment information\n                  </motion.p>\n                </div>\n              </div>\n            </motion.div>\n          )}\n        </div>\n      </div>\n    </section>\n  );\n};\n\nexport default Cart;\n"}