{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "server.log"}, "originalCode": "nohup: ignoring input\n12:56:48 AM [express] Initializing database...\nInitializing database...\nDatabase already contains data. Skipping seeding.\nDatabase initialization completed!\n12:56:50 AM [express] Database initialized successfully!\n12:56:50 AM [express] serving on port 5000\n12:57:05 AM [api] GET /api/admin/settings\n12:57:05 AM [express] GET /api/admin/settings 200 in 241ms :: {\"id\":2,\"restaurant_open\":true,\"busines…\n12:57:21 AM [api] GET /api/categories\n12:57:21 AM [api] GET /api/dishes\n12:57:21 AM [api] GET /api/admin/settings\n12:57:21 AM [express] GET /api/categories 200 in 68ms :: [{\"id\":13,\"name\":\"Signature BBQ\",\"imageUrl\":…\n12:57:21 AM [express] GET /api/dishes 304 in 74ms :: [{\"id\":82,\"name\":\"Smoked Beef Brisket\",\"descript…\n12:57:21 AM [express] GET /api/admin/settings 200 in 260ms :: {\"id\":2,\"restaurant_open\":true,\"busines…\n12:57:22 AM [api] GET /api/admin/settings\n12:57:22 AM [express] GET /api/admin/settings 200 in 63ms :: {\"id\":2,\"restaurant_open\":true,\"business…\n12:57:23 AM [api] GET /api/admin/settings\n12:57:23 AM [express] GET /api/admin/settings 304 in 276ms :: {\"id\":2,\"restaurant_open\":true,\"busines…\n12:57:24 AM [api] GET /api/admin/settings\n12:57:24 AM [express] GET /api/admin/settings 304 in 249ms :: {\"id\":2,\"restaurant_open\":true,\"busines…\n12:57:30 AM [api] PUT /api/admin/settings\n12:57:30 AM [express] PUT /api/admin/settings 200 in 143ms :: {\"id\":2,\"restaurant_open\":true,\"busines…\n12:57:37 AM [api] GET /api/admin/settings\n12:57:37 AM [express] GET /api/admin/settings 200 in 63ms :: {\"id\":1,\"restaurant_open\":true,\"business…\n12:57:46 AM [api] GET /api/settings\n12:57:46 AM [express] GET /api/settings 200 in 75ms :: {\"delivery_fee\":100,\"estimated_time\":\"10-35 mi…\n12:57:53 AM [api] PUT /api/admin/settings\n12:57:53 AM [express] PUT /api/admin/settings 200 in 124ms :: {\"id\":1,\"restaurant_open\":true,\"busines…\n12:58:00 AM [api] GET /api/settings\n12:58:00 AM [express] GET /api/settings 200 in 70ms :: {\"delivery_fee\":75,\"estimated_time\":\"10-35 min…\n12:58:32 AM [api] GET /api/admin/settings\n12:58:32 AM [express] GET /api/admin/settings 200 in 243ms :: {\"id\":1,\"restaurant_open\":true,\"busines…\n12:58:32 AM [api] GET /api/admin/settings\n12:58:32 AM [api] GET /api/admin/settings\n12:58:32 AM [express] GET /api/admin/settings 200 in 255ms :: {\"id\":1,\"restaurant_open\":true,\"busines…\n12:58:32 AM [express] GET /api/admin/settings 304 in 246ms :: {\"id\":1,\"restaurant_open\":true,\"busines…\n12:58:37 AM [api] GET /api/dishes\n12:58:37 AM [api] GET /api/categories\n12:58:38 AM [express] GET /api/dishes 304 in 60ms :: [{\"id\":82,\"name\":\"Smoked Beef Brisket\",\"descript…\n12:58:38 AM [express] GET /api/categories 200 in 60ms :: [{\"id\":13,\"name\":\"Signature BBQ\",\"imageUrl\":…\n12:58:42 AM [api] GET /api/settings\n12:58:42 AM [express] GET /api/settings 200 in 64ms :: {\"delivery_fee\":75,\"estimated_time\":\"10-35 min…\n12:58:53 AM [api] PUT /api/admin/settings\n12:58:53 AM [express] PUT /api/admin/settings 200 in 488ms :: {\"id\":1,\"restaurant_open\":true,\"busines…\n12:58:54 AM [api] GET /api/admin/settings\n12:58:54 AM [express] GET /api/admin/settings 200 in 237ms :: {\"id\":1,\"restaurant_open\":true,\"busines…\n12:59:01 AM [api] GET /api/settings\n12:59:01 AM [api] GET /api/admin/settings\n12:59:01 AM [express] GET /api/settings 200 in 61ms :: {\"delivery_fee\":150,\"estimated_time\":\"10-35 mi…\n12:59:02 AM [express] GET /api/admin/settings 200 in 322ms :: {\"id\":1,\"restaurant_open\":true,\"busines…\n12:59:23 AM [api] GET /api/categories\n12:59:23 AM [api] GET /api/items\n12:59:23 AM [express] GET /api/categories 304 in 250ms :: [{\"id\":13,\"name\":\"Signature BBQ\",\"imageUrl\"…\n12:59:23 AM [express] GET /api/items 304 in 251ms :: [{\"id\":82,\"name\":\"Smoked Beef Brisket\",\"descript…\n12:59:54 AM [api] POST /api/categories\n12:59:54 AM [express] POST /api/categories 201 in 266ms :: {\"id\":28,\"name\":\"MAZEN\",\"imageUrl\":null}\n1:00:12 AM [api] POST /api/items\n1:00:12 AM [express] POST /api/items 201 in 300ms :: {\"id\":104,\"name\":\"parasite\",\"description\":\"jkni…\n1:00:25 AM [api] GET /api/settings\n1:00:25 AM [api] GET /api/admin/settings\n1:00:25 AM [express] GET /api/settings 304 in 61ms :: {\"delivery_fee\":150,\"estimated_time\":\"10-35 mi…\n1:00:25 AM [express] GET /api/admin/settings 304 in 238ms :: {\"id\":1,\"restaurant_open\":true,\"busines…\n1:00:27 AM [api] GET /api/categories\n1:00:27 AM [api] GET /api/dishes\n1:00:27 AM [express] GET /api/categories 200 in 61ms :: [{\"id\":13,\"name\":\"Signature BBQ\",\"imageUrl\":…\n1:00:27 AM [express] GET /api/dishes 200 in 64ms :: [{\"id\":82,\"name\":\"Smoked Beef Brisket\",\"descript…\n1:00:44 AM [api] GET /api/admin/settings\n1:00:44 AM [api] GET /api/dishes\n1:00:44 AM [api] GET /api/categories\n1:00:44 AM [express] GET /api/dishes 304 in 65ms :: [{\"id\":82,\"name\":\"Smoked Beef Brisket\",\"descript…\n1:00:44 AM [express] GET /api/categories 304 in 67ms :: [{\"id\":13,\"name\":\"Signature BBQ\",\"imageUrl\":…\n1:00:45 AM [express] GET /api/admin/settings 304 in 247ms :: {\"id\":1,\"restaurant_open\":true,\"busines…\n", "modifiedCode": "nohup: ignoring input\n12:56:48 AM [express] Initializing database...\nInitializing database...\nDatabase already contains data. Skipping seeding.\nDatabase initialization completed!\n12:56:50 AM [express] Database initialized successfully!\n12:56:50 AM [express] serving on port 5000\n12:57:05 AM [api] GET /api/admin/settings\n12:57:05 AM [express] GET /api/admin/settings 200 in 241ms :: {\"id\":2,\"restaurant_open\":true,\"busines…\n12:57:21 AM [api] GET /api/categories\n12:57:21 AM [api] GET /api/dishes\n12:57:21 AM [api] GET /api/admin/settings\n12:57:21 AM [express] GET /api/categories 200 in 68ms :: [{\"id\":13,\"name\":\"Signature BBQ\",\"imageUrl\":…\n12:57:21 AM [express] GET /api/dishes 304 in 74ms :: [{\"id\":82,\"name\":\"Smoked Beef Brisket\",\"descript…\n12:57:21 AM [express] GET /api/admin/settings 200 in 260ms :: {\"id\":2,\"restaurant_open\":true,\"busines…\n12:57:22 AM [api] GET /api/admin/settings\n12:57:22 AM [express] GET /api/admin/settings 200 in 63ms :: {\"id\":2,\"restaurant_open\":true,\"business…\n12:57:23 AM [api] GET /api/admin/settings\n12:57:23 AM [express] GET /api/admin/settings 304 in 276ms :: {\"id\":2,\"restaurant_open\":true,\"busines…\n12:57:24 AM [api] GET /api/admin/settings\n12:57:24 AM [express] GET /api/admin/settings 304 in 249ms :: {\"id\":2,\"restaurant_open\":true,\"busines…\n12:57:30 AM [api] PUT /api/admin/settings\n12:57:30 AM [express] PUT /api/admin/settings 200 in 143ms :: {\"id\":2,\"restaurant_open\":true,\"busines…\n12:57:37 AM [api] GET /api/admin/settings\n12:57:37 AM [express] GET /api/admin/settings 200 in 63ms :: {\"id\":1,\"restaurant_open\":true,\"business…\n12:57:46 AM [api] GET /api/settings\n12:57:46 AM [express] GET /api/settings 200 in 75ms :: {\"delivery_fee\":100,\"estimated_time\":\"10-35 mi…\n12:57:53 AM [api] PUT /api/admin/settings\n12:57:53 AM [express] PUT /api/admin/settings 200 in 124ms :: {\"id\":1,\"restaurant_open\":true,\"busines…\n12:58:00 AM [api] GET /api/settings\n12:58:00 AM [express] GET /api/settings 200 in 70ms :: {\"delivery_fee\":75,\"estimated_time\":\"10-35 min…\n12:58:32 AM [api] GET /api/admin/settings\n12:58:32 AM [express] GET /api/admin/settings 200 in 243ms :: {\"id\":1,\"restaurant_open\":true,\"busines…\n12:58:32 AM [api] GET /api/admin/settings\n12:58:32 AM [api] GET /api/admin/settings\n12:58:32 AM [express] GET /api/admin/settings 200 in 255ms :: {\"id\":1,\"restaurant_open\":true,\"busines…\n12:58:32 AM [express] GET /api/admin/settings 304 in 246ms :: {\"id\":1,\"restaurant_open\":true,\"busines…\n12:58:37 AM [api] GET /api/dishes\n12:58:37 AM [api] GET /api/categories\n12:58:38 AM [express] GET /api/dishes 304 in 60ms :: [{\"id\":82,\"name\":\"Smoked Beef Brisket\",\"descript…\n12:58:38 AM [express] GET /api/categories 200 in 60ms :: [{\"id\":13,\"name\":\"Signature BBQ\",\"imageUrl\":…\n12:58:42 AM [api] GET /api/settings\n12:58:42 AM [express] GET /api/settings 200 in 64ms :: {\"delivery_fee\":75,\"estimated_time\":\"10-35 min…\n12:58:53 AM [api] PUT /api/admin/settings\n12:58:53 AM [express] PUT /api/admin/settings 200 in 488ms :: {\"id\":1,\"restaurant_open\":true,\"busines…\n12:58:54 AM [api] GET /api/admin/settings\n12:58:54 AM [express] GET /api/admin/settings 200 in 237ms :: {\"id\":1,\"restaurant_open\":true,\"busines…\n12:59:01 AM [api] GET /api/settings\n12:59:01 AM [api] GET /api/admin/settings\n12:59:01 AM [express] GET /api/settings 200 in 61ms :: {\"delivery_fee\":150,\"estimated_time\":\"10-35 mi…\n12:59:02 AM [express] GET /api/admin/settings 200 in 322ms :: {\"id\":1,\"restaurant_open\":true,\"busines…\n12:59:23 AM [api] GET /api/categories\n12:59:23 AM [api] GET /api/items\n12:59:23 AM [express] GET /api/categories 304 in 250ms :: [{\"id\":13,\"name\":\"Signature BBQ\",\"imageUrl\"…\n12:59:23 AM [express] GET /api/items 304 in 251ms :: [{\"id\":82,\"name\":\"Smoked Beef Brisket\",\"descript…\n12:59:54 AM [api] POST /api/categories\n12:59:54 AM [express] POST /api/categories 201 in 266ms :: {\"id\":28,\"name\":\"MAZEN\",\"imageUrl\":null}\n1:00:12 AM [api] POST /api/items\n1:00:12 AM [express] POST /api/items 201 in 300ms :: {\"id\":104,\"name\":\"parasite\",\"description\":\"jkni…\n1:00:25 AM [api] GET /api/settings\n1:00:25 AM [api] GET /api/admin/settings\n1:00:25 AM [express] GET /api/settings 304 in 61ms :: {\"delivery_fee\":150,\"estimated_time\":\"10-35 mi…\n1:00:25 AM [express] GET /api/admin/settings 304 in 238ms :: {\"id\":1,\"restaurant_open\":true,\"busines…\n1:00:27 AM [api] GET /api/categories\n1:00:27 AM [api] GET /api/dishes\n1:00:27 AM [express] GET /api/categories 200 in 61ms :: [{\"id\":13,\"name\":\"Signature BBQ\",\"imageUrl\":…\n1:00:27 AM [express] GET /api/dishes 200 in 64ms :: [{\"id\":82,\"name\":\"Smoked Beef Brisket\",\"descript…\n1:00:44 AM [api] GET /api/admin/settings\n1:00:44 AM [api] GET /api/dishes\n1:00:44 AM [api] GET /api/categories\n1:00:44 AM [express] GET /api/dishes 304 in 65ms :: [{\"id\":82,\"name\":\"Smoked Beef Brisket\",\"descript…\n1:00:44 AM [express] GET /api/categories 304 in 67ms :: [{\"id\":13,\"name\":\"Signature BBQ\",\"imageUrl\":…\n1:00:45 AM [express] GET /api/admin/settings 304 in 247ms :: {\"id\":1,\"restaurant_open\":true,\"busines…\n"}