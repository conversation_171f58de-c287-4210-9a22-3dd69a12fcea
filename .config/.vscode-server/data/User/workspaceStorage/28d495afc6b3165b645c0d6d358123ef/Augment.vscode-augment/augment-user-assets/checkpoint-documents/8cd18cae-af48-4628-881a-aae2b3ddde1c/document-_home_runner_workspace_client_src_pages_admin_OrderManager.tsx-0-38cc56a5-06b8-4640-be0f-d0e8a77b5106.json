{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/pages/admin/OrderManager.tsx"}, "originalCode": "import { useState, useEffect } from 'react';\nimport { motion } from 'framer-motion';\nimport { \n  Clock, \n  CheckCircle2, \n  X, \n  Truck, \n  Bar<PERSON>hart, \n  ChefHat,\n  ShoppingBag,\n  Package,\n  AlertTriangle\n} from 'lucide-react';\nimport AdminLayout from './AdminLayout';\nimport SingleUpdateButton from '@/components/admin/SingleUpdateButton';\n// We'll use native fetch instead of apiRequest for this component\nimport { format, parseISO } from 'date-fns';\n\n// Status options for orders\nconst ORDER_STATUSES = [\n  { value: 'confirmed', label: 'Confirmed', icon: <CheckCircle2 className=\"w-4 h-4\" /> },\n  { value: 'processing', label: 'Processing', icon: <Clock className=\"w-4 h-4\" /> },\n  { value: 'preparing', label: 'Preparing', icon: <ChefHat className=\"w-4 h-4\" /> },\n  { value: 'ready_for_pickup', label: 'Ready for Pickup', icon: <Package className=\"w-4 h-4\" /> },\n  { value: 'ready_for_delivery', label: 'Ready for Delivery', icon: <ShoppingBag className=\"w-4 h-4\" /> },\n  { value: 'out_for_delivery', label: 'Out for Delivery', icon: <Truck className=\"w-4 h-4\" /> },\n  { value: 'delivered', label: 'Delivered', icon: <CheckCircle2 className=\"w-4 h-4\" /> },\n  { value: 'completed', label: 'Completed', icon: <BarChart className=\"w-4 h-4\" /> },\n  { value: 'cancelled', label: 'Cancelled', icon: <X className=\"w-4 h-4\" /> }\n];\n\n// Format currency for display\nconst formatCurrency = (amount: number) => {\n  return `$${(amount / 100).toFixed(2)}`;\n};\n\n// Format date for display\nconst formatDate = (dateString: string) => {\n  try {\n    const date = parseISO(dateString);\n    return format(date, 'MMM d, h:mm a');\n  } catch (e) {\n    return dateString;\n  }\n};\n\ninterface OrderItem {\n  id: number;\n  name: string;\n  price: number;\n  quantity: number;\n}\n\ninterface OrderCustomer {\n  firstName: string;\n  lastName: string;\n  phone: string;\n  email: string;\n}\n\ninterface OrderDetails {\n  type: 'delivery' | 'takeaway';\n  time: 'asap' | 'scheduled';\n  scheduledTime: string | null;\n}\n\ninterface Order {\n  id: number;\n  createdAt: string;\n  status: string;\n  items: OrderItem[];\n  customer: OrderCustomer;\n  orderDetails: OrderDetails;\n  subtotal: number;\n  deliveryFee: number;\n  total: number;\n  paymentMethod: string;\n  notes: string | null;\n}\n\n// Component for status badge\nconst StatusBadge = ({ status }: { status: string }) => {\n  const statusObj = ORDER_STATUSES.find(s => s.value === status) || {\n    value: status,\n    label: status.charAt(0).toUpperCase() + status.slice(1).replace('_', ' '),\n    icon: <AlertTriangle className=\"w-4 h-4\" />\n  };\n\n  const getBadgeColor = () => {\n    switch (status) {\n      case 'confirmed':\n        return 'bg-blue-900/30 text-blue-400 border-blue-700/30';\n      case 'processing':\n        return 'bg-purple-900/30 text-purple-400 border-purple-700/30';\n      case 'preparing':\n        return 'bg-yellow-900/30 text-yellow-400 border-yellow-700/30';\n      case 'ready_for_pickup':\n      case 'ready_for_delivery':\n        return 'bg-green-900/30 text-green-400 border-green-700/30';\n      case 'out_for_delivery':\n        return 'bg-cyan-900/30 text-cyan-400 border-cyan-700/30';\n      case 'delivered':\n      case 'completed':\n        return 'bg-emerald-900/30 text-emerald-400 border-emerald-700/30';\n      case 'cancelled':\n        return 'bg-red-900/30 text-red-400 border-red-700/30';\n      default:\n        return 'bg-gray-900/30 text-gray-400 border-gray-700/30';\n    }\n  };\n  \n  return (\n    <span className={`inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium border ${getBadgeColor()}`}>\n      <span className=\"mr-1\">{statusObj.icon}</span>\n      {statusObj.label}\n    </span>\n  );\n};\n\nconst OrderManager = () => {\n  const [orders, setOrders] = useState<Order[]>([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState<string | null>(null);\n  const [statusFilter, setStatusFilter] = useState('active');\n  const [selectedOrder, setSelectedOrder] = useState<Order | null>(null);\n  const [isUpdating, setIsUpdating] = useState(false);\n\n  // Fetch orders on component mount and when status filter changes\n  useEffect(() => {\n    const fetchOrders = async () => {\n      setLoading(true);\n      try {\n        const response = await fetch(`/api/admin/orders?status=${statusFilter}`);\n        if (!response.ok) {\n          throw new Error(`Failed to fetch orders: ${response.status}`);\n        }\n        const data = await response.json();\n        setOrders(data);\n        setError(null);\n      } catch (err) {\n        console.error('Error fetching orders:', err);\n        setError('Failed to load orders. Please try again.');\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    fetchOrders();\n  }, [statusFilter]);\n\n  // Determine the next status based on current status and order type\n  const getNextStatus = (order: Order): string | null => {\n    const { status, orderDetails } = order;\n    const orderType = orderDetails.type;\n    \n    switch (status) {\n      case 'confirmed':\n        return 'preparing';\n      case 'preparing':\n        return orderType === 'delivery' ? 'ready_for_delivery' : 'ready_for_pickup';\n      case 'ready_for_pickup':\n        return 'completed';\n      case 'ready_for_delivery':\n        return 'out_for_delivery';\n      case 'out_for_delivery':\n        return 'delivered';\n      case 'delivered':\n        return 'completed';\n      default:\n        return null;\n    }\n  };\n  \n  // Get human-readable label for the next status\n  const getNextStatusLabel = (order: Order): string => {\n    const nextStatus = getNextStatus(order);\n    if (!nextStatus) return '';\n    \n    // Find the status object that matches the next status\n    const statusObj = ORDER_STATUSES.find(s => s.value === nextStatus);\n    return statusObj ? statusObj.label : nextStatus.replace('_', ' ').replace(/\\b\\w/g, l => l.toUpperCase());\n  };\n\n  // Update order status\n  const updateOrderStatus = async (orderId: number, newStatus: string) => {\n    if (!newStatus) return;\n    \n    setIsUpdating(true);\n    try {\n      const response = await fetch(`/api/admin/orders/${orderId}/status`, {\n        method: 'PUT',\n        headers: {\n          'Content-Type': 'application/json'\n        },\n        body: JSON.stringify({ newStatus })\n      });\n      \n      if (!response.ok) {\n        throw new Error(`Failed to update order status: ${response.status}`);\n      }\n      \n      // Update the orders list with the new status\n      setOrders(orders.map(order => \n        order.id === orderId \n          ? { ...order, status: newStatus }\n          : order\n      ));\n      \n      // If the selected order is the one being updated, update it as well\n      if (selectedOrder && selectedOrder.id === orderId) {\n        setSelectedOrder({ ...selectedOrder, status: newStatus });\n      }\n      \n      // Show success notification\n      console.log(`Order #${orderId} status updated to ${newStatus}`);\n      \n    } catch (err) {\n      console.error('Error updating order status:', err);\n      setError('Failed to update order status. Please try again.');\n    } finally {\n      setIsUpdating(false);\n    }\n  };\n\n  // Handle order selection\n  const handleOrderClick = (order: Order) => {\n    setSelectedOrder(order);\n  };\n\n  return (\n    <AdminLayout>\n      <motion.div \n        className=\"space-y-6\"\n        initial={{ opacity: 0, y: 20 }}\n        animate={{ opacity: 1, y: 0 }}\n        transition={{ duration: 0.3 }}\n      >\n        <div className=\"flex justify-between items-center\">\n          <h1 className=\"text-2xl font-bold bg-gradient-to-r from-orange-400 to-red-500 text-transparent bg-clip-text\">\n            Kitchen Order Manager\n          </h1>\n          <div className=\"flex space-x-2\">\n            <select\n              value={statusFilter}\n              onChange={(e) => setStatusFilter(e.target.value)}\n              className=\"bg-gray-900 border border-gray-700 rounded-lg px-3 py-2 text-sm text-white focus:ring-cyan-500 focus:border-cyan-500\"\n            >\n              <option value=\"active\">Active Orders</option>\n              {ORDER_STATUSES.map(status => (\n                <option key={status.value} value={status.value}>\n                  {status.label} Only\n                </option>\n              ))}\n              <option value=\"all\">All Orders</option>\n            </select>\n          </div>\n        </div>\n\n        {error && (\n          <div className=\"bg-red-900/40 border border-red-700 text-red-400 p-4 rounded-lg\">\n            {error}\n          </div>\n        )}\n\n        {loading ? (\n          <div className=\"flex justify-center items-center py-12\">\n            <div className=\"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-cyan-500\"></div>\n          </div>\n        ) : (\n          <div className=\"grid grid-cols-1 xl:grid-cols-[1fr_1.5fr] gap-6\">\n            {/* Orders List */}\n            <div className=\"bg-gray-900/50 rounded-xl border border-gray-800 overflow-hidden backdrop-blur-sm h-[calc(100vh-220px)] flex flex-col\">\n              <div className=\"p-4 border-b border-gray-800 bg-gray-900/80\">\n                <h2 className=\"text-lg font-medium text-white flex items-center\">\n                  <ShoppingBag className=\"w-5 h-5 mr-2 text-orange-400\" />\n                  Orders ({orders.length})\n                </h2>\n              </div>\n              \n              <div className=\"flex-1 overflow-y-auto\">\n                {orders.length === 0 ? (\n                  <div className=\"flex flex-col items-center justify-center h-full p-6 text-center\">\n                    <ShoppingBag className=\"w-16 h-16 text-gray-600 mb-4\" />\n                    <p className=\"text-gray-400\">No orders found matching your filter.</p>\n                    {statusFilter !== 'all' && (\n                      <button \n                        onClick={() => setStatusFilter('all')}\n                        className=\"mt-4 text-cyan-400 hover:text-cyan-300\"\n                      >\n                        View all orders\n                      </button>\n                    )}\n                  </div>\n                ) : (\n                  <ul className=\"divide-y divide-gray-800\">\n                    {orders.map(order => (\n                      <li \n                        key={order.id}\n                        className={`p-4 hover:bg-gray-800/50 cursor-pointer transition-colors ${selectedOrder?.id === order.id ? 'bg-gray-800/70' : ''}`}\n                        onClick={() => handleOrderClick(order)}\n                      >\n                        <div className=\"flex justify-between items-center mb-2\">\n                          <span className=\"font-medium text-white\">Order #{order.id}</span>\n                          <StatusBadge status={order.status} />\n                        </div>\n                        <div className=\"flex justify-between text-sm text-gray-400 mb-2\">\n                          <span>{formatDate(order.createdAt)}</span>\n                          <span className=\"font-medium text-white\">{formatCurrency(order.total)}</span>\n                        </div>\n                        <div className=\"flex justify-between text-sm\">\n                          <span className=\"text-gray-400\">\n                            {order.customer.firstName} {order.customer.lastName}\n                          </span>\n                          <span className=\"text-cyan-400 font-medium\">\n                            {order.orderDetails.type === 'delivery' ? 'Delivery' : 'Takeaway'}\n                          </span>\n                        </div>\n                      </li>\n                    ))}\n                  </ul>\n                )}\n              </div>\n            </div>\n            \n            {/* Order Details */}\n            <div className=\"bg-gray-900/50 rounded-xl border border-gray-800 overflow-hidden backdrop-blur-sm h-[calc(100vh-220px)] flex flex-col\">\n              {selectedOrder ? (\n                <>\n                  <div className=\"p-4 border-b border-gray-800 bg-gray-900/80 flex justify-between items-center\">\n                    <h2 className=\"text-lg font-medium text-white\">Order #{selectedOrder.id} Details</h2>\n                    <StatusBadge status={selectedOrder.status} />\n                  </div>\n                  \n                  <div className=\"flex-1 overflow-y-auto p-4 space-y-6\">\n                    {/* Order Info */}\n                    <div className=\"grid grid-cols-2 gap-4 text-sm\">\n                      <div className=\"space-y-1\">\n                        <p className=\"text-gray-500\">Order Date</p>\n                        <p className=\"text-white\">{formatDate(selectedOrder.createdAt)}</p>\n                      </div>\n                      <div className=\"space-y-1\">\n                        <p className=\"text-gray-500\">Order Type</p>\n                        <p className=\"text-white capitalize\">{selectedOrder.orderDetails.type}</p>\n                      </div>\n                      <div className=\"space-y-1\">\n                        <p className=\"text-gray-500\">Payment Method</p>\n                        <p className=\"text-white capitalize\">{selectedOrder.paymentMethod}</p>\n                      </div>\n                      <div className=\"space-y-1\">\n                        <p className=\"text-gray-500\">Timing</p>\n                        <p className=\"text-white capitalize\">\n                          {selectedOrder.orderDetails.time === 'asap' \n                            ? 'ASAP' \n                            : `Scheduled: ${selectedOrder.orderDetails.scheduledTime \n                                ? formatDate(selectedOrder.orderDetails.scheduledTime) \n                                : 'N/A'}`\n                          }\n                        </p>\n                      </div>\n                    </div>\n                    \n                    {/* Customer Info */}\n                    <div>\n                      <h3 className=\"text-md font-medium text-white mb-3\">Customer Information</h3>\n                      <div className=\"bg-gray-800/50 rounded-lg p-4 text-sm space-y-2\">\n                        <p className=\"text-white\">\n                          {selectedOrder.customer.firstName} {selectedOrder.customer.lastName}\n                        </p>\n                        <p className=\"text-gray-400\">{selectedOrder.customer.email}</p>\n                        <p className=\"text-gray-400\">{selectedOrder.customer.phone}</p>\n                      </div>\n                    </div>\n                    \n                    {/* Order Items */}\n                    <div>\n                      <h3 className=\"text-md font-medium text-white mb-3\">Order Items</h3>\n                      <div className=\"bg-gray-800/50 rounded-lg overflow-hidden\">\n                        <ul className=\"divide-y divide-gray-700\">\n                          {selectedOrder.items.map(item => (\n                            <li key={item.id} className=\"p-3 flex justify-between\">\n                              <div className=\"flex items-start\">\n                                <span className=\"text-orange-400 font-medium mr-2\">{item.quantity}x</span>\n                                <span className=\"text-white\">{item.name}</span>\n                              </div>\n                              <span className=\"text-gray-300\">{formatCurrency(item.price * item.quantity)}</span>\n                            </li>\n                          ))}\n                        </ul>\n                        \n                        {/* Order Totals */}\n                        <div className=\"border-t border-gray-700 p-3 space-y-1 text-sm\">\n                          <div className=\"flex justify-between\">\n                            <span className=\"text-gray-400\">Subtotal</span>\n                            <span className=\"text-white\">{formatCurrency(selectedOrder.subtotal)}</span>\n                          </div>\n                          <div className=\"flex justify-between\">\n                            <span className=\"text-gray-400\">Delivery Fee</span>\n                            <span className=\"text-white\">{formatCurrency(selectedOrder.deliveryFee)}</span>\n                          </div>\n                          <div className=\"flex justify-between font-medium pt-1\">\n                            <span className=\"text-gray-300\">Total</span>\n                            <span className=\"text-white\">{formatCurrency(selectedOrder.total)}</span>\n                          </div>\n                        </div>\n                      </div>\n                    </div>\n                    \n                    {/* Order Notes */}\n                    {selectedOrder.notes && (\n                      <div>\n                        <h3 className=\"text-md font-medium text-white mb-3\">Special Instructions</h3>\n                        <div className=\"bg-gray-800/50 rounded-lg p-4 text-sm text-gray-300\">\n                          {selectedOrder.notes}\n                        </div>\n                      </div>\n                    )}\n                    \n                    {/* Status Management - Simplified with single update button */}\n                    <div>\n                      <h3 className=\"text-md font-medium text-white mb-3\">Update Order Status</h3>\n                      <div className=\"bg-gray-800/50 rounded-lg p-4\">\n                        <SingleUpdateButton \n                          currentStatus={selectedOrder.status}\n                          nextStatus={getNextStatus(selectedOrder)}\n                          nextStatusLabel={getNextStatusLabel(selectedOrder)}\n                          isUpdating={isUpdating}\n                          onUpdate={() => {\n                            const nextStatus = getNextStatus(selectedOrder);\n                            if (nextStatus) {\n                              updateOrderStatus(selectedOrder.id, nextStatus);\n                            }\n                          }}\n                        />\n                      </div>\n                    </div>\n                  </div>\n                </>\n              ) : (\n                <div className=\"flex flex-col items-center justify-center h-full p-6 text-center\">\n                  <Package className=\"w-16 h-16 text-gray-600 mb-4\" />\n                  <p className=\"text-gray-400\">Select an order to see details.</p>\n                </div>\n              )}\n            </div>\n          </div>\n        )}\n      </motion.div>\n    </AdminLayout>\n  );\n};\n\nexport default OrderManager;", "modifiedCode": "import { useState, useEffect } from 'react';\nimport { motion } from 'framer-motion';\nimport { \n  Clock, \n  CheckCircle2, \n  X, \n  Truck, \n  Bar<PERSON>hart, \n  ChefHat,\n  ShoppingBag,\n  Package,\n  AlertTriangle\n} from 'lucide-react';\nimport AdminLayout from './AdminLayout';\nimport SingleUpdateButton from '@/components/admin/SingleUpdateButton';\n// We'll use native fetch instead of apiRequest for this component\nimport { format, parseISO } from 'date-fns';\n\n// Status options for orders\nconst ORDER_STATUSES = [\n  { value: 'confirmed', label: 'Confirmed', icon: <CheckCircle2 className=\"w-4 h-4\" /> },\n  { value: 'processing', label: 'Processing', icon: <Clock className=\"w-4 h-4\" /> },\n  { value: 'preparing', label: 'Preparing', icon: <ChefHat className=\"w-4 h-4\" /> },\n  { value: 'ready_for_pickup', label: 'Ready for Pickup', icon: <Package className=\"w-4 h-4\" /> },\n  { value: 'ready_for_delivery', label: 'Ready for Delivery', icon: <ShoppingBag className=\"w-4 h-4\" /> },\n  { value: 'out_for_delivery', label: 'Out for Delivery', icon: <Truck className=\"w-4 h-4\" /> },\n  { value: 'delivered', label: 'Delivered', icon: <CheckCircle2 className=\"w-4 h-4\" /> },\n  { value: 'completed', label: 'Completed', icon: <BarChart className=\"w-4 h-4\" /> },\n  { value: 'cancelled', label: 'Cancelled', icon: <X className=\"w-4 h-4\" /> }\n];\n\n// Format currency for display\nconst formatCurrency = (amount: number) => {\n  return `$${(amount / 100).toFixed(2)}`;\n};\n\n// Format date for display\nconst formatDate = (dateString: string) => {\n  try {\n    const date = parseISO(dateString);\n    return format(date, 'MMM d, h:mm a');\n  } catch (e) {\n    return dateString;\n  }\n};\n\ninterface OrderItem {\n  id: number;\n  name: string;\n  price: number;\n  quantity: number;\n}\n\ninterface OrderCustomer {\n  firstName: string;\n  lastName: string;\n  phone: string;\n  email: string;\n}\n\ninterface OrderDetails {\n  type: 'delivery' | 'takeaway';\n  time: 'asap' | 'scheduled';\n  scheduledTime: string | null;\n}\n\ninterface Order {\n  id: number;\n  createdAt: string;\n  status: string;\n  items: OrderItem[];\n  customer: OrderCustomer;\n  orderDetails: OrderDetails;\n  subtotal: number;\n  deliveryFee: number;\n  total: number;\n  paymentMethod: string;\n  notes: string | null;\n}\n\n// Component for status badge\nconst StatusBadge = ({ status }: { status: string }) => {\n  const statusObj = ORDER_STATUSES.find(s => s.value === status) || {\n    value: status,\n    label: status.charAt(0).toUpperCase() + status.slice(1).replace('_', ' '),\n    icon: <AlertTriangle className=\"w-4 h-4\" />\n  };\n\n  const getBadgeColor = () => {\n    switch (status) {\n      case 'confirmed':\n        return 'bg-blue-900/30 text-blue-400 border-blue-700/30';\n      case 'processing':\n        return 'bg-purple-900/30 text-purple-400 border-purple-700/30';\n      case 'preparing':\n        return 'bg-yellow-900/30 text-yellow-400 border-yellow-700/30';\n      case 'ready_for_pickup':\n      case 'ready_for_delivery':\n        return 'bg-green-900/30 text-green-400 border-green-700/30';\n      case 'out_for_delivery':\n        return 'bg-cyan-900/30 text-cyan-400 border-cyan-700/30';\n      case 'delivered':\n      case 'completed':\n        return 'bg-emerald-900/30 text-emerald-400 border-emerald-700/30';\n      case 'cancelled':\n        return 'bg-red-900/30 text-red-400 border-red-700/30';\n      default:\n        return 'bg-gray-900/30 text-gray-400 border-gray-700/30';\n    }\n  };\n  \n  return (\n    <span className={`inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium border ${getBadgeColor()}`}>\n      <span className=\"mr-1\">{statusObj.icon}</span>\n      {statusObj.label}\n    </span>\n  );\n};\n\nconst OrderManager = () => {\n  const [orders, setOrders] = useState<Order[]>([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState<string | null>(null);\n  const [statusFilter, setStatusFilter] = useState('active');\n  const [selectedOrder, setSelectedOrder] = useState<Order | null>(null);\n  const [isUpdating, setIsUpdating] = useState(false);\n\n  // Fetch orders on component mount and when status filter changes\n  useEffect(() => {\n    const fetchOrders = async () => {\n      setLoading(true);\n      try {\n        const response = await fetch(`/api/admin/orders?status=${statusFilter}`);\n        if (!response.ok) {\n          throw new Error(`Failed to fetch orders: ${response.status}`);\n        }\n        const data = await response.json();\n        setOrders(data);\n        setError(null);\n      } catch (err) {\n        console.error('Error fetching orders:', err);\n        setError('Failed to load orders. Please try again.');\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    fetchOrders();\n  }, [statusFilter]);\n\n  // Determine the next status based on current status and order type\n  const getNextStatus = (order: Order): string | null => {\n    const { status, orderDetails } = order;\n    const orderType = orderDetails.type;\n    \n    switch (status) {\n      case 'confirmed':\n        return 'preparing';\n      case 'preparing':\n        return orderType === 'delivery' ? 'ready_for_delivery' : 'ready_for_pickup';\n      case 'ready_for_pickup':\n        return 'completed';\n      case 'ready_for_delivery':\n        return 'out_for_delivery';\n      case 'out_for_delivery':\n        return 'delivered';\n      case 'delivered':\n        return 'completed';\n      default:\n        return null;\n    }\n  };\n  \n  // Get human-readable label for the next status\n  const getNextStatusLabel = (order: Order): string => {\n    const nextStatus = getNextStatus(order);\n    if (!nextStatus) return '';\n    \n    // Find the status object that matches the next status\n    const statusObj = ORDER_STATUSES.find(s => s.value === nextStatus);\n    return statusObj ? statusObj.label : nextStatus.replace('_', ' ').replace(/\\b\\w/g, l => l.toUpperCase());\n  };\n\n  // Update order status\n  const updateOrderStatus = async (orderId: number, newStatus: string) => {\n    if (!newStatus) return;\n    \n    setIsUpdating(true);\n    try {\n      const response = await fetch(`/api/admin/orders/${orderId}/status`, {\n        method: 'PUT',\n        headers: {\n          'Content-Type': 'application/json'\n        },\n        body: JSON.stringify({ newStatus })\n      });\n      \n      if (!response.ok) {\n        throw new Error(`Failed to update order status: ${response.status}`);\n      }\n      \n      // Update the orders list with the new status\n      setOrders(orders.map(order => \n        order.id === orderId \n          ? { ...order, status: newStatus }\n          : order\n      ));\n      \n      // If the selected order is the one being updated, update it as well\n      if (selectedOrder && selectedOrder.id === orderId) {\n        setSelectedOrder({ ...selectedOrder, status: newStatus });\n      }\n      \n      // Show success notification\n      console.log(`Order #${orderId} status updated to ${newStatus}`);\n      \n    } catch (err) {\n      console.error('Error updating order status:', err);\n      setError('Failed to update order status. Please try again.');\n    } finally {\n      setIsUpdating(false);\n    }\n  };\n\n  // Handle order selection\n  const handleOrderClick = (order: Order) => {\n    setSelectedOrder(order);\n  };\n\n  return (\n    <AdminLayout>\n      <motion.div \n        className=\"space-y-6\"\n        initial={{ opacity: 0, y: 20 }}\n        animate={{ opacity: 1, y: 0 }}\n        transition={{ duration: 0.3 }}\n      >\n        <div className=\"flex justify-between items-center\">\n          <h1 className=\"text-2xl font-bold bg-gradient-to-r from-orange-400 to-red-500 text-transparent bg-clip-text\">\n            Kitchen Order Manager\n          </h1>\n          <div className=\"flex space-x-2\">\n            <select\n              value={statusFilter}\n              onChange={(e) => setStatusFilter(e.target.value)}\n              className=\"bg-gray-900 border border-gray-700 rounded-lg px-3 py-2 text-sm text-white focus:ring-cyan-500 focus:border-cyan-500\"\n            >\n              <option value=\"active\">Active Orders</option>\n              {ORDER_STATUSES.map(status => (\n                <option key={status.value} value={status.value}>\n                  {status.label} Only\n                </option>\n              ))}\n              <option value=\"all\">All Orders</option>\n            </select>\n          </div>\n        </div>\n\n        {error && (\n          <div className=\"bg-red-900/40 border border-red-700 text-red-400 p-4 rounded-lg\">\n            {error}\n          </div>\n        )}\n\n        {loading ? (\n          <div className=\"flex justify-center items-center py-12\">\n            <div className=\"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-cyan-500\"></div>\n          </div>\n        ) : (\n          <div className=\"grid grid-cols-1 xl:grid-cols-[1fr_1.5fr] gap-6\">\n            {/* Orders List */}\n            <div className=\"bg-gray-900/50 rounded-xl border border-gray-800 overflow-hidden backdrop-blur-sm h-[calc(100vh-220px)] flex flex-col\">\n              <div className=\"p-4 border-b border-gray-800 bg-gray-900/80\">\n                <h2 className=\"text-lg font-medium text-white flex items-center\">\n                  <ShoppingBag className=\"w-5 h-5 mr-2 text-orange-400\" />\n                  Orders ({orders.length})\n                </h2>\n              </div>\n              \n              <div className=\"flex-1 overflow-y-auto\">\n                {orders.length === 0 ? (\n                  <div className=\"flex flex-col items-center justify-center h-full p-6 text-center\">\n                    <ShoppingBag className=\"w-16 h-16 text-gray-600 mb-4\" />\n                    <p className=\"text-gray-400\">No orders found matching your filter.</p>\n                    {statusFilter !== 'all' && (\n                      <button \n                        onClick={() => setStatusFilter('all')}\n                        className=\"mt-4 text-cyan-400 hover:text-cyan-300\"\n                      >\n                        View all orders\n                      </button>\n                    )}\n                  </div>\n                ) : (\n                  <ul className=\"divide-y divide-gray-800\">\n                    {orders.map(order => (\n                      <li \n                        key={order.id}\n                        className={`p-4 hover:bg-gray-800/50 cursor-pointer transition-colors ${selectedOrder?.id === order.id ? 'bg-gray-800/70' : ''}`}\n                        onClick={() => handleOrderClick(order)}\n                      >\n                        <div className=\"flex justify-between items-center mb-2\">\n                          <span className=\"font-medium text-white\">Order #{order.id}</span>\n                          <StatusBadge status={order.status} />\n                        </div>\n                        <div className=\"flex justify-between text-sm text-gray-400 mb-2\">\n                          <span>{formatDate(order.createdAt)}</span>\n                          <span className=\"font-medium text-white\">{formatCurrency(order.total)}</span>\n                        </div>\n                        <div className=\"flex justify-between text-sm\">\n                          <span className=\"text-gray-400\">\n                            {order.customer.firstName} {order.customer.lastName}\n                          </span>\n                          <span className=\"text-cyan-400 font-medium\">\n                            {order.orderDetails.type === 'delivery' ? 'Delivery' : 'Takeaway'}\n                          </span>\n                        </div>\n                      </li>\n                    ))}\n                  </ul>\n                )}\n              </div>\n            </div>\n            \n            {/* Order Details */}\n            <div className=\"bg-gray-900/50 rounded-xl border border-gray-800 overflow-hidden backdrop-blur-sm h-[calc(100vh-220px)] flex flex-col\">\n              {selectedOrder ? (\n                <>\n                  <div className=\"p-4 border-b border-gray-800 bg-gray-900/80 flex justify-between items-center\">\n                    <h2 className=\"text-lg font-medium text-white\">Order #{selectedOrder.id} Details</h2>\n                    <StatusBadge status={selectedOrder.status} />\n                  </div>\n                  \n                  <div className=\"flex-1 overflow-y-auto p-4 space-y-6\">\n                    {/* Order Info */}\n                    <div className=\"grid grid-cols-2 gap-4 text-sm\">\n                      <div className=\"space-y-1\">\n                        <p className=\"text-gray-500\">Order Date</p>\n                        <p className=\"text-white\">{formatDate(selectedOrder.createdAt)}</p>\n                      </div>\n                      <div className=\"space-y-1\">\n                        <p className=\"text-gray-500\">Order Type</p>\n                        <p className=\"text-white capitalize\">{selectedOrder.orderDetails.type}</p>\n                      </div>\n                      <div className=\"space-y-1\">\n                        <p className=\"text-gray-500\">Payment Method</p>\n                        <p className=\"text-white capitalize\">{selectedOrder.paymentMethod}</p>\n                      </div>\n                      <div className=\"space-y-1\">\n                        <p className=\"text-gray-500\">Timing</p>\n                        <p className=\"text-white capitalize\">\n                          {selectedOrder.orderDetails.time === 'asap' \n                            ? 'ASAP' \n                            : `Scheduled: ${selectedOrder.orderDetails.scheduledTime \n                                ? formatDate(selectedOrder.orderDetails.scheduledTime) \n                                : 'N/A'}`\n                          }\n                        </p>\n                      </div>\n                    </div>\n                    \n                    {/* Customer Info */}\n                    <div>\n                      <h3 className=\"text-md font-medium text-white mb-3\">Customer Information</h3>\n                      <div className=\"bg-gray-800/50 rounded-lg p-4 text-sm space-y-2\">\n                        <p className=\"text-white\">\n                          {selectedOrder.customer.firstName} {selectedOrder.customer.lastName}\n                        </p>\n                        <p className=\"text-gray-400\">{selectedOrder.customer.email}</p>\n                        <p className=\"text-gray-400\">{selectedOrder.customer.phone}</p>\n                      </div>\n                    </div>\n                    \n                    {/* Order Items */}\n                    <div>\n                      <h3 className=\"text-md font-medium text-white mb-3\">Order Items</h3>\n                      <div className=\"bg-gray-800/50 rounded-lg overflow-hidden\">\n                        <ul className=\"divide-y divide-gray-700\">\n                          {selectedOrder.items.map(item => (\n                            <li key={item.id} className=\"p-3 flex justify-between\">\n                              <div className=\"flex items-start\">\n                                <span className=\"text-orange-400 font-medium mr-2\">{item.quantity}x</span>\n                                <span className=\"text-white\">{item.name}</span>\n                              </div>\n                              <span className=\"text-gray-300\">{formatCurrency(item.price * item.quantity)}</span>\n                            </li>\n                          ))}\n                        </ul>\n                        \n                        {/* Order Totals */}\n                        <div className=\"border-t border-gray-700 p-3 space-y-1 text-sm\">\n                          <div className=\"flex justify-between\">\n                            <span className=\"text-gray-400\">Subtotal</span>\n                            <span className=\"text-white\">{formatCurrency(selectedOrder.subtotal)}</span>\n                          </div>\n                          <div className=\"flex justify-between\">\n                            <span className=\"text-gray-400\">Delivery Fee</span>\n                            <span className=\"text-white\">{formatCurrency(selectedOrder.deliveryFee)}</span>\n                          </div>\n                          <div className=\"flex justify-between font-medium pt-1\">\n                            <span className=\"text-gray-300\">Total</span>\n                            <span className=\"text-white\">{formatCurrency(selectedOrder.total)}</span>\n                          </div>\n                        </div>\n                      </div>\n                    </div>\n                    \n                    {/* Order Notes */}\n                    {selectedOrder.notes && (\n                      <div>\n                        <h3 className=\"text-md font-medium text-white mb-3\">Special Instructions</h3>\n                        <div className=\"bg-gray-800/50 rounded-lg p-4 text-sm text-gray-300\">\n                          {selectedOrder.notes}\n                        </div>\n                      </div>\n                    )}\n                    \n                    {/* Status Management - Simplified with single update button */}\n                    <div>\n                      <h3 className=\"text-md font-medium text-white mb-3\">Update Order Status</h3>\n                      <div className=\"bg-gray-800/50 rounded-lg p-4\">\n                        <SingleUpdateButton \n                          currentStatus={selectedOrder.status}\n                          nextStatus={getNextStatus(selectedOrder)}\n                          nextStatusLabel={getNextStatusLabel(selectedOrder)}\n                          isUpdating={isUpdating}\n                          onUpdate={() => {\n                            const nextStatus = getNextStatus(selectedOrder);\n                            if (nextStatus) {\n                              updateOrderStatus(selectedOrder.id, nextStatus);\n                            }\n                          }}\n                        />\n                      </div>\n                    </div>\n                  </div>\n                </>\n              ) : (\n                <div className=\"flex flex-col items-center justify-center h-full p-6 text-center\">\n                  <Package className=\"w-16 h-16 text-gray-600 mb-4\" />\n                  <p className=\"text-gray-400\">Select an order to see details.</p>\n                </div>\n              )}\n            </div>\n          </div>\n        )}\n      </motion.div>\n    </AdminLayout>\n  );\n};\n\nexport default OrderManager;"}