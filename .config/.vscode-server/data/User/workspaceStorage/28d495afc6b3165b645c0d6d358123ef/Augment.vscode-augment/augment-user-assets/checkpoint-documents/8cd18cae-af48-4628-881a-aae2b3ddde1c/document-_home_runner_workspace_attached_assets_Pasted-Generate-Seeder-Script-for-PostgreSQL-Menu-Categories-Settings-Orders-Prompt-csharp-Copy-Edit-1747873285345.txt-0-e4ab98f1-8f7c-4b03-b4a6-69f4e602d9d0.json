{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "attached_assets/Pasted-Generate-Seeder-Script-for-PostgreSQL-Menu-Categories-Settings-Orders-Prompt-csharp-Copy-Edit-1747873285345.txt"}, "originalCode": "Generate Seeder Script for PostgreSQL (Menu, Categories, Settings, Orders)\nPrompt:\n\ncsharp\nCopy\nEdit\nCreate a SQL seed script to populate PostgreSQL tables for a restaurant system. The data is meant for development/testing and should include:\n\n### 1. Restaurant Settings:\nInsert default values into `restaurant_settings`:\n- restaurant_open = true\n- business_hours = JSON:\n  {\n    \"mon\": { \"open\": \"10:00\", \"close\": \"22:00\", \"delivery\": true },\n    \"tue\": { ... },\n    ...\n  }\n- delivery_fee = 49\n- estimated_time = \"25–35 min\"\n\n### 2. Categories:\nInsert ~5 categories:\n- BurgerZ\n- PizzaZ\n- SaladZ\n- DrinkZ\n- DessertZ\n\nInclude mock image URLs for each.\n\n### 3. Menu Items:\nFor each category, insert 3–5 menu items with:\n- name, description, price (e.g., 89–229 NOK)\n- image URL\n- linked category_id\n\n### 4. Orders:\nInsert mock orders into `orders`:\n- 15–20 orders from the past 30 days\n- each with varying totals (e.g., 180–560 NOK)\n- `created_at` should simulate recent dates\n\nAlso insert related `order_items` for each order with random item IDs and quantities.\n\nEnsure the script is runnable directly in psql or via Prisma (if Prisma is used).\n🧑‍💻 Prompt – Build a Basic Admin CMS-style Dashboard UI\nPrompt:\n\nmarkdown\nCopy\nEdit\nCreate a lightweight, elegant **Admin Dashboard** in React + TailwindCSS that mimics a CMS layout.\n\n### Layout:\n- Fixed left sidebar with navigation:\n  - Dashboard (overview)\n  - Menu\n  - Categories\n  - Settings\n  - Analytics\n\n- Top navbar with:\n  - Admin name or logo\n  - Restaurant status toggle (Open/Closed)\n\n### Features:\n- Dashboard home `/admin`:\n  - Glowing cards:\n    - Today’s Orders\n    - Total Revenue This Week\n    - Restaurant Status\n  - Quick link buttons (e.g., “Add New Item”)\n\n- Sidebar navigation with glowing active link and transition\n- Use Framer Motion for:\n  - Page transitions\n  - Slide-in modals for forms\n  - Animated status indicators (open/closed)\n\n- Mobile responsive: Sidebar collapses into menu drawer\n\n### Components:\n- `Sidebar.jsx`\n- `Topbar.jsx`\n- `DashboardCard.jsx`\n- `StatusToggle.jsx`\n\nMake all components reusable and neon-themed with glassmorphism effects. Use Tailwind’s `backdrop-blur`, `ring`, `shadow`, and `transition` utilities.\n✅ Output:\nA real-feeling dashboard suitable for use by admins or staff.\n\nSeed data makes development smooth from day one.\n\nEntire admin backend and UI is ready for production-grade integration.\n\n", "modifiedCode": "Generate Seeder Script for PostgreSQL (Menu, Categories, Settings, Orders)\nPrompt:\n\ncsharp\nCopy\nEdit\nCreate a SQL seed script to populate PostgreSQL tables for a restaurant system. The data is meant for development/testing and should include:\n\n### 1. Restaurant Settings:\nInsert default values into `restaurant_settings`:\n- restaurant_open = true\n- business_hours = JSON:\n  {\n    \"mon\": { \"open\": \"10:00\", \"close\": \"22:00\", \"delivery\": true },\n    \"tue\": { ... },\n    ...\n  }\n- delivery_fee = 49\n- estimated_time = \"25–35 min\"\n\n### 2. Categories:\nInsert ~5 categories:\n- BurgerZ\n- PizzaZ\n- SaladZ\n- DrinkZ\n- DessertZ\n\nInclude mock image URLs for each.\n\n### 3. Menu Items:\nFor each category, insert 3–5 menu items with:\n- name, description, price (e.g., 89–229 NOK)\n- image URL\n- linked category_id\n\n### 4. Orders:\nInsert mock orders into `orders`:\n- 15–20 orders from the past 30 days\n- each with varying totals (e.g., 180–560 NOK)\n- `created_at` should simulate recent dates\n\nAlso insert related `order_items` for each order with random item IDs and quantities.\n\nEnsure the script is runnable directly in psql or via Prisma (if Prisma is used).\n🧑‍💻 Prompt – Build a Basic Admin CMS-style Dashboard UI\nPrompt:\n\nmarkdown\nCopy\nEdit\nCreate a lightweight, elegant **Admin Dashboard** in React + TailwindCSS that mimics a CMS layout.\n\n### Layout:\n- Fixed left sidebar with navigation:\n  - Dashboard (overview)\n  - Menu\n  - Categories\n  - Settings\n  - Analytics\n\n- Top navbar with:\n  - Admin name or logo\n  - Restaurant status toggle (Open/Closed)\n\n### Features:\n- Dashboard home `/admin`:\n  - Glowing cards:\n    - Today’s Orders\n    - Total Revenue This Week\n    - Restaurant Status\n  - Quick link buttons (e.g., “Add New Item”)\n\n- Sidebar navigation with glowing active link and transition\n- Use Framer Motion for:\n  - Page transitions\n  - Slide-in modals for forms\n  - Animated status indicators (open/closed)\n\n- Mobile responsive: Sidebar collapses into menu drawer\n\n### Components:\n- `Sidebar.jsx`\n- `Topbar.jsx`\n- `DashboardCard.jsx`\n- `StatusToggle.jsx`\n\nMake all components reusable and neon-themed with glassmorphism effects. Use Tailwind’s `backdrop-blur`, `ring`, `shadow`, and `transition` utilities.\n✅ Output:\nA real-feeling dashboard suitable for use by admins or staff.\n\nSeed data makes development smooth from day one.\n\nEntire admin backend and UI is ready for production-grade integration.\n\n"}