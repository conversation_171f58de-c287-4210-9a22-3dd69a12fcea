{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "scripts/seed_database.sql"}, "originalCode": "-- Barbecuez Restaurant Database Seeder Script\n-- This script populates the database with sample data for development/testing\n\n-- Clear existing data (if any)\nTRUNCATE TABLE categories CASCADE;\nTRUNCATE TABLE menu_items CASCADE;\nTRUNCATE TABLE orders CASCADE;\n\n-- Create restaurant_settings table if it doesn't exist\nCREATE TABLE IF NOT EXISTS restaurant_settings (\n  id SERIAL PRIMARY KEY,\n  restaurant_open BOOLEAN NOT NULL DEFAULT true,\n  business_hours JSONB NOT NULL,\n  delivery_fee INTEGER NOT NULL,\n  estimated_time TEXT NOT NULL\n);\n\n-- Update restaurant settings (using upsert to avoid duplicate key errors)\nINSERT INTO restaurant_settings (id, restaurant_open, business_hours, delivery_fee, estimated_time)\nVALUES (\n  1, \n  true, \n  '{\n    \"monday\": { \"open\": \"10:00\", \"close\": \"22:00\", \"delivery\": true },\n    \"tuesday\": { \"open\": \"10:00\", \"close\": \"22:00\", \"delivery\": true },\n    \"wednesday\": { \"open\": \"10:00\", \"close\": \"22:00\", \"delivery\": true },\n    \"thursday\": { \"open\": \"10:00\", \"close\": \"22:00\", \"delivery\": true },\n    \"friday\": { \"open\": \"10:00\", \"close\": \"23:00\", \"delivery\": true },\n    \"saturday\": { \"open\": \"10:00\", \"close\": \"23:00\", \"delivery\": true },\n    \"sunday\": { \"open\": \"12:00\", \"close\": \"21:00\", \"delivery\": true }\n  }',\n  49,\n  '25-35 min'\n) \nON CONFLICT (id) DO UPDATE SET \n  restaurant_open = EXCLUDED.restaurant_open,\n  business_hours = EXCLUDED.business_hours,\n  delivery_fee = EXCLUDED.delivery_fee,\n  estimated_time = EXCLUDED.estimated_time;\n\n-- Categories are already inserted, skip this part\n-- INSERT INTO categories (name, image_url)\n-- VALUES \n--   ('Signature BBQ', 'https://images.unsplash.com/photo-1529193591184-b1d58069ecdd?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=700&q=80'),\n--   ('BBQ Burgers', 'https://images.unsplash.com/photo-1568901346375-23c9450c58cd?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=700&q=80'),\n--   ('Sides & Shareables', 'https://images.unsplash.com/photo-1576020422280-3c10743b934b?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=700&q=80'),\n--   ('Beverages', 'https://images.unsplash.com/photo-1581345331973-9e6c5c0ec1b9?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=700&q=80'),\n--   ('Desserts', 'https://images.unsplash.com/photo-1488900128323-21503983a07e?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=700&q=80');\n\n-- Insert menu items\nINSERT INTO menu_items (name, description, price, image_url, category_id, available)\nVALUES\n  -- Signature BBQ (ID: 13)\n  ('Smoked Beef Brisket', '24-hour slow-smoked USDA prime beef brisket with our signature rub', 189, 'https://images.unsplash.com/photo-1529193591184-b1d58069ecdd?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=700&q=80', 13, true),\n  ('Baby Back Ribs', 'Tender pork ribs smoked for 6 hours and glazed with our bourbon BBQ sauce', 179, 'https://images.unsplash.com/photo-1544025162-d76694265947?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=700&q=80', 13, true),\n  ('Pulled Pork Platter', 'Hickory-smoked pulled pork served with texas toast and two sides', 159, 'https://images.unsplash.com/photo-1623653538388-3082fd168cd7?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=700&q=80', 13, true),\n  ('BBQ Chicken Half', 'Juicy half chicken smoked and finished on the grill with our signature sauce', 149, 'https://images.unsplash.com/photo-1635321593217-40050ad13c74?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=700&q=80', 13, true),\n  ('Smoked Sausage Links', 'House-made smoked sausages with jalapeño and cheddar', 139, 'https://images.unsplash.com/photo-1597712700478-7caa2afb6d5b?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=700&q=80', 13, true),\n  \n  -- BBQ Burgers (ID: 14)\n  ('Smokehouse Burger', 'Angus beef patty topped with smoked brisket, cheddar, and bourbon BBQ sauce', 169, 'https://images.unsplash.com/photo-1568901346375-23c9450c58cd?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=700&q=80', 14, true),\n  ('The Triple B', 'Beef, bacon, and brisket burger with smoked gouda and caramelized onions', 179, 'https://images.unsplash.com/photo-1553979459-d2229ba7433b?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=700&q=80', 14, true),\n  ('Pulled Pork Burger', 'Beef patty topped with pulled pork, coleslaw, and Memphis BBQ sauce', 159, 'https://images.unsplash.com/photo-1572802419224-296b0aeee0d9?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=700&q=80', 14, true),\n  ('Smoked Portobello Burger', 'Smoked portobello mushroom with Swiss cheese and truffle aioli', 149, 'https://images.unsplash.com/photo-1520072959219-c595dc870360?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=700&q=80', 14, true),\n\n  -- Sides & Shareables (ID: 15)\n  ('Burnt Ends', 'Crispy, caramelized brisket ends tossed in BBQ sauce', 129, 'https://images.unsplash.com/photo-1576020422280-3c10743b934b?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=700&q=80', 15, true),\n  ('Loaded BBQ Fries', 'Hand-cut fries topped with pulled pork, cheese sauce, and jalapeños', 119, 'https://images.unsplash.com/photo-1585109649408-c35fe02e1335?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=700&q=80', 15, true),\n  ('Smoked Wings', 'Hickory-smoked wings with your choice of sauce: Classic BBQ, Buffalo, or Honey Sriracha', 139, 'https://images.unsplash.com/photo-1567620832903-9fc6debc209f?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=700&q=80', 15, true),\n  ('Mac & Cheese', 'Creamy five-cheese mac topped with crispy breadcrumbs', 89, 'https://images.unsplash.com/photo-1543352634-a1c51d9f1fa7?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=700&q=80', 15, true),\n  ('Cornbread Skillet', 'Sweet jalapeño cornbread baked in a cast-iron skillet with honey butter', 79, 'https://images.unsplash.com/photo-1612203985729-70726954388c?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=700&q=80', 15, true),\n  \n  -- Beverages (ID: 16)\n  ('Craft Beer Selection', 'Rotating selection of local craft beers', 89, 'https://images.unsplash.com/photo-1581345331973-9e6c5c0ec1b9?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=700&q=80', 16, true),\n  ('House-Made Lemonade', 'Fresh-squeezed lemonade with hint of smoked rosemary', 59, 'https://images.unsplash.com/photo-1523677011781-c91d1bbe2f9e?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=700&q=80', 16, true),\n  ('Sweet Tea', 'Southern-style sweet tea infused with mint', 49, 'https://images.unsplash.com/photo-1543007168-9844a6f9a822?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=700&q=80', 16, true),\n  ('Bourbon Selection', 'Selection of premium bourbons, neat or on the rocks', 129, 'https://images.unsplash.com/photo-1527281400683-1aae777175f8?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=700&q=80', 16, true),\n  \n  -- Desserts (ID: 17)\n  ('Smoked Apple Cobbler', 'Smoked apples with cinnamon streusel topping and vanilla ice cream', 99, 'https://images.unsplash.com/photo-1488900128323-21503983a07e?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=700&q=80', 17, true),\n  ('Bourbon Pecan Pie', 'Homemade pecan pie with a bourbon twist, served with whipped cream', 89, 'https://images.unsplash.com/photo-1608830597604-619220679440?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=700&q=80', 17, true),\n  ('Chocolate Bacon Brownie', 'Rich chocolate brownie with candied bacon bits and caramel sauce', 99, 'https://images.unsplash.com/photo-1624353365286-3f8d5abc3881?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=700&q=80', 17, true),\n  ('Banana Pudding', 'Classic Southern banana pudding with vanilla wafers and fresh bananas', 79, 'https://images.unsplash.com/photo-1576618148400-f54bed99fcfd?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=700&q=80', 17, true);\n\n-- Insert sample orders\nINSERT INTO orders (customer, items, subtotal, delivery_fee, total, status, payment_method, notes, created_at)\nVALUES\n  -- Day 1\n  (\n    '{\"firstName\":\"John\",\"lastName\":\"Smith\",\"email\":\"<EMAIL>\",\"phone\":\"55512345678\",\"address\":\"123 Main St\",\"postalCode\":\"0123\",\"city\":\"Oslo\"}',\n    '[{\"id\":1,\"name\":\"Smoked Beef Brisket\",\"price\":189,\"quantity\":1},{\"id\":11,\"name\":\"Mac & Cheese\",\"price\":89,\"quantity\":1},{\"id\":14,\"name\":\"Sweet Tea\",\"price\":49,\"quantity\":2}]',\n    376, 49, 425, 'completed', 'card', 'Please include extra napkins', NOW() - INTERVAL '25 days'\n  ),\n  (\n    '{\"firstName\":\"Emma\",\"lastName\":\"Johnson\",\"email\":\"<EMAIL>\",\"phone\":\"55587654321\",\"address\":\"456 Park Ave\",\"postalCode\":\"0124\",\"city\":\"Oslo\"}',\n    '[{\"id\":2,\"name\":\"Baby Back Ribs\",\"price\":179,\"quantity\":1},{\"id\":10,\"name\":\"Cornbread Skillet\",\"price\":79,\"quantity\":1},{\"id\":13,\"name\":\"House-Made Lemonade\",\"price\":59,\"quantity\":2}]',\n    376, 49, 425, 'completed', 'card', null, NOW() - INTERVAL '24 days'\n  ),\n  \n  -- Day 2\n  (\n    '{\"firstName\":\"Michael\",\"lastName\":\"Brown\",\"email\":\"<EMAIL>\",\"phone\":\"55523456789\",\"address\":\"789 Elm St\",\"postalCode\":\"0125\",\"city\":\"Oslo\"}',\n    '[{\"id\":7,\"name\":\"Smokehouse Burger\",\"price\":169,\"quantity\":2},{\"id\":11,\"name\":\"Mac & Cheese\",\"price\":89,\"quantity\":1},{\"id\":15,\"name\":\"Craft Beer Selection\",\"price\":89,\"quantity\":2}]',\n    605, 49, 654, 'completed', 'cash', 'Ring doorbell please', NOW() - INTERVAL '22 days'\n  ),\n  \n  -- Day 3\n  (\n    '{\"firstName\":\"Sophia\",\"lastName\":\"Davis\",\"email\":\"<EMAIL>\",\"phone\":\"55534567890\",\"address\":\"101 Pine Rd\",\"postalCode\":\"0126\",\"city\":\"Oslo\"}',\n    '[{\"id\":4,\"name\":\"BBQ Chicken Half\",\"price\":149,\"quantity\":1},{\"id\":9,\"name\":\"Smoked Wings\",\"price\":139,\"quantity\":1},{\"id\":19,\"name\":\"Bourbon Pecan Pie\",\"price\":89,\"quantity\":1}]',\n    377, 49, 426, 'completed', 'card', null, NOW() - INTERVAL '20 days'\n  ),\n  (\n    '{\"firstName\":\"David\",\"lastName\":\"Wilson\",\"email\":\"<EMAIL>\",\"phone\":\"55545678901\",\"address\":\"202 Oak St\",\"postalCode\":\"0127\",\"city\":\"Oslo\"}',\n    '[{\"id\":3,\"name\":\"Pulled Pork Platter\",\"price\":159,\"quantity\":1},{\"id\":8,\"name\":\"Loaded BBQ Fries\",\"price\":119,\"quantity\":1},{\"id\":14,\"name\":\"Sweet Tea\",\"price\":49,\"quantity\":2}]',\n    376, 49, 425, 'completed', 'card', 'Extra BBQ sauce please', NOW() - INTERVAL '20 days'\n  ),\n  \n  -- Day 4\n  (\n    '{\"firstName\":\"Olivia\",\"lastName\":\"Taylor\",\"email\":\"<EMAIL>\",\"phone\":\"55556789012\",\"address\":\"303 Maple Ave\",\"postalCode\":\"0128\",\"city\":\"Oslo\"}',\n    '[{\"id\":1,\"name\":\"Smoked Beef Brisket\",\"price\":189,\"quantity\":1},{\"id\":2,\"name\":\"Baby Back Ribs\",\"price\":179,\"quantity\":1},{\"id\":11,\"name\":\"Mac & Cheese\",\"price\":89,\"quantity\":1},{\"id\":13,\"name\":\"House-Made Lemonade\",\"price\":59,\"quantity\":2}]',\n    575, 49, 624, 'completed', 'card', null, NOW() - INTERVAL '18 days'\n  ),\n  \n  -- Day 5\n  (\n    '{\"firstName\":\"James\",\"lastName\":\"Anderson\",\"email\":\"<EMAIL>\",\"phone\":\"55567890123\",\"address\":\"404 Cedar Blvd\",\"postalCode\":\"0129\",\"city\":\"Oslo\"}',\n    '[{\"id\":5,\"name\":\"Smoked Sausage Links\",\"price\":139,\"quantity\":1},{\"id\":9,\"name\":\"Smoked Wings\",\"price\":139,\"quantity\":1},{\"id\":15,\"name\":\"Craft Beer Selection\",\"price\":89,\"quantity\":1}]',\n    367, 49, 416, 'completed', 'cash', null, NOW() - INTERVAL '16 days'\n  ),\n  \n  -- Day 6\n  (\n    '{\"firstName\":\"Emily\",\"lastName\":\"Thomas\",\"email\":\"<EMAIL>\",\"phone\":\"55578901234\",\"address\":\"505 Birch St\",\"postalCode\":\"0130\",\"city\":\"Oslo\"}',\n    '[{\"id\":7,\"name\":\"Smokehouse Burger\",\"price\":169,\"quantity\":2},{\"id\":8,\"name\":\"Loaded BBQ Fries\",\"price\":119,\"quantity\":1},{\"id\":16,\"name\":\"Bourbon Selection\",\"price\":129,\"quantity\":1}]',\n    586, 49, 635, 'completed', 'card', 'No onions on burgers please', NOW() - INTERVAL '14 days'\n  ),\n  \n  -- Day 7\n  (\n    '{\"firstName\":\"William\",\"lastName\":\"Jackson\",\"email\":\"<EMAIL>\",\"phone\":\"55589012345\",\"address\":\"606 Walnut Dr\",\"postalCode\":\"0131\",\"city\":\"Oslo\"}',\n    '[{\"id\":3,\"name\":\"Pulled Pork Platter\",\"price\":159,\"quantity\":1},{\"id\":20,\"name\":\"Chocolate Bacon Brownie\",\"price\":99,\"quantity\":1},{\"id\":13,\"name\":\"House-Made Lemonade\",\"price\":59,\"quantity\":1}]',\n    317, 49, 366, 'completed', 'card', null, NOW() - INTERVAL '12 days'\n  ),\n  \n  -- Day 8\n  (\n    '{\"firstName\":\"Ava\",\"lastName\":\"White\",\"email\":\"<EMAIL>\",\"phone\":\"55590123456\",\"address\":\"707 Spruce Ct\",\"postalCode\":\"0132\",\"city\":\"Oslo\"}',\n    '[{\"id\":2,\"name\":\"Baby Back Ribs\",\"price\":179,\"quantity\":1},{\"id\":10,\"name\":\"Cornbread Skillet\",\"price\":79,\"quantity\":1},{\"id\":21,\"name\":\"Banana Pudding\",\"price\":79,\"quantity\":1}]',\n    337, 49, 386, 'completed', 'card', null, NOW() - INTERVAL '10 days'\n  ),\n  (\n    '{\"firstName\":\"Alexander\",\"lastName\":\"Harris\",\"email\":\"<EMAIL>\",\"phone\":\"55501234567\",\"address\":\"808 Ash Ln\",\"postalCode\":\"0133\",\"city\":\"Oslo\"}',\n    '[{\"id\":6,\"name\":\"The Triple B\",\"price\":179,\"quantity\":2},{\"id\":11,\"name\":\"Mac & Cheese\",\"price\":89,\"quantity\":1},{\"id\":15,\"name\":\"Craft Beer Selection\",\"price\":89,\"quantity\":2}]',\n    625, 49, 674, 'completed', 'cash', 'Burgers medium rare please', NOW() - INTERVAL '10 days'\n  ),\n  \n  -- Day 9\n  (\n    '{\"firstName\":\"Charlotte\",\"lastName\":\"Clark\",\"email\":\"<EMAIL>\",\"phone\":\"55512345098\",\"address\":\"909 Fir Way\",\"postalCode\":\"0134\",\"city\":\"Oslo\"}',\n    '[{\"id\":4,\"name\":\"BBQ Chicken Half\",\"price\":149,\"quantity\":1},{\"id\":8,\"name\":\"Loaded BBQ Fries\",\"price\":119,\"quantity\":1},{\"id\":18,\"name\":\"Smoked Apple Cobbler\",\"price\":99,\"quantity\":1},{\"id\":14,\"name\":\"Sweet Tea\",\"price\":49,\"quantity\":1}]',\n    416, 49, 465, 'completed', 'card', null, NOW() - INTERVAL '8 days'\n  ),\n  \n  -- Day 10 (Today - 6 days)\n  (\n    '{\"firstName\":\"Daniel\",\"lastName\":\"Lewis\",\"email\":\"<EMAIL>\",\"phone\":\"55523456098\",\"address\":\"111 Holly Rd\",\"postalCode\":\"0135\",\"city\":\"Oslo\"}',\n    '[{\"id\":1,\"name\":\"Smoked Beef Brisket\",\"price\":189,\"quantity\":1},{\"id\":11,\"name\":\"Mac & Cheese\",\"price\":89,\"quantity\":1},{\"id\":13,\"name\":\"House-Made Lemonade\",\"price\":59,\"quantity\":1}]',\n    337, 49, 386, 'completed', 'card', null, NOW() - INTERVAL '6 days'\n  ),\n  (\n    '{\"firstName\":\"Amelia\",\"lastName\":\"Walker\",\"email\":\"<EMAIL>\",\"phone\":\"55534567098\",\"address\":\"222 Cherry St\",\"postalCode\":\"0136\",\"city\":\"Oslo\"}',\n    '[{\"id\":9,\"name\":\"Smoked Wings\",\"price\":139,\"quantity\":2},{\"id\":8,\"name\":\"Loaded BBQ Fries\",\"price\":119,\"quantity\":1},{\"id\":15,\"name\":\"Craft Beer Selection\",\"price\":89,\"quantity\":2}]',\n    575, 49, 624, 'completed', 'card', 'Extra hot sauce on wings', NOW() - INTERVAL '6 days'\n  ),\n  \n  -- Day 11 (Today - 4 days)\n  (\n    '{\"firstName\":\"Henry\",\"lastName\":\"Hall\",\"email\":\"<EMAIL>\",\"phone\":\"55545678098\",\"address\":\"333 Juniper Ave\",\"postalCode\":\"0137\",\"city\":\"Oslo\"}',\n    '[{\"id\":5,\"name\":\"Smoked Sausage Links\",\"price\":139,\"quantity\":1},{\"id\":2,\"name\":\"Baby Back Ribs\",\"price\":179,\"quantity\":1},{\"id\":10,\"name\":\"Cornbread Skillet\",\"price\":79,\"quantity\":1},{\"id\":14,\"name\":\"Sweet Tea\",\"price\":49,\"quantity\":2}]',\n    495, 49, 544, 'completed', 'cash', null, NOW() - INTERVAL '4 days'\n  ),\n  \n  -- Day 12 (Today - 3 days)\n  (\n    '{\"firstName\":\"Victoria\",\"lastName\":\"Young\",\"email\":\"<EMAIL>\",\"phone\":\"55556789098\",\"address\":\"444 Acorn Blvd\",\"postalCode\":\"0138\",\"city\":\"Oslo\"}',\n    '[{\"id\":7,\"name\":\"Smokehouse Burger\",\"price\":169,\"quantity\":1},{\"id\":8,\"name\":\"Loaded BBQ Fries\",\"price\":119,\"quantity\":1},{\"id\":20,\"name\":\"Chocolate Bacon Brownie\",\"price\":99,\"quantity\":1},{\"id\":13,\"name\":\"House-Made Lemonade\",\"price\":59,\"quantity\":1}]',\n    446, 49, 495, 'completed', 'card', null, NOW() - INTERVAL '3 days'\n  ),\n  \n  -- Day 13 (Today - 2 days)\n  (\n    '{\"firstName\":\"Joseph\",\"lastName\":\"King\",\"email\":\"<EMAIL>\",\"phone\":\"55567890098\",\"address\":\"555 Redwood Dr\",\"postalCode\":\"0139\",\"city\":\"Oslo\"}',\n    '[{\"id\":3,\"name\":\"Pulled Pork Platter\",\"price\":159,\"quantity\":1},{\"id\":11,\"name\":\"Mac & Cheese\",\"price\":89,\"quantity\":1},{\"id\":18,\"name\":\"Smoked Apple Cobbler\",\"price\":99,\"quantity\":1},{\"id\":14,\"name\":\"Sweet Tea\",\"price\":49,\"quantity\":1}]',\n    396, 49, 445, 'completed', 'card', null, NOW() - INTERVAL '2 days'\n  ),\n  (\n    '{\"firstName\":\"Eleanor\",\"lastName\":\"Scott\",\"email\":\"<EMAIL>\",\"phone\":\"55578901098\",\"address\":\"666 Willow Way\",\"postalCode\":\"0140\",\"city\":\"Oslo\"}',\n    '[{\"id\":1,\"name\":\"Smoked Beef Brisket\",\"price\":189,\"quantity\":2},{\"id\":9,\"name\":\"Smoked Wings\",\"price\":139,\"quantity\":1},{\"id\":10,\"name\":\"Cornbread Skillet\",\"price\":79,\"quantity\":1},{\"id\":15,\"name\":\"Craft Beer Selection\",\"price\":89,\"quantity\":2}]',\n    774, 49, 823, 'completed', 'card', 'One brisket extra well done', NOW() - INTERVAL '2 days'\n  ),\n  \n  -- Day 14 (Today - 1 day)\n  (\n    '{\"firstName\":\"Andrew\",\"lastName\":\"Adams\",\"email\":\"<EMAIL>\",\"phone\":\"55589012098\",\"address\":\"777 Sequoia Ct\",\"postalCode\":\"0141\",\"city\":\"Oslo\"}',\n    '[{\"id\":6,\"name\":\"The Triple B\",\"price\":179,\"quantity\":2},{\"id\":8,\"name\":\"Loaded BBQ Fries\",\"price\":119,\"quantity\":2},{\"id\":21,\"name\":\"Banana Pudding\",\"price\":79,\"quantity\":2},{\"id\":13,\"name\":\"House-Made Lemonade\",\"price\":59,\"quantity\":4}]',\n    910, 49, 959, 'completed', 'card', 'Birthday celebration', NOW() - INTERVAL '1 day'\n  ),\n  \n  -- Day 15 (Today)\n  (\n    '{\"firstName\":\"Mia\",\"lastName\":\"Nelson\",\"email\":\"<EMAIL>\",\"phone\":\"55590123098\",\"address\":\"888 Cypress Ln\",\"postalCode\":\"0142\",\"city\":\"Oslo\"}',\n    '[{\"id\":5,\"name\":\"Smoked Sausage Links\",\"price\":139,\"quantity\":1},{\"id\":9,\"name\":\"Smoked Wings\",\"price\":139,\"quantity\":1},{\"id\":11,\"name\":\"Mac & Cheese\",\"price\":89,\"quantity\":1},{\"id\":16,\"name\":\"Bourbon Selection\",\"price\":129,\"quantity\":1}]',\n    496, 49, 545, 'in_progress', 'card', null, NOW()\n  ),\n  (\n    '{\"firstName\":\"Benjamin\",\"lastName\":\"Baker\",\"email\":\"<EMAIL>\",\"phone\":\"55501234098\",\"address\":\"999 Poplar Ave\",\"postalCode\":\"0143\",\"city\":\"Oslo\"}',\n    '[{\"id\":2,\"name\":\"Baby Back Ribs\",\"price\":179,\"quantity\":1},{\"id\":3,\"name\":\"Pulled Pork Platter\",\"price\":159,\"quantity\":1},{\"id\":8,\"name\":\"Loaded BBQ Fries\",\"price\":119,\"quantity\":1},{\"id\":18,\"name\":\"Smoked Apple Cobbler\",\"price\":99,\"quantity\":1},{\"id\":14,\"name\":\"Sweet Tea\",\"price\":49,\"quantity\":2}]',\n    654, 49, 703, 'processing', 'card', 'Please deliver to back door', NOW()\n  );\n\n-- Create summary view for analytics\nCREATE OR REPLACE VIEW revenue_summary AS\nSELECT\n  DATE_TRUNC('day', created_at) AS date,\n  COUNT(*) AS order_count,\n  SUM(subtotal) AS subtotal_revenue,\n  SUM(delivery_fee) AS delivery_revenue,\n  SUM(total) AS total_revenue\nFROM orders\nGROUP BY DATE_TRUNC('day', created_at)\nORDER BY date DESC;\n\n-- Create category revenue view\nCREATE OR REPLACE VIEW category_revenue AS\nWITH order_items AS (\n  SELECT\n    o.id as order_id,\n    o.created_at,\n    i.id as item_id,\n    i.price,\n    i.quantity,\n    (i.price * i.quantity) as item_total,\n    m.category_id\n  FROM\n    orders o,\n    jsonb_to_recordset(o.items) AS i(id int, price int, quantity int)\n  JOIN\n    menu_items m ON i.id = m.id\n)\nSELECT\n  c.name as category,\n  SUM(oi.item_total) as total\nFROM\n  order_items oi\nJOIN\n  categories c ON oi.category_id = c.id\nGROUP BY\n  c.name\nORDER BY\n  total DESC;\n\nSELECT 'Database successfully seeded with sample data' AS result;", "modifiedCode": "-- Barbecuez Restaurant Database Seeder Script\n-- This script populates the database with sample data for development/testing\n\n-- Clear existing data (if any)\nTRUNCATE TABLE categories CASCADE;\nTRUNCATE TABLE menu_items CASCADE;\nTRUNCATE TABLE orders CASCADE;\n\n-- Create restaurant_settings table if it doesn't exist\nCREATE TABLE IF NOT EXISTS restaurant_settings (\n  id SERIAL PRIMARY KEY,\n  restaurant_open BOOLEAN NOT NULL DEFAULT true,\n  business_hours JSONB NOT NULL,\n  delivery_fee INTEGER NOT NULL,\n  estimated_time TEXT NOT NULL\n);\n\n-- Update restaurant settings (using upsert to avoid duplicate key errors)\nINSERT INTO restaurant_settings (id, restaurant_open, business_hours, delivery_fee, estimated_time)\nVALUES (\n  1, \n  true, \n  '{\n    \"monday\": { \"open\": \"10:00\", \"close\": \"22:00\", \"delivery\": true },\n    \"tuesday\": { \"open\": \"10:00\", \"close\": \"22:00\", \"delivery\": true },\n    \"wednesday\": { \"open\": \"10:00\", \"close\": \"22:00\", \"delivery\": true },\n    \"thursday\": { \"open\": \"10:00\", \"close\": \"22:00\", \"delivery\": true },\n    \"friday\": { \"open\": \"10:00\", \"close\": \"23:00\", \"delivery\": true },\n    \"saturday\": { \"open\": \"10:00\", \"close\": \"23:00\", \"delivery\": true },\n    \"sunday\": { \"open\": \"12:00\", \"close\": \"21:00\", \"delivery\": true }\n  }',\n  49,\n  '25-35 min'\n) \nON CONFLICT (id) DO UPDATE SET \n  restaurant_open = EXCLUDED.restaurant_open,\n  business_hours = EXCLUDED.business_hours,\n  delivery_fee = EXCLUDED.delivery_fee,\n  estimated_time = EXCLUDED.estimated_time;\n\n-- Categories are already inserted, skip this part\n-- INSERT INTO categories (name, image_url)\n-- VALUES \n--   ('Signature BBQ', 'https://images.unsplash.com/photo-1529193591184-b1d58069ecdd?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=700&q=80'),\n--   ('BBQ Burgers', 'https://images.unsplash.com/photo-1568901346375-23c9450c58cd?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=700&q=80'),\n--   ('Sides & Shareables', 'https://images.unsplash.com/photo-1576020422280-3c10743b934b?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=700&q=80'),\n--   ('Beverages', 'https://images.unsplash.com/photo-1581345331973-9e6c5c0ec1b9?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=700&q=80'),\n--   ('Desserts', 'https://images.unsplash.com/photo-1488900128323-21503983a07e?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=700&q=80');\n\n-- Insert menu items\nINSERT INTO menu_items (name, description, price, image_url, category_id, available)\nVALUES\n  -- Signature BBQ (ID: 13)\n  ('Smoked Beef Brisket', '24-hour slow-smoked USDA prime beef brisket with our signature rub', 189, 'https://images.unsplash.com/photo-1529193591184-b1d58069ecdd?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=700&q=80', 13, true),\n  ('Baby Back Ribs', 'Tender pork ribs smoked for 6 hours and glazed with our bourbon BBQ sauce', 179, 'https://images.unsplash.com/photo-1544025162-d76694265947?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=700&q=80', 13, true),\n  ('Pulled Pork Platter', 'Hickory-smoked pulled pork served with texas toast and two sides', 159, 'https://images.unsplash.com/photo-1623653538388-3082fd168cd7?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=700&q=80', 13, true),\n  ('BBQ Chicken Half', 'Juicy half chicken smoked and finished on the grill with our signature sauce', 149, 'https://images.unsplash.com/photo-1635321593217-40050ad13c74?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=700&q=80', 13, true),\n  ('Smoked Sausage Links', 'House-made smoked sausages with jalapeño and cheddar', 139, 'https://images.unsplash.com/photo-1597712700478-7caa2afb6d5b?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=700&q=80', 13, true),\n  \n  -- BBQ Burgers (ID: 14)\n  ('Smokehouse Burger', 'Angus beef patty topped with smoked brisket, cheddar, and bourbon BBQ sauce', 169, 'https://images.unsplash.com/photo-1568901346375-23c9450c58cd?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=700&q=80', 14, true),\n  ('The Triple B', 'Beef, bacon, and brisket burger with smoked gouda and caramelized onions', 179, 'https://images.unsplash.com/photo-1553979459-d2229ba7433b?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=700&q=80', 14, true),\n  ('Pulled Pork Burger', 'Beef patty topped with pulled pork, coleslaw, and Memphis BBQ sauce', 159, 'https://images.unsplash.com/photo-1572802419224-296b0aeee0d9?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=700&q=80', 14, true),\n  ('Smoked Portobello Burger', 'Smoked portobello mushroom with Swiss cheese and truffle aioli', 149, 'https://images.unsplash.com/photo-1520072959219-c595dc870360?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=700&q=80', 14, true),\n\n  -- Sides & Shareables (ID: 15)\n  ('Burnt Ends', 'Crispy, caramelized brisket ends tossed in BBQ sauce', 129, 'https://images.unsplash.com/photo-1576020422280-3c10743b934b?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=700&q=80', 15, true),\n  ('Loaded BBQ Fries', 'Hand-cut fries topped with pulled pork, cheese sauce, and jalapeños', 119, 'https://images.unsplash.com/photo-1585109649408-c35fe02e1335?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=700&q=80', 15, true),\n  ('Smoked Wings', 'Hickory-smoked wings with your choice of sauce: Classic BBQ, Buffalo, or Honey Sriracha', 139, 'https://images.unsplash.com/photo-1567620832903-9fc6debc209f?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=700&q=80', 15, true),\n  ('Mac & Cheese', 'Creamy five-cheese mac topped with crispy breadcrumbs', 89, 'https://images.unsplash.com/photo-1543352634-a1c51d9f1fa7?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=700&q=80', 15, true),\n  ('Cornbread Skillet', 'Sweet jalapeño cornbread baked in a cast-iron skillet with honey butter', 79, 'https://images.unsplash.com/photo-1612203985729-70726954388c?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=700&q=80', 15, true),\n  \n  -- Beverages (ID: 16)\n  ('Craft Beer Selection', 'Rotating selection of local craft beers', 89, 'https://images.unsplash.com/photo-1581345331973-9e6c5c0ec1b9?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=700&q=80', 16, true),\n  ('House-Made Lemonade', 'Fresh-squeezed lemonade with hint of smoked rosemary', 59, 'https://images.unsplash.com/photo-1523677011781-c91d1bbe2f9e?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=700&q=80', 16, true),\n  ('Sweet Tea', 'Southern-style sweet tea infused with mint', 49, 'https://images.unsplash.com/photo-1543007168-9844a6f9a822?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=700&q=80', 16, true),\n  ('Bourbon Selection', 'Selection of premium bourbons, neat or on the rocks', 129, 'https://images.unsplash.com/photo-1527281400683-1aae777175f8?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=700&q=80', 16, true),\n  \n  -- Desserts (ID: 17)\n  ('Smoked Apple Cobbler', 'Smoked apples with cinnamon streusel topping and vanilla ice cream', 99, 'https://images.unsplash.com/photo-1488900128323-21503983a07e?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=700&q=80', 17, true),\n  ('Bourbon Pecan Pie', 'Homemade pecan pie with a bourbon twist, served with whipped cream', 89, 'https://images.unsplash.com/photo-1608830597604-619220679440?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=700&q=80', 17, true),\n  ('Chocolate Bacon Brownie', 'Rich chocolate brownie with candied bacon bits and caramel sauce', 99, 'https://images.unsplash.com/photo-1624353365286-3f8d5abc3881?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=700&q=80', 17, true),\n  ('Banana Pudding', 'Classic Southern banana pudding with vanilla wafers and fresh bananas', 79, 'https://images.unsplash.com/photo-1576618148400-f54bed99fcfd?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=700&q=80', 17, true);\n\n-- Insert sample orders\nINSERT INTO orders (customer, items, subtotal, delivery_fee, total, status, payment_method, notes, created_at)\nVALUES\n  -- Day 1\n  (\n    '{\"firstName\":\"John\",\"lastName\":\"Smith\",\"email\":\"<EMAIL>\",\"phone\":\"55512345678\",\"address\":\"123 Main St\",\"postalCode\":\"0123\",\"city\":\"Oslo\"}',\n    '[{\"id\":1,\"name\":\"Smoked Beef Brisket\",\"price\":189,\"quantity\":1},{\"id\":11,\"name\":\"Mac & Cheese\",\"price\":89,\"quantity\":1},{\"id\":14,\"name\":\"Sweet Tea\",\"price\":49,\"quantity\":2}]',\n    376, 49, 425, 'completed', 'card', 'Please include extra napkins', NOW() - INTERVAL '25 days'\n  ),\n  (\n    '{\"firstName\":\"Emma\",\"lastName\":\"Johnson\",\"email\":\"<EMAIL>\",\"phone\":\"55587654321\",\"address\":\"456 Park Ave\",\"postalCode\":\"0124\",\"city\":\"Oslo\"}',\n    '[{\"id\":2,\"name\":\"Baby Back Ribs\",\"price\":179,\"quantity\":1},{\"id\":10,\"name\":\"Cornbread Skillet\",\"price\":79,\"quantity\":1},{\"id\":13,\"name\":\"House-Made Lemonade\",\"price\":59,\"quantity\":2}]',\n    376, 49, 425, 'completed', 'card', null, NOW() - INTERVAL '24 days'\n  ),\n  \n  -- Day 2\n  (\n    '{\"firstName\":\"Michael\",\"lastName\":\"Brown\",\"email\":\"<EMAIL>\",\"phone\":\"55523456789\",\"address\":\"789 Elm St\",\"postalCode\":\"0125\",\"city\":\"Oslo\"}',\n    '[{\"id\":7,\"name\":\"Smokehouse Burger\",\"price\":169,\"quantity\":2},{\"id\":11,\"name\":\"Mac & Cheese\",\"price\":89,\"quantity\":1},{\"id\":15,\"name\":\"Craft Beer Selection\",\"price\":89,\"quantity\":2}]',\n    605, 49, 654, 'completed', 'cash', 'Ring doorbell please', NOW() - INTERVAL '22 days'\n  ),\n  \n  -- Day 3\n  (\n    '{\"firstName\":\"Sophia\",\"lastName\":\"Davis\",\"email\":\"<EMAIL>\",\"phone\":\"55534567890\",\"address\":\"101 Pine Rd\",\"postalCode\":\"0126\",\"city\":\"Oslo\"}',\n    '[{\"id\":4,\"name\":\"BBQ Chicken Half\",\"price\":149,\"quantity\":1},{\"id\":9,\"name\":\"Smoked Wings\",\"price\":139,\"quantity\":1},{\"id\":19,\"name\":\"Bourbon Pecan Pie\",\"price\":89,\"quantity\":1}]',\n    377, 49, 426, 'completed', 'card', null, NOW() - INTERVAL '20 days'\n  ),\n  (\n    '{\"firstName\":\"David\",\"lastName\":\"Wilson\",\"email\":\"<EMAIL>\",\"phone\":\"55545678901\",\"address\":\"202 Oak St\",\"postalCode\":\"0127\",\"city\":\"Oslo\"}',\n    '[{\"id\":3,\"name\":\"Pulled Pork Platter\",\"price\":159,\"quantity\":1},{\"id\":8,\"name\":\"Loaded BBQ Fries\",\"price\":119,\"quantity\":1},{\"id\":14,\"name\":\"Sweet Tea\",\"price\":49,\"quantity\":2}]',\n    376, 49, 425, 'completed', 'card', 'Extra BBQ sauce please', NOW() - INTERVAL '20 days'\n  ),\n  \n  -- Day 4\n  (\n    '{\"firstName\":\"Olivia\",\"lastName\":\"Taylor\",\"email\":\"<EMAIL>\",\"phone\":\"55556789012\",\"address\":\"303 Maple Ave\",\"postalCode\":\"0128\",\"city\":\"Oslo\"}',\n    '[{\"id\":1,\"name\":\"Smoked Beef Brisket\",\"price\":189,\"quantity\":1},{\"id\":2,\"name\":\"Baby Back Ribs\",\"price\":179,\"quantity\":1},{\"id\":11,\"name\":\"Mac & Cheese\",\"price\":89,\"quantity\":1},{\"id\":13,\"name\":\"House-Made Lemonade\",\"price\":59,\"quantity\":2}]',\n    575, 49, 624, 'completed', 'card', null, NOW() - INTERVAL '18 days'\n  ),\n  \n  -- Day 5\n  (\n    '{\"firstName\":\"James\",\"lastName\":\"Anderson\",\"email\":\"<EMAIL>\",\"phone\":\"55567890123\",\"address\":\"404 Cedar Blvd\",\"postalCode\":\"0129\",\"city\":\"Oslo\"}',\n    '[{\"id\":5,\"name\":\"Smoked Sausage Links\",\"price\":139,\"quantity\":1},{\"id\":9,\"name\":\"Smoked Wings\",\"price\":139,\"quantity\":1},{\"id\":15,\"name\":\"Craft Beer Selection\",\"price\":89,\"quantity\":1}]',\n    367, 49, 416, 'completed', 'cash', null, NOW() - INTERVAL '16 days'\n  ),\n  \n  -- Day 6\n  (\n    '{\"firstName\":\"Emily\",\"lastName\":\"Thomas\",\"email\":\"<EMAIL>\",\"phone\":\"55578901234\",\"address\":\"505 Birch St\",\"postalCode\":\"0130\",\"city\":\"Oslo\"}',\n    '[{\"id\":7,\"name\":\"Smokehouse Burger\",\"price\":169,\"quantity\":2},{\"id\":8,\"name\":\"Loaded BBQ Fries\",\"price\":119,\"quantity\":1},{\"id\":16,\"name\":\"Bourbon Selection\",\"price\":129,\"quantity\":1}]',\n    586, 49, 635, 'completed', 'card', 'No onions on burgers please', NOW() - INTERVAL '14 days'\n  ),\n  \n  -- Day 7\n  (\n    '{\"firstName\":\"William\",\"lastName\":\"Jackson\",\"email\":\"<EMAIL>\",\"phone\":\"55589012345\",\"address\":\"606 Walnut Dr\",\"postalCode\":\"0131\",\"city\":\"Oslo\"}',\n    '[{\"id\":3,\"name\":\"Pulled Pork Platter\",\"price\":159,\"quantity\":1},{\"id\":20,\"name\":\"Chocolate Bacon Brownie\",\"price\":99,\"quantity\":1},{\"id\":13,\"name\":\"House-Made Lemonade\",\"price\":59,\"quantity\":1}]',\n    317, 49, 366, 'completed', 'card', null, NOW() - INTERVAL '12 days'\n  ),\n  \n  -- Day 8\n  (\n    '{\"firstName\":\"Ava\",\"lastName\":\"White\",\"email\":\"<EMAIL>\",\"phone\":\"55590123456\",\"address\":\"707 Spruce Ct\",\"postalCode\":\"0132\",\"city\":\"Oslo\"}',\n    '[{\"id\":2,\"name\":\"Baby Back Ribs\",\"price\":179,\"quantity\":1},{\"id\":10,\"name\":\"Cornbread Skillet\",\"price\":79,\"quantity\":1},{\"id\":21,\"name\":\"Banana Pudding\",\"price\":79,\"quantity\":1}]',\n    337, 49, 386, 'completed', 'card', null, NOW() - INTERVAL '10 days'\n  ),\n  (\n    '{\"firstName\":\"Alexander\",\"lastName\":\"Harris\",\"email\":\"<EMAIL>\",\"phone\":\"55501234567\",\"address\":\"808 Ash Ln\",\"postalCode\":\"0133\",\"city\":\"Oslo\"}',\n    '[{\"id\":6,\"name\":\"The Triple B\",\"price\":179,\"quantity\":2},{\"id\":11,\"name\":\"Mac & Cheese\",\"price\":89,\"quantity\":1},{\"id\":15,\"name\":\"Craft Beer Selection\",\"price\":89,\"quantity\":2}]',\n    625, 49, 674, 'completed', 'cash', 'Burgers medium rare please', NOW() - INTERVAL '10 days'\n  ),\n  \n  -- Day 9\n  (\n    '{\"firstName\":\"Charlotte\",\"lastName\":\"Clark\",\"email\":\"<EMAIL>\",\"phone\":\"55512345098\",\"address\":\"909 Fir Way\",\"postalCode\":\"0134\",\"city\":\"Oslo\"}',\n    '[{\"id\":4,\"name\":\"BBQ Chicken Half\",\"price\":149,\"quantity\":1},{\"id\":8,\"name\":\"Loaded BBQ Fries\",\"price\":119,\"quantity\":1},{\"id\":18,\"name\":\"Smoked Apple Cobbler\",\"price\":99,\"quantity\":1},{\"id\":14,\"name\":\"Sweet Tea\",\"price\":49,\"quantity\":1}]',\n    416, 49, 465, 'completed', 'card', null, NOW() - INTERVAL '8 days'\n  ),\n  \n  -- Day 10 (Today - 6 days)\n  (\n    '{\"firstName\":\"Daniel\",\"lastName\":\"Lewis\",\"email\":\"<EMAIL>\",\"phone\":\"55523456098\",\"address\":\"111 Holly Rd\",\"postalCode\":\"0135\",\"city\":\"Oslo\"}',\n    '[{\"id\":1,\"name\":\"Smoked Beef Brisket\",\"price\":189,\"quantity\":1},{\"id\":11,\"name\":\"Mac & Cheese\",\"price\":89,\"quantity\":1},{\"id\":13,\"name\":\"House-Made Lemonade\",\"price\":59,\"quantity\":1}]',\n    337, 49, 386, 'completed', 'card', null, NOW() - INTERVAL '6 days'\n  ),\n  (\n    '{\"firstName\":\"Amelia\",\"lastName\":\"Walker\",\"email\":\"<EMAIL>\",\"phone\":\"55534567098\",\"address\":\"222 Cherry St\",\"postalCode\":\"0136\",\"city\":\"Oslo\"}',\n    '[{\"id\":9,\"name\":\"Smoked Wings\",\"price\":139,\"quantity\":2},{\"id\":8,\"name\":\"Loaded BBQ Fries\",\"price\":119,\"quantity\":1},{\"id\":15,\"name\":\"Craft Beer Selection\",\"price\":89,\"quantity\":2}]',\n    575, 49, 624, 'completed', 'card', 'Extra hot sauce on wings', NOW() - INTERVAL '6 days'\n  ),\n  \n  -- Day 11 (Today - 4 days)\n  (\n    '{\"firstName\":\"Henry\",\"lastName\":\"Hall\",\"email\":\"<EMAIL>\",\"phone\":\"55545678098\",\"address\":\"333 Juniper Ave\",\"postalCode\":\"0137\",\"city\":\"Oslo\"}',\n    '[{\"id\":5,\"name\":\"Smoked Sausage Links\",\"price\":139,\"quantity\":1},{\"id\":2,\"name\":\"Baby Back Ribs\",\"price\":179,\"quantity\":1},{\"id\":10,\"name\":\"Cornbread Skillet\",\"price\":79,\"quantity\":1},{\"id\":14,\"name\":\"Sweet Tea\",\"price\":49,\"quantity\":2}]',\n    495, 49, 544, 'completed', 'cash', null, NOW() - INTERVAL '4 days'\n  ),\n  \n  -- Day 12 (Today - 3 days)\n  (\n    '{\"firstName\":\"Victoria\",\"lastName\":\"Young\",\"email\":\"<EMAIL>\",\"phone\":\"55556789098\",\"address\":\"444 Acorn Blvd\",\"postalCode\":\"0138\",\"city\":\"Oslo\"}',\n    '[{\"id\":7,\"name\":\"Smokehouse Burger\",\"price\":169,\"quantity\":1},{\"id\":8,\"name\":\"Loaded BBQ Fries\",\"price\":119,\"quantity\":1},{\"id\":20,\"name\":\"Chocolate Bacon Brownie\",\"price\":99,\"quantity\":1},{\"id\":13,\"name\":\"House-Made Lemonade\",\"price\":59,\"quantity\":1}]',\n    446, 49, 495, 'completed', 'card', null, NOW() - INTERVAL '3 days'\n  ),\n  \n  -- Day 13 (Today - 2 days)\n  (\n    '{\"firstName\":\"Joseph\",\"lastName\":\"King\",\"email\":\"<EMAIL>\",\"phone\":\"55567890098\",\"address\":\"555 Redwood Dr\",\"postalCode\":\"0139\",\"city\":\"Oslo\"}',\n    '[{\"id\":3,\"name\":\"Pulled Pork Platter\",\"price\":159,\"quantity\":1},{\"id\":11,\"name\":\"Mac & Cheese\",\"price\":89,\"quantity\":1},{\"id\":18,\"name\":\"Smoked Apple Cobbler\",\"price\":99,\"quantity\":1},{\"id\":14,\"name\":\"Sweet Tea\",\"price\":49,\"quantity\":1}]',\n    396, 49, 445, 'completed', 'card', null, NOW() - INTERVAL '2 days'\n  ),\n  (\n    '{\"firstName\":\"Eleanor\",\"lastName\":\"Scott\",\"email\":\"<EMAIL>\",\"phone\":\"55578901098\",\"address\":\"666 Willow Way\",\"postalCode\":\"0140\",\"city\":\"Oslo\"}',\n    '[{\"id\":1,\"name\":\"Smoked Beef Brisket\",\"price\":189,\"quantity\":2},{\"id\":9,\"name\":\"Smoked Wings\",\"price\":139,\"quantity\":1},{\"id\":10,\"name\":\"Cornbread Skillet\",\"price\":79,\"quantity\":1},{\"id\":15,\"name\":\"Craft Beer Selection\",\"price\":89,\"quantity\":2}]',\n    774, 49, 823, 'completed', 'card', 'One brisket extra well done', NOW() - INTERVAL '2 days'\n  ),\n  \n  -- Day 14 (Today - 1 day)\n  (\n    '{\"firstName\":\"Andrew\",\"lastName\":\"Adams\",\"email\":\"<EMAIL>\",\"phone\":\"55589012098\",\"address\":\"777 Sequoia Ct\",\"postalCode\":\"0141\",\"city\":\"Oslo\"}',\n    '[{\"id\":6,\"name\":\"The Triple B\",\"price\":179,\"quantity\":2},{\"id\":8,\"name\":\"Loaded BBQ Fries\",\"price\":119,\"quantity\":2},{\"id\":21,\"name\":\"Banana Pudding\",\"price\":79,\"quantity\":2},{\"id\":13,\"name\":\"House-Made Lemonade\",\"price\":59,\"quantity\":4}]',\n    910, 49, 959, 'completed', 'card', 'Birthday celebration', NOW() - INTERVAL '1 day'\n  ),\n  \n  -- Day 15 (Today)\n  (\n    '{\"firstName\":\"Mia\",\"lastName\":\"Nelson\",\"email\":\"<EMAIL>\",\"phone\":\"55590123098\",\"address\":\"888 Cypress Ln\",\"postalCode\":\"0142\",\"city\":\"Oslo\"}',\n    '[{\"id\":5,\"name\":\"Smoked Sausage Links\",\"price\":139,\"quantity\":1},{\"id\":9,\"name\":\"Smoked Wings\",\"price\":139,\"quantity\":1},{\"id\":11,\"name\":\"Mac & Cheese\",\"price\":89,\"quantity\":1},{\"id\":16,\"name\":\"Bourbon Selection\",\"price\":129,\"quantity\":1}]',\n    496, 49, 545, 'in_progress', 'card', null, NOW()\n  ),\n  (\n    '{\"firstName\":\"Benjamin\",\"lastName\":\"Baker\",\"email\":\"<EMAIL>\",\"phone\":\"55501234098\",\"address\":\"999 Poplar Ave\",\"postalCode\":\"0143\",\"city\":\"Oslo\"}',\n    '[{\"id\":2,\"name\":\"Baby Back Ribs\",\"price\":179,\"quantity\":1},{\"id\":3,\"name\":\"Pulled Pork Platter\",\"price\":159,\"quantity\":1},{\"id\":8,\"name\":\"Loaded BBQ Fries\",\"price\":119,\"quantity\":1},{\"id\":18,\"name\":\"Smoked Apple Cobbler\",\"price\":99,\"quantity\":1},{\"id\":14,\"name\":\"Sweet Tea\",\"price\":49,\"quantity\":2}]',\n    654, 49, 703, 'processing', 'card', 'Please deliver to back door', NOW()\n  );\n\n-- Create summary view for analytics\nCREATE OR REPLACE VIEW revenue_summary AS\nSELECT\n  DATE_TRUNC('day', created_at) AS date,\n  COUNT(*) AS order_count,\n  SUM(subtotal) AS subtotal_revenue,\n  SUM(delivery_fee) AS delivery_revenue,\n  SUM(total) AS total_revenue\nFROM orders\nGROUP BY DATE_TRUNC('day', created_at)\nORDER BY date DESC;\n\n-- Create category revenue view\nCREATE OR REPLACE VIEW category_revenue AS\nWITH order_items AS (\n  SELECT\n    o.id as order_id,\n    o.created_at,\n    i.id as item_id,\n    i.price,\n    i.quantity,\n    (i.price * i.quantity) as item_total,\n    m.category_id\n  FROM\n    orders o,\n    jsonb_to_recordset(o.items) AS i(id int, price int, quantity int)\n  JOIN\n    menu_items m ON i.id = m.id\n)\nSELECT\n  c.name as category,\n  SUM(oi.item_total) as total\nFROM\n  order_items oi\nJOIN\n  categories c ON oi.category_id = c.id\nGROUP BY\n  c.name\nORDER BY\n  total DESC;\n\nSELECT 'Database successfully seeded with sample data' AS result;"}