{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "attached_assets/Pasted-Redesign-the-Menu-Page-to-provide-a-luxurious-animated-interactive-food-ordering-experience-that-a-1747869938419.txt"}, "originalCode": "Redesign the Menu Page to provide a luxurious, animated, interactive food ordering experience that aligns with the visual identity of Barbecuez Restaurant. It should feel premium, cinematic, and deeply engaging.\n\n✨ Visual Design Goals:\nBuild a dark, elegant layout using:\n\nPrimary background: #000000\n\nText + accent neon glows:\n\nElectric Blue #00FFFF\n\nNeon Pink #FF00FF\n\nLime Green #39FF14\n\nInclude subtle glassmorphism or frosted overlays for food cards and modals.\n\nUse neon outlines, animated shadows, and soft 3D lift effects on hover.\n\nIntegrate motion transitions using Framer Motion (fade-in, slide-in, scale-in).\n\nResponsive design with full mobile optimization.\n\n🧩 Main Sections & Features\n1. Category Grid (Top Section)\nDisplay all menu categories in a neon-tinted grid (3-column desktop, 1-column mobile).\n\nEach tile includes:\n\nBackground image with dark overlay\n\nCategory title in bold neon text (SaladZ, BurgerZ, DrinkZ, etc.)\n\nHover effect: category image zoom-in + glowing border\n\nOn click: smooth scroll or transition to that category’s item list\n\n2. Dynamic Item Listing (Per Category)\nDisplay all items from the selected category in glowing, modern cards.\n\nEach item card should include:\n\nDish image (with soft shadow)\n\nName, short description\n\nPrice in NOK\n\n\"Customize\" button with neon border and hover glow\n\nAnimate cards on scroll with Framer Motion (staggered appearance)\n\n3. Customization Modal / Slide-In Panel\nWhen user clicks \"Customize\", open a side modal or centered glassmorphism-style dialog\n\nInclude:\n\nHero image of the dish with floating ingredient icons\n\nCustomization groups like:\n\n🥫 Saus & Topping\n\n🧀 Ost\n\n🧅 Grønnsaker\n\n🍔 Ekstra Produkter\n\nEach option has:\n\nIcon/image\n\nName + Extra Price\n\nToggle with animated check effect\n\nHighlight selected options with orange neon glow\n\nReal-time price recalculation with animation\n\nInclude CTA button: “Add to Cart” with scaling glow animation\n\n🎠 Optional: Featured Menu Slider\nAdd a featured section between categories showing trending dishes in:\n\nA carousel (e.g., Swiper.js or React Slick)\n\nEach slide has a glowing neon frame and hover effects\n\nInclude dish image, title, and CTA\n\n🛠️ Technical Instructions\nUse React functional components with clean, modular structure:\n\nCategoryGrid.jsx\n\nMenuItemCard.jsx\n\nCustomizeModal.jsx\n\nOptionToggle.jsx\n\nFeaturedSlider.jsx\n\nUse Framer Motion for:\n\nEntrance animations\n\nButton hovers\n\nModal transitions\n\nScroll effects\n\nTailwind Config:\n\nExtend theme to support custom neon shadows (box-shadow, ring, text-shadow)\n\nSetup dark mode by default\n\nUse @apply for neon utility classes (e.g., .neon-button, .neon-border)\n\nFetch dynamic data using Axios from endpoints:\n\n/api/categories\n\n/api/items?categoryId=X\n\n/api/items/:id/customization\n\n✅ Output\nA high-end, immersive Menu page that:\n\nFeels luxurious and modern\n\nAnimates smoothly on all devices\n\nHandles category filtering and customization dynamically\n\nIs visually aligned with the rest of the Barbecuez brand\n\nFully connects with backend for fetching categories, items, and customization\n\n", "modifiedCode": "Redesign the Menu Page to provide a luxurious, animated, interactive food ordering experience that aligns with the visual identity of Barbecuez Restaurant. It should feel premium, cinematic, and deeply engaging.\n\n✨ Visual Design Goals:\nBuild a dark, elegant layout using:\n\nPrimary background: #000000\n\nText + accent neon glows:\n\nElectric Blue #00FFFF\n\nNeon Pink #FF00FF\n\nLime Green #39FF14\n\nInclude subtle glassmorphism or frosted overlays for food cards and modals.\n\nUse neon outlines, animated shadows, and soft 3D lift effects on hover.\n\nIntegrate motion transitions using Framer Motion (fade-in, slide-in, scale-in).\n\nResponsive design with full mobile optimization.\n\n🧩 Main Sections & Features\n1. Category Grid (Top Section)\nDisplay all menu categories in a neon-tinted grid (3-column desktop, 1-column mobile).\n\nEach tile includes:\n\nBackground image with dark overlay\n\nCategory title in bold neon text (SaladZ, BurgerZ, DrinkZ, etc.)\n\nHover effect: category image zoom-in + glowing border\n\nOn click: smooth scroll or transition to that category’s item list\n\n2. Dynamic Item Listing (Per Category)\nDisplay all items from the selected category in glowing, modern cards.\n\nEach item card should include:\n\nDish image (with soft shadow)\n\nName, short description\n\nPrice in NOK\n\n\"Customize\" button with neon border and hover glow\n\nAnimate cards on scroll with Framer Motion (staggered appearance)\n\n3. Customization Modal / Slide-In Panel\nWhen user clicks \"Customize\", open a side modal or centered glassmorphism-style dialog\n\nInclude:\n\nHero image of the dish with floating ingredient icons\n\nCustomization groups like:\n\n🥫 Saus & Topping\n\n🧀 Ost\n\n🧅 Grønnsaker\n\n🍔 Ekstra Produkter\n\nEach option has:\n\nIcon/image\n\nName + Extra Price\n\nToggle with animated check effect\n\nHighlight selected options with orange neon glow\n\nReal-time price recalculation with animation\n\nInclude CTA button: “Add to Cart” with scaling glow animation\n\n🎠 Optional: Featured Menu Slider\nAdd a featured section between categories showing trending dishes in:\n\nA carousel (e.g., Swiper.js or React Slick)\n\nEach slide has a glowing neon frame and hover effects\n\nInclude dish image, title, and CTA\n\n🛠️ Technical Instructions\nUse React functional components with clean, modular structure:\n\nCategoryGrid.jsx\n\nMenuItemCard.jsx\n\nCustomizeModal.jsx\n\nOptionToggle.jsx\n\nFeaturedSlider.jsx\n\nUse Framer Motion for:\n\nEntrance animations\n\nButton hovers\n\nModal transitions\n\nScroll effects\n\nTailwind Config:\n\nExtend theme to support custom neon shadows (box-shadow, ring, text-shadow)\n\nSetup dark mode by default\n\nUse @apply for neon utility classes (e.g., .neon-button, .neon-border)\n\nFetch dynamic data using Axios from endpoints:\n\n/api/categories\n\n/api/items?categoryId=X\n\n/api/items/:id/customization\n\n✅ Output\nA high-end, immersive Menu page that:\n\nFeels luxurious and modern\n\nAnimates smoothly on all devices\n\nHandles category filtering and customization dynamically\n\nIs visually aligned with the rest of the Barbecuez brand\n\nFully connects with backend for fetching categories, items, and customization\n\n"}