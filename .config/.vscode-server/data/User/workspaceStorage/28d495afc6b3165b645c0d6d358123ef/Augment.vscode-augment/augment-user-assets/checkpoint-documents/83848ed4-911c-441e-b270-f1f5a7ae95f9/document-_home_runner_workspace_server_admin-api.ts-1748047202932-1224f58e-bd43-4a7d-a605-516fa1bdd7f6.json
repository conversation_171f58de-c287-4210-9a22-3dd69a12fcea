{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "server/admin-api.ts"}, "originalCode": "import { Request, Response, Router } from 'express';\nimport { storage } from './storage';\nimport { sampleOrders } from './mock-orders';\n\nconst adminApiRouter = Router();\n\n// Admin Settings Endpoints\nadminApiRouter.get('/settings', async (req: Request, res: Response) => {\n  try {\n    const settings = {\n      id: 1,\n      restaurant_open: true,\n      business_hours: {\n        \"monday\": { \"open\": \"10:00\", \"close\": \"22:00\", \"delivery\": true },\n        \"tuesday\": { \"open\": \"10:00\", \"close\": \"22:00\", \"delivery\": true },\n        \"wednesday\": { \"open\": \"10:00\", \"close\": \"22:00\", \"delivery\": true },\n        \"thursday\": { \"open\": \"10:00\", \"close\": \"22:00\", \"delivery\": true },\n        \"friday\": { \"open\": \"10:00\", \"close\": \"23:00\", \"delivery\": true },\n        \"saturday\": { \"open\": \"10:00\", \"close\": \"23:00\", \"delivery\": true },\n        \"sunday\": { \"open\": \"12:00\", \"close\": \"21:00\", \"delivery\": true }\n      },\n      delivery_fee: 49,\n      estimated_time: \"25-35 min\"\n    };\n\n    res.json(settings);\n  } catch (error) {\n    console.error('Error fetching admin settings:', error);\n    res.status(500).json({ error: 'Failed to fetch admin settings' });\n  }\n});\n\nadminApiRouter.put('/settings', async (req: Request, res: Response) => {\n  try {\n    // Here you would save the settings to database\n    // For now we'll just echo back the request\n    res.json(req.body);\n  } catch (error) {\n    console.error('Error updating admin settings:', error);\n    res.status(500).json({ error: 'Failed to update admin settings' });\n  }\n});\n\n// Analytics Endpoints\nadminApiRouter.get('/analytics/summary', async (req: Request, res: Response) => {\n  try {\n    const summary = {\n      today: 12500,\n      week: 87230,\n      month: 245890,\n      orderCount: 198\n    };\n\n    res.json(summary);\n  } catch (error) {\n    console.error('Error fetching analytics summary:', error);\n    res.status(500).json({ error: 'Failed to fetch analytics summary' });\n  }\n});\n\nadminApiRouter.get('/analytics/daily', async (req: Request, res: Response) => {\n  try {\n    const dailyRevenue = [\n      { date: '2023-05-15', total: 10200 },\n      { date: '2023-05-16', total: 11450 },\n      { date: '2023-05-17', total: 9870 },\n      { date: '2023-05-18', total: 12340 },\n      { date: '2023-05-19', total: 14560 },\n      { date: '2023-05-20', total: 15780 },\n      { date: '2023-05-21', total: 13030 }\n    ];\n\n    res.json(dailyRevenue);\n  } catch (error) {\n    console.error('Error fetching daily revenue:', error);\n    res.status(500).json({ error: 'Failed to fetch daily revenue' });\n  }\n});\n\nadminApiRouter.get('/analytics/categories', async (req: Request, res: Response) => {\n  try {\n    const categoryRevenue = [\n      { category: 'BBQ Mains', total: 98450 },\n      { category: 'Sides', total: 45670 },\n      { category: 'Burgers', total: 67890 },\n      { category: 'Drinks', total: 23450 },\n      { category: 'Desserts', total: 10430 }\n    ];\n\n    res.json(categoryRevenue);\n  } catch (error) {\n    console.error('Error fetching category revenue:', error);\n    res.status(500).json({ error: 'Failed to fetch category revenue' });\n  }\n});\n\n// Order Management API Routes\nadminApiRouter.get('/orders', async (req: Request, res: Response) => {\n  try {\n    const statusFilter = req.query.status as string;\n    // Use the mock orders for demonstration\n    let orders = sampleOrders;\n\n    // Apply status filtering if applicable\n    if (statusFilter && statusFilter !== 'all') {\n      if (statusFilter === 'active') {\n        // Active orders are those that are not completed or cancelled\n        orders = orders.filter(order =>\n          !['completed', 'cancelled'].includes(order.status)\n        );\n      } else {\n        orders = orders.filter(order => order.status === statusFilter);\n      }\n    }\n\n    res.json(orders);\n  } catch (error) {\n    console.error('Error fetching orders:', error);\n    res.status(500).json({ error: 'Failed to fetch orders' });\n  }\n});\n\nadminApiRouter.get('/orders/:id', async (req: Request, res: Response) => {\n  try {\n    const orderId = parseInt(req.params.id);\n    const order = await storage.getOrderById(orderId);\n\n    if (!order) {\n      return res.status(404).json({ error: 'Order not found' });\n    }\n\n    res.json(order);\n  } catch (error) {\n    console.error(`Error fetching order ${req.params.id}:`, error);\n    res.status(500).json({ error: 'Failed to fetch order' });\n  }\n});\n\nadminApiRouter.put('/orders/:id/status', async (req: Request, res: Response) => {\n  try {\n    const orderId = parseInt(req.params.id);\n    const { newStatus } = req.body;\n\n    if (!newStatus) {\n      return res.status(400).json({ error: 'New status is required' });\n    }\n\n    // Find the order in our sample orders\n    const orderIndex = sampleOrders.findIndex(order => order.id === orderId);\n    if (orderIndex === -1) {\n      return res.status(404).json({ error: 'Order not found' });\n    }\n\n    // Update the status in our mock data\n    sampleOrders[orderIndex].status = newStatus;\n\n    res.json({\n      success: true,\n      message: `Order ${orderId} status updated to ${newStatus}`,\n      order: sampleOrders[orderIndex]\n    });\n  } catch (error) {\n    console.error(`Error updating order status for ${req.params.id}:`, error);\n    res.status(500).json({ error: 'Failed to update order status' });\n  }\n});\n\nadminApiRouter.post('/dispatch/to-driver', async (req: Request, res: Response) => {\n  try {\n    const { orderId } = req.body;\n\n    if (!orderId) {\n      return res.status(400).json({ error: 'Order ID is required' });\n    }\n\n    // In a real app, this would dispatch to a driver system\n    // For now, we just acknowledge receipt\n    res.json({\n      success: true,\n      message: `Order #${orderId} has been dispatched to a driver`,\n      timestamp: new Date().toISOString()\n    });\n  } catch (error) {\n    console.error('Error dispatching order to driver:', error);\n    res.status(500).json({ error: 'Failed to dispatch order to driver' });\n  }\n});\n\n// Category Management API Routes\nadminApiRouter.get('/categories', async (req: Request, res: Response) => {\n  try {\n    const categories = await storage.getAllMenuCategories();\n    res.json(categories);\n  } catch (error) {\n    console.error('Error fetching categories:', error);\n    res.status(500).json({ error: 'Failed to fetch categories' });\n  }\n});\n\nadminApiRouter.post('/categories', async (req: Request, res: Response) => {\n  try {\n    const { name, imageUrl } = req.body;\n\n    if (!name) {\n      return res.status(400).json({ error: 'Category name is required' });\n    }\n\n    const category = await storage.createMenuCategory({ name, imageUrl });\n    res.status(201).json(category);\n  } catch (error) {\n    console.error('Error creating category:', error);\n    res.status(500).json({ error: 'Failed to create category' });\n  }\n});\n\nadminApiRouter.put('/categories/:id', async (req: Request, res: Response) => {\n  try {\n    const id = parseInt(req.params.id);\n    const { name, imageUrl } = req.body;\n\n    if (!name) {\n      return res.status(400).json({ error: 'Category name is required' });\n    }\n\n    const category = await storage.updateMenuCategory(id, { name, imageUrl });\n\n    if (!category) {\n      return res.status(404).json({ error: 'Category not found' });\n    }\n\n    res.json(category);\n  } catch (error) {\n    console.error('Error updating category:', error);\n    res.status(500).json({ error: 'Failed to update category' });\n  }\n});\n\nadminApiRouter.delete('/categories/:id', async (req: Request, res: Response) => {\n  try {\n    const id = parseInt(req.params.id);\n    const success = await storage.deleteMenuCategory(id);\n\n    if (!success) {\n      return res.status(404).json({ error: 'Category not found' });\n    }\n\n    res.json({ message: 'Category deleted successfully' });\n  } catch (error) {\n    console.error('Error deleting category:', error);\n    res.status(500).json({ error: 'Failed to delete category' });\n  }\n});\n\n// Menu Item Management API Routes\nadminApiRouter.get('/items', async (req: Request, res: Response) => {\n  try {\n    const items = await storage.getAllMenuItems();\n    res.json(items);\n  } catch (error) {\n    console.error('Error fetching menu items:', error);\n    res.status(500).json({ error: 'Failed to fetch menu items' });\n  }\n});\n\nadminApiRouter.post('/items', async (req: Request, res: Response) => {\n  try {\n    const { name, description, price, imageUrl, categoryId, available } = req.body;\n\n    if (!name || !price) {\n      return res.status(400).json({ error: 'Name and price are required' });\n    }\n\n    const item = await storage.createMenuItem({\n      name,\n      description,\n      price,\n      imageUrl,\n      categoryId,\n      available: available !== undefined ? available : true,\n      rating: 0,\n      reviews: 0\n    });\n\n    res.status(201).json(item);\n  } catch (error) {\n    console.error('Error creating menu item:', error);\n    res.status(500).json({ error: 'Failed to create menu item' });\n  }\n});\n\nadminApiRouter.put('/items/:id', async (req: Request, res: Response) => {\n  try {\n    const id = parseInt(req.params.id);\n    const { name, description, price, imageUrl, categoryId, available } = req.body;\n\n    if (!name || !price) {\n      return res.status(400).json({ error: 'Name and price are required' });\n    }\n\n    const item = await storage.updateMenuItem(id, {\n      name,\n      description,\n      price,\n      imageUrl,\n      categoryId,\n      available\n    });\n\n    if (!item) {\n      return res.status(404).json({ error: 'Menu item not found' });\n    }\n\n    res.json(item);\n  } catch (error) {\n    console.error('Error updating menu item:', error);\n    res.status(500).json({ error: 'Failed to update menu item' });\n  }\n});\n\nadminApiRouter.delete('/items/:id', async (req: Request, res: Response) => {\n  try {\n    const id = parseInt(req.params.id);\n    const success = await storage.deleteMenuItem(id);\n\n    if (!success) {\n      return res.status(404).json({ error: 'Menu item not found' });\n    }\n\n    res.json({ message: 'Menu item deleted successfully' });\n  } catch (error) {\n    console.error('Error deleting menu item:', error);\n    res.status(500).json({ error: 'Failed to delete menu item' });\n  }\n});\n\nexport default adminApiRouter;", "modifiedCode": "import { Request, Response, Router } from 'express';\nimport { storage } from './storage';\nimport { sampleOrders } from './mock-orders';\n\nconst adminApiRouter = Router();\n\n// Admin Settings Endpoints\nadminApiRouter.get('/settings', async (req: Request, res: Response) => {\n  try {\n    const settings = await storage.getRestaurantSettings();\n\n    if (!settings) {\n      // Return default settings if none exist\n      const defaultSettings = {\n        id: 1,\n        restaurant_open: true,\n        business_hours: {\n          \"monday\": { \"open\": \"10:00\", \"close\": \"22:00\", \"delivery\": true },\n          \"tuesday\": { \"open\": \"10:00\", \"close\": \"22:00\", \"delivery\": true },\n          \"wednesday\": { \"open\": \"10:00\", \"close\": \"22:00\", \"delivery\": true },\n          \"thursday\": { \"open\": \"10:00\", \"close\": \"22:00\", \"delivery\": true },\n          \"friday\": { \"open\": \"10:00\", \"close\": \"23:00\", \"delivery\": true },\n          \"saturday\": { \"open\": \"10:00\", \"close\": \"23:00\", \"delivery\": true },\n          \"sunday\": { \"open\": \"12:00\", \"close\": \"21:00\", \"delivery\": true }\n        },\n        delivery_fee: 49,\n        estimated_time: \"25-35 min\"\n      };\n      return res.json(defaultSettings);\n    }\n\n    // Convert database format to API format\n    const apiSettings = {\n      id: settings.id,\n      restaurant_open: settings.restaurantOpen,\n      business_hours: settings.businessHours,\n      delivery_fee: settings.deliveryFee,\n      estimated_time: settings.estimatedTime\n    };\n\n    res.json(apiSettings);\n  } catch (error) {\n    console.error('Error fetching admin settings:', error);\n    res.status(500).json({ error: 'Failed to fetch admin settings' });\n  }\n});\n\nadminApiRouter.put('/settings', async (req: Request, res: Response) => {\n  try {\n    const { restaurant_open, business_hours, delivery_fee, estimated_time } = req.body;\n\n    // Convert API format to database format\n    const dbSettings = {\n      restaurantOpen: restaurant_open,\n      businessHours: business_hours,\n      deliveryFee: delivery_fee,\n      estimatedTime: estimated_time\n    };\n\n    const updatedSettings = await storage.updateRestaurantSettings(dbSettings);\n\n    if (!updatedSettings) {\n      return res.status(500).json({ error: 'Failed to update settings' });\n    }\n\n    // Convert back to API format for response\n    const apiSettings = {\n      id: updatedSettings.id,\n      restaurant_open: updatedSettings.restaurantOpen,\n      business_hours: updatedSettings.businessHours,\n      delivery_fee: updatedSettings.deliveryFee,\n      estimated_time: updatedSettings.estimatedTime\n    };\n\n    res.json(apiSettings);\n  } catch (error) {\n    console.error('Error updating admin settings:', error);\n    res.status(500).json({ error: 'Failed to update admin settings' });\n  }\n});\n\n// Analytics Endpoints\nadminApiRouter.get('/analytics/summary', async (req: Request, res: Response) => {\n  try {\n    const summary = {\n      today: 12500,\n      week: 87230,\n      month: 245890,\n      orderCount: 198\n    };\n\n    res.json(summary);\n  } catch (error) {\n    console.error('Error fetching analytics summary:', error);\n    res.status(500).json({ error: 'Failed to fetch analytics summary' });\n  }\n});\n\nadminApiRouter.get('/analytics/daily', async (req: Request, res: Response) => {\n  try {\n    const dailyRevenue = [\n      { date: '2023-05-15', total: 10200 },\n      { date: '2023-05-16', total: 11450 },\n      { date: '2023-05-17', total: 9870 },\n      { date: '2023-05-18', total: 12340 },\n      { date: '2023-05-19', total: 14560 },\n      { date: '2023-05-20', total: 15780 },\n      { date: '2023-05-21', total: 13030 }\n    ];\n\n    res.json(dailyRevenue);\n  } catch (error) {\n    console.error('Error fetching daily revenue:', error);\n    res.status(500).json({ error: 'Failed to fetch daily revenue' });\n  }\n});\n\nadminApiRouter.get('/analytics/categories', async (req: Request, res: Response) => {\n  try {\n    const categoryRevenue = [\n      { category: 'BBQ Mains', total: 98450 },\n      { category: 'Sides', total: 45670 },\n      { category: 'Burgers', total: 67890 },\n      { category: 'Drinks', total: 23450 },\n      { category: 'Desserts', total: 10430 }\n    ];\n\n    res.json(categoryRevenue);\n  } catch (error) {\n    console.error('Error fetching category revenue:', error);\n    res.status(500).json({ error: 'Failed to fetch category revenue' });\n  }\n});\n\n// Order Management API Routes\nadminApiRouter.get('/orders', async (req: Request, res: Response) => {\n  try {\n    const statusFilter = req.query.status as string;\n    // Use the mock orders for demonstration\n    let orders = sampleOrders;\n\n    // Apply status filtering if applicable\n    if (statusFilter && statusFilter !== 'all') {\n      if (statusFilter === 'active') {\n        // Active orders are those that are not completed or cancelled\n        orders = orders.filter(order =>\n          !['completed', 'cancelled'].includes(order.status)\n        );\n      } else {\n        orders = orders.filter(order => order.status === statusFilter);\n      }\n    }\n\n    res.json(orders);\n  } catch (error) {\n    console.error('Error fetching orders:', error);\n    res.status(500).json({ error: 'Failed to fetch orders' });\n  }\n});\n\nadminApiRouter.get('/orders/:id', async (req: Request, res: Response) => {\n  try {\n    const orderId = parseInt(req.params.id);\n    const order = await storage.getOrderById(orderId);\n\n    if (!order) {\n      return res.status(404).json({ error: 'Order not found' });\n    }\n\n    res.json(order);\n  } catch (error) {\n    console.error(`Error fetching order ${req.params.id}:`, error);\n    res.status(500).json({ error: 'Failed to fetch order' });\n  }\n});\n\nadminApiRouter.put('/orders/:id/status', async (req: Request, res: Response) => {\n  try {\n    const orderId = parseInt(req.params.id);\n    const { newStatus } = req.body;\n\n    if (!newStatus) {\n      return res.status(400).json({ error: 'New status is required' });\n    }\n\n    // Find the order in our sample orders\n    const orderIndex = sampleOrders.findIndex(order => order.id === orderId);\n    if (orderIndex === -1) {\n      return res.status(404).json({ error: 'Order not found' });\n    }\n\n    // Update the status in our mock data\n    sampleOrders[orderIndex].status = newStatus;\n\n    res.json({\n      success: true,\n      message: `Order ${orderId} status updated to ${newStatus}`,\n      order: sampleOrders[orderIndex]\n    });\n  } catch (error) {\n    console.error(`Error updating order status for ${req.params.id}:`, error);\n    res.status(500).json({ error: 'Failed to update order status' });\n  }\n});\n\nadminApiRouter.post('/dispatch/to-driver', async (req: Request, res: Response) => {\n  try {\n    const { orderId } = req.body;\n\n    if (!orderId) {\n      return res.status(400).json({ error: 'Order ID is required' });\n    }\n\n    // In a real app, this would dispatch to a driver system\n    // For now, we just acknowledge receipt\n    res.json({\n      success: true,\n      message: `Order #${orderId} has been dispatched to a driver`,\n      timestamp: new Date().toISOString()\n    });\n  } catch (error) {\n    console.error('Error dispatching order to driver:', error);\n    res.status(500).json({ error: 'Failed to dispatch order to driver' });\n  }\n});\n\n// Category Management API Routes\nadminApiRouter.get('/categories', async (req: Request, res: Response) => {\n  try {\n    const categories = await storage.getAllMenuCategories();\n    res.json(categories);\n  } catch (error) {\n    console.error('Error fetching categories:', error);\n    res.status(500).json({ error: 'Failed to fetch categories' });\n  }\n});\n\nadminApiRouter.post('/categories', async (req: Request, res: Response) => {\n  try {\n    const { name, imageUrl } = req.body;\n\n    if (!name) {\n      return res.status(400).json({ error: 'Category name is required' });\n    }\n\n    const category = await storage.createMenuCategory({ name, imageUrl });\n    res.status(201).json(category);\n  } catch (error) {\n    console.error('Error creating category:', error);\n    res.status(500).json({ error: 'Failed to create category' });\n  }\n});\n\nadminApiRouter.put('/categories/:id', async (req: Request, res: Response) => {\n  try {\n    const id = parseInt(req.params.id);\n    const { name, imageUrl } = req.body;\n\n    if (!name) {\n      return res.status(400).json({ error: 'Category name is required' });\n    }\n\n    const category = await storage.updateMenuCategory(id, { name, imageUrl });\n\n    if (!category) {\n      return res.status(404).json({ error: 'Category not found' });\n    }\n\n    res.json(category);\n  } catch (error) {\n    console.error('Error updating category:', error);\n    res.status(500).json({ error: 'Failed to update category' });\n  }\n});\n\nadminApiRouter.delete('/categories/:id', async (req: Request, res: Response) => {\n  try {\n    const id = parseInt(req.params.id);\n    const success = await storage.deleteMenuCategory(id);\n\n    if (!success) {\n      return res.status(404).json({ error: 'Category not found' });\n    }\n\n    res.json({ message: 'Category deleted successfully' });\n  } catch (error) {\n    console.error('Error deleting category:', error);\n    res.status(500).json({ error: 'Failed to delete category' });\n  }\n});\n\n// Menu Item Management API Routes\nadminApiRouter.get('/items', async (req: Request, res: Response) => {\n  try {\n    const items = await storage.getAllMenuItems();\n    res.json(items);\n  } catch (error) {\n    console.error('Error fetching menu items:', error);\n    res.status(500).json({ error: 'Failed to fetch menu items' });\n  }\n});\n\nadminApiRouter.post('/items', async (req: Request, res: Response) => {\n  try {\n    const { name, description, price, imageUrl, categoryId, available } = req.body;\n\n    if (!name || !price) {\n      return res.status(400).json({ error: 'Name and price are required' });\n    }\n\n    const item = await storage.createMenuItem({\n      name,\n      description,\n      price,\n      imageUrl,\n      categoryId,\n      available: available !== undefined ? available : true,\n      rating: 0,\n      reviews: 0\n    });\n\n    res.status(201).json(item);\n  } catch (error) {\n    console.error('Error creating menu item:', error);\n    res.status(500).json({ error: 'Failed to create menu item' });\n  }\n});\n\nadminApiRouter.put('/items/:id', async (req: Request, res: Response) => {\n  try {\n    const id = parseInt(req.params.id);\n    const { name, description, price, imageUrl, categoryId, available } = req.body;\n\n    if (!name || !price) {\n      return res.status(400).json({ error: 'Name and price are required' });\n    }\n\n    const item = await storage.updateMenuItem(id, {\n      name,\n      description,\n      price,\n      imageUrl,\n      categoryId,\n      available\n    });\n\n    if (!item) {\n      return res.status(404).json({ error: 'Menu item not found' });\n    }\n\n    res.json(item);\n  } catch (error) {\n    console.error('Error updating menu item:', error);\n    res.status(500).json({ error: 'Failed to update menu item' });\n  }\n});\n\nadminApiRouter.delete('/items/:id', async (req: Request, res: Response) => {\n  try {\n    const id = parseInt(req.params.id);\n    const success = await storage.deleteMenuItem(id);\n\n    if (!success) {\n      return res.status(404).json({ error: 'Menu item not found' });\n    }\n\n    res.json({ message: 'Menu item deleted successfully' });\n  } catch (error) {\n    console.error('Error deleting menu item:', error);\n    res.status(500).json({ error: 'Failed to delete menu item' });\n  }\n});\n\nexport default adminApiRouter;"}