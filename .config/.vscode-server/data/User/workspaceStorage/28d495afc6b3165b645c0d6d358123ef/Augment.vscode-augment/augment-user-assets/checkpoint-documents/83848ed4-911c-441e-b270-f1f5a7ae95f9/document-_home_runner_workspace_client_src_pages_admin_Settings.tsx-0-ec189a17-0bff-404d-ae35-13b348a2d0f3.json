{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/pages/admin/Settings.tsx"}, "originalCode": "import { useState, useEffect } from \"react\";\nimport { motion } from \"framer-motion\";\nimport AdminLayout from \"./AdminLayout\";\nimport { getAdminSettings, updateAdminSettings, AdminSettings as SettingsType } from \"@/api/adminApi\";\nimport { useToast } from \"@/hooks/use-toast\";\n\n// Days of the week for business hours\nconst DAYS_OF_WEEK = [\n  \"Monday\", \"Tuesday\", \"Wednesday\", \"Thursday\", \"Friday\", \"Saturday\", \"Sunday\"\n];\n\n// Map the business hours from API format to our component format\nconst formatBusinessHours = (data: SettingsType | null) => {\n  if (!data || !data.business_hours) return {};\n  \n  const hours: Record<string, { open: boolean; deliveryAvailable: boolean; openTime: string; closeTime: string }> = {};\n  \n  // Convert from API format to our component format\n  for (const day of DAYS_OF_WEEK) {\n    const dayLower = day.toLowerCase();\n    if (data.business_hours[dayLower]) {\n      hours[dayLower] = {\n        open: true, // If it exists in the data, it's open\n        deliveryAvailable: data.business_hours[dayLower].delivery,\n        openTime: data.business_hours[dayLower].open,\n        closeTime: data.business_hours[dayLower].close\n      };\n    } else {\n      // Default values if not present\n      hours[dayLower] = {\n        open: false,\n        deliveryAvailable: false,\n        openTime: \"10:00\",\n        closeTime: \"22:00\"\n      };\n    }\n  }\n  \n  return hours;\n};\n\nconst Settings = () => {\n  const { toast } = useToast();\n  \n  // Restaurant status state\n  const [isOpen, setIsOpen] = useState(true);\n  const [isSaving, setIsSaving] = useState(false);\n  const [saveSuccess, setSaveSuccess] = useState(false);\n  const [isLoading, setIsLoading] = useState(true);\n  \n  // Initialize business hours with default values for all days\n  const defaultBusinessHours = DAYS_OF_WEEK.reduce((acc, day) => {\n    acc[day.toLowerCase()] = {\n      open: true,\n      deliveryAvailable: true,\n      openTime: \"10:00\",\n      closeTime: \"22:00\"\n    };\n    return acc;\n  }, {} as Record<string, { open: boolean; deliveryAvailable: boolean; openTime: string; closeTime: string }>);\n  \n  // Business hours state - initialize with default hours, will update from API\n  const [businessHours, setBusinessHours] = useState<Record<string, { open: boolean; deliveryAvailable: boolean; openTime: string; closeTime: string }>>(defaultBusinessHours);\n  \n  // Delivery settings\n  const [deliveryFee, setDeliveryFee] = useState(49);\n  const [deliveryTimeMin, setDeliveryTimeMin] = useState(25);\n  const [deliveryTimeMax, setDeliveryTimeMax] = useState(35);\n  const [estimatedTime, setEstimatedTime] = useState(\"25-35 min\");\n  \n  // Load settings from API\n  useEffect(() => {\n    const loadSettings = async () => {\n      setIsLoading(true);\n      \n      let data;\n      // Try to load settings from API\n      try {\n        const response = await fetch('/api/admin/settings');\n        data = await response.json();\n        console.log(\"Settings loaded from API:\", data);\n      } catch (error) {\n        console.log(\"Failed to load settings from API, using defaults\");\n        // Use default settings if the API call fails\n        data = {\n          id: 1,\n          restaurant_open: true,\n          business_hours: {\n            monday: { open: \"10:00\", close: \"22:00\", delivery: true },\n            tuesday: { open: \"10:00\", close: \"22:00\", delivery: true },\n            wednesday: { open: \"10:00\", close: \"22:00\", delivery: true },\n            thursday: { open: \"10:00\", close: \"22:00\", delivery: true },\n            friday: { open: \"10:00\", close: \"23:00\", delivery: true },\n            saturday: { open: \"10:00\", close: \"23:00\", delivery: true },\n            sunday: { open: \"12:00\", close: \"21:00\", delivery: true }\n          },\n          delivery_fee: 49,\n          estimated_time: \"25-35 min\"\n        };\n        \n        toast({\n          title: \"Using Default Settings\",\n          description: \"Could not connect to settings API, using defaults\",\n          variant: \"default\"\n        });\n      }\n      \n      try {\n        // Update state with the data (either from API or defaults)\n        setIsOpen(data.restaurant_open);\n        setBusinessHours(formatBusinessHours(data));\n        setDeliveryFee(data.delivery_fee);\n        setEstimatedTime(data.estimated_time);\n        \n        // Parse time range from string like \"25-35 min\"\n        const timeMatch = data.estimated_time.match(/(\\d+)-(\\d+)/);\n        if (timeMatch) {\n          setDeliveryTimeMin(parseInt(timeMatch[1]));\n          setDeliveryTimeMax(parseInt(timeMatch[2]));\n        }\n      } catch (error) {\n        console.error('Error processing settings data:', error);\n        toast({\n          title: \"Error\",\n          description: \"Failed to process restaurant settings\",\n          variant: \"destructive\"\n        });\n      } finally {\n        setIsLoading(false);\n      }\n    };\n    \n    loadSettings();\n  }, [toast]);\n  \n  const handleDayToggle = (day: string) => {\n    setBusinessHours(prev => ({\n      ...prev,\n      [day.toLowerCase()]: {\n        ...prev[day.toLowerCase()],\n        open: !prev[day.toLowerCase()].open\n      }\n    }));\n  };\n  \n  const handleDeliveryToggle = (day: string) => {\n    setBusinessHours(prev => ({\n      ...prev,\n      [day.toLowerCase()]: {\n        ...prev[day.toLowerCase()],\n        deliveryAvailable: !prev[day.toLowerCase()].deliveryAvailable\n      }\n    }));\n  };\n  \n  const handleTimeChange = (day: string, field: 'openTime' | 'closeTime', value: string) => {\n    setBusinessHours(prev => ({\n      ...prev,\n      [day.toLowerCase()]: {\n        ...prev[day.toLowerCase()],\n        [field]: value\n      }\n    }));\n  };\n  \n  const handleSave = async () => {\n    setIsSaving(true);\n    \n    try {\n      // Convert the business hours format to API format\n      const businessHoursForApi: Record<string, { open: string; close: string; delivery: boolean }> = {};\n      \n      for (const day of DAYS_OF_WEEK) {\n        const dayLower = day.toLowerCase();\n        const dayData = businessHours[dayLower];\n        \n        // Only include open days in the API data\n        if (dayData && dayData.open) {\n          businessHoursForApi[dayLower] = {\n            open: dayData.openTime,\n            close: dayData.closeTime,\n            delivery: dayData.deliveryAvailable\n          };\n        }\n      }\n      \n      // Prepare data for API\n      const settingsData = {\n        restaurant_open: isOpen,\n        business_hours: businessHoursForApi,\n        delivery_fee: deliveryFee,\n        estimated_time: `${deliveryTimeMin}-${deliveryTimeMax} min`\n      };\n      \n      // Save to API\n      await fetch('/api/admin/settings', {\n        method: 'PUT',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify(settingsData),\n      });\n      \n      // Show success message\n      toast({\n        title: \"Settings saved\",\n        description: \"Your restaurant settings have been updated\",\n        variant: \"default\"\n      });\n      \n      setSaveSuccess(true);\n      setTimeout(() => setSaveSuccess(false), 3000);\n    } catch (error) {\n      console.error('Error saving settings:', error);\n      toast({\n        title: \"Error\",\n        description: \"Failed to save restaurant settings\",\n        variant: \"destructive\"\n      });\n    } finally {\n      setIsSaving(false);\n    }\n  };\n  \n  return (\n    <AdminLayout>\n      <div className=\"max-w-5xl mx-auto\">\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.5 }}\n        >\n          <h1 className=\"text-3xl font-bold mb-2 bg-gradient-to-r from-purple-400 via-cyan-400 to-purple-400 text-transparent bg-clip-text\">\n            Restaurant Settings\n          </h1>\n          <p className=\"text-gray-400 mb-8\">\n            Manage your restaurant's operational settings and business hours.\n          </p>\n          \n          {/* Restaurant Status */}\n          <div className=\"mb-10 bg-gray-900/50 backdrop-blur-sm rounded-xl p-6 border border-gray-800\">\n            <h2 className=\"text-xl font-bold mb-4 text-white\">Restaurant Status</h2>\n            \n            <div className=\"flex items-center\">\n              <div className=\"mr-4\">\n                <label className=\"relative inline-flex items-center cursor-pointer\">\n                  <input \n                    type=\"checkbox\"\n                    checked={isOpen}\n                    onChange={() => setIsOpen(!isOpen)}\n                    className=\"sr-only peer\"\n                  />\n                  <div className={`w-14 h-7 rounded-full peer transition-all \n                                  ${isOpen \n                                    ? 'bg-gradient-to-r from-green-600 to-green-400' \n                                    : 'bg-gradient-to-r from-red-600 to-red-400'} \n                                  peer-focus:outline-none peer-focus:ring-2 peer-focus:ring-cyan-300 \n                                  peer-checked:after:translate-x-7 after:content-[''] after:absolute \n                                  after:top-0.5 after:left-[2px] after:bg-white after:border-gray-300 \n                                  after:border after:rounded-full after:h-6 after:w-6 after:transition-all \n                                  peer-checked:bg-green-600`}>\n                  </div>\n                  <span className=\"ml-3 text-lg text-white\">\n                    Restaurant is {isOpen ? 'OPEN' : 'CLOSED'} \n                  </span>\n                </label>\n              </div>\n              \n              <div className={`ml-3 p-2 rounded text-sm \n                              ${isOpen ? 'bg-green-900/50 text-green-300' : 'bg-red-900/50 text-red-300'}`}>\n                {isOpen \n                  ? '✓ Customers can place orders' \n                  : '✕ Customers cannot place orders'}\n              </div>\n            </div>\n          </div>\n          \n          {/* Business Hours */}\n          <div className=\"mb-10 bg-gray-900/50 backdrop-blur-sm rounded-xl p-6 border border-gray-800\">\n            <h2 className=\"text-xl font-bold mb-4 text-white\">Business Hours</h2>\n            \n            <div className=\"overflow-x-auto\">\n              <table className=\"w-full\">\n                <thead>\n                  <tr className=\"border-b border-gray-800\">\n                    <th className=\"py-3 text-left text-gray-400\">Day</th>\n                    <th className=\"py-3 text-left text-gray-400\">Status</th>\n                    <th className=\"py-3 text-left text-gray-400\">Hours</th>\n                    <th className=\"py-3 text-left text-gray-400\">Delivery Available</th>\n                  </tr>\n                </thead>\n                <tbody>\n                  {DAYS_OF_WEEK.map((day) => {\n                    const dayData = businessHours[day.toLowerCase()];\n                    return (\n                      <tr key={day} className=\"border-b border-gray-800/50\">\n                        <td className=\"py-4\">\n                          <span className=\"font-medium text-white\">{day}</span>\n                        </td>\n                        <td className=\"py-4\">\n                          <label className=\"relative inline-flex items-center cursor-pointer\">\n                            <input \n                              type=\"checkbox\"\n                              checked={dayData.open}\n                              onChange={() => handleDayToggle(day.toLowerCase())}\n                              className=\"sr-only peer\"\n                            />\n                            <div className=\"w-11 h-6 rounded-full peer \n                                        bg-gray-700 peer-checked:bg-cyan-900 \n                                        peer-focus:outline-none peer-focus:ring-2 peer-focus:ring-cyan-300 \n                                        peer-checked:after:translate-x-5 after:content-[''] after:absolute \n                                        after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 \n                                        after:border after:rounded-full after:h-5 after:w-5 after:transition-all\">\n                            </div>\n                            <span className=\"ml-3 text-sm text-gray-300\">\n                              {dayData.open ? 'Open' : 'Closed'}\n                            </span>\n                          </label>\n                        </td>\n                        <td className=\"py-4\">\n                          <div className=\"flex items-center space-x-2\">\n                            <input \n                              type=\"time\"\n                              value={dayData.openTime}\n                              onChange={(e) => handleTimeChange(day.toLowerCase(), 'openTime', e.target.value)}\n                              disabled={!dayData.open}\n                              className=\"bg-gray-800 border border-gray-700 text-white rounded px-3 py-2 \n                                      focus:outline-none focus:ring-2 focus:ring-cyan-500 focus:border-transparent\n                                      disabled:opacity-50 disabled:cursor-not-allowed\"\n                            />\n                            <span className=\"text-gray-400\">to</span>\n                            <input \n                              type=\"time\"\n                              value={dayData.closeTime}\n                              onChange={(e) => handleTimeChange(day.toLowerCase(), 'closeTime', e.target.value)}\n                              disabled={!dayData.open}\n                              className=\"bg-gray-800 border border-gray-700 text-white rounded px-3 py-2 \n                                      focus:outline-none focus:ring-2 focus:ring-cyan-500 focus:border-transparent\n                                      disabled:opacity-50 disabled:cursor-not-allowed\"\n                            />\n                          </div>\n                        </td>\n                        <td className=\"py-4\">\n                          <label className=\"relative inline-flex items-center cursor-pointer\">\n                            <input \n                              type=\"checkbox\"\n                              checked={dayData.deliveryAvailable}\n                              onChange={() => handleDeliveryToggle(day.toLowerCase())}\n                              disabled={!dayData.open}\n                              className=\"sr-only peer\"\n                            />\n                            <div className=\"w-11 h-6 rounded-full peer \n                                        bg-gray-700 peer-checked:bg-purple-800 \n                                        peer-focus:outline-none peer-focus:ring-2 peer-focus:ring-purple-300 \n                                        peer-checked:after:translate-x-5 after:content-[''] after:absolute \n                                        after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 \n                                        after:border after:rounded-full after:h-5 after:w-5 after:transition-all\n                                        disabled:opacity-50 disabled:cursor-not-allowed\">\n                            </div>\n                            <span className=\"ml-3 text-sm text-gray-300\">\n                              {dayData.deliveryAvailable ? 'Yes' : 'No'}\n                            </span>\n                          </label>\n                        </td>\n                      </tr>\n                    );\n                  })}\n                </tbody>\n              </table>\n            </div>\n          </div>\n          \n          {/* Delivery Settings */}\n          <div className=\"mb-10 bg-gray-900/50 backdrop-blur-sm rounded-xl p-6 border border-gray-800\">\n            <h2 className=\"text-xl font-bold mb-4 text-white\">Delivery Settings</h2>\n            \n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n              <div>\n                <label className=\"block text-gray-300 mb-2\">Delivery Fee (NOK)</label>\n                <input \n                  type=\"number\"\n                  value={deliveryFee}\n                  onChange={(e) => setDeliveryFee(parseInt(e.target.value))}\n                  className=\"bg-gray-800 border border-gray-700 text-white rounded px-3 py-2 w-full\n                           focus:outline-none focus:ring-2 focus:ring-cyan-500 focus:border-transparent\"\n                />\n              </div>\n              \n              <div className=\"grid grid-cols-2 gap-4\">\n                <div>\n                  <label className=\"block text-gray-300 mb-2\">Est. Time Min (min)</label>\n                  <input \n                    type=\"number\"\n                    value={deliveryTimeMin}\n                    onChange={(e) => setDeliveryTimeMin(parseInt(e.target.value))}\n                    className=\"bg-gray-800 border border-gray-700 text-white rounded px-3 py-2 w-full\n                             focus:outline-none focus:ring-2 focus:ring-cyan-500 focus:border-transparent\"\n                  />\n                </div>\n                \n                <div>\n                  <label className=\"block text-gray-300 mb-2\">Est. Time Max (min)</label>\n                  <input \n                    type=\"number\"\n                    value={deliveryTimeMax}\n                    onChange={(e) => setDeliveryTimeMax(parseInt(e.target.value))}\n                    className=\"bg-gray-800 border border-gray-700 text-white rounded px-3 py-2 w-full\n                             focus:outline-none focus:ring-2 focus:ring-cyan-500 focus:border-transparent\"\n                  />\n                </div>\n              </div>\n              \n              <div className=\"md:col-span-2\">\n                <p className=\"text-gray-400 text-sm\">\n                  This will be displayed to customers as: \n                  <span className=\"ml-2 text-cyan-400 font-medium\">\n                    Delivery Time: {deliveryTimeMin}–{deliveryTimeMax} min, Fee: {deliveryFee} NOK\n                  </span>\n                </p>\n              </div>\n            </div>\n          </div>\n          \n          {/* Save Button */}\n          <div className=\"flex justify-end\">\n            <button\n              onClick={handleSave}\n              disabled={isSaving}\n              className=\"relative overflow-hidden rounded-lg px-6 py-3 group\"\n            >\n              {/* Button Background */}\n              <span className=\"absolute inset-0 w-full h-full bg-gradient-to-r from-cyan-700 to-cyan-600\"></span>\n              \n              {/* Button Glow Effect */}\n              <span className={`absolute inset-0 w-full h-full transition-all duration-300 \n                               bg-gradient-to-r from-cyan-600 via-cyan-400 to-cyan-600\n                               opacity-0 group-hover:opacity-50 group-hover:blur-lg\n                               ${saveSuccess ? 'opacity-50 blur-lg' : ''}`}>\n              </span>\n              \n              {/* Button Border */}\n              <span className=\"absolute inset-0 w-full h-full border border-cyan-500 rounded-lg\"></span>\n              \n              {/* Button Text */}\n              <span className=\"relative z-10 flex items-center justify-center text-white font-medium\">\n                {isSaving ? (\n                  <>\n                    <svg className=\"animate-spin -ml-1 mr-2 h-4 w-4 text-white\" xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\">\n                      <circle className=\"opacity-25\" cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\" strokeWidth=\"4\"></circle>\n                      <path className=\"opacity-75\" fill=\"currentColor\" d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"></path>\n                    </svg>\n                    Saving...\n                  </>\n                ) : saveSuccess ? (\n                  <>\n                    <svg className=\"w-4 h-4 mr-2\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M5 13l4 4L19 7\" />\n                    </svg>\n                    Saved!\n                  </>\n                ) : (\n                  'Save Settings'\n                )}\n              </span>\n            </button>\n          </div>\n        </motion.div>\n      </div>\n    </AdminLayout>\n  );\n};\n\nexport default Settings;", "modifiedCode": "import { useState, useEffect } from \"react\";\nimport { motion } from \"framer-motion\";\nimport AdminLayout from \"./AdminLayout\";\nimport { getAdminSettings, updateAdminSettings, AdminSettings as SettingsType } from \"@/api/adminApi\";\nimport { useToast } from \"@/hooks/use-toast\";\n\n// Days of the week for business hours\nconst DAYS_OF_WEEK = [\n  \"Monday\", \"Tuesday\", \"Wednesday\", \"Thursday\", \"Friday\", \"Saturday\", \"Sunday\"\n];\n\n// Map the business hours from API format to our component format\nconst formatBusinessHours = (data: SettingsType | null) => {\n  if (!data || !data.business_hours) return {};\n  \n  const hours: Record<string, { open: boolean; deliveryAvailable: boolean; openTime: string; closeTime: string }> = {};\n  \n  // Convert from API format to our component format\n  for (const day of DAYS_OF_WEEK) {\n    const dayLower = day.toLowerCase();\n    if (data.business_hours[dayLower]) {\n      hours[dayLower] = {\n        open: true, // If it exists in the data, it's open\n        deliveryAvailable: data.business_hours[dayLower].delivery,\n        openTime: data.business_hours[dayLower].open,\n        closeTime: data.business_hours[dayLower].close\n      };\n    } else {\n      // Default values if not present\n      hours[dayLower] = {\n        open: false,\n        deliveryAvailable: false,\n        openTime: \"10:00\",\n        closeTime: \"22:00\"\n      };\n    }\n  }\n  \n  return hours;\n};\n\nconst Settings = () => {\n  const { toast } = useToast();\n  \n  // Restaurant status state\n  const [isOpen, setIsOpen] = useState(true);\n  const [isSaving, setIsSaving] = useState(false);\n  const [saveSuccess, setSaveSuccess] = useState(false);\n  const [isLoading, setIsLoading] = useState(true);\n  \n  // Initialize business hours with default values for all days\n  const defaultBusinessHours = DAYS_OF_WEEK.reduce((acc, day) => {\n    acc[day.toLowerCase()] = {\n      open: true,\n      deliveryAvailable: true,\n      openTime: \"10:00\",\n      closeTime: \"22:00\"\n    };\n    return acc;\n  }, {} as Record<string, { open: boolean; deliveryAvailable: boolean; openTime: string; closeTime: string }>);\n  \n  // Business hours state - initialize with default hours, will update from API\n  const [businessHours, setBusinessHours] = useState<Record<string, { open: boolean; deliveryAvailable: boolean; openTime: string; closeTime: string }>>(defaultBusinessHours);\n  \n  // Delivery settings\n  const [deliveryFee, setDeliveryFee] = useState(49);\n  const [deliveryTimeMin, setDeliveryTimeMin] = useState(25);\n  const [deliveryTimeMax, setDeliveryTimeMax] = useState(35);\n  const [estimatedTime, setEstimatedTime] = useState(\"25-35 min\");\n  \n  // Load settings from API\n  useEffect(() => {\n    const loadSettings = async () => {\n      setIsLoading(true);\n      \n      let data;\n      // Try to load settings from API\n      try {\n        const response = await fetch('/api/admin/settings');\n        data = await response.json();\n        console.log(\"Settings loaded from API:\", data);\n      } catch (error) {\n        console.log(\"Failed to load settings from API, using defaults\");\n        // Use default settings if the API call fails\n        data = {\n          id: 1,\n          restaurant_open: true,\n          business_hours: {\n            monday: { open: \"10:00\", close: \"22:00\", delivery: true },\n            tuesday: { open: \"10:00\", close: \"22:00\", delivery: true },\n            wednesday: { open: \"10:00\", close: \"22:00\", delivery: true },\n            thursday: { open: \"10:00\", close: \"22:00\", delivery: true },\n            friday: { open: \"10:00\", close: \"23:00\", delivery: true },\n            saturday: { open: \"10:00\", close: \"23:00\", delivery: true },\n            sunday: { open: \"12:00\", close: \"21:00\", delivery: true }\n          },\n          delivery_fee: 49,\n          estimated_time: \"25-35 min\"\n        };\n        \n        toast({\n          title: \"Using Default Settings\",\n          description: \"Could not connect to settings API, using defaults\",\n          variant: \"default\"\n        });\n      }\n      \n      try {\n        // Update state with the data (either from API or defaults)\n        setIsOpen(data.restaurant_open);\n        setBusinessHours(formatBusinessHours(data));\n        setDeliveryFee(data.delivery_fee);\n        setEstimatedTime(data.estimated_time);\n        \n        // Parse time range from string like \"25-35 min\"\n        const timeMatch = data.estimated_time.match(/(\\d+)-(\\d+)/);\n        if (timeMatch) {\n          setDeliveryTimeMin(parseInt(timeMatch[1]));\n          setDeliveryTimeMax(parseInt(timeMatch[2]));\n        }\n      } catch (error) {\n        console.error('Error processing settings data:', error);\n        toast({\n          title: \"Error\",\n          description: \"Failed to process restaurant settings\",\n          variant: \"destructive\"\n        });\n      } finally {\n        setIsLoading(false);\n      }\n    };\n    \n    loadSettings();\n  }, [toast]);\n  \n  const handleDayToggle = (day: string) => {\n    setBusinessHours(prev => ({\n      ...prev,\n      [day.toLowerCase()]: {\n        ...prev[day.toLowerCase()],\n        open: !prev[day.toLowerCase()].open\n      }\n    }));\n  };\n  \n  const handleDeliveryToggle = (day: string) => {\n    setBusinessHours(prev => ({\n      ...prev,\n      [day.toLowerCase()]: {\n        ...prev[day.toLowerCase()],\n        deliveryAvailable: !prev[day.toLowerCase()].deliveryAvailable\n      }\n    }));\n  };\n  \n  const handleTimeChange = (day: string, field: 'openTime' | 'closeTime', value: string) => {\n    setBusinessHours(prev => ({\n      ...prev,\n      [day.toLowerCase()]: {\n        ...prev[day.toLowerCase()],\n        [field]: value\n      }\n    }));\n  };\n  \n  const handleSave = async () => {\n    setIsSaving(true);\n    \n    try {\n      // Convert the business hours format to API format\n      const businessHoursForApi: Record<string, { open: string; close: string; delivery: boolean }> = {};\n      \n      for (const day of DAYS_OF_WEEK) {\n        const dayLower = day.toLowerCase();\n        const dayData = businessHours[dayLower];\n        \n        // Only include open days in the API data\n        if (dayData && dayData.open) {\n          businessHoursForApi[dayLower] = {\n            open: dayData.openTime,\n            close: dayData.closeTime,\n            delivery: dayData.deliveryAvailable\n          };\n        }\n      }\n      \n      // Prepare data for API\n      const settingsData = {\n        restaurant_open: isOpen,\n        business_hours: businessHoursForApi,\n        delivery_fee: deliveryFee,\n        estimated_time: `${deliveryTimeMin}-${deliveryTimeMax} min`\n      };\n      \n      // Save to API\n      await fetch('/api/admin/settings', {\n        method: 'PUT',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify(settingsData),\n      });\n      \n      // Show success message\n      toast({\n        title: \"Settings saved\",\n        description: \"Your restaurant settings have been updated\",\n        variant: \"default\"\n      });\n      \n      setSaveSuccess(true);\n      setTimeout(() => setSaveSuccess(false), 3000);\n    } catch (error) {\n      console.error('Error saving settings:', error);\n      toast({\n        title: \"Error\",\n        description: \"Failed to save restaurant settings\",\n        variant: \"destructive\"\n      });\n    } finally {\n      setIsSaving(false);\n    }\n  };\n  \n  return (\n    <AdminLayout>\n      <div className=\"max-w-5xl mx-auto\">\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.5 }}\n        >\n          <h1 className=\"text-3xl font-bold mb-2 bg-gradient-to-r from-purple-400 via-cyan-400 to-purple-400 text-transparent bg-clip-text\">\n            Restaurant Settings\n          </h1>\n          <p className=\"text-gray-400 mb-8\">\n            Manage your restaurant's operational settings and business hours.\n          </p>\n          \n          {/* Restaurant Status */}\n          <div className=\"mb-10 bg-gray-900/50 backdrop-blur-sm rounded-xl p-6 border border-gray-800\">\n            <h2 className=\"text-xl font-bold mb-4 text-white\">Restaurant Status</h2>\n            \n            <div className=\"flex items-center\">\n              <div className=\"mr-4\">\n                <label className=\"relative inline-flex items-center cursor-pointer\">\n                  <input \n                    type=\"checkbox\"\n                    checked={isOpen}\n                    onChange={() => setIsOpen(!isOpen)}\n                    className=\"sr-only peer\"\n                  />\n                  <div className={`w-14 h-7 rounded-full peer transition-all \n                                  ${isOpen \n                                    ? 'bg-gradient-to-r from-green-600 to-green-400' \n                                    : 'bg-gradient-to-r from-red-600 to-red-400'} \n                                  peer-focus:outline-none peer-focus:ring-2 peer-focus:ring-cyan-300 \n                                  peer-checked:after:translate-x-7 after:content-[''] after:absolute \n                                  after:top-0.5 after:left-[2px] after:bg-white after:border-gray-300 \n                                  after:border after:rounded-full after:h-6 after:w-6 after:transition-all \n                                  peer-checked:bg-green-600`}>\n                  </div>\n                  <span className=\"ml-3 text-lg text-white\">\n                    Restaurant is {isOpen ? 'OPEN' : 'CLOSED'} \n                  </span>\n                </label>\n              </div>\n              \n              <div className={`ml-3 p-2 rounded text-sm \n                              ${isOpen ? 'bg-green-900/50 text-green-300' : 'bg-red-900/50 text-red-300'}`}>\n                {isOpen \n                  ? '✓ Customers can place orders' \n                  : '✕ Customers cannot place orders'}\n              </div>\n            </div>\n          </div>\n          \n          {/* Business Hours */}\n          <div className=\"mb-10 bg-gray-900/50 backdrop-blur-sm rounded-xl p-6 border border-gray-800\">\n            <h2 className=\"text-xl font-bold mb-4 text-white\">Business Hours</h2>\n            \n            <div className=\"overflow-x-auto\">\n              <table className=\"w-full\">\n                <thead>\n                  <tr className=\"border-b border-gray-800\">\n                    <th className=\"py-3 text-left text-gray-400\">Day</th>\n                    <th className=\"py-3 text-left text-gray-400\">Status</th>\n                    <th className=\"py-3 text-left text-gray-400\">Hours</th>\n                    <th className=\"py-3 text-left text-gray-400\">Delivery Available</th>\n                  </tr>\n                </thead>\n                <tbody>\n                  {DAYS_OF_WEEK.map((day) => {\n                    const dayData = businessHours[day.toLowerCase()];\n                    return (\n                      <tr key={day} className=\"border-b border-gray-800/50\">\n                        <td className=\"py-4\">\n                          <span className=\"font-medium text-white\">{day}</span>\n                        </td>\n                        <td className=\"py-4\">\n                          <label className=\"relative inline-flex items-center cursor-pointer\">\n                            <input \n                              type=\"checkbox\"\n                              checked={dayData.open}\n                              onChange={() => handleDayToggle(day.toLowerCase())}\n                              className=\"sr-only peer\"\n                            />\n                            <div className=\"w-11 h-6 rounded-full peer \n                                        bg-gray-700 peer-checked:bg-cyan-900 \n                                        peer-focus:outline-none peer-focus:ring-2 peer-focus:ring-cyan-300 \n                                        peer-checked:after:translate-x-5 after:content-[''] after:absolute \n                                        after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 \n                                        after:border after:rounded-full after:h-5 after:w-5 after:transition-all\">\n                            </div>\n                            <span className=\"ml-3 text-sm text-gray-300\">\n                              {dayData.open ? 'Open' : 'Closed'}\n                            </span>\n                          </label>\n                        </td>\n                        <td className=\"py-4\">\n                          <div className=\"flex items-center space-x-2\">\n                            <input \n                              type=\"time\"\n                              value={dayData.openTime}\n                              onChange={(e) => handleTimeChange(day.toLowerCase(), 'openTime', e.target.value)}\n                              disabled={!dayData.open}\n                              className=\"bg-gray-800 border border-gray-700 text-white rounded px-3 py-2 \n                                      focus:outline-none focus:ring-2 focus:ring-cyan-500 focus:border-transparent\n                                      disabled:opacity-50 disabled:cursor-not-allowed\"\n                            />\n                            <span className=\"text-gray-400\">to</span>\n                            <input \n                              type=\"time\"\n                              value={dayData.closeTime}\n                              onChange={(e) => handleTimeChange(day.toLowerCase(), 'closeTime', e.target.value)}\n                              disabled={!dayData.open}\n                              className=\"bg-gray-800 border border-gray-700 text-white rounded px-3 py-2 \n                                      focus:outline-none focus:ring-2 focus:ring-cyan-500 focus:border-transparent\n                                      disabled:opacity-50 disabled:cursor-not-allowed\"\n                            />\n                          </div>\n                        </td>\n                        <td className=\"py-4\">\n                          <label className=\"relative inline-flex items-center cursor-pointer\">\n                            <input \n                              type=\"checkbox\"\n                              checked={dayData.deliveryAvailable}\n                              onChange={() => handleDeliveryToggle(day.toLowerCase())}\n                              disabled={!dayData.open}\n                              className=\"sr-only peer\"\n                            />\n                            <div className=\"w-11 h-6 rounded-full peer \n                                        bg-gray-700 peer-checked:bg-purple-800 \n                                        peer-focus:outline-none peer-focus:ring-2 peer-focus:ring-purple-300 \n                                        peer-checked:after:translate-x-5 after:content-[''] after:absolute \n                                        after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 \n                                        after:border after:rounded-full after:h-5 after:w-5 after:transition-all\n                                        disabled:opacity-50 disabled:cursor-not-allowed\">\n                            </div>\n                            <span className=\"ml-3 text-sm text-gray-300\">\n                              {dayData.deliveryAvailable ? 'Yes' : 'No'}\n                            </span>\n                          </label>\n                        </td>\n                      </tr>\n                    );\n                  })}\n                </tbody>\n              </table>\n            </div>\n          </div>\n          \n          {/* Delivery Settings */}\n          <div className=\"mb-10 bg-gray-900/50 backdrop-blur-sm rounded-xl p-6 border border-gray-800\">\n            <h2 className=\"text-xl font-bold mb-4 text-white\">Delivery Settings</h2>\n            \n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n              <div>\n                <label className=\"block text-gray-300 mb-2\">Delivery Fee (NOK)</label>\n                <input \n                  type=\"number\"\n                  value={deliveryFee}\n                  onChange={(e) => setDeliveryFee(parseInt(e.target.value))}\n                  className=\"bg-gray-800 border border-gray-700 text-white rounded px-3 py-2 w-full\n                           focus:outline-none focus:ring-2 focus:ring-cyan-500 focus:border-transparent\"\n                />\n              </div>\n              \n              <div className=\"grid grid-cols-2 gap-4\">\n                <div>\n                  <label className=\"block text-gray-300 mb-2\">Est. Time Min (min)</label>\n                  <input \n                    type=\"number\"\n                    value={deliveryTimeMin}\n                    onChange={(e) => setDeliveryTimeMin(parseInt(e.target.value))}\n                    className=\"bg-gray-800 border border-gray-700 text-white rounded px-3 py-2 w-full\n                             focus:outline-none focus:ring-2 focus:ring-cyan-500 focus:border-transparent\"\n                  />\n                </div>\n                \n                <div>\n                  <label className=\"block text-gray-300 mb-2\">Est. Time Max (min)</label>\n                  <input \n                    type=\"number\"\n                    value={deliveryTimeMax}\n                    onChange={(e) => setDeliveryTimeMax(parseInt(e.target.value))}\n                    className=\"bg-gray-800 border border-gray-700 text-white rounded px-3 py-2 w-full\n                             focus:outline-none focus:ring-2 focus:ring-cyan-500 focus:border-transparent\"\n                  />\n                </div>\n              </div>\n              \n              <div className=\"md:col-span-2\">\n                <p className=\"text-gray-400 text-sm\">\n                  This will be displayed to customers as: \n                  <span className=\"ml-2 text-cyan-400 font-medium\">\n                    Delivery Time: {deliveryTimeMin}–{deliveryTimeMax} min, Fee: {deliveryFee} NOK\n                  </span>\n                </p>\n              </div>\n            </div>\n          </div>\n          \n          {/* Save Button */}\n          <div className=\"flex justify-end\">\n            <button\n              onClick={handleSave}\n              disabled={isSaving}\n              className=\"relative overflow-hidden rounded-lg px-6 py-3 group\"\n            >\n              {/* Button Background */}\n              <span className=\"absolute inset-0 w-full h-full bg-gradient-to-r from-cyan-700 to-cyan-600\"></span>\n              \n              {/* Button Glow Effect */}\n              <span className={`absolute inset-0 w-full h-full transition-all duration-300 \n                               bg-gradient-to-r from-cyan-600 via-cyan-400 to-cyan-600\n                               opacity-0 group-hover:opacity-50 group-hover:blur-lg\n                               ${saveSuccess ? 'opacity-50 blur-lg' : ''}`}>\n              </span>\n              \n              {/* Button Border */}\n              <span className=\"absolute inset-0 w-full h-full border border-cyan-500 rounded-lg\"></span>\n              \n              {/* Button Text */}\n              <span className=\"relative z-10 flex items-center justify-center text-white font-medium\">\n                {isSaving ? (\n                  <>\n                    <svg className=\"animate-spin -ml-1 mr-2 h-4 w-4 text-white\" xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\">\n                      <circle className=\"opacity-25\" cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\" strokeWidth=\"4\"></circle>\n                      <path className=\"opacity-75\" fill=\"currentColor\" d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"></path>\n                    </svg>\n                    Saving...\n                  </>\n                ) : saveSuccess ? (\n                  <>\n                    <svg className=\"w-4 h-4 mr-2\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M5 13l4 4L19 7\" />\n                    </svg>\n                    Saved!\n                  </>\n                ) : (\n                  'Save Settings'\n                )}\n              </span>\n            </button>\n          </div>\n        </motion.div>\n      </div>\n    </AdminLayout>\n  );\n};\n\nexport default Settings;"}