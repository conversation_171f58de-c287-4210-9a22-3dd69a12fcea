{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/CustomizeModal.tsx"}, "originalCode": "import { useState, useEffect } from 'react';\nimport { Dish } from '@shared/schema';\nimport { formatCurrency } from '@/lib/utils';\nimport Button from './Button';\nimport { useCart } from '@/context/CartContext';\n\n// Define types for customization options\ninterface CustomizationOption {\n  id: string;\n  name: string;\n  price: number;\n  icon: string;\n}\n\ninterface CustomizationGroup {\n  id: string;\n  title: string;\n  options: CustomizationOption[];\n}\n\ninterface CustomizeModalProps {\n  dish: Dish;\n  isOpen: boolean;\n  onClose: () => void;\n}\n\nconst CustomizeModal = ({ dish, isOpen, onClose }: CustomizeModalProps) => {\n  const { addToCart } = useCart();\n  const [selectedOptions, setSelectedOptions] = useState<string[]>([]);\n  const [totalPrice, setTotalPrice] = useState(dish.price);\n\n  // Mock customization groups\n  const customizationGroups: CustomizationGroup[] = [\n    {\n      id: 'sauces',\n      title: 'Saus & Topping',\n      options: [\n        { id: 'bbq-sauce', name: 'BBQ Sauce', price: 10, icon: '🍯' },\n        { id: 'hot-sauce', name: 'Hot Sauce', price: 10, icon: '🌶️' },\n        { id: 'mayo', name: 'Mayo', price: 10, icon: '🥄' },\n        { id: 'honey-mustard', name: 'Honey Mustard', price: 15, icon: '🍯' }\n      ]\n    },\n    {\n      id: 'cheese',\n      title: 'Ost',\n      options: [\n        { id: 'cheddar', name: 'Cheddar', price: 15, icon: '🧀' },\n        { id: 'blue-cheese', name: 'Blue Cheese', price: 20, icon: '🧀' },\n        { id: 'mozzarella', name: 'Mozzarella', price: 15, icon: '🧀' }\n      ]\n    },\n    {\n      id: 'extras',\n      title: 'Ekstra Produkter',\n      options: [\n        { id: 'bacon', name: 'Bacon', price: 25, icon: '🥓' },\n        { id: 'double-meat', name: 'Double Meat', price: 40, icon: '🥩' },\n        { id: 'egg', name: 'Fried Egg', price: 15, icon: '🍳' }\n      ]\n    },\n    {\n      id: 'vegetables',\n      title: 'Grønnsaker',\n      options: [\n        { id: 'lettuce', name: 'Lettuce', price: 0, icon: '🥬' },\n        { id: 'tomato', name: 'Tomato', price: 0, icon: '🍅' },\n        { id: 'onion', name: 'Onion', price: 0, icon: '🧅' },\n        { id: 'cucumber', name: 'Cucumber', price: 0, icon: '🥒' },\n        { id: 'avocado', name: 'Avocado', price: 20, icon: '🥑' }\n      ]\n    }\n  ];\n\n  // Reset selected options when modal opens with a new dish\n  useEffect(() => {\n    if (isOpen) {\n      setSelectedOptions([]);\n      setTotalPrice(dish.price);\n    }\n  }, [isOpen, dish]);\n\n  // Handle option selection\n  const toggleOption = (optionId: string, price: number) => {\n    setSelectedOptions(prev => {\n      if (prev.includes(optionId)) {\n        // Remove option\n        setTotalPrice(current => current - price);\n        return prev.filter(id => id !== optionId);\n      } else {\n        // Add option\n        setTotalPrice(current => current + price);\n        return [...prev, optionId];\n      }\n    });\n  };\n\n  // Handle adding customized item to cart\n  const handleAddToCart = () => {\n    // Create a customized dish with selected options information\n    const customizedDish: Dish = {\n      ...dish,\n      price: totalPrice,\n      description: `${dish.description} (Customized with ${selectedOptions.length} extras)`\n    };\n\n    addToCart(customizedDish);\n    onClose();\n  };\n\n  if (!isOpen) return null;\n\n  return (\n    <div className=\"fixed inset-0 bg-black bg-opacity-80 z-50 flex items-center justify-center p-4\">\n      <div className=\"bg-gray-900 rounded-lg max-w-4xl w-full max-h-[90vh] overflow-y-auto border border-gray-800\">\n        {/* Header with close button */}\n        <div className=\"flex justify-between items-center p-5 border-b border-gray-800\">\n          <h2 className=\"font-playfair text-2xl font-bold text-white\">{dish.name}</h2>\n          <button\n            onClick={onClose}\n            className=\"text-gray-400 hover:text-secondary transition-colors\"\n            aria-label=\"Close modal\"\n          >\n            <i className=\"fas fa-times text-xl\"></i>\n          </button>\n        </div>\n\n        {/* Content */}\n        <div className=\"p-5\">\n          <div className=\"grid grid-cols-1 md:grid-cols-2 gap-8 mb-8\">\n            {/* Dish image */}\n            <div className=\"rounded-lg overflow-hidden\">\n              <img\n                src={dish.imageUrl}\n                alt={dish.name}\n                className=\"w-full h-64 object-cover\"\n              />\n            </div>\n\n            {/* Dish description */}\n            <div>\n              <p className=\"font-poppins text-gray-300 mb-4\">{dish.description}</p>\n              <div className=\"flex items-center mb-2\">\n                <span className=\"text-yellow-400 mr-1\"><i className=\"fas fa-star\"></i></span>\n                <span className=\"font-poppins text-sm text-gray-300\">\n                  {(dish.rating || 0) / 10} ({dish.reviews || 0} reviews)\n                </span>\n              </div>\n              <div className=\"font-poppins text-xl font-medium text-accent mb-4\">\n                Base price: {formatCurrency(dish.price)}\n              </div>\n\n              <p className=\"font-poppins text-gray-400 text-sm mb-4\">\n                Customize your dish with the options below. Select or deselect options to build your perfect meal.\n              </p>\n            </div>\n          </div>\n\n          {/* Customization Options */}\n          <div className=\"space-y-8\">\n            {customizationGroups.map(group => (\n              <div key={group.id} className=\"border-t border-gray-800 pt-4\">\n                <h3 className=\"font-playfair text-xl font-medium text-white mb-4\">{group.title}</h3>\n\n                <div className=\"grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-3\">\n                  {group.options.map(option => {\n                    const isSelected = selectedOptions.includes(option.id);\n\n                    return (\n                      <button\n                        key={option.id}\n                        onClick={() => toggleOption(option.id, option.price)}\n                        className={`flex items-center p-3 rounded-md border transition-all duration-300 ${\n                          isSelected\n                            ? 'border-secondary box-glow-pink bg-gray-800'\n                            : 'border-gray-700 hover:border-gray-600'\n                        }`}\n                      >\n                        <div className=\"text-xl mr-3\">{option.icon}</div>\n                        <div className=\"flex-1 text-left\">\n                          <div className=\"font-poppins text-white\">{option.name}</div>\n                          <div className=\"font-poppins text-xs text-gray-400\">\n                            {option.price > 0 ? `+${formatCurrency(option.price)}` : 'Included'}\n                          </div>\n                        </div>\n                        <div className={`w-5 h-5 rounded-full flex items-center justify-center ml-2 ${\n                          isSelected ? 'bg-secondary text-black' : 'bg-gray-700'\n                        }`}>\n                          {isSelected && <i className=\"fas fa-check text-xs\"></i>}\n                        </div>\n                      </button>\n                    );\n                  })}\n                </div>\n              </div>\n            ))}\n          </div>\n        </div>\n\n        {/* Footer with total price and actions */}\n        <div className=\"bg-gray-800 p-5 flex flex-col md:flex-row items-center justify-between sticky bottom-0\">\n          <div className=\"mb-4 md:mb-0\">\n            <div className=\"font-poppins text-gray-300 text-sm\">Total Price</div>\n            <div className=\"font-playfair text-2xl font-bold text-accent text-glow-green animate-glow\">\n              {formatCurrency(totalPrice)}\n            </div>\n          </div>\n\n          <div className=\"flex space-x-4\">\n            <Button\n              variant=\"outline-accent\"\n              size=\"md\"\n              onClick={onClose}\n            >\n              Cancel\n            </Button>\n\n            <Button\n              variant=\"secondary\"\n              size=\"md\"\n              onClick={handleAddToCart}\n            >\n              Add to Cart\n            </Button>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default CustomizeModal;", "modifiedCode": "import { useState, useEffect } from 'react';\nimport { Dish } from '@shared/schema';\nimport { formatCurrency } from '@/lib/utils';\nimport Button from './Button';\nimport { useCart } from '@/context/CartContext';\n\n// Define types for customization options\ninterface CustomizationOption {\n  id: string;\n  name: string;\n  price: number;\n  icon: string;\n}\n\ninterface CustomizationGroup {\n  id: string;\n  title: string;\n  options: CustomizationOption[];\n}\n\ninterface CustomizeModalProps {\n  dish: Dish;\n  isOpen: boolean;\n  onClose: () => void;\n}\n\nconst CustomizeModal = ({ dish, isOpen, onClose }: CustomizeModalProps) => {\n  const { addToCart } = useCart();\n  const [selectedOptions, setSelectedOptions] = useState<string[]>([]);\n  const [totalPrice, setTotalPrice] = useState(dish.price);\n\n  // Mock customization groups\n  const customizationGroups: CustomizationGroup[] = [\n    {\n      id: 'sauces',\n      title: 'Saus & Topping',\n      options: [\n        { id: 'bbq-sauce', name: 'BBQ Sauce', price: 10, icon: '🍯' },\n        { id: 'hot-sauce', name: 'Hot Sauce', price: 10, icon: '🌶️' },\n        { id: 'mayo', name: 'Mayo', price: 10, icon: '🥄' },\n        { id: 'honey-mustard', name: 'Honey Mustard', price: 15, icon: '🍯' }\n      ]\n    },\n    {\n      id: 'cheese',\n      title: 'Ost',\n      options: [\n        { id: 'cheddar', name: 'Cheddar', price: 15, icon: '🧀' },\n        { id: 'blue-cheese', name: 'Blue Cheese', price: 20, icon: '🧀' },\n        { id: 'mozzarella', name: 'Mozzarella', price: 15, icon: '🧀' }\n      ]\n    },\n    {\n      id: 'extras',\n      title: 'Ekstra Produkter',\n      options: [\n        { id: 'bacon', name: 'Bacon', price: 25, icon: '🥓' },\n        { id: 'double-meat', name: 'Double Meat', price: 40, icon: '🥩' },\n        { id: 'egg', name: 'Fried Egg', price: 15, icon: '🍳' }\n      ]\n    },\n    {\n      id: 'vegetables',\n      title: 'Grønnsaker',\n      options: [\n        { id: 'lettuce', name: 'Lettuce', price: 0, icon: '🥬' },\n        { id: 'tomato', name: 'Tomato', price: 0, icon: '🍅' },\n        { id: 'onion', name: 'Onion', price: 0, icon: '🧅' },\n        { id: 'cucumber', name: 'Cucumber', price: 0, icon: '🥒' },\n        { id: 'avocado', name: 'Avocado', price: 20, icon: '🥑' }\n      ]\n    }\n  ];\n\n  // Reset selected options when modal opens with a new dish\n  useEffect(() => {\n    if (isOpen) {\n      setSelectedOptions([]);\n      setTotalPrice(dish.price);\n    }\n  }, [isOpen, dish]);\n\n  // Handle option selection\n  const toggleOption = (optionId: string, price: number) => {\n    setSelectedOptions(prev => {\n      if (prev.includes(optionId)) {\n        // Remove option\n        setTotalPrice(current => current - price);\n        return prev.filter(id => id !== optionId);\n      } else {\n        // Add option\n        setTotalPrice(current => current + price);\n        return [...prev, optionId];\n      }\n    });\n  };\n\n  // Handle adding customized item to cart\n  const handleAddToCart = () => {\n    // Create a customized dish with selected options information\n    const customizedDish: Dish = {\n      ...dish,\n      price: totalPrice,\n      description: `${dish.description} (Customized with ${selectedOptions.length} extras)`\n    };\n\n    addToCart(customizedDish);\n    onClose();\n  };\n\n  if (!isOpen) return null;\n\n  return (\n    <div className=\"fixed inset-0 bg-black bg-opacity-80 z-50 flex items-center justify-center p-4\">\n      <div className=\"bg-gray-900 rounded-lg max-w-4xl w-full max-h-[90vh] overflow-y-auto border border-gray-800\">\n        {/* Header with close button */}\n        <div className=\"flex justify-between items-center p-5 border-b border-gray-800\">\n          <h2 className=\"font-playfair text-2xl font-bold text-white\">{dish.name}</h2>\n          <button\n            onClick={onClose}\n            className=\"text-gray-400 hover:text-secondary transition-colors\"\n            aria-label=\"Close modal\"\n          >\n            <i className=\"fas fa-times text-xl\"></i>\n          </button>\n        </div>\n\n        {/* Content */}\n        <div className=\"p-5\">\n          <div className=\"grid grid-cols-1 md:grid-cols-2 gap-8 mb-8\">\n            {/* Dish image */}\n            <div className=\"rounded-lg overflow-hidden\">\n              <img\n                src={(dish as any).imageUrl || (dish as any).image_url || 'https://images.unsplash.com/photo-1555939594-58d7cb561ad1?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&h=400'}\n                alt={dish.name}\n                className=\"w-full h-64 object-cover\"\n              />\n            </div>\n\n            {/* Dish description */}\n            <div>\n              <p className=\"font-poppins text-gray-300 mb-4\">{dish.description}</p>\n              <div className=\"flex items-center mb-2\">\n                <span className=\"text-yellow-400 mr-1\"><i className=\"fas fa-star\"></i></span>\n                <span className=\"font-poppins text-sm text-gray-300\">\n                  {(dish.rating || 0) / 10} ({dish.reviews || 0} reviews)\n                </span>\n              </div>\n              <div className=\"font-poppins text-xl font-medium text-accent mb-4\">\n                Base price: {formatCurrency(dish.price)}\n              </div>\n\n              <p className=\"font-poppins text-gray-400 text-sm mb-4\">\n                Customize your dish with the options below. Select or deselect options to build your perfect meal.\n              </p>\n            </div>\n          </div>\n\n          {/* Customization Options */}\n          <div className=\"space-y-8\">\n            {customizationGroups.map(group => (\n              <div key={group.id} className=\"border-t border-gray-800 pt-4\">\n                <h3 className=\"font-playfair text-xl font-medium text-white mb-4\">{group.title}</h3>\n\n                <div className=\"grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-3\">\n                  {group.options.map(option => {\n                    const isSelected = selectedOptions.includes(option.id);\n\n                    return (\n                      <button\n                        key={option.id}\n                        onClick={() => toggleOption(option.id, option.price)}\n                        className={`flex items-center p-3 rounded-md border transition-all duration-300 ${\n                          isSelected\n                            ? 'border-secondary box-glow-pink bg-gray-800'\n                            : 'border-gray-700 hover:border-gray-600'\n                        }`}\n                      >\n                        <div className=\"text-xl mr-3\">{option.icon}</div>\n                        <div className=\"flex-1 text-left\">\n                          <div className=\"font-poppins text-white\">{option.name}</div>\n                          <div className=\"font-poppins text-xs text-gray-400\">\n                            {option.price > 0 ? `+${formatCurrency(option.price)}` : 'Included'}\n                          </div>\n                        </div>\n                        <div className={`w-5 h-5 rounded-full flex items-center justify-center ml-2 ${\n                          isSelected ? 'bg-secondary text-black' : 'bg-gray-700'\n                        }`}>\n                          {isSelected && <i className=\"fas fa-check text-xs\"></i>}\n                        </div>\n                      </button>\n                    );\n                  })}\n                </div>\n              </div>\n            ))}\n          </div>\n        </div>\n\n        {/* Footer with total price and actions */}\n        <div className=\"bg-gray-800 p-5 flex flex-col md:flex-row items-center justify-between sticky bottom-0\">\n          <div className=\"mb-4 md:mb-0\">\n            <div className=\"font-poppins text-gray-300 text-sm\">Total Price</div>\n            <div className=\"font-playfair text-2xl font-bold text-accent text-glow-green animate-glow\">\n              {formatCurrency(totalPrice)}\n            </div>\n          </div>\n\n          <div className=\"flex space-x-4\">\n            <Button\n              variant=\"outline-accent\"\n              size=\"md\"\n              onClick={onClose}\n            >\n              Cancel\n            </Button>\n\n            <Button\n              variant=\"secondary\"\n              size=\"md\"\n              onClick={handleAddToCart}\n            >\n              Add to Cart\n            </Button>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default CustomizeModal;"}