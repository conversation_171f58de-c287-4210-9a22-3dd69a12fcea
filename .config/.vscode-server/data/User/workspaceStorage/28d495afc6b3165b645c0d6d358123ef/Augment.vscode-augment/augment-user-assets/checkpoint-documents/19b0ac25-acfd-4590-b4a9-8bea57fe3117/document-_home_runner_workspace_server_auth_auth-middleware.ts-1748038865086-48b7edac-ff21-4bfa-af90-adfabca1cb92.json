{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "server/auth/auth-middleware.ts"}, "modifiedCode": "import { Request, Response, NextFunction } from 'express';\n\n// Middleware to check if user is authenticated\nexport const isAuthenticated = (req: Request, res: Response, next: NextFunction) => {\n  // In a real application, this would check for a valid session or JWT token\n  // For this demo, we'll just check if the user object exists in the request\n  if (req.user) {\n    return next();\n  }\n  return res.status(401).json({ message: 'Unauthorized' });\n};\n\n// Middleware to check if user has admin role\nexport const isAdmin = (req: Request, res: Response, next: NextFunction) => {\n  // First check if authenticated\n  if (!req.user) {\n    return res.status(401).json({ message: 'Unauthorized' });\n  }\n  \n  // Then check if user has admin role\n  if ((req.user as any).role === 'admin') {\n    return next();\n  }\n  \n  return res.status(403).json({ message: 'Forbidden: Admin access required' });\n};\n\n// Middleware to check if user has manager role\nexport const isManager = (req: Request, res: Response, next: NextFunction) => {\n  // First check if authenticated\n  if (!req.user) {\n    return res.status(401).json({ message: 'Unauthorized' });\n  }\n  \n  // Then check if user has manager role\n  if ((req.user as any).role === 'manager') {\n    return next();\n  }\n  \n  return res.status(403).json({ message: 'Forbidden: Manager access required' });\n};\n\n// Middleware to check if user has driver role\nexport const isDriver = (req: Request, res: Response, next: NextFunction) => {\n  // First check if authenticated\n  if (!req.user) {\n    return res.status(401).json({ message: 'Unauthorized' });\n  }\n  \n  // Then check if user has driver role\n  if ((req.user as any).role === 'driver') {\n    return next();\n  }\n  \n  return res.status(403).json({ message: 'Forbidden: Driver access required' });\n};\n"}