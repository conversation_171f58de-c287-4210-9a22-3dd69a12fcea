{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/pages/Menu.tsx"}, "originalCode": "import { useState, useEffect } from \"react\";\nimport { useQuery } from \"@tanstack/react-query\";\nimport { getDishes, getCategories } from \"@/api/api\";\nimport { Dish } from \"@shared/schema\";\nimport Button from \"@/components/Button\";\nimport CategoryCard from \"@/components/CategoryCard\";\nimport MenuItemCard from \"@/components/MenuItemCard\";\nimport CustomizeModal from \"@/components/CustomizeModal\";\nimport { motion, AnimatePresence } from \"framer-motion\";\nimport { useInView } from \"react-intersection-observer\";\nimport { Swiper, SwiperSlide } from \"swiper/react\";\nimport { Autoplay, EffectCards } from \"swiper/modules\";\n\n// Import Swiper styles\nimport \"swiper/css\";\nimport \"swiper/css/effect-cards\";\n\n// Define enhanced category interface with images\ninterface MenuCategory {\n  id: string;\n  name: string;\n  image: string;\n}\n\n// API category interface\ninterface ApiCategory {\n  id: number;\n  name: string;\n  imageUrl: string;\n}\n\nconst Menu = () => {\n  // Animation refs\n  const [headerRef, headerInView] = useInView({\n    triggerOnce: true,\n    threshold: 0.1,\n  });\n\n  const [featuredRef, featuredInView] = useInView({\n    triggerOnce: false,\n    threshold: 0.2,\n  });\n\n  const [categoriesRef, categoriesInView] = useInView({\n    triggerOnce: true,\n    threshold: 0.1,\n  });\n\n  // Animation variants\n  const containerVariants = {\n    hidden: { opacity: 0 },\n    visible: {\n      opacity: 1,\n      transition: {\n        staggerChildren: 0.1,\n      }\n    }\n  };\n\n  const itemVariants = {\n    hidden: { y: 20, opacity: 0 },\n    visible: {\n      y: 0,\n      opacity: 1,\n      transition: { type: \"spring\", stiffness: 100 }\n    }\n  };\n\n  // State management\n  const [viewMode, setViewMode] = useState<'categories' | 'items'>('categories');\n  const [selectedCategory, setSelectedCategory] = useState<string>('');\n  const [selectedDish, setSelectedDish] = useState<Dish | null>(null);\n  const [isModalOpen, setIsModalOpen] = useState(false);\n  const [visibleDishes, setVisibleDishes] = useState<number>(6);\n\n  // Featured dishes for the slider\n  const featuredDishes = [\n    {\n      id: 'f1',\n      name: 'Signature BBQ Platter',\n      description: 'Our chef\\'s selection of premium meats',\n      price: 399,\n      image: 'https://images.unsplash.com/photo-1508615263227-c5d58c1e5821?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&h=400'\n    },\n    {\n      id: 'f2',\n      name: 'Smokehouse Burger Supreme',\n      description: 'Our award-winning burger with all the fixings',\n      price: 269,\n      image: 'https://images.unsplash.com/photo-1568901346375-23c9450c58cd?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&h=400'\n    },\n    {\n      id: 'f3',\n      name: 'Cedar Plank Salmon',\n      description: 'Fresh Norwegian salmon with maple glaze',\n      price: 299,\n      image: 'https://images.unsplash.com/photo-1519708227418-c8fd9a32b7a2?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&h=400'\n    }\n  ];\n\n  // Fetch dishes from API\n  const { data: dishes, isLoading: dishesLoading, error: dishesError } = useQuery({\n    queryKey: ['/api/dishes'],\n    queryFn: getDishes,\n    staleTime: 300000, // 5 minutes\n  });\n\n  // Fetch categories from API\n  const { data: apiCategories, isLoading: categoriesLoading, error: categoriesError } = useQuery({\n    queryKey: ['/api/categories'],\n    queryFn: getCategories,\n    staleTime: 300000, // 5 minutes\n  });\n\n  // Derived loading and error states\n  const isLoading = dishesLoading || categoriesLoading;\n  const error = dishesError || categoriesError;\n\n  // Process API categories into the format needed for the UI\n  const [menuCategories, setMenuCategories] = useState<MenuCategory[]>([]);\n  const [categoryMapping, setCategoryMapping] = useState<Record<string, string>>({});\n\n  // Convert API categories to menu categories format\n  useEffect(() => {\n    if (apiCategories && apiCategories.length > 0) {\n      // Convert API categories to menu categories\n      const processedCategories: MenuCategory[] = apiCategories.map((cat: any) => ({\n        id: cat.id.toString(),\n        name: cat.name,\n        // Use the imageUrl from API or a default image if not available\n        image: cat.imageUrl || `https://images.unsplash.com/photo-1555939594-58d7cb561ad1?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&h=400`\n      }));\n\n      // Create category mapping for filtering dishes\n      const mapping: Record<string, string> = {};\n      apiCategories.forEach((cat: any) => {\n        mapping[cat.id.toString()] = cat.name;\n      });\n\n      setMenuCategories(processedCategories);\n      setCategoryMapping(mapping);\n    } else {\n      // Fallback to default categories if API returns empty\n      setMenuCategories([\n        {\n          id: 'bbq',\n          name: 'Signature BBQ',\n          image: 'https://images.unsplash.com/photo-1529193591184-b1d58069ecdd?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&h=400'\n        },\n        {\n          id: 'starters',\n          name: 'Starters',\n          image: 'https://images.unsplash.com/photo-1527477396000-e27163b481c2?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&h=400'\n        },\n        {\n          id: 'main',\n          name: 'Main Course',\n          image: 'https://images.unsplash.com/photo-1519708227418-c8fd9a32b7a2?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&h=400'\n        },\n        {\n          id: 'desserts',\n          name: 'Desserts',\n          image: 'https://images.unsplash.com/photo-1624353365286-3f8d62daad51?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&h=400'\n        }\n      ]);\n\n      setCategoryMapping({\n        'bbq': 'Signature BBQ',\n        'starters': 'Starters',\n        'main': 'Main Course',\n        'desserts': 'Desserts'\n      });\n    }\n  }, [apiCategories]);\n\n  // Handle category selection\n  const handleCategorySelect = (categoryId: string) => {\n    setSelectedCategory(categoryId);\n    setViewMode('items');\n    // Reset visible dishes when category changes\n    setVisibleDishes(6);\n    // Scroll to top of menu section\n    document.getElementById('menu')?.scrollIntoView({ behavior: 'smooth' });\n  };\n\n  // Handle back to categories\n  const handleBackToCategories = () => {\n    setViewMode('categories');\n  };\n\n  // Handle dish customization\n  const handleCustomizeDish = (dish: Dish) => {\n    setSelectedDish(dish);\n    setIsModalOpen(true);\n  };\n\n  // Handle load more dishes\n  const loadMoreDishes = () => {\n    setVisibleDishes(prev => prev + 3);\n  };\n\n  // Filter dishes based on selected category\n  const filteredDishes = dishes ?\n    dishes.filter((dish: Dish) => {\n      if (!selectedCategory) return true;\n\n      // Handle both old and new API formats\n      const categoryId = dish.categoryId || dish.category_id;\n      return categoryId?.toString() === selectedCategory;\n    }) : [];\n\n  // Get displayed dishes based on visibility limit\n  const displayedDishes = filteredDishes.slice(0, visibleDishes);\n  const hasMoreDishes = filteredDishes.length > visibleDishes;\n\n  // Get displayed category name\n  const getCategoryDisplayName = () => {\n    const category = menuCategories.find(cat => cat.id === selectedCategory);\n    return category ? category.name : 'All Items';\n  };\n\n  return (\n    <section id=\"menu\" className=\"min-h-screen py-32 bg-black relative overflow-hidden\">\n      {/* Animated Gradient Background */}\n      <div\n        className=\"absolute inset-0 z-0 bg-black\"\n        style={{\n          background: \"radial-gradient(circle at 20% 80%, rgba(57, 255, 20, 0.03), transparent 33%), \" +\n                    \"radial-gradient(circle at 80% 20%, rgba(0, 255, 255, 0.03), transparent 33%), \" +\n                    \"radial-gradient(circle at 50% 50%, rgba(255, 0, 255, 0.03), rgba(0, 0, 0, 1) 100%)\",\n        }}\n      ></div>\n\n      {/* Neon Grid Overlay */}\n      <div className=\"absolute inset-0 z-0 opacity-[0.03]\"\n           style={{\n             backgroundImage: \"linear-gradient(to right, #FF00FF 1px, transparent 1px), linear-gradient(to bottom, #FF00FF 1px, transparent 1px)\",\n             backgroundSize: \"40px 40px\"\n           }}>\n      </div>\n\n      <div className=\"container mx-auto px-4 relative z-10\">\n        {/* Header Section */}\n        <motion.div\n          ref={headerRef}\n          initial={{ opacity: 0, y: -20 }}\n          animate={headerInView ? { opacity: 1, y: 0 } : {}}\n          transition={{ duration: 0.8 }}\n          className=\"text-center mb-16\"\n        >\n          <motion.h2\n            className=\"font-playfair text-4xl md:text-5xl lg:text-6xl font-bold mb-6 relative inline-block\"\n            initial={{ opacity: 0 }}\n            animate={headerInView ? { opacity: 1 } : {}}\n            transition={{ duration: 0.5, delay: 0.3 }}\n          >\n            {viewMode === 'categories' ? (\n              <>\n                <span className=\"text-white\">Our </span>\n                <motion.span\n                  className=\"text-transparent bg-clip-text bg-gradient-to-r from-cyan-400 via-fuchsia-500 to-lime-300 relative z-10\"\n                  animate={{\n                    textShadow: [\n                      \"0 0 5px rgba(0, 255, 255, 0.7), 0 0 10px rgba(0, 255, 255, 0.5), 0 0 15px rgba(0, 255, 255, 0.3)\",\n                      \"0 0 5px rgba(255, 0, 255, 0.7), 0 0 10px rgba(255, 0, 255, 0.5), 0 0 15px rgba(255, 0, 255, 0.3)\",\n                      \"0 0 5px rgba(57, 255, 20, 0.7), 0 0 10px rgba(57, 255, 20, 0.5), 0 0 15px rgba(57, 255, 20, 0.3)\",\n                      \"0 0 5px rgba(0, 255, 255, 0.7), 0 0 10px rgba(0, 255, 255, 0.5), 0 0 15px rgba(0, 255, 255, 0.3)\",\n                    ]\n                  }}\n                  transition={{ duration: 5, repeat: Infinity }}\n                >\n                  Menu\n                </motion.span>\n              </>\n            ) : (\n              <>\n                <span className=\"text-white\">{getCategoryDisplayName()} </span>\n                <motion.span\n                  className=\"text-transparent bg-clip-text bg-gradient-to-r from-fuchsia-500 to-cyan-400 relative z-10\"\n                  animate={{\n                    textShadow: [\n                      \"0 0 5px rgba(255, 0, 255, 0.7), 0 0 10px rgba(255, 0, 255, 0.5), 0 0 15px rgba(255, 0, 255, 0.3)\",\n                      \"0 0 5px rgba(0, 255, 255, 0.7), 0 0 10px rgba(0, 255, 255, 0.5), 0 0 15px rgba(0, 255, 255, 0.3)\",\n                      \"0 0 5px rgba(255, 0, 255, 0.7), 0 0 10px rgba(255, 0, 255, 0.5), 0 0 15px rgba(255, 0, 255, 0.3)\",\n                    ]\n                  }}\n                  transition={{ duration: 3, repeat: Infinity }}\n                >\n                  Selection\n                </motion.span>\n              </>\n            )}\n          </motion.h2>\n\n          <motion.p\n            className=\"font-poppins text-lg md:text-xl text-gray-300 max-w-2xl mx-auto\"\n            initial={{ opacity: 0, y: 20 }}\n            animate={headerInView ? { opacity: 1, y: 0 } : {}}\n            transition={{ duration: 0.6, delay: 0.5 }}\n          >\n            {viewMode === 'categories'\n              ? 'Explore our handcrafted selection of premium barbecue dishes'\n              : `Discover our ${getCategoryDisplayName()} offerings crafted with premium ingredients`\n            }\n          </motion.p>\n        </motion.div>\n\n        {/* Featured Menu Slider - Only show on category view */}\n        {viewMode === 'categories' && (\n          <motion.div\n            ref={featuredRef}\n            initial={{ opacity: 0 }}\n            animate={featuredInView ? { opacity: 1 } : {}}\n            transition={{ duration: 0.8 }}\n            className=\"mb-20\"\n          >\n            <motion.h3\n              className=\"text-2xl md:text-3xl font-playfair font-bold mb-8 text-center\"\n              initial={{ opacity: 0, y: 20 }}\n              animate={featuredInView ? { opacity: 1, y: 0 } : {}}\n              transition={{ duration: 0.6 }}\n            >\n              <span className=\"text-white\">Featured </span>\n              <span className=\"text-transparent bg-clip-text bg-gradient-to-r from-lime-300 to-cyan-400\">\n                Selections\n              </span>\n            </motion.h3>\n\n            <div className=\"px-4 sm:px-8 md:px-16 py-8\">\n              <Swiper\n                effect=\"cards\"\n                grabCursor={true}\n                modules={[EffectCards, Autoplay]}\n                className=\"max-w-xs mx-auto\"\n                autoplay={{\n                  delay: 3000,\n                  disableOnInteraction: false,\n                }}\n              >\n                {featuredDishes.map((dish) => (\n                  <SwiperSlide key={dish.id} className=\"bg-transparent\">\n                    <div className=\"bg-black/20 backdrop-blur-sm rounded-xl overflow-hidden\n                                    border border-gray-800 shadow-[0_0_15px_rgba(0,255,255,0.15)]\n                                    hover:shadow-[0_0_30px_rgba(0,255,255,0.3)] transition-all duration-500\">\n                      <div className=\"relative h-72 overflow-hidden\">\n                        <img\n                          src={dish.image}\n                          alt={dish.name}\n                          className=\"w-full h-full object-cover\"\n                        />\n                        <div className=\"absolute inset-0 bg-gradient-to-t from-black via-black/50 to-transparent\"></div>\n                        <div className=\"absolute bottom-0 left-0 right-0 p-6\">\n                          <h3 className=\"text-xl font-playfair font-bold text-white mb-2\">{dish.name}</h3>\n                          <p className=\"text-gray-300 text-sm mb-3\">{dish.description}</p>\n\n                          <div className=\"flex justify-between items-center\">\n                            <span className=\"text-cyan-400 font-semibold\">{dish.price} NOK</span>\n                            <motion.button\n                              className=\"bg-gradient-to-r from-fuchsia-600 to-cyan-600 text-white\n                                       px-4 py-2 rounded-md text-sm font-medium\n                                       hover:from-fuchsia-500 hover:to-cyan-500 transition-all\"\n                              whileHover={{ scale: 1.05 }}\n                              whileTap={{ scale: 0.95 }}\n                              onClick={() => handleCategorySelect('bbq')} // Redirect to appropriate category\n                            >\n                              Order Now\n                            </motion.button>\n                          </div>\n                        </div>\n                      </div>\n                    </div>\n                  </SwiperSlide>\n                ))}\n              </Swiper>\n            </div>\n          </motion.div>\n        )}\n\n        {/* Back Button (when viewing items) */}\n        {viewMode === 'items' && (\n          <motion.div\n            className=\"mb-8\"\n            initial={{ opacity: 0, x: -20 }}\n            animate={{ opacity: 1, x: 0 }}\n            transition={{ duration: 0.5 }}\n          >\n            <motion.button\n              className=\"group flex items-center px-5 py-2.5 relative overflow-hidden\n                         text-cyan-400 border border-cyan-400 rounded-md\n                         hover:text-white transition-colors duration-300\"\n              onClick={handleBackToCategories}\n              whileHover={{ scale: 1.03 }}\n              whileTap={{ scale: 0.97 }}\n            >\n              {/* Button glow effect */}\n              <span className=\"absolute inset-0 w-full h-full transition-all duration-300\n                               bg-gradient-to-r from-cyan-500 to-blue-500\n                               opacity-0 group-hover:opacity-100\"></span>\n\n              <span className=\"relative flex items-center\">\n                <svg\n                  className=\"w-4 h-4 mr-2 transition-transform duration-300 group-hover:-translate-x-1\"\n                  fill=\"none\"\n                  viewBox=\"0 0 24 24\"\n                  stroke=\"currentColor\"\n                >\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M15 19l-7-7 7-7\" />\n                </svg>\n                Back to Categories\n              </span>\n            </motion.button>\n          </motion.div>\n        )}\n\n        {/* Categories Grid View */}\n        {viewMode === 'categories' && (\n          <motion.div\n            ref={categoriesRef}\n            variants={containerVariants}\n            initial=\"hidden\"\n            animate={categoriesInView ? \"visible\" : \"hidden\"}\n            className=\"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-8\"\n          >\n            {menuCategories.map((category, index) => (\n              <motion.div\n                key={category.id}\n                variants={itemVariants}\n                transition={{ duration: 0.5, delay: index * 0.1 }}\n                className=\"h-full\"\n              >\n                <CategoryCard\n                  category={category}\n                  isActive={selectedCategory === category.id}\n                  onClick={() => handleCategorySelect(category.id)}\n                />\n              </motion.div>\n            ))}\n          </motion.div>\n        )}\n\n        {/* Menu Items Grid View */}\n        {viewMode === 'items' && (\n          isLoading ? (\n            <motion.div\n              className=\"text-center py-12\"\n              initial={{ opacity: 0 }}\n              animate={{ opacity: 1 }}\n              transition={{ duration: 0.3 }}\n            >\n              <div className=\"inline-block h-16 w-16 mb-6 relative\">\n                <div className=\"absolute inset-0 rounded-full border-t-2 border-b-2 border-cyan-400 animate-spin\"></div>\n                <div className=\"absolute inset-2 rounded-full border-r-2 border-l-2 border-fuchsia-500 animate-spin animation-delay-150\"></div>\n                <div className=\"absolute inset-4 rounded-full border-t-2 border-b-2 border-lime-300 animate-spin animation-delay-300\"></div>\n              </div>\n              <p className=\"font-poppins text-cyan-400\">Preparing your delicious options...</p>\n            </motion.div>\n          ) : error ? (\n            <motion.div\n              className=\"text-center py-12 backdrop-blur-sm bg-black/40 rounded-lg border border-red-900\"\n              initial={{ opacity: 0, y: 20 }}\n              animate={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.5 }}\n            >\n              <div className=\"text-red-500 text-5xl mb-4\">\n                <svg className=\"w-16 h-16 mx-auto\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z\" />\n                </svg>\n              </div>\n              <h3 className=\"font-playfair text-2xl font-bold text-white mb-3\">Something went wrong</h3>\n              <p className=\"font-poppins text-gray-400 mb-6 max-w-md mx-auto\">We couldn't load the menu at this time. Our chefs are working to fix the issue.</p>\n              <motion.button\n                className=\"bg-gradient-to-r from-red-600 to-red-800 text-white\n                          px-6 py-3 rounded-md text-sm font-medium inline-flex items-center\n                          hover:from-red-500 hover:to-red-700 transition-all\"\n                whileHover={{ scale: 1.05 }}\n                whileTap={{ scale: 0.95 }}\n                onClick={() => window.location.reload()}\n              >\n                <svg className=\"w-4 h-4 mr-2\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15\" />\n                </svg>\n                Try Again\n              </motion.button>\n            </motion.div>\n          ) : displayedDishes.length === 0 ? (\n            <motion.div\n              className=\"text-center py-16 backdrop-blur-sm bg-black/40 rounded-lg border border-gray-800\"\n              initial={{ opacity: 0, y: 20 }}\n              animate={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.5 }}\n            >\n              <div className=\"text-gray-500 text-5xl mb-4\">\n                <svg className=\"w-16 h-16 mx-auto\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10\" />\n                </svg>\n              </div>\n              <h3 className=\"font-playfair text-2xl font-bold text-white mb-3\">No items found</h3>\n              <p className=\"font-poppins text-gray-400 mb-6\">No dishes available in this category at the moment.</p>\n              <motion.button\n                className=\"bg-gradient-to-r from-cyan-600 to-cyan-800 text-white\n                           px-6 py-3 rounded-md text-sm font-medium inline-flex items-center\n                           hover:from-cyan-500 hover:to-cyan-700 transition-all\"\n                whileHover={{ scale: 1.05 }}\n                whileTap={{ scale: 0.95 }}\n                onClick={handleBackToCategories}\n              >\n                <svg className=\"w-4 h-4 mr-2\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M15 19l-7-7 7-7\" />\n                </svg>\n                Back to Categories\n              </motion.button>\n            </motion.div>\n          ) : (\n            <AnimatePresence>\n              <motion.div\n                variants={containerVariants}\n                initial=\"hidden\"\n                animate=\"visible\"\n                className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8\"\n              >\n                {displayedDishes.map((dish: Dish, index) => (\n                  <motion.div\n                    key={dish.id}\n                    variants={itemVariants}\n                    transition={{ duration: 0.5, delay: index * 0.08 }}\n                  >\n                    <MenuItemCard\n                      item={dish}\n                      onCustomize={() => handleCustomizeDish(dish)}\n                    />\n                  </motion.div>\n                ))}\n              </motion.div>\n\n              {/* Load More Button */}\n              {hasMoreDishes && (\n                <motion.div\n                  className=\"text-center mt-16\"\n                  initial={{ opacity: 0, y: 20 }}\n                  animate={{ opacity: 1, y: 0 }}\n                  transition={{ duration: 0.5, delay: 0.3 }}\n                >\n                  <motion.button\n                    className=\"group relative px-8 py-4 overflow-hidden rounded-md bg-transparent\"\n                    whileHover={{ scale: 1.03 }}\n                    whileTap={{ scale: 0.97 }}\n                    onClick={loadMoreDishes}\n                  >\n                    {/* Button Background */}\n                    <span className=\"absolute inset-0 w-full h-full bg-gradient-to-r from-fuchsia-800/70 to-fuchsia-600/70\"></span>\n\n                    {/* Button Glow Effect */}\n                    <span className=\"absolute inset-0 w-full h-full transition-all duration-300\n                                    bg-gradient-to-r from-fuchsia-600 via-purple-600 to-fuchsia-600\n                                    opacity-0 group-hover:opacity-100 group-hover:blur-md\"></span>\n\n                    {/* Button Border */}\n                    <span className=\"absolute inset-0 w-full h-full border border-fuchsia-500 rounded-md\"></span>\n\n                    {/* Button Text */}\n                    <span className=\"relative font-medium text-lg text-white tracking-wider flex items-center\">\n                      Load More\n                      <svg className=\"w-5 h-5 ml-2 transition-transform duration-300 group-hover:translate-x-1\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M19 9l-7 7-7-7\" />\n                      </svg>\n                    </span>\n                  </motion.button>\n                </motion.div>\n              )}\n            </AnimatePresence>\n          )\n        )}\n      </div>\n\n      {/* Customize Modal */}\n      <AnimatePresence>\n        {selectedDish && isModalOpen && (\n          <CustomizeModal\n            dish={selectedDish}\n            isOpen={isModalOpen}\n            onClose={() => setIsModalOpen(false)}\n          />\n        )}\n      </AnimatePresence>\n    </section>\n  );\n};\n\nexport default Menu;\n", "modifiedCode": "import { useState, useEffect } from \"react\";\nimport { useQuery } from \"@tanstack/react-query\";\nimport { getDishes, getCategories } from \"@/api/api\";\nimport { Dish } from \"@shared/schema\";\nimport Button from \"@/components/Button\";\nimport CategoryCard from \"@/components/CategoryCard\";\nimport MenuItemCard from \"@/components/MenuItemCard\";\nimport CustomizeModal from \"@/components/CustomizeModal\";\nimport { motion, AnimatePresence } from \"framer-motion\";\nimport { useInView } from \"react-intersection-observer\";\nimport { Swiper, SwiperSlide } from \"swiper/react\";\nimport { Autoplay, EffectCards } from \"swiper/modules\";\n\n// Import Swiper styles\nimport \"swiper/css\";\nimport \"swiper/css/effect-cards\";\n\n// Define enhanced category interface with images\ninterface MenuCategory {\n  id: string;\n  name: string;\n  image: string;\n}\n\n// API category interface\ninterface ApiCategory {\n  id: number;\n  name: string;\n  imageUrl: string;\n}\n\nconst Menu = () => {\n  // Animation refs\n  const [headerRef, headerInView] = useInView({\n    triggerOnce: true,\n    threshold: 0.1,\n  });\n\n  const [featuredRef, featuredInView] = useInView({\n    triggerOnce: false,\n    threshold: 0.2,\n  });\n\n  const [categoriesRef, categoriesInView] = useInView({\n    triggerOnce: true,\n    threshold: 0.1,\n  });\n\n  // Animation variants\n  const containerVariants = {\n    hidden: { opacity: 0 },\n    visible: {\n      opacity: 1,\n      transition: {\n        staggerChildren: 0.1,\n      }\n    }\n  };\n\n  const itemVariants = {\n    hidden: { y: 20, opacity: 0 },\n    visible: {\n      y: 0,\n      opacity: 1,\n      transition: { type: \"spring\", stiffness: 100 }\n    }\n  };\n\n  // State management\n  const [viewMode, setViewMode] = useState<'categories' | 'items'>('categories');\n  const [selectedCategory, setSelectedCategory] = useState<string>('');\n  const [selectedDish, setSelectedDish] = useState<Dish | null>(null);\n  const [isModalOpen, setIsModalOpen] = useState(false);\n  const [visibleDishes, setVisibleDishes] = useState<number>(6);\n\n  // Featured dishes for the slider\n  const featuredDishes = [\n    {\n      id: 'f1',\n      name: 'Signature BBQ Platter',\n      description: 'Our chef\\'s selection of premium meats',\n      price: 399,\n      image: 'https://images.unsplash.com/photo-1508615263227-c5d58c1e5821?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&h=400'\n    },\n    {\n      id: 'f2',\n      name: 'Smokehouse Burger Supreme',\n      description: 'Our award-winning burger with all the fixings',\n      price: 269,\n      image: 'https://images.unsplash.com/photo-1568901346375-23c9450c58cd?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&h=400'\n    },\n    {\n      id: 'f3',\n      name: 'Cedar Plank Salmon',\n      description: 'Fresh Norwegian salmon with maple glaze',\n      price: 299,\n      image: 'https://images.unsplash.com/photo-1519708227418-c8fd9a32b7a2?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&h=400'\n    }\n  ];\n\n  // Fetch dishes from API\n  const { data: dishes, isLoading: dishesLoading, error: dishesError } = useQuery({\n    queryKey: ['/api/dishes'],\n    queryFn: getDishes,\n    staleTime: 300000, // 5 minutes\n  });\n\n  // Fetch categories from API\n  const { data: apiCategories, isLoading: categoriesLoading, error: categoriesError } = useQuery({\n    queryKey: ['/api/categories'],\n    queryFn: getCategories,\n    staleTime: 300000, // 5 minutes\n  });\n\n  // Derived loading and error states\n  const isLoading = dishesLoading || categoriesLoading;\n  const error = dishesError || categoriesError;\n\n  // Process API categories into the format needed for the UI\n  const [menuCategories, setMenuCategories] = useState<MenuCategory[]>([]);\n  const [categoryMapping, setCategoryMapping] = useState<Record<string, string>>({});\n\n  // Convert API categories to menu categories format\n  useEffect(() => {\n    if (apiCategories && apiCategories.length > 0) {\n      // Convert API categories to menu categories\n      const processedCategories: MenuCategory[] = apiCategories.map((cat: any) => ({\n        id: cat.id.toString(),\n        name: cat.name,\n        // Use the imageUrl from API or a default image if not available\n        image: cat.imageUrl || `https://images.unsplash.com/photo-1555939594-58d7cb561ad1?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&h=400`\n      }));\n\n      // Create category mapping for filtering dishes\n      const mapping: Record<string, string> = {};\n      apiCategories.forEach((cat: any) => {\n        mapping[cat.id.toString()] = cat.name;\n      });\n\n      setMenuCategories(processedCategories);\n      setCategoryMapping(mapping);\n    } else {\n      // Fallback to default categories if API returns empty\n      setMenuCategories([\n        {\n          id: 'bbq',\n          name: 'Signature BBQ',\n          image: 'https://images.unsplash.com/photo-1529193591184-b1d58069ecdd?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&h=400'\n        },\n        {\n          id: 'starters',\n          name: 'Starters',\n          image: 'https://images.unsplash.com/photo-1527477396000-e27163b481c2?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&h=400'\n        },\n        {\n          id: 'main',\n          name: 'Main Course',\n          image: 'https://images.unsplash.com/photo-1519708227418-c8fd9a32b7a2?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&h=400'\n        },\n        {\n          id: 'desserts',\n          name: 'Desserts',\n          image: 'https://images.unsplash.com/photo-1624353365286-3f8d62daad51?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&h=400'\n        }\n      ]);\n\n      setCategoryMapping({\n        'bbq': 'Signature BBQ',\n        'starters': 'Starters',\n        'main': 'Main Course',\n        'desserts': 'Desserts'\n      });\n    }\n  }, [apiCategories]);\n\n  // Handle category selection\n  const handleCategorySelect = (categoryId: string) => {\n    setSelectedCategory(categoryId);\n    setViewMode('items');\n    // Reset visible dishes when category changes\n    setVisibleDishes(6);\n    // Scroll to top of menu section\n    document.getElementById('menu')?.scrollIntoView({ behavior: 'smooth' });\n  };\n\n  // Handle back to categories\n  const handleBackToCategories = () => {\n    setViewMode('categories');\n  };\n\n  // Handle dish customization\n  const handleCustomizeDish = (dish: Dish) => {\n    setSelectedDish(dish);\n    setIsModalOpen(true);\n  };\n\n  // Handle load more dishes\n  const loadMoreDishes = () => {\n    setVisibleDishes(prev => prev + 3);\n  };\n\n  // Filter dishes based on selected category\n  const filteredDishes = dishes ?\n    dishes.filter((dish: Dish) => {\n      if (!selectedCategory) return true;\n\n      // Handle different API formats\n      const categoryId = (dish as any).categoryId || (dish as any).category_id;\n      return categoryId?.toString() === selectedCategory;\n    }) : [];\n\n  // Get displayed dishes based on visibility limit\n  const displayedDishes = filteredDishes.slice(0, visibleDishes);\n  const hasMoreDishes = filteredDishes.length > visibleDishes;\n\n  // Get displayed category name\n  const getCategoryDisplayName = () => {\n    const category = menuCategories.find(cat => cat.id === selectedCategory);\n    return category ? category.name : 'All Items';\n  };\n\n  return (\n    <section id=\"menu\" className=\"min-h-screen py-32 bg-black relative overflow-hidden\">\n      {/* Animated Gradient Background */}\n      <div\n        className=\"absolute inset-0 z-0 bg-black\"\n        style={{\n          background: \"radial-gradient(circle at 20% 80%, rgba(57, 255, 20, 0.03), transparent 33%), \" +\n                    \"radial-gradient(circle at 80% 20%, rgba(0, 255, 255, 0.03), transparent 33%), \" +\n                    \"radial-gradient(circle at 50% 50%, rgba(255, 0, 255, 0.03), rgba(0, 0, 0, 1) 100%)\",\n        }}\n      ></div>\n\n      {/* Neon Grid Overlay */}\n      <div className=\"absolute inset-0 z-0 opacity-[0.03]\"\n           style={{\n             backgroundImage: \"linear-gradient(to right, #FF00FF 1px, transparent 1px), linear-gradient(to bottom, #FF00FF 1px, transparent 1px)\",\n             backgroundSize: \"40px 40px\"\n           }}>\n      </div>\n\n      <div className=\"container mx-auto px-4 relative z-10\">\n        {/* Header Section */}\n        <motion.div\n          ref={headerRef}\n          initial={{ opacity: 0, y: -20 }}\n          animate={headerInView ? { opacity: 1, y: 0 } : {}}\n          transition={{ duration: 0.8 }}\n          className=\"text-center mb-16\"\n        >\n          <motion.h2\n            className=\"font-playfair text-4xl md:text-5xl lg:text-6xl font-bold mb-6 relative inline-block\"\n            initial={{ opacity: 0 }}\n            animate={headerInView ? { opacity: 1 } : {}}\n            transition={{ duration: 0.5, delay: 0.3 }}\n          >\n            {viewMode === 'categories' ? (\n              <>\n                <span className=\"text-white\">Our </span>\n                <motion.span\n                  className=\"text-transparent bg-clip-text bg-gradient-to-r from-cyan-400 via-fuchsia-500 to-lime-300 relative z-10\"\n                  animate={{\n                    textShadow: [\n                      \"0 0 5px rgba(0, 255, 255, 0.7), 0 0 10px rgba(0, 255, 255, 0.5), 0 0 15px rgba(0, 255, 255, 0.3)\",\n                      \"0 0 5px rgba(255, 0, 255, 0.7), 0 0 10px rgba(255, 0, 255, 0.5), 0 0 15px rgba(255, 0, 255, 0.3)\",\n                      \"0 0 5px rgba(57, 255, 20, 0.7), 0 0 10px rgba(57, 255, 20, 0.5), 0 0 15px rgba(57, 255, 20, 0.3)\",\n                      \"0 0 5px rgba(0, 255, 255, 0.7), 0 0 10px rgba(0, 255, 255, 0.5), 0 0 15px rgba(0, 255, 255, 0.3)\",\n                    ]\n                  }}\n                  transition={{ duration: 5, repeat: Infinity }}\n                >\n                  Menu\n                </motion.span>\n              </>\n            ) : (\n              <>\n                <span className=\"text-white\">{getCategoryDisplayName()} </span>\n                <motion.span\n                  className=\"text-transparent bg-clip-text bg-gradient-to-r from-fuchsia-500 to-cyan-400 relative z-10\"\n                  animate={{\n                    textShadow: [\n                      \"0 0 5px rgba(255, 0, 255, 0.7), 0 0 10px rgba(255, 0, 255, 0.5), 0 0 15px rgba(255, 0, 255, 0.3)\",\n                      \"0 0 5px rgba(0, 255, 255, 0.7), 0 0 10px rgba(0, 255, 255, 0.5), 0 0 15px rgba(0, 255, 255, 0.3)\",\n                      \"0 0 5px rgba(255, 0, 255, 0.7), 0 0 10px rgba(255, 0, 255, 0.5), 0 0 15px rgba(255, 0, 255, 0.3)\",\n                    ]\n                  }}\n                  transition={{ duration: 3, repeat: Infinity }}\n                >\n                  Selection\n                </motion.span>\n              </>\n            )}\n          </motion.h2>\n\n          <motion.p\n            className=\"font-poppins text-lg md:text-xl text-gray-300 max-w-2xl mx-auto\"\n            initial={{ opacity: 0, y: 20 }}\n            animate={headerInView ? { opacity: 1, y: 0 } : {}}\n            transition={{ duration: 0.6, delay: 0.5 }}\n          >\n            {viewMode === 'categories'\n              ? 'Explore our handcrafted selection of premium barbecue dishes'\n              : `Discover our ${getCategoryDisplayName()} offerings crafted with premium ingredients`\n            }\n          </motion.p>\n        </motion.div>\n\n        {/* Featured Menu Slider - Only show on category view */}\n        {viewMode === 'categories' && (\n          <motion.div\n            ref={featuredRef}\n            initial={{ opacity: 0 }}\n            animate={featuredInView ? { opacity: 1 } : {}}\n            transition={{ duration: 0.8 }}\n            className=\"mb-20\"\n          >\n            <motion.h3\n              className=\"text-2xl md:text-3xl font-playfair font-bold mb-8 text-center\"\n              initial={{ opacity: 0, y: 20 }}\n              animate={featuredInView ? { opacity: 1, y: 0 } : {}}\n              transition={{ duration: 0.6 }}\n            >\n              <span className=\"text-white\">Featured </span>\n              <span className=\"text-transparent bg-clip-text bg-gradient-to-r from-lime-300 to-cyan-400\">\n                Selections\n              </span>\n            </motion.h3>\n\n            <div className=\"px-4 sm:px-8 md:px-16 py-8\">\n              <Swiper\n                effect=\"cards\"\n                grabCursor={true}\n                modules={[EffectCards, Autoplay]}\n                className=\"max-w-xs mx-auto\"\n                autoplay={{\n                  delay: 3000,\n                  disableOnInteraction: false,\n                }}\n              >\n                {featuredDishes.map((dish) => (\n                  <SwiperSlide key={dish.id} className=\"bg-transparent\">\n                    <div className=\"bg-black/20 backdrop-blur-sm rounded-xl overflow-hidden\n                                    border border-gray-800 shadow-[0_0_15px_rgba(0,255,255,0.15)]\n                                    hover:shadow-[0_0_30px_rgba(0,255,255,0.3)] transition-all duration-500\">\n                      <div className=\"relative h-72 overflow-hidden\">\n                        <img\n                          src={dish.image}\n                          alt={dish.name}\n                          className=\"w-full h-full object-cover\"\n                        />\n                        <div className=\"absolute inset-0 bg-gradient-to-t from-black via-black/50 to-transparent\"></div>\n                        <div className=\"absolute bottom-0 left-0 right-0 p-6\">\n                          <h3 className=\"text-xl font-playfair font-bold text-white mb-2\">{dish.name}</h3>\n                          <p className=\"text-gray-300 text-sm mb-3\">{dish.description}</p>\n\n                          <div className=\"flex justify-between items-center\">\n                            <span className=\"text-cyan-400 font-semibold\">{dish.price} NOK</span>\n                            <motion.button\n                              className=\"bg-gradient-to-r from-fuchsia-600 to-cyan-600 text-white\n                                       px-4 py-2 rounded-md text-sm font-medium\n                                       hover:from-fuchsia-500 hover:to-cyan-500 transition-all\"\n                              whileHover={{ scale: 1.05 }}\n                              whileTap={{ scale: 0.95 }}\n                              onClick={() => handleCategorySelect('bbq')} // Redirect to appropriate category\n                            >\n                              Order Now\n                            </motion.button>\n                          </div>\n                        </div>\n                      </div>\n                    </div>\n                  </SwiperSlide>\n                ))}\n              </Swiper>\n            </div>\n          </motion.div>\n        )}\n\n        {/* Back Button (when viewing items) */}\n        {viewMode === 'items' && (\n          <motion.div\n            className=\"mb-8\"\n            initial={{ opacity: 0, x: -20 }}\n            animate={{ opacity: 1, x: 0 }}\n            transition={{ duration: 0.5 }}\n          >\n            <motion.button\n              className=\"group flex items-center px-5 py-2.5 relative overflow-hidden\n                         text-cyan-400 border border-cyan-400 rounded-md\n                         hover:text-white transition-colors duration-300\"\n              onClick={handleBackToCategories}\n              whileHover={{ scale: 1.03 }}\n              whileTap={{ scale: 0.97 }}\n            >\n              {/* Button glow effect */}\n              <span className=\"absolute inset-0 w-full h-full transition-all duration-300\n                               bg-gradient-to-r from-cyan-500 to-blue-500\n                               opacity-0 group-hover:opacity-100\"></span>\n\n              <span className=\"relative flex items-center\">\n                <svg\n                  className=\"w-4 h-4 mr-2 transition-transform duration-300 group-hover:-translate-x-1\"\n                  fill=\"none\"\n                  viewBox=\"0 0 24 24\"\n                  stroke=\"currentColor\"\n                >\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M15 19l-7-7 7-7\" />\n                </svg>\n                Back to Categories\n              </span>\n            </motion.button>\n          </motion.div>\n        )}\n\n        {/* Categories Grid View */}\n        {viewMode === 'categories' && (\n          <motion.div\n            ref={categoriesRef}\n            variants={containerVariants}\n            initial=\"hidden\"\n            animate={categoriesInView ? \"visible\" : \"hidden\"}\n            className=\"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-8\"\n          >\n            {menuCategories.map((category, index) => (\n              <motion.div\n                key={category.id}\n                variants={itemVariants}\n                transition={{ duration: 0.5, delay: index * 0.1 }}\n                className=\"h-full\"\n              >\n                <CategoryCard\n                  category={category}\n                  isActive={selectedCategory === category.id}\n                  onClick={() => handleCategorySelect(category.id)}\n                />\n              </motion.div>\n            ))}\n          </motion.div>\n        )}\n\n        {/* Menu Items Grid View */}\n        {viewMode === 'items' && (\n          isLoading ? (\n            <motion.div\n              className=\"text-center py-12\"\n              initial={{ opacity: 0 }}\n              animate={{ opacity: 1 }}\n              transition={{ duration: 0.3 }}\n            >\n              <div className=\"inline-block h-16 w-16 mb-6 relative\">\n                <div className=\"absolute inset-0 rounded-full border-t-2 border-b-2 border-cyan-400 animate-spin\"></div>\n                <div className=\"absolute inset-2 rounded-full border-r-2 border-l-2 border-fuchsia-500 animate-spin animation-delay-150\"></div>\n                <div className=\"absolute inset-4 rounded-full border-t-2 border-b-2 border-lime-300 animate-spin animation-delay-300\"></div>\n              </div>\n              <p className=\"font-poppins text-cyan-400\">Preparing your delicious options...</p>\n            </motion.div>\n          ) : error ? (\n            <motion.div\n              className=\"text-center py-12 backdrop-blur-sm bg-black/40 rounded-lg border border-red-900\"\n              initial={{ opacity: 0, y: 20 }}\n              animate={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.5 }}\n            >\n              <div className=\"text-red-500 text-5xl mb-4\">\n                <svg className=\"w-16 h-16 mx-auto\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z\" />\n                </svg>\n              </div>\n              <h3 className=\"font-playfair text-2xl font-bold text-white mb-3\">Something went wrong</h3>\n              <p className=\"font-poppins text-gray-400 mb-6 max-w-md mx-auto\">We couldn't load the menu at this time. Our chefs are working to fix the issue.</p>\n              <motion.button\n                className=\"bg-gradient-to-r from-red-600 to-red-800 text-white\n                          px-6 py-3 rounded-md text-sm font-medium inline-flex items-center\n                          hover:from-red-500 hover:to-red-700 transition-all\"\n                whileHover={{ scale: 1.05 }}\n                whileTap={{ scale: 0.95 }}\n                onClick={() => window.location.reload()}\n              >\n                <svg className=\"w-4 h-4 mr-2\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15\" />\n                </svg>\n                Try Again\n              </motion.button>\n            </motion.div>\n          ) : displayedDishes.length === 0 ? (\n            <motion.div\n              className=\"text-center py-16 backdrop-blur-sm bg-black/40 rounded-lg border border-gray-800\"\n              initial={{ opacity: 0, y: 20 }}\n              animate={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.5 }}\n            >\n              <div className=\"text-gray-500 text-5xl mb-4\">\n                <svg className=\"w-16 h-16 mx-auto\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10\" />\n                </svg>\n              </div>\n              <h3 className=\"font-playfair text-2xl font-bold text-white mb-3\">No items found</h3>\n              <p className=\"font-poppins text-gray-400 mb-6\">No dishes available in this category at the moment.</p>\n              <motion.button\n                className=\"bg-gradient-to-r from-cyan-600 to-cyan-800 text-white\n                           px-6 py-3 rounded-md text-sm font-medium inline-flex items-center\n                           hover:from-cyan-500 hover:to-cyan-700 transition-all\"\n                whileHover={{ scale: 1.05 }}\n                whileTap={{ scale: 0.95 }}\n                onClick={handleBackToCategories}\n              >\n                <svg className=\"w-4 h-4 mr-2\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M15 19l-7-7 7-7\" />\n                </svg>\n                Back to Categories\n              </motion.button>\n            </motion.div>\n          ) : (\n            <AnimatePresence>\n              <motion.div\n                variants={containerVariants}\n                initial=\"hidden\"\n                animate=\"visible\"\n                className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8\"\n              >\n                {displayedDishes.map((dish: Dish, index) => (\n                  <motion.div\n                    key={dish.id}\n                    variants={itemVariants}\n                    transition={{ duration: 0.5, delay: index * 0.08 }}\n                  >\n                    <MenuItemCard\n                      item={dish}\n                      onCustomize={() => handleCustomizeDish(dish)}\n                    />\n                  </motion.div>\n                ))}\n              </motion.div>\n\n              {/* Load More Button */}\n              {hasMoreDishes && (\n                <motion.div\n                  className=\"text-center mt-16\"\n                  initial={{ opacity: 0, y: 20 }}\n                  animate={{ opacity: 1, y: 0 }}\n                  transition={{ duration: 0.5, delay: 0.3 }}\n                >\n                  <motion.button\n                    className=\"group relative px-8 py-4 overflow-hidden rounded-md bg-transparent\"\n                    whileHover={{ scale: 1.03 }}\n                    whileTap={{ scale: 0.97 }}\n                    onClick={loadMoreDishes}\n                  >\n                    {/* Button Background */}\n                    <span className=\"absolute inset-0 w-full h-full bg-gradient-to-r from-fuchsia-800/70 to-fuchsia-600/70\"></span>\n\n                    {/* Button Glow Effect */}\n                    <span className=\"absolute inset-0 w-full h-full transition-all duration-300\n                                    bg-gradient-to-r from-fuchsia-600 via-purple-600 to-fuchsia-600\n                                    opacity-0 group-hover:opacity-100 group-hover:blur-md\"></span>\n\n                    {/* Button Border */}\n                    <span className=\"absolute inset-0 w-full h-full border border-fuchsia-500 rounded-md\"></span>\n\n                    {/* Button Text */}\n                    <span className=\"relative font-medium text-lg text-white tracking-wider flex items-center\">\n                      Load More\n                      <svg className=\"w-5 h-5 ml-2 transition-transform duration-300 group-hover:translate-x-1\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M19 9l-7 7-7-7\" />\n                      </svg>\n                    </span>\n                  </motion.button>\n                </motion.div>\n              )}\n            </AnimatePresence>\n          )\n        )}\n      </div>\n\n      {/* Customize Modal */}\n      <AnimatePresence>\n        {selectedDish && isModalOpen && (\n          <CustomizeModal\n            dish={selectedDish}\n            isOpen={isModalOpen}\n            onClose={() => setIsModalOpen(false)}\n          />\n        )}\n      </AnimatePresence>\n    </section>\n  );\n};\n\nexport default Menu;\n"}