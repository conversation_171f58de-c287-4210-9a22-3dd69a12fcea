{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "attached_assets/Pasted-Build-the-Manager-Kitchen-Page-for-Order-Status-Control-React-TailwindCSS-Framer-Motion-Obj-1747874192450.txt"}, "originalCode": "Build the Manager Kitchen Page for Order Status Control (React + TailwindCSS + Framer Motion)\n🎯 Objective:\nCreate a powerful \"Manager\" page for the kitchen to manage all incoming orders. This page should allow updating the order status dynamically, with specific rules depending on whether the order is Takeaway or Delivery.\n\n🧩 Main Features & Logic Rules\n✅ General Layout\nDark, dashboard-style interface optimized for kitchen use\n\nResponsive grid/table of active orders\n\nEach order card/row includes:\n\nOrder ID\n\nOrder type (Takeaway or Delivery)\n\nItems summary\n\nTimestamp (order time)\n\nCurrent status badge (with color and glow)\n\nNext action button depending on type and status\n\nIf Delivery → includes \"Send to Driver\" logic at final kitchen stage\n\n🔁 Status Flow Logic\n🥡 If order type is TAKEAWAY\nConfirmed\n\n→ Preparing\n\n→ Complete ✅ (final status)\n\n🚚 If order type is DELIVERY\nConfirmed\n\n→ Preparing\n\n→ Ready for Delivery ✅ (final status in manager panel)\n\nManager clicks “Send to Driver” → triggers handoff to Driver Page\n\nOrder becomes visible in Driver system with status = With Driver\n\n📋 Order Card UI\nEach order should display:\n\nOrder ID, Time\n\nType badge: \"Takeaway\" or \"Delivery\" (colored neon)\n\nCustomer name & phone (optional)\n\nOrdered items list (names + quantities)\n\nCurrent status indicator\n\nAction button:\n\nLabel and function depend on current status & type\n\nBut<PERSON> uses glowing neon style and scales on hover\n\n⚙️ Logic Handling\nOrders should be pulled via:\n\nGET /api/orders?status=in_progress\n\nOrder status update:\n\nPUT /api/orders/:id/status\n\njson\nCopy\nEdit\n{ \"newStatus\": \"preparing\" }\nUpon final delivery-ready status (ready for delivery), a separate API call should be triggered:\n\nPOST /api/dispatch/to-driver\n\njson\nCopy\nEdit\n{ \"orderId\": 123 }\n🖼️ Design & Animation (UX)\nUse Framer Motion to animate:\n\nStatus change (e.g., fade out old badge, fade in new one)\n\nButton press (scale + glow)\n\nCards entering the screen (staggered motion)\n\nTailwind Styles:\n\nNeon glowing status labels\n\nBackground: #000000 with glassmorphism card containers\n\nColor-coded statuses:\n\nConfirmed: Neutral glow\n\nPreparing: Orange glow\n\nComplete: Green glow\n\nReady for Delivery: Lime\n\nWith Driver: Blue\n\n📁 Component Structure Suggestion\nbash\nCopy\nEdit\n/pages\n └── ManagerPage.jsx\n\n/components\n └── OrderCard.jsx\n └── StatusBadge.jsx\n └── ActionButton.jsx\n✅ Output\nA fully working, animated Manager control page that:\n\nDistinguishes clearly between Takeaway and Delivery logic\n\nAllows kitchen staff to update order statuses progressively\n\nTriggers dispatch logic only for delivery orders\n\nPrepares delivery orders for driver handoff\n\n🧩 Optional Enhancements\nAuto-refresh orders every 30 seconds\n\nSound alert when new order is confirmed\n\nFilter by status or type (tabs or dropdown)\n\nGroup orders into columns by status (e.g., Kanban-style)\n\nهل ترغب الآن بـ Prompt مكمل لبناء صفحة Driver Page التي تستقبل الطلبات الجاهزة من المطبخ وتتابعها حتى التسليم؟\n\n\n\n\n\n\n\nYou said:\nyes\n\n\nChatGPT said:\nرائع! إليك الآن Prompt مفصل لبناء صفحة \"Driver Page\" الخاصة بالسائق باستخدام React + TailwindCSS + Framer Motion، حيث يستقبل السائق الطلبات \"الجاهزة للتوصيل\" من المطبخ، ويتحكم بحالة الطلب من لحظة استلامه حتى لحظة التسليم.\n\n🛵 Prompt – Build the Driver Order Page for Delivery Control (React + TailwindCSS + Framer Motion)\n🎯 Objective:\nBuild a dedicated Driver Page that allows delivery staff to:\n\nView only orders that are marked \"Ready for Delivery\".\n\nUpdate the order status as it progresses through the delivery cycle:\n\nWith Driver → Delivered\n\nEnsure simple, responsive, fast interaction with glowing UI and motion feedback.\n\n🧩 Main Features & Logic Flow\n1. Order Flow (Driver Side)\nOnce the kitchen marks a delivery order as \"ready for delivery\", it appears in the Driver Page.\n\nDelivery Status Transitions:\nReady for Delivery\n→ (Driver accepts) → With Driver\n\nWith Driver\n→ (Upon drop-off) → Delivered ✅\n\nOnly delivery orders with status = ready for delivery or with driver should be visible to drivers.\n\n2. Driver Page UI Elements\nEach order card should show:\n\nOrder ID\n\nCustomer name & phone\n\nDelivery address\n\nEstimated time\n\nOrdered items (names & quantity)\n\nCurrent status (badge)\n\nAction button:\n\n“Accept Delivery” → changes to With Driver\n\n“Mark as Delivered” → final status\n\n🖼️ Design Style\nBlack background with neon glowing elements\n\nUse Tailwind + Framer Motion for:\n\nSlide-in card animation\n\nButton hover scale/glow\n\nStatus badge color animation\n\nColor-coded status tags:\n\nReady for Delivery: Neon Pink #FF00FF\n\nWith Driver: Electric Blue #00FFFF\n\nDelivered: Lime Green #39FF14\n\n⚙️ API Integration\nGET /api/driver/orders\n\nReturn only delivery orders with status = 'ready for delivery' OR 'with driver'\n\nPUT /api/orders/:id/status\n\nPayload:\n\njson\nCopy\nEdit\n{ \"newStatus\": \"with driver\" }\nPUT /api/orders/:id/status\n\nPayload:\n\njson\nCopy\nEdit\n{ \"newStatus\": \"delivered\" }\n📁 Recommended File Structure\nbash\nCopy\nEdit\n/pages\n └── DriverPage.jsx\n\n/components\n └── DriverOrderCard.jsx\n └── StatusLabel.jsx\n └── ActionButton.jsx\n🔐 Optional Features\nRequire driver login or PIN (basic auth)\n\nSound or animation when new order becomes available\n\nShow \"number of active deliveries\"\n\nButton to call or message customer (if mobile)\n\n✅ Output\nA fully functional Driver Delivery Dashboard that:\n\nOnly shows relevant delivery orders\n\nTransitions status from ready → with driver → delivered\n\nIs simple, fast, and visually consistent with the rest of the platform\n\nKeeps the kitchen and customer updated via backend status updates\n\n"}