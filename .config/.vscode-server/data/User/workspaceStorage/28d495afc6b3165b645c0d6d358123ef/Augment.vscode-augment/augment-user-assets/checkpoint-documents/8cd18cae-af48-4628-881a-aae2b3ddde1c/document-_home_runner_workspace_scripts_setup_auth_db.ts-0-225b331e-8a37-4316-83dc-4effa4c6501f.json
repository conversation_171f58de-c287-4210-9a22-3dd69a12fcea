{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "scripts/setup_auth_db.ts"}, "originalCode": "import { pool } from '../server/db';\nimport * as crypto from 'crypto';\n\n// Function to hash password\nasync function hashPassword(password: string): Promise<string> {\n  const salt = crypto.randomBytes(16).toString(\"hex\");\n  return new Promise((resolve, reject) => {\n    crypto.scrypt(password, salt, 64, (err, derivedKey) => {\n      if (err) reject(err);\n      resolve(`${derivedKey.toString(\"hex\")}.${salt}`);\n    });\n  });\n}\n\nasync function setupAuthDatabase() {\n  const client = await pool.connect();\n  \n  try {\n    console.log('Starting database authentication setup...');\n    \n    // Begin transaction\n    await client.query('BEGIN');\n    \n    // 1. Check if users table exists\n    const tableCheck = await client.query(`\n      SELECT EXISTS (\n        SELECT 1 FROM information_schema.tables \n        WHERE table_schema = 'public' \n        AND table_name = 'users'\n      );\n    `);\n    \n    // 2. Create users table if it doesn't exist\n    if (!tableCheck.rows[0].exists) {\n      console.log('Creating users table...');\n      await client.query(`\n        CREATE TABLE \"users\" (\n          \"id\" SERIAL PRIMARY KEY,\n          \"username\" TEXT NOT NULL UNIQUE,\n          \"email\" TEXT NOT NULL UNIQUE,\n          \"password\" TEXT NOT NULL,\n          \"first_name\" TEXT,\n          \"last_name\" TEXT,\n          \"role\" TEXT NOT NULL DEFAULT 'customer',\n          \"is_active\" BOOLEAN NOT NULL DEFAULT TRUE,\n          \"created_at\" TIMESTAMP DEFAULT CURRENT_TIMESTAMP,\n          \"updated_at\" TIMESTAMP DEFAULT CURRENT_TIMESTAMP\n        );\n      `);\n      console.log('Users table created successfully!');\n    } else {\n      console.log('Users table already exists.');\n    }\n    \n    // 3. Create session table for authentication\n    const sessionTableCheck = await client.query(`\n      SELECT EXISTS (\n        SELECT 1 FROM information_schema.tables \n        WHERE table_schema = 'public' \n        AND table_name = 'session'\n      );\n    `);\n    \n    if (!sessionTableCheck.rows[0].exists) {\n      console.log('Creating session table...');\n      await client.query(`\n        CREATE TABLE \"session\" (\n          \"sid\" VARCHAR NOT NULL PRIMARY KEY,\n          \"sess\" JSON NOT NULL,\n          \"expire\" TIMESTAMP(6) NOT NULL\n        );\n        CREATE INDEX \"IDX_session_expire\" ON \"session\" (\"expire\");\n      `);\n      console.log('Session table created successfully!');\n    } else {\n      console.log('Session table already exists.');\n    }\n    \n    // 4. Check if default users exist\n    const usersExist = await client.query(`\n      SELECT COUNT(*) FROM users \n      WHERE username IN ('admin', 'manager', 'driver');\n    `);\n    \n    // 5. Add default users if they don't exist\n    if (parseInt(usersExist.rows[0].count) < 3) {\n      console.log('Creating default users...');\n      \n      // Hash passwords\n      const adminPassword = await hashPassword('admin123');\n      const managerPassword = await hashPassword('manager123');\n      const driverPassword = await hashPassword('driver123');\n      \n      // Insert admin user if doesn't exist\n      const adminExists = await client.query(`\n        SELECT EXISTS (SELECT 1 FROM users WHERE username = 'admin');\n      `);\n      \n      if (!adminExists.rows[0].exists) {\n        await client.query(`\n          INSERT INTO users (username, email, password, first_name, last_name, role)\n          VALUES ('admin', '<EMAIL>', $1, 'Admin', 'User', 'admin');\n        `, [adminPassword]);\n        console.log('Admin user created!');\n      }\n      \n      // Insert manager user if doesn't exist\n      const managerExists = await client.query(`\n        SELECT EXISTS (SELECT 1 FROM users WHERE username = 'manager');\n      `);\n      \n      if (!managerExists.rows[0].exists) {\n        await client.query(`\n          INSERT INTO users (username, email, password, first_name, last_name, role)\n          VALUES ('manager', '<EMAIL>', $1, 'Kitchen', 'Manager', 'manager');\n        `, [managerPassword]);\n        console.log('Manager user created!');\n      }\n      \n      // Insert driver user if doesn't exist\n      const driverExists = await client.query(`\n        SELECT EXISTS (SELECT 1 FROM users WHERE username = 'driver');\n      `);\n      \n      if (!driverExists.rows[0].exists) {\n        await client.query(`\n          INSERT INTO users (username, email, password, first_name, last_name, role)\n          VALUES ('driver', '<EMAIL>', $1, 'Delivery', 'Driver', 'driver');\n        `, [driverPassword]);\n        console.log('Driver user created!');\n      }\n    } else {\n      console.log('Default users already exist.');\n    }\n    \n    // 6. Commit transaction\n    await client.query('COMMIT');\n    console.log('Database authentication setup completed successfully!');\n    \n  } catch (error) {\n    // Rollback in case of error\n    await client.query('ROLLBACK');\n    console.error('Error setting up authentication database:', error);\n    throw error;\n  } finally {\n    // Release client\n    client.release();\n  }\n}\n\n// Run the setup function\nsetupAuthDatabase()\n  .then(() => {\n    console.log('Authentication database setup completed.');\n    process.exit(0);\n  })\n  .catch(error => {\n    console.error('Failed to set up authentication database:', error);\n    process.exit(1);\n  });", "modifiedCode": "import { pool } from '../server/db';\nimport * as crypto from 'crypto';\n\n// Function to hash password\nasync function hashPassword(password: string): Promise<string> {\n  const salt = crypto.randomBytes(16).toString(\"hex\");\n  return new Promise((resolve, reject) => {\n    crypto.scrypt(password, salt, 64, (err, derivedKey) => {\n      if (err) reject(err);\n      resolve(`${derivedKey.toString(\"hex\")}.${salt}`);\n    });\n  });\n}\n\nasync function setupAuthDatabase() {\n  const client = await pool.connect();\n  \n  try {\n    console.log('Starting database authentication setup...');\n    \n    // Begin transaction\n    await client.query('BEGIN');\n    \n    // 1. Check if users table exists\n    const tableCheck = await client.query(`\n      SELECT EXISTS (\n        SELECT 1 FROM information_schema.tables \n        WHERE table_schema = 'public' \n        AND table_name = 'users'\n      );\n    `);\n    \n    // 2. Create users table if it doesn't exist\n    if (!tableCheck.rows[0].exists) {\n      console.log('Creating users table...');\n      await client.query(`\n        CREATE TABLE \"users\" (\n          \"id\" SERIAL PRIMARY KEY,\n          \"username\" TEXT NOT NULL UNIQUE,\n          \"email\" TEXT NOT NULL UNIQUE,\n          \"password\" TEXT NOT NULL,\n          \"first_name\" TEXT,\n          \"last_name\" TEXT,\n          \"role\" TEXT NOT NULL DEFAULT 'customer',\n          \"is_active\" BOOLEAN NOT NULL DEFAULT TRUE,\n          \"created_at\" TIMESTAMP DEFAULT CURRENT_TIMESTAMP,\n          \"updated_at\" TIMESTAMP DEFAULT CURRENT_TIMESTAMP\n        );\n      `);\n      console.log('Users table created successfully!');\n    } else {\n      console.log('Users table already exists.');\n    }\n    \n    // 3. Create session table for authentication\n    const sessionTableCheck = await client.query(`\n      SELECT EXISTS (\n        SELECT 1 FROM information_schema.tables \n        WHERE table_schema = 'public' \n        AND table_name = 'session'\n      );\n    `);\n    \n    if (!sessionTableCheck.rows[0].exists) {\n      console.log('Creating session table...');\n      await client.query(`\n        CREATE TABLE \"session\" (\n          \"sid\" VARCHAR NOT NULL PRIMARY KEY,\n          \"sess\" JSON NOT NULL,\n          \"expire\" TIMESTAMP(6) NOT NULL\n        );\n        CREATE INDEX \"IDX_session_expire\" ON \"session\" (\"expire\");\n      `);\n      console.log('Session table created successfully!');\n    } else {\n      console.log('Session table already exists.');\n    }\n    \n    // 4. Check if default users exist\n    const usersExist = await client.query(`\n      SELECT COUNT(*) FROM users \n      WHERE username IN ('admin', 'manager', 'driver');\n    `);\n    \n    // 5. Add default users if they don't exist\n    if (parseInt(usersExist.rows[0].count) < 3) {\n      console.log('Creating default users...');\n      \n      // Hash passwords\n      const adminPassword = await hashPassword('admin123');\n      const managerPassword = await hashPassword('manager123');\n      const driverPassword = await hashPassword('driver123');\n      \n      // Insert admin user if doesn't exist\n      const adminExists = await client.query(`\n        SELECT EXISTS (SELECT 1 FROM users WHERE username = 'admin');\n      `);\n      \n      if (!adminExists.rows[0].exists) {\n        await client.query(`\n          INSERT INTO users (username, email, password, first_name, last_name, role)\n          VALUES ('admin', '<EMAIL>', $1, 'Admin', 'User', 'admin');\n        `, [adminPassword]);\n        console.log('Admin user created!');\n      }\n      \n      // Insert manager user if doesn't exist\n      const managerExists = await client.query(`\n        SELECT EXISTS (SELECT 1 FROM users WHERE username = 'manager');\n      `);\n      \n      if (!managerExists.rows[0].exists) {\n        await client.query(`\n          INSERT INTO users (username, email, password, first_name, last_name, role)\n          VALUES ('manager', '<EMAIL>', $1, 'Kitchen', 'Manager', 'manager');\n        `, [managerPassword]);\n        console.log('Manager user created!');\n      }\n      \n      // Insert driver user if doesn't exist\n      const driverExists = await client.query(`\n        SELECT EXISTS (SELECT 1 FROM users WHERE username = 'driver');\n      `);\n      \n      if (!driverExists.rows[0].exists) {\n        await client.query(`\n          INSERT INTO users (username, email, password, first_name, last_name, role)\n          VALUES ('driver', '<EMAIL>', $1, 'Delivery', 'Driver', 'driver');\n        `, [driverPassword]);\n        console.log('Driver user created!');\n      }\n    } else {\n      console.log('Default users already exist.');\n    }\n    \n    // 6. Commit transaction\n    await client.query('COMMIT');\n    console.log('Database authentication setup completed successfully!');\n    \n  } catch (error) {\n    // Rollback in case of error\n    await client.query('ROLLBACK');\n    console.error('Error setting up authentication database:', error);\n    throw error;\n  } finally {\n    // Release client\n    client.release();\n  }\n}\n\n// Run the setup function\nsetupAuthDatabase()\n  .then(() => {\n    console.log('Authentication database setup completed.');\n    process.exit(0);\n  })\n  .catch(error => {\n    console.error('Failed to set up authentication database:', error);\n    process.exit(1);\n  });"}