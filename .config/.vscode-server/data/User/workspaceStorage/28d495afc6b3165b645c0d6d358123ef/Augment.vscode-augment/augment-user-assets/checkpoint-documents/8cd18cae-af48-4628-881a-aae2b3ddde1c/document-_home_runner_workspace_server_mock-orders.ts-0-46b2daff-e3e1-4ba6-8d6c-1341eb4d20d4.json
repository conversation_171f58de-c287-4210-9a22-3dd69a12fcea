{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "server/mock-orders.ts"}, "originalCode": "export interface Order {\n  id: number;\n  createdAt: string;\n  status: string;\n  items: {\n    id: number;\n    name: string;\n    price: number;\n    quantity: number;\n  }[];\n  customer: {\n    firstName: string;\n    lastName: string;\n    phone: string;\n    email: string;\n  };\n  orderDetails: {\n    type: 'delivery' | 'takeaway';\n    time: 'asap' | 'scheduled';\n    scheduledTime: string | null;\n  };\n  subtotal: number;\n  deliveryFee: number;\n  total: number;\n  paymentMethod: string;\n  notes: string | null;\n}\n\n// Sample orders that will be shown in the kitchen order manager\nexport const sampleOrders: Order[] = [\n  {\n    id: 1001,\n    createdAt: new Date(Date.now() - 15 * 60000).toISOString(), // 15 minutes ago\n    status: 'confirmed',\n    items: [\n      { id: 22, name: 'Smoked Beef Brisket', price: 189, quantity: 1 },\n      { id: 32, name: 'Mac & Cheese', price: 89, quantity: 1 },\n      { id: 35, name: 'Sweet Tea', price: 49, quantity: 2 }\n    ],\n    customer: {\n      firstName: 'John',\n      lastName: 'Smith',\n      phone: '************',\n      email: '<EMAIL>'\n    },\n    orderDetails: {\n      type: 'delivery',\n      time: 'asap',\n      scheduledTime: null\n    },\n    subtotal: 376,\n    deliveryFee: 49,\n    total: 425,\n    paymentMethod: 'card',\n    notes: 'Ring doorbell, please'\n  },\n  {\n    id: 1002,\n    createdAt: new Date(Date.now() - 10 * 60000).toISOString(), // 10 minutes ago\n    status: 'processing',\n    items: [\n      { id: 23, name: 'Baby Back Ribs', price: 179, quantity: 1 },\n      { id: 30, name: 'Loaded BBQ Fries', price: 119, quantity: 1 },\n      { id: 34, name: 'House-Made Lemonade', price: 59, quantity: 1 }\n    ],\n    customer: {\n      firstName: 'Emma',\n      lastName: 'Johnson',\n      phone: '************',\n      email: '<EMAIL>'\n    },\n    orderDetails: {\n      type: 'takeaway',\n      time: 'scheduled',\n      scheduledTime: new Date(Date.now() + 120 * 60000).toISOString() // 2 hours from now\n    },\n    subtotal: 357,\n    deliveryFee: 0,\n    total: 357,\n    paymentMethod: 'card',\n    notes: null\n  },\n  {\n    id: 1003,\n    createdAt: new Date(Date.now() - 25 * 60000).toISOString(), // 25 minutes ago\n    status: 'preparing',\n    items: [\n      { id: 26, name: 'Smokehouse Burger', price: 169, quantity: 2 },\n      { id: 31, name: 'Smoked Wings', price: 139, quantity: 1 },\n      { id: 34, name: 'Craft Beer Selection', price: 89, quantity: 2 }\n    ],\n    customer: {\n      firstName: 'Michael',\n      lastName: 'Brown',\n      phone: '************',\n      email: '<EMAIL>'\n    },\n    orderDetails: {\n      type: 'delivery',\n      time: 'asap',\n      scheduledTime: null\n    },\n    subtotal: 655,\n    deliveryFee: 49,\n    total: 704,\n    paymentMethod: 'cash',\n    notes: 'No onions on burgers please'\n  },\n  {\n    id: 1004,\n    createdAt: new Date(Date.now() - 30 * 60000).toISOString(), // 30 minutes ago\n    status: 'ready_for_delivery',\n    items: [\n      { id: 22, name: 'Smoked Beef Brisket', price: 189, quantity: 1 },\n      { id: 30, name: 'Loaded BBQ Fries', price: 119, quantity: 1 },\n      { id: 39, name: 'Smoked Apple Cobbler', price: 99, quantity: 1 }\n    ],\n    customer: {\n      firstName: 'Sarah',\n      lastName: 'Davis',\n      phone: '************',\n      email: '<EMAIL>'\n    },\n    orderDetails: {\n      type: 'delivery',\n      time: 'asap',\n      scheduledTime: null\n    },\n    subtotal: 407,\n    deliveryFee: 49,\n    total: 456,\n    paymentMethod: 'card',\n    notes: 'Leave at door'\n  },\n  {\n    id: 1005,\n    createdAt: new Date(Date.now() - 20 * 60000).toISOString(), // 20 minutes ago\n    status: 'preparing',\n    items: [\n      { id: 28, name: 'Pulled Pork Burger', price: 159, quantity: 1 },\n      { id: 31, name: 'Smoked Wings', price: 139, quantity: 1 },\n      { id: 32, name: 'Mac & Cheese', price: 89, quantity: 1 }\n    ],\n    customer: {\n      firstName: 'David',\n      lastName: 'Wilson',\n      phone: '************',\n      email: '<EMAIL>'\n    },\n    orderDetails: {\n      type: 'takeaway',\n      time: 'asap',\n      scheduledTime: null\n    },\n    subtotal: 387,\n    deliveryFee: 0,\n    total: 387,\n    paymentMethod: 'card',\n    notes: null\n  }\n];", "modifiedCode": "export interface Order {\n  id: number;\n  createdAt: string;\n  status: string;\n  items: {\n    id: number;\n    name: string;\n    price: number;\n    quantity: number;\n  }[];\n  customer: {\n    firstName: string;\n    lastName: string;\n    phone: string;\n    email: string;\n  };\n  orderDetails: {\n    type: 'delivery' | 'takeaway';\n    time: 'asap' | 'scheduled';\n    scheduledTime: string | null;\n  };\n  subtotal: number;\n  deliveryFee: number;\n  total: number;\n  paymentMethod: string;\n  notes: string | null;\n}\n\n// Sample orders that will be shown in the kitchen order manager\nexport const sampleOrders: Order[] = [\n  {\n    id: 1001,\n    createdAt: new Date(Date.now() - 15 * 60000).toISOString(), // 15 minutes ago\n    status: 'confirmed',\n    items: [\n      { id: 22, name: 'Smoked Beef Brisket', price: 189, quantity: 1 },\n      { id: 32, name: 'Mac & Cheese', price: 89, quantity: 1 },\n      { id: 35, name: 'Sweet Tea', price: 49, quantity: 2 }\n    ],\n    customer: {\n      firstName: 'John',\n      lastName: 'Smith',\n      phone: '************',\n      email: '<EMAIL>'\n    },\n    orderDetails: {\n      type: 'delivery',\n      time: 'asap',\n      scheduledTime: null\n    },\n    subtotal: 376,\n    deliveryFee: 49,\n    total: 425,\n    paymentMethod: 'card',\n    notes: 'Ring doorbell, please'\n  },\n  {\n    id: 1002,\n    createdAt: new Date(Date.now() - 10 * 60000).toISOString(), // 10 minutes ago\n    status: 'processing',\n    items: [\n      { id: 23, name: 'Baby Back Ribs', price: 179, quantity: 1 },\n      { id: 30, name: 'Loaded BBQ Fries', price: 119, quantity: 1 },\n      { id: 34, name: 'House-Made Lemonade', price: 59, quantity: 1 }\n    ],\n    customer: {\n      firstName: 'Emma',\n      lastName: 'Johnson',\n      phone: '************',\n      email: '<EMAIL>'\n    },\n    orderDetails: {\n      type: 'takeaway',\n      time: 'scheduled',\n      scheduledTime: new Date(Date.now() + 120 * 60000).toISOString() // 2 hours from now\n    },\n    subtotal: 357,\n    deliveryFee: 0,\n    total: 357,\n    paymentMethod: 'card',\n    notes: null\n  },\n  {\n    id: 1003,\n    createdAt: new Date(Date.now() - 25 * 60000).toISOString(), // 25 minutes ago\n    status: 'preparing',\n    items: [\n      { id: 26, name: 'Smokehouse Burger', price: 169, quantity: 2 },\n      { id: 31, name: 'Smoked Wings', price: 139, quantity: 1 },\n      { id: 34, name: 'Craft Beer Selection', price: 89, quantity: 2 }\n    ],\n    customer: {\n      firstName: 'Michael',\n      lastName: 'Brown',\n      phone: '************',\n      email: '<EMAIL>'\n    },\n    orderDetails: {\n      type: 'delivery',\n      time: 'asap',\n      scheduledTime: null\n    },\n    subtotal: 655,\n    deliveryFee: 49,\n    total: 704,\n    paymentMethod: 'cash',\n    notes: 'No onions on burgers please'\n  },\n  {\n    id: 1004,\n    createdAt: new Date(Date.now() - 30 * 60000).toISOString(), // 30 minutes ago\n    status: 'ready_for_delivery',\n    items: [\n      { id: 22, name: 'Smoked Beef Brisket', price: 189, quantity: 1 },\n      { id: 30, name: 'Loaded BBQ Fries', price: 119, quantity: 1 },\n      { id: 39, name: 'Smoked Apple Cobbler', price: 99, quantity: 1 }\n    ],\n    customer: {\n      firstName: 'Sarah',\n      lastName: 'Davis',\n      phone: '************',\n      email: '<EMAIL>'\n    },\n    orderDetails: {\n      type: 'delivery',\n      time: 'asap',\n      scheduledTime: null\n    },\n    subtotal: 407,\n    deliveryFee: 49,\n    total: 456,\n    paymentMethod: 'card',\n    notes: 'Leave at door'\n  },\n  {\n    id: 1005,\n    createdAt: new Date(Date.now() - 20 * 60000).toISOString(), // 20 minutes ago\n    status: 'preparing',\n    items: [\n      { id: 28, name: 'Pulled Pork Burger', price: 159, quantity: 1 },\n      { id: 31, name: 'Smoked Wings', price: 139, quantity: 1 },\n      { id: 32, name: 'Mac & Cheese', price: 89, quantity: 1 }\n    ],\n    customer: {\n      firstName: 'David',\n      lastName: 'Wilson',\n      phone: '************',\n      email: '<EMAIL>'\n    },\n    orderDetails: {\n      type: 'takeaway',\n      time: 'asap',\n      scheduledTime: null\n    },\n    subtotal: 387,\n    deliveryFee: 0,\n    total: 387,\n    paymentMethod: 'card',\n    notes: null\n  }\n];"}