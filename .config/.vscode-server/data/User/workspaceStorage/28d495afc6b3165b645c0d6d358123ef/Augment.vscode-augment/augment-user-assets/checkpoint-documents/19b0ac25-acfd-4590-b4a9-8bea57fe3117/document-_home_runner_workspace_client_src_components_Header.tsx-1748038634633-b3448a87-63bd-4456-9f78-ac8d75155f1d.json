{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/Header.tsx"}, "originalCode": "import { useState, useEffect } from \"react\";\nimport { Link, useLocation } from \"wouter\";\nimport { useCart } from \"@/context/CartContext\";\n\nconst Header = () => {\n  const [isMenuOpen, setIsMenuOpen] = useState(false);\n  const [scrolled, setScrolled] = useState(false);\n  const [location] = useLocation();\n  const { cartItems } = useCart();\n\n  const cartItemsCount = cartItems.reduce((total, item) => total + item.quantity, 0);\n\n  useEffect(() => {\n    const handleScroll = () => {\n      if (window.scrollY > 50) {\n        setScrolled(true);\n      } else {\n        setScrolled(false);\n      }\n    };\n\n    window.addEventListener(\"scroll\", handleScroll);\n    return () => {\n      window.removeEventListener(\"scroll\", handleScroll);\n    };\n  }, []);\n\n  const toggleMenu = () => {\n    setIsMenuOpen(!isMenuOpen);\n  };\n\n  const closeMenu = () => {\n    setIsMenuOpen(false);\n  };\n\n  const navLinks = [\n    { name: \"Home\", path: \"/\" },\n    { name: \"<PERSON>u\", path: \"/menu\" },\n    { name: \"Cart\", path: \"/cart\" },\n    { name: \"Checkout\", path: \"/checkout\" },\n    { name: \"Contact\", path: \"/contact\" },\n  ];\n\n  return (\n    <header\n      className={`fixed top-0 left-0 right-0 z-50 bg-black transition-all duration-300 ${\n        scrolled ? \"bg-opacity-95 border-b border-gray-800\" : \"bg-opacity-70\"\n      }`}\n    >\n      <div className=\"container mx-auto px-4 py-4\">\n        <div className=\"flex justify-between items-center\">\n          <div className=\"flex items-center\">\n            <Link href=\"/\" className=\"text-white font-playfair text-2xl font-bold tracking-wider\">\n              <span className=\"text-secondary text-glow-pink\">BARBECUEZ</span>\n            </Link>\n          </div>\n\n          {/* Desktop Menu */}\n          <nav className=\"hidden md:flex space-x-8\">\n            {navLinks.map((link) => (\n              <Link\n                key={link.path}\n                href={link.path}\n                className={`font-poppins transition duration-300 ${\n                  location === link.path ? \"text-primary text-glow-blue\" : \"text-white hover:text-primary\"\n                }`}\n              >\n                {link.name}\n              </Link>\n            ))}\n          </nav>\n\n          {/* Mobile Menu Button */}\n          <div className=\"md:hidden\">\n            <button\n              onClick={toggleMenu}\n              className=\"text-white focus:outline-none\"\n              aria-label=\"Toggle mobile menu\"\n            >\n              <i className=\"fas fa-bars text-2xl\"></i>\n            </button>\n          </div>\n\n          {/* Cart Icon */}\n          <div className=\"hidden md:block\">\n            <Link href=\"/cart\" className=\"relative text-white hover:text-accent transition duration-300\">\n              <i className=\"fas fa-shopping-bag text-2xl\"></i>\n              {cartItemsCount > 0 && (\n                <span className=\"absolute -top-2 -right-2 bg-secondary text-white text-xs font-bold rounded-full h-5 w-5 flex items-center justify-center\">\n                  {cartItemsCount}\n                </span>\n              )}\n            </Link>\n          </div>\n        </div>\n      </div>\n\n      {/* Mobile Menu */}\n      <div className={`md:hidden bg-black bg-opacity-95 transition-all duration-300 ${isMenuOpen ? 'block' : 'hidden'}`}>\n        <div className=\"container mx-auto px-4 py-3 space-y-3\">\n          {navLinks.map((link) => (\n            <Link\n              key={link.path}\n              href={link.path}\n              className={`block py-2 font-poppins transition duration-300 ${\n                location === link.path ? \"text-primary text-glow-blue\" : \"text-white hover:text-primary\"\n              }`}\n              onClick={closeMenu}\n            >\n              {link.name}\n            </Link>\n          ))}\n\n          {/* Mobile Cart Link with Counter */}\n          <div className=\"flex items-center py-2\">\n            <Link href=\"/cart\" className=\"relative text-white hover:text-accent transition duration-300 flex items-center\" onClick={closeMenu}>\n              <i className=\"fas fa-shopping-bag text-xl mr-2\"></i>\n              <span>Cart</span>\n              {cartItemsCount > 0 && (\n                <span className=\"ml-2 bg-secondary text-white text-xs font-bold rounded-full h-5 w-5 flex items-center justify-center\">\n                  {cartItemsCount}\n                </span>\n              )}\n            </Link>\n          </div>\n        </div>\n      </div>\n    </header>\n  );\n};\n\nexport default Header;\n", "modifiedCode": "import { useState, useEffect } from \"react\";\nimport { Link, useLocation } from \"wouter\";\nimport { useCart } from \"@/context/CartContext\";\n\nconst Header = () => {\n  const [isMenuOpen, setIsMenuOpen] = useState(false);\n  const [scrolled, setScrolled] = useState(false);\n  const [location] = useLocation();\n  const { cartItems } = useCart();\n\n  const cartItemsCount = cartItems.reduce((total, item) => total + item.quantity, 0);\n\n  useEffect(() => {\n    const handleScroll = () => {\n      if (window.scrollY > 50) {\n        setScrolled(true);\n      } else {\n        setScrolled(false);\n      }\n    };\n\n    window.addEventListener(\"scroll\", handleScroll);\n    return () => {\n      window.removeEventListener(\"scroll\", handleScroll);\n    };\n  }, []);\n\n  const toggleMenu = () => {\n    setIsMenuOpen(!isMenuOpen);\n  };\n\n  const closeMenu = () => {\n    setIsMenuOpen(false);\n  };\n\n  const navLinks = [\n    { name: \"Home\", path: \"/\" },\n    { name: \"<PERSON>u\", path: \"/menu\" },\n    { name: \"Cart\", path: \"/cart\" },\n    { name: \"Contact\", path: \"/contact\" },\n  ];\n\n  return (\n    <header\n      className={`fixed top-0 left-0 right-0 z-50 bg-black transition-all duration-300 ${\n        scrolled ? \"bg-opacity-95 border-b border-gray-800\" : \"bg-opacity-70\"\n      }`}\n    >\n      <div className=\"container mx-auto px-4 py-4\">\n        <div className=\"flex justify-between items-center\">\n          <div className=\"flex items-center\">\n            <Link href=\"/\" className=\"text-white font-playfair text-2xl font-bold tracking-wider\">\n              <span className=\"text-secondary text-glow-pink\">BARBECUEZ</span>\n            </Link>\n          </div>\n\n          {/* Desktop Menu */}\n          <nav className=\"hidden md:flex space-x-8\">\n            {navLinks.map((link) => (\n              <Link\n                key={link.path}\n                href={link.path}\n                className={`font-poppins transition duration-300 ${\n                  location === link.path ? \"text-primary text-glow-blue\" : \"text-white hover:text-primary\"\n                }`}\n              >\n                {link.name}\n              </Link>\n            ))}\n          </nav>\n\n          {/* Mobile Menu Button */}\n          <div className=\"md:hidden\">\n            <button\n              onClick={toggleMenu}\n              className=\"text-white focus:outline-none\"\n              aria-label=\"Toggle mobile menu\"\n            >\n              <i className=\"fas fa-bars text-2xl\"></i>\n            </button>\n          </div>\n\n          {/* Cart Icon */}\n          <div className=\"hidden md:block\">\n            <Link href=\"/cart\" className=\"relative text-white hover:text-accent transition duration-300\">\n              <i className=\"fas fa-shopping-bag text-2xl\"></i>\n              {cartItemsCount > 0 && (\n                <span className=\"absolute -top-2 -right-2 bg-secondary text-white text-xs font-bold rounded-full h-5 w-5 flex items-center justify-center\">\n                  {cartItemsCount}\n                </span>\n              )}\n            </Link>\n          </div>\n        </div>\n      </div>\n\n      {/* Mobile Menu */}\n      <div className={`md:hidden bg-black bg-opacity-95 transition-all duration-300 ${isMenuOpen ? 'block' : 'hidden'}`}>\n        <div className=\"container mx-auto px-4 py-3 space-y-3\">\n          {navLinks.map((link) => (\n            <Link\n              key={link.path}\n              href={link.path}\n              className={`block py-2 font-poppins transition duration-300 ${\n                location === link.path ? \"text-primary text-glow-blue\" : \"text-white hover:text-primary\"\n              }`}\n              onClick={closeMenu}\n            >\n              {link.name}\n            </Link>\n          ))}\n\n          {/* Mobile Cart Link with Counter */}\n          <div className=\"flex items-center py-2\">\n            <Link href=\"/cart\" className=\"relative text-white hover:text-accent transition duration-300 flex items-center\" onClick={closeMenu}>\n              <i className=\"fas fa-shopping-bag text-xl mr-2\"></i>\n              <span>Cart</span>\n              {cartItemsCount > 0 && (\n                <span className=\"ml-2 bg-secondary text-white text-xs font-bold rounded-full h-5 w-5 flex items-center justify-center\">\n                  {cartItemsCount}\n                </span>\n              )}\n            </Link>\n          </div>\n        </div>\n      </div>\n    </header>\n  );\n};\n\nexport default Header;\n"}