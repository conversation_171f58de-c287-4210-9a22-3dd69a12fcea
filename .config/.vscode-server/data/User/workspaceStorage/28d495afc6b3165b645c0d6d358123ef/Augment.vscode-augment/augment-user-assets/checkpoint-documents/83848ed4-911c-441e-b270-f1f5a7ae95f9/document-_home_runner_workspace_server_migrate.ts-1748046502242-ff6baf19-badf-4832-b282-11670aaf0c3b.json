{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "server/migrate.ts"}, "originalCode": "import { drizzle } from \"drizzle-orm/neon-serverless\";\nimport { migrate } from \"drizzle-orm/neon-serverless/migrator\";\nimport { Pool } from \"@neondatabase/serverless\";\n\n// Database connection for migrations\nconst connectionString = process.env.DATABASE_URL;\n\nif (!connectionString) {\n  throw new Error(\"DATABASE_URL must be set for migrations\");\n}\n\n// Create a connection for migrations\nconst migrationClient = new Pool({ connectionString });\nconst db = drizzle({ client: migrationClient });\n\nasync function runMigrations() {\n  try {\n    console.log(\"Running database migrations...\");\n\n    // Run migrations from the drizzle folder\n    await migrate(db, { migrationsFolder: \"./drizzle\" });\n\n    console.log(\"Migrations completed successfully!\");\n  } catch (error) {\n    console.error(\"Migration failed:\", error);\n    process.exit(1);\n  } finally {\n    // Close the connection pool\n    migrationClient.end();\n  }\n}\n\n// Run migrations if this file is executed directly\nif (import.meta.url === `file://${process.argv[1]}`) {\n  runMigrations();\n}\n\nexport { runMigrations };\n", "modifiedCode": "import { drizzle } from \"drizzle-orm/neon-serverless\";\nimport { migrate } from \"drizzle-orm/neon-serverless/migrator\";\nimport { Pool } from \"@neondatabase/serverless\";\n\n// Database connection for migrations\nconst connectionString = process.env.DATABASE_URL;\n\nif (!connectionString) {\n  throw new Error(\"DATABASE_URL must be set for migrations\");\n}\n\n// Create a connection for migrations\nconst migrationClient = new Pool({ connectionString });\nconst db = drizzle({ client: migrationClient });\n\nasync function runMigrations() {\n  try {\n    console.log(\"Running database migrations...\");\n\n    // Run migrations from the migrations folder\n    await migrate(db, { migrationsFolder: \"./migrations\" });\n\n    console.log(\"Migrations completed successfully!\");\n  } catch (error) {\n    console.error(\"Migration failed:\", error);\n    process.exit(1);\n  } finally {\n    // Close the connection pool\n    migrationClient.end();\n  }\n}\n\n// Run migrations if this file is executed directly\nif (import.meta.url === `file://${process.argv[1]}`) {\n  runMigrations();\n}\n\nexport { runMigrations };\n"}