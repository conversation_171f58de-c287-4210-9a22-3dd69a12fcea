{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/pages/auth/AuthPage.tsx"}, "originalCode": "import { useState } from \"react\";\nimport { useLocation } from \"wouter\";\nimport { motion } from \"framer-motion\";\nimport { z } from \"zod\";\nimport { useForm } from \"react-hook-form\";\nimport { zodResolver } from \"@hookform/resolvers/zod\";\nimport { Eye, EyeOff, Lock, User } from \"lucide-react\";\n\n// Define validation schema\nconst loginSchema = z.object({\n  username: z.string().min(3, \"Username must be at least 3 characters\"),\n  password: z.string().min(6, \"Password must be at least 6 characters\"),\n});\n\ntype LoginFormData = z.infer<typeof loginSchema>;\n\n// Mock admin credentials - In a real app, this would be authenticated against a database\nconst ADMIN_CREDENTIALS = {\n  username: \"admin\",\n  password: \"admin123\"\n};\n\nconst MANAGER_CREDENTIALS = {\n  username: \"manager\",\n  password: \"manager123\"\n};\n\nconst DRIVER_CREDENTIALS = {\n  username: \"driver\",\n  password: \"driver123\"\n};\n\nconst AuthPage = () => {\n  const [, setLocation] = useLocation();\n  const [showPassword, setShowPassword] = useState(false);\n  const [isLoading, setIsLoading] = useState(false);\n  const [error, setError] = useState(\"\");\n\n  // Form handling\n  const { register, handleSubmit, formState: { errors } } = useForm<LoginFormData>({\n    resolver: zodResolver(loginSchema),\n    defaultValues: {\n      username: \"\",\n      password: \"\"\n    }\n  });\n\n  const togglePasswordVisibility = () => {\n    setShowPassword(!showPassword);\n  };\n\n  const onSubmit = async (data: LoginFormData) => {\n    setIsLoading(true);\n    setError(\"\");\n\n    // Simulate API call delay\n    await new Promise(resolve => setTimeout(resolve, 1000));\n\n    try {\n      // Check credentials - in a real app, this would be a fetch to /api/login\n      if (data.username === ADMIN_CREDENTIALS.username && data.password === ADMIN_CREDENTIALS.password) {\n        alert(\"Login Successful! Welcome to the Admin Portal\");\n        // Redirect to admin dashboard\n        setLocation(\"/admin/settings\");\n        return;\n      } else if (data.username === MANAGER_CREDENTIALS.username && data.password === MANAGER_CREDENTIALS.password) {\n        alert(\"Login Successful! Welcome to the Kitchen Manager\");\n        // Redirect to manager dashboard\n        setLocation(\"/manager\");\n        return;\n      } else if (data.username === DRIVER_CREDENTIALS.username && data.password === DRIVER_CREDENTIALS.password) {\n        alert(\"Login Successful! Welcome to the Delivery Dashboard\");\n        // Redirect to driver dashboard\n        setLocation(\"/driver\");\n        return;\n      }\n\n      // If we get here, login failed\n      setError(\"Invalid username or password\");\n    } catch (err) {\n      setError(\"An error occurred during login\");\n      console.error(\"Login error:\", err);\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  return (\n    <div className=\"min-h-screen bg-black text-white flex flex-col md:flex-row\">\n      {/* Left side - Login Form */}\n      <div className=\"w-full md:w-1/2 flex flex-col justify-center p-6 md:p-12\">\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.5 }}\n          className=\"max-w-md mx-auto w-full\"\n        >\n          <div className=\"text-center mb-8\">\n            <h1 className=\"text-3xl font-bold bg-gradient-to-r from-pink-500 via-purple-500 to-cyan-500 text-transparent bg-clip-text mb-2\">\n              Barbecuez Staff Portal\n            </h1>\n            <p className=\"text-gray-400\">Sign in to access your dashboard</p>\n          </div>\n\n          {error && (\n            <div className=\"bg-red-900/40 border border-red-700 text-red-400 p-4 rounded-lg mb-6\">\n              {error}\n            </div>\n          )}\n\n          <form onSubmit={handleSubmit(onSubmit)} className=\"space-y-6\">\n            <div className=\"space-y-2\">\n              <label htmlFor=\"username\" className=\"text-sm font-medium text-gray-300 block\">\n                Username\n              </label>\n              <div className=\"relative\">\n                <div className=\"absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none\">\n                  <User className=\"h-5 w-5 text-gray-500\" />\n                </div>\n                <input\n                  id=\"username\"\n                  type=\"text\"\n                  {...register(\"username\")}\n                  className={`bg-gray-900 border ${\n                    errors.username ? \"border-red-600\" : \"border-gray-700\"\n                  } text-white rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full pl-10 p-3 placeholder-gray-500`}\n                  placeholder=\"Enter your username\"\n                />\n              </div>\n              {errors.username && (\n                <p className=\"text-red-500 text-sm\">{errors.username.message}</p>\n              )}\n            </div>\n\n            <div className=\"space-y-2\">\n              <label htmlFor=\"password\" className=\"text-sm font-medium text-gray-300 block\">\n                Password\n              </label>\n              <div className=\"relative\">\n                <div className=\"absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none\">\n                  <Lock className=\"h-5 w-5 text-gray-500\" />\n                </div>\n                <input\n                  id=\"password\"\n                  type={showPassword ? \"text\" : \"password\"}\n                  {...register(\"password\")}\n                  className={`bg-gray-900 border ${\n                    errors.password ? \"border-red-600\" : \"border-gray-700\"\n                  } text-white rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full pl-10 p-3 pr-10 placeholder-gray-500`}\n                  placeholder=\"Enter your password\"\n                />\n                <div className=\"absolute inset-y-0 right-0 flex items-center pr-3\">\n                  <button\n                    type=\"button\"\n                    onClick={togglePasswordVisibility}\n                    className=\"text-gray-500 hover:text-gray-300 focus:outline-none\"\n                  >\n                    {showPassword ? (\n                      <EyeOff className=\"h-5 w-5\" />\n                    ) : (\n                      <Eye className=\"h-5 w-5\" />\n                    )}\n                  </button>\n                </div>\n              </div>\n              {errors.password && (\n                <p className=\"text-red-500 text-sm\">{errors.password.message}</p>\n              )}\n            </div>\n\n            <motion.button\n              type=\"submit\"\n              disabled={isLoading}\n              className=\"w-full bg-gradient-to-r from-pink-600 to-purple-600 hover:from-pink-700 hover:to-purple-700 text-white font-medium rounded-lg p-3 flex items-center justify-center disabled:opacity-70 disabled:cursor-not-allowed\"\n              whileHover={{ scale: 1.02 }}\n              whileTap={{ scale: 0.98 }}\n            >\n              {isLoading ? (\n                <>\n                  <svg className=\"animate-spin -ml-1 mr-3 h-5 w-5 text-white\" xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\">\n                    <circle className=\"opacity-25\" cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\" strokeWidth=\"4\"></circle>\n                    <path className=\"opacity-75\" fill=\"currentColor\" d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"></path>\n                  </svg>\n                  Signing in...\n                </>\n              ) : (\n                \"Sign In\"\n              )}\n            </motion.button>\n\n            <div className=\"text-sm text-center text-gray-500 mt-4\">\n              <p>Need help? Contact system administrator</p>\n            </div>\n          </form>\n\n          {/* Quick login for testing */}\n          {process.env.NODE_ENV === \"development\" && (\n            <div className=\"mt-8 p-4 border border-gray-800 rounded-lg\">\n              <h3 className=\"text-sm font-medium text-gray-400 mb-2\">Test Accounts:</h3>\n              <div className=\"grid grid-cols-3 gap-2 text-xs\">\n                <div className=\"bg-gray-900 p-2 rounded border border-gray-800\">\n                  <p className=\"font-medium text-cyan-400\">Admin</p>\n                  <p className=\"text-gray-500\">admin / admin123</p>\n                </div>\n                <div className=\"bg-gray-900 p-2 rounded border border-gray-800\">\n                  <p className=\"font-medium text-orange-400\">Manager</p>\n                  <p className=\"text-gray-500\">manager / manager123</p>\n                </div>\n                <div className=\"bg-gray-900 p-2 rounded border border-gray-800\">\n                  <p className=\"font-medium text-pink-400\">Driver</p>\n                  <p className=\"text-gray-500\">driver / driver123</p>\n                </div>\n              </div>\n            </div>\n          )}\n        </motion.div>\n      </div>\n\n      {/* Right side - Hero Section */}\n      <div className=\"hidden md:block md:w-1/2 bg-gradient-to-br from-gray-900 to-black relative overflow-hidden\">\n        <div className=\"absolute inset-0 bg-black opacity-60\"></div>\n        <div\n          className=\"absolute inset-0 bg-[url('https://images.unsplash.com/photo-1544025162-d76694265947?q=80&w=2069')] bg-cover bg-center\"\n          style={{ backgroundPosition: 'center 40%' }}\n        >\n          <div className=\"absolute inset-0 bg-gradient-to-r from-black via-black/90 to-transparent\"></div>\n        </div>\n\n        <div className=\"relative h-full flex flex-col justify-center px-12 z-10\">\n          <h2 className=\"text-4xl font-bold text-white mb-6\">\n            Welcome to the<br />\n            <span className=\"bg-gradient-to-r from-pink-500 via-purple-500 to-cyan-500 text-transparent bg-clip-text\">\n              Barbecuez Staff Portal\n            </span>\n          </h2>\n\n          <div className=\"space-y-6 max-w-md\">\n            <div className=\"flex items-start space-x-3\">\n              <div className=\"mt-1 bg-pink-600/20 p-1 rounded\">\n                <svg className=\"h-5 w-5 text-pink-500\" xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M5 13l4 4L19 7\" />\n                </svg>\n              </div>\n              <div>\n                <h3 className=\"text-lg font-medium text-white\">Admin Portal</h3>\n                <p className=\"text-gray-400\">Manage restaurant settings, menu items, and view analytics</p>\n              </div>\n            </div>\n\n            <div className=\"flex items-start space-x-3\">\n              <div className=\"mt-1 bg-purple-600/20 p-1 rounded\">\n                <svg className=\"h-5 w-5 text-purple-500\" xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M5 13l4 4L19 7\" />\n                </svg>\n              </div>\n              <div>\n                <h3 className=\"text-lg font-medium text-white\">Kitchen Manager</h3>\n                <p className=\"text-gray-400\">Process and track orders in real-time with status updates</p>\n              </div>\n            </div>\n\n            <div className=\"flex items-start space-x-3\">\n              <div className=\"mt-1 bg-cyan-600/20 p-1 rounded\">\n                <svg className=\"h-5 w-5 text-cyan-500\" xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M5 13l4 4L19 7\" />\n                </svg>\n              </div>\n              <div>\n                <h3 className=\"text-lg font-medium text-white\">Delivery Dashboard</h3>\n                <p className=\"text-gray-400\">Track deliveries and update customer order status</p>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default AuthPage;", "modifiedCode": "import { useState } from \"react\";\nimport { useLocation } from \"wouter\";\nimport { motion } from \"framer-motion\";\nimport { z } from \"zod\";\nimport { useForm } from \"react-hook-form\";\nimport { zodResolver } from \"@hookform/resolvers/zod\";\nimport { Eye, EyeOff, Lock, User } from \"lucide-react\";\n\n// Define validation schema\nconst loginSchema = z.object({\n  username: z.string().min(3, \"Username must be at least 3 characters\"),\n  password: z.string().min(6, \"Password must be at least 6 characters\"),\n});\n\ntype LoginFormData = z.infer<typeof loginSchema>;\n\nconst AuthPage = () => {\n  const [, setLocation] = useLocation();\n  const [showPassword, setShowPassword] = useState(false);\n  const [isLoading, setIsLoading] = useState(false);\n  const [error, setError] = useState(\"\");\n\n  // Form handling\n  const { register, handleSubmit, formState: { errors } } = useForm<LoginFormData>({\n    resolver: zodResolver(loginSchema),\n    defaultValues: {\n      username: \"\",\n      password: \"\"\n    }\n  });\n\n  const togglePasswordVisibility = () => {\n    setShowPassword(!showPassword);\n  };\n\n  const onSubmit = async (data: LoginFormData) => {\n    setIsLoading(true);\n    setError(\"\");\n\n    // Simulate API call delay\n    await new Promise(resolve => setTimeout(resolve, 1000));\n\n    try {\n      // Check credentials - in a real app, this would be a fetch to /api/login\n      if (data.username === ADMIN_CREDENTIALS.username && data.password === ADMIN_CREDENTIALS.password) {\n        alert(\"Login Successful! Welcome to the Admin Portal\");\n        // Redirect to admin dashboard\n        setLocation(\"/admin/settings\");\n        return;\n      } else if (data.username === MANAGER_CREDENTIALS.username && data.password === MANAGER_CREDENTIALS.password) {\n        alert(\"Login Successful! Welcome to the Kitchen Manager\");\n        // Redirect to manager dashboard\n        setLocation(\"/manager\");\n        return;\n      } else if (data.username === DRIVER_CREDENTIALS.username && data.password === DRIVER_CREDENTIALS.password) {\n        alert(\"Login Successful! Welcome to the Delivery Dashboard\");\n        // Redirect to driver dashboard\n        setLocation(\"/driver\");\n        return;\n      }\n\n      // If we get here, login failed\n      setError(\"Invalid username or password\");\n    } catch (err) {\n      setError(\"An error occurred during login\");\n      console.error(\"Login error:\", err);\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  return (\n    <div className=\"min-h-screen bg-black text-white flex flex-col md:flex-row\">\n      {/* Left side - Login Form */}\n      <div className=\"w-full md:w-1/2 flex flex-col justify-center p-6 md:p-12\">\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.5 }}\n          className=\"max-w-md mx-auto w-full\"\n        >\n          <div className=\"text-center mb-8\">\n            <h1 className=\"text-3xl font-bold bg-gradient-to-r from-pink-500 via-purple-500 to-cyan-500 text-transparent bg-clip-text mb-2\">\n              Barbecuez Staff Portal\n            </h1>\n            <p className=\"text-gray-400\">Sign in to access your dashboard</p>\n          </div>\n\n          {error && (\n            <div className=\"bg-red-900/40 border border-red-700 text-red-400 p-4 rounded-lg mb-6\">\n              {error}\n            </div>\n          )}\n\n          <form onSubmit={handleSubmit(onSubmit)} className=\"space-y-6\">\n            <div className=\"space-y-2\">\n              <label htmlFor=\"username\" className=\"text-sm font-medium text-gray-300 block\">\n                Username\n              </label>\n              <div className=\"relative\">\n                <div className=\"absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none\">\n                  <User className=\"h-5 w-5 text-gray-500\" />\n                </div>\n                <input\n                  id=\"username\"\n                  type=\"text\"\n                  {...register(\"username\")}\n                  className={`bg-gray-900 border ${\n                    errors.username ? \"border-red-600\" : \"border-gray-700\"\n                  } text-white rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full pl-10 p-3 placeholder-gray-500`}\n                  placeholder=\"Enter your username\"\n                />\n              </div>\n              {errors.username && (\n                <p className=\"text-red-500 text-sm\">{errors.username.message}</p>\n              )}\n            </div>\n\n            <div className=\"space-y-2\">\n              <label htmlFor=\"password\" className=\"text-sm font-medium text-gray-300 block\">\n                Password\n              </label>\n              <div className=\"relative\">\n                <div className=\"absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none\">\n                  <Lock className=\"h-5 w-5 text-gray-500\" />\n                </div>\n                <input\n                  id=\"password\"\n                  type={showPassword ? \"text\" : \"password\"}\n                  {...register(\"password\")}\n                  className={`bg-gray-900 border ${\n                    errors.password ? \"border-red-600\" : \"border-gray-700\"\n                  } text-white rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full pl-10 p-3 pr-10 placeholder-gray-500`}\n                  placeholder=\"Enter your password\"\n                />\n                <div className=\"absolute inset-y-0 right-0 flex items-center pr-3\">\n                  <button\n                    type=\"button\"\n                    onClick={togglePasswordVisibility}\n                    className=\"text-gray-500 hover:text-gray-300 focus:outline-none\"\n                  >\n                    {showPassword ? (\n                      <EyeOff className=\"h-5 w-5\" />\n                    ) : (\n                      <Eye className=\"h-5 w-5\" />\n                    )}\n                  </button>\n                </div>\n              </div>\n              {errors.password && (\n                <p className=\"text-red-500 text-sm\">{errors.password.message}</p>\n              )}\n            </div>\n\n            <motion.button\n              type=\"submit\"\n              disabled={isLoading}\n              className=\"w-full bg-gradient-to-r from-pink-600 to-purple-600 hover:from-pink-700 hover:to-purple-700 text-white font-medium rounded-lg p-3 flex items-center justify-center disabled:opacity-70 disabled:cursor-not-allowed\"\n              whileHover={{ scale: 1.02 }}\n              whileTap={{ scale: 0.98 }}\n            >\n              {isLoading ? (\n                <>\n                  <svg className=\"animate-spin -ml-1 mr-3 h-5 w-5 text-white\" xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\">\n                    <circle className=\"opacity-25\" cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\" strokeWidth=\"4\"></circle>\n                    <path className=\"opacity-75\" fill=\"currentColor\" d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"></path>\n                  </svg>\n                  Signing in...\n                </>\n              ) : (\n                \"Sign In\"\n              )}\n            </motion.button>\n\n            <div className=\"text-sm text-center text-gray-500 mt-4\">\n              <p>Need help? Contact system administrator</p>\n            </div>\n          </form>\n\n          {/* Quick login for testing */}\n          {process.env.NODE_ENV === \"development\" && (\n            <div className=\"mt-8 p-4 border border-gray-800 rounded-lg\">\n              <h3 className=\"text-sm font-medium text-gray-400 mb-2\">Test Accounts:</h3>\n              <div className=\"grid grid-cols-3 gap-2 text-xs\">\n                <div className=\"bg-gray-900 p-2 rounded border border-gray-800\">\n                  <p className=\"font-medium text-cyan-400\">Admin</p>\n                  <p className=\"text-gray-500\">admin / admin123</p>\n                </div>\n                <div className=\"bg-gray-900 p-2 rounded border border-gray-800\">\n                  <p className=\"font-medium text-orange-400\">Manager</p>\n                  <p className=\"text-gray-500\">manager / manager123</p>\n                </div>\n                <div className=\"bg-gray-900 p-2 rounded border border-gray-800\">\n                  <p className=\"font-medium text-pink-400\">Driver</p>\n                  <p className=\"text-gray-500\">driver / driver123</p>\n                </div>\n              </div>\n            </div>\n          )}\n        </motion.div>\n      </div>\n\n      {/* Right side - Hero Section */}\n      <div className=\"hidden md:block md:w-1/2 bg-gradient-to-br from-gray-900 to-black relative overflow-hidden\">\n        <div className=\"absolute inset-0 bg-black opacity-60\"></div>\n        <div\n          className=\"absolute inset-0 bg-[url('https://images.unsplash.com/photo-1544025162-d76694265947?q=80&w=2069')] bg-cover bg-center\"\n          style={{ backgroundPosition: 'center 40%' }}\n        >\n          <div className=\"absolute inset-0 bg-gradient-to-r from-black via-black/90 to-transparent\"></div>\n        </div>\n\n        <div className=\"relative h-full flex flex-col justify-center px-12 z-10\">\n          <h2 className=\"text-4xl font-bold text-white mb-6\">\n            Welcome to the<br />\n            <span className=\"bg-gradient-to-r from-pink-500 via-purple-500 to-cyan-500 text-transparent bg-clip-text\">\n              Barbecuez Staff Portal\n            </span>\n          </h2>\n\n          <div className=\"space-y-6 max-w-md\">\n            <div className=\"flex items-start space-x-3\">\n              <div className=\"mt-1 bg-pink-600/20 p-1 rounded\">\n                <svg className=\"h-5 w-5 text-pink-500\" xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M5 13l4 4L19 7\" />\n                </svg>\n              </div>\n              <div>\n                <h3 className=\"text-lg font-medium text-white\">Admin Portal</h3>\n                <p className=\"text-gray-400\">Manage restaurant settings, menu items, and view analytics</p>\n              </div>\n            </div>\n\n            <div className=\"flex items-start space-x-3\">\n              <div className=\"mt-1 bg-purple-600/20 p-1 rounded\">\n                <svg className=\"h-5 w-5 text-purple-500\" xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M5 13l4 4L19 7\" />\n                </svg>\n              </div>\n              <div>\n                <h3 className=\"text-lg font-medium text-white\">Kitchen Manager</h3>\n                <p className=\"text-gray-400\">Process and track orders in real-time with status updates</p>\n              </div>\n            </div>\n\n            <div className=\"flex items-start space-x-3\">\n              <div className=\"mt-1 bg-cyan-600/20 p-1 rounded\">\n                <svg className=\"h-5 w-5 text-cyan-500\" xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M5 13l4 4L19 7\" />\n                </svg>\n              </div>\n              <div>\n                <h3 className=\"text-lg font-medium text-white\">Delivery Dashboard</h3>\n                <p className=\"text-gray-400\">Track deliveries and update customer order status</p>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default AuthPage;"}