{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "server/schema.sql"}, "originalCode": "-- Restaurant Settings Table\nCREATE TABLE IF NOT EXISTS restaurant_settings (\n  id SERIAL PRIMARY KEY,\n  restaurant_open BOOLEAN DEFAULT true,\n  business_hours JSONB DEFAULT '{}',\n  delivery_fee INTEGER DEFAULT 49,\n  estimated_time VARCHAR(255) DEFAULT '25-35 min'\n);\n\n-- Categories Table\nCREATE TABLE IF NOT EXISTS categories (\n  id SERIAL PRIMARY KEY,\n  name VARCHAR(255) NOT NULL,\n  image_url TEXT\n);\n\n-- Menu Items Table\nCREATE TABLE IF NOT EXISTS menu_items (\n  id SERIAL PRIMARY KEY,\n  name VARCHAR(255) NOT NULL,\n  description TEXT,\n  price INTEGER NOT NULL,\n  image_url TEXT,\n  category_id INTEGER REFERENCES categories(id) ON DELETE SET NULL,\n  available BOOLEAN DEFAULT true\n);\n\n-- Orders Table\nCREATE TABLE IF NOT EXISTS orders (\n  id SERIAL PRIMARY KEY,\n  customer JSONB NOT NULL,\n  items JSONB NOT NULL,\n  subtotal INTEGER NOT NULL,\n  delivery_fee INTEGER NOT NULL,\n  total INTEGER NOT NULL,\n  status VARCHAR(50) DEFAULT 'pending',\n  payment_method VARCHAR(50) NOT NULL,\n  notes TEXT,\n  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP\n);\n\n-- Insert sample data for restaurant settings\nINSERT INTO restaurant_settings (restaurant_open, business_hours, delivery_fee, estimated_time)\nVALUES (\n  true,\n  '{\n    \"monday\": {\"open\": \"10:00\", \"close\": \"22:00\", \"delivery\": true},\n    \"tuesday\": {\"open\": \"10:00\", \"close\": \"22:00\", \"delivery\": true},\n    \"wednesday\": {\"open\": \"10:00\", \"close\": \"22:00\", \"delivery\": true},\n    \"thursday\": {\"open\": \"10:00\", \"close\": \"22:00\", \"delivery\": true},\n    \"friday\": {\"open\": \"10:00\", \"close\": \"23:00\", \"delivery\": true},\n    \"saturday\": {\"open\": \"10:00\", \"close\": \"23:00\", \"delivery\": true},\n    \"sunday\": {\"open\": \"12:00\", \"close\": \"21:00\", \"delivery\": true}\n  }',\n  49,\n  '25-35 min'\n) ON CONFLICT (id) DO NOTHING;\n\n-- Insert sample categories\nINSERT INTO categories (name, image_url)\nVALUES\n  ('Starters', 'https://images.unsplash.com/photo-1541745537411-b8046dc6d66c?q=80&w=2788&auto=format&fit=crop'),\n  ('Main Courses', 'https://images.unsplash.com/photo-1544025162-d76694265947?q=80&w=2788&auto=format&fit=crop'),\n  ('BBQ Specials', 'https://images.unsplash.com/photo-1529193591184-b1d58069ecdd?q=80&w=2790&auto=format&fit=crop'),\n  ('Sides', 'https://images.unsplash.com/photo-1618040521538-e0cfee99a9aa?q=80&w=2940&auto=format&fit=crop'),\n  ('Desserts', 'https://images.unsplash.com/photo-1551024601-bec78aea704b?q=80&w=2864&auto=format&fit=crop'),\n  ('Drinks', 'https://images.unsplash.com/photo-1551538827-9c037cb4f32a?q=80&w=2832&auto=format&fit=crop')\nON CONFLICT DO NOTHING;\n\n-- Insert sample menu items\nINSERT INTO menu_items (name, description, price, image_url, category_id, available)\nVALUES\n  ('BBQ Chicken Wings', 'Juicy chicken wings marinated in our signature BBQ sauce', 129, 'https://images.unsplash.com/photo-1608039755401-742074f0548d?q=80&w=2960&auto=format&fit=crop', 1, true),\n  ('Loaded Nachos', 'Crispy nachos with melted cheese, jalapeños, guacamole, and sour cream', 119, 'https://images.unsplash.com/photo-1582169296194-e4d644c48063?q=80&w=2800&auto=format&fit=crop', 1, true),\n  ('Smoked Brisket', 'Slow-smoked beef brisket with our house rub, served with coleslaw and cornbread', 249, 'https://images.unsplash.com/photo-1529193591184-b1d58069ecdd?q=80&w=2790&auto=format&fit=crop', 3, true),\n  ('BBQ Ribs Platter', 'Fall-off-the-bone pork ribs with our signature sauce, served with fries and coleslaw', 229, 'https://images.unsplash.com/photo-1544025162-d76694265947?q=80&w=2788&auto=format&fit=crop', 3, true),\n  ('Grilled Salmon', 'Fresh Atlantic salmon with lemon herb butter, served with mashed potatoes and grilled asparagus', 239, 'https://images.unsplash.com/photo-1519708227418-c8fd9a32b7a2?q=80&w=2070&auto=format&fit=crop', 2, true),\n  ('Barbecue Burger', 'Juicy beef patty with cheddar, bacon, onion rings, and our signature BBQ sauce', 189, 'https://images.unsplash.com/photo-1568901346375-23c9450c58cd?q=80&w=2899&auto=format&fit=crop', 2, true),\n  ('Mac & Cheese', 'Creamy three-cheese blend with a crispy breadcrumb topping', 79, 'https://images.unsplash.com/photo-1543352634-a1c51d9f1fa7?q=80&w=2940&auto=format&fit=crop', 4, true),\n  ('Sweet Potato Fries', 'Crispy sweet potato fries with chipotle mayo dip', 69, 'https://images.unsplash.com/photo-1604152135912-04a022e73fba?q=80&w=2987&auto=format&fit=crop', 4, true),\n  ('New York Cheesecake', 'Creamy cheesecake with berry compote', 99, 'https://images.unsplash.com/photo-1611293388250-580b08c4a145?q=80&w=2940&auto=format&fit=crop', 5, true),\n  ('Chocolate Lava Cake', 'Warm chocolate cake with a molten center, served with vanilla ice cream', 109, 'https://images.unsplash.com/photo-1606313564200-e75d8e3aabc3?q=80&w=2942&auto=format&fit=crop', 5, true),\n  ('Craft Beer', 'Selection of local craft beers', 89, 'https://images.unsplash.com/photo-1600788886242-5c96aabe3757?q=80&w=2787&auto=format&fit=crop', 6, true),\n  ('Signature Cocktails', 'Ask your server for our seasonal cocktail options', 119, 'https://images.unsplash.com/photo-1551024601-bec78aea704b?q=80&w=2864&auto=format&fit=crop', 6, true)\nON CONFLICT DO NOTHING;\n\n-- Insert sample orders\nINSERT INTO orders (customer, items, subtotal, delivery_fee, total, status, payment_method, notes)\nVALUES\n  (\n    '{\"firstName\": \"John\", \"lastName\": \"Doe\", \"email\": \"<EMAIL>\", \"phone\": \"12345678\", \"address\": \"123 Main St\", \"postalCode\": \"0001\", \"city\": \"Oslo\"}',\n    '[{\"id\": 3, \"name\": \"Smoked Brisket\", \"price\": 249, \"quantity\": 1}, {\"id\": 7, \"name\": \"Mac & Cheese\", \"price\": 79, \"quantity\": 1}]',\n    328,\n    49,\n    377,\n    'delivered',\n    'card',\n    'Please include extra sauce'\n  ),\n  (\n    '{\"firstName\": \"Jane\", \"lastName\": \"Smith\", \"email\": \"<EMAIL>\", \"phone\": \"87654321\", \"address\": \"456 Oak Ave\", \"postalCode\": \"0002\", \"city\": \"Oslo\"}',\n    '[{\"id\": 4, \"name\": \"BBQ Ribs Platter\", \"price\": 229, \"quantity\": 2}, {\"id\": 8, \"name\": \"Sweet Potato Fries\", \"price\": 69, \"quantity\": 1}]',\n    527,\n    49,\n    576,\n    'delivered',\n    'card',\n    NULL\n  ),\n  (\n    '{\"firstName\": \"Mike\", \"lastName\": \"Johnson\", \"email\": \"<EMAIL>\", \"phone\": \"55554444\", \"address\": \"789 Pine St\", \"postalCode\": \"0003\", \"city\": \"Oslo\"}',\n    '[{\"id\": 6, \"name\": \"Barbecue Burger\", \"price\": 189, \"quantity\": 1}, {\"id\": 11, \"name\": \"Craft Beer\", \"price\": 89, \"quantity\": 1}]',\n    278,\n    49,\n    327,\n    'in-progress',\n    'cash',\n    'Ring the bell when you arrive'\n  ),\n  (\n    '{\"firstName\": \"Sarah\", \"lastName\": \"Williams\", \"email\": \"<EMAIL>\", \"phone\": \"33332222\", \"address\": \"101 Maple Dr\", \"postalCode\": \"0004\", \"city\": \"Oslo\"}',\n    '[{\"id\": 1, \"name\": \"BBQ Chicken Wings\", \"price\": 129, \"quantity\": 1}, {\"id\": 2, \"name\": \"Loaded Nachos\", \"price\": 119, \"quantity\": 1}, {\"id\": 10, \"name\": \"Chocolate Lava Cake\", \"price\": 109, \"quantity\": 1}]',\n    357,\n    49,\n    406,\n    'pending',\n    'card',\n    NULL\n  )\nON CONFLICT DO NOTHING;", "modifiedCode": "-- Restaurant Settings Table\nCREATE TABLE IF NOT EXISTS restaurant_settings (\n  id SERIAL PRIMARY KEY,\n  restaurant_open BOOLEAN DEFAULT true,\n  business_hours JSONB DEFAULT '{}',\n  delivery_fee INTEGER DEFAULT 49,\n  estimated_time VARCHAR(255) DEFAULT '25-35 min'\n);\n\n-- Categories Table\nCREATE TABLE IF NOT EXISTS categories (\n  id SERIAL PRIMARY KEY,\n  name VARCHAR(255) NOT NULL,\n  image_url TEXT\n);\n\n-- Menu Items Table\nCREATE TABLE IF NOT EXISTS menu_items (\n  id SERIAL PRIMARY KEY,\n  name VARCHAR(255) NOT NULL,\n  description TEXT,\n  price INTEGER NOT NULL,\n  image_url TEXT,\n  category_id INTEGER REFERENCES categories(id) ON DELETE SET NULL,\n  available BOOLEAN DEFAULT true\n);\n\n-- Orders Table\nCREATE TABLE IF NOT EXISTS orders (\n  id SERIAL PRIMARY KEY,\n  customer JSONB NOT NULL,\n  items JSONB NOT NULL,\n  subtotal INTEGER NOT NULL,\n  delivery_fee INTEGER NOT NULL,\n  total INTEGER NOT NULL,\n  status VARCHAR(50) DEFAULT 'pending',\n  payment_method VARCHAR(50) NOT NULL,\n  notes TEXT,\n  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP\n);\n\n-- Insert sample data for restaurant settings\nINSERT INTO restaurant_settings (restaurant_open, business_hours, delivery_fee, estimated_time)\nVALUES (\n  true,\n  '{\n    \"monday\": {\"open\": \"10:00\", \"close\": \"22:00\", \"delivery\": true},\n    \"tuesday\": {\"open\": \"10:00\", \"close\": \"22:00\", \"delivery\": true},\n    \"wednesday\": {\"open\": \"10:00\", \"close\": \"22:00\", \"delivery\": true},\n    \"thursday\": {\"open\": \"10:00\", \"close\": \"22:00\", \"delivery\": true},\n    \"friday\": {\"open\": \"10:00\", \"close\": \"23:00\", \"delivery\": true},\n    \"saturday\": {\"open\": \"10:00\", \"close\": \"23:00\", \"delivery\": true},\n    \"sunday\": {\"open\": \"12:00\", \"close\": \"21:00\", \"delivery\": true}\n  }',\n  49,\n  '25-35 min'\n) ON CONFLICT (id) DO NOTHING;\n\n-- Insert sample categories\nINSERT INTO categories (name, image_url)\nVALUES\n  ('Starters', 'https://images.unsplash.com/photo-1541745537411-b8046dc6d66c?q=80&w=2788&auto=format&fit=crop'),\n  ('Main Courses', 'https://images.unsplash.com/photo-1544025162-d76694265947?q=80&w=2788&auto=format&fit=crop'),\n  ('BBQ Specials', 'https://images.unsplash.com/photo-1529193591184-b1d58069ecdd?q=80&w=2790&auto=format&fit=crop'),\n  ('Sides', 'https://images.unsplash.com/photo-1618040521538-e0cfee99a9aa?q=80&w=2940&auto=format&fit=crop'),\n  ('Desserts', 'https://images.unsplash.com/photo-1551024601-bec78aea704b?q=80&w=2864&auto=format&fit=crop'),\n  ('Drinks', 'https://images.unsplash.com/photo-1551538827-9c037cb4f32a?q=80&w=2832&auto=format&fit=crop')\nON CONFLICT DO NOTHING;\n\n-- Insert sample menu items\nINSERT INTO menu_items (name, description, price, image_url, category_id, available)\nVALUES\n  ('BBQ Chicken Wings', 'Juicy chicken wings marinated in our signature BBQ sauce', 129, 'https://images.unsplash.com/photo-1608039755401-742074f0548d?q=80&w=2960&auto=format&fit=crop', 1, true),\n  ('Loaded Nachos', 'Crispy nachos with melted cheese, jalapeños, guacamole, and sour cream', 119, 'https://images.unsplash.com/photo-1582169296194-e4d644c48063?q=80&w=2800&auto=format&fit=crop', 1, true),\n  ('Smoked Brisket', 'Slow-smoked beef brisket with our house rub, served with coleslaw and cornbread', 249, 'https://images.unsplash.com/photo-1529193591184-b1d58069ecdd?q=80&w=2790&auto=format&fit=crop', 3, true),\n  ('BBQ Ribs Platter', 'Fall-off-the-bone pork ribs with our signature sauce, served with fries and coleslaw', 229, 'https://images.unsplash.com/photo-1544025162-d76694265947?q=80&w=2788&auto=format&fit=crop', 3, true),\n  ('Grilled Salmon', 'Fresh Atlantic salmon with lemon herb butter, served with mashed potatoes and grilled asparagus', 239, 'https://images.unsplash.com/photo-1519708227418-c8fd9a32b7a2?q=80&w=2070&auto=format&fit=crop', 2, true),\n  ('Barbecue Burger', 'Juicy beef patty with cheddar, bacon, onion rings, and our signature BBQ sauce', 189, 'https://images.unsplash.com/photo-1568901346375-23c9450c58cd?q=80&w=2899&auto=format&fit=crop', 2, true),\n  ('Mac & Cheese', 'Creamy three-cheese blend with a crispy breadcrumb topping', 79, 'https://images.unsplash.com/photo-1543352634-a1c51d9f1fa7?q=80&w=2940&auto=format&fit=crop', 4, true),\n  ('Sweet Potato Fries', 'Crispy sweet potato fries with chipotle mayo dip', 69, 'https://images.unsplash.com/photo-1604152135912-04a022e73fba?q=80&w=2987&auto=format&fit=crop', 4, true),\n  ('New York Cheesecake', 'Creamy cheesecake with berry compote', 99, 'https://images.unsplash.com/photo-1611293388250-580b08c4a145?q=80&w=2940&auto=format&fit=crop', 5, true),\n  ('Chocolate Lava Cake', 'Warm chocolate cake with a molten center, served with vanilla ice cream', 109, 'https://images.unsplash.com/photo-1606313564200-e75d8e3aabc3?q=80&w=2942&auto=format&fit=crop', 5, true),\n  ('Craft Beer', 'Selection of local craft beers', 89, 'https://images.unsplash.com/photo-1600788886242-5c96aabe3757?q=80&w=2787&auto=format&fit=crop', 6, true),\n  ('Signature Cocktails', 'Ask your server for our seasonal cocktail options', 119, 'https://images.unsplash.com/photo-1551024601-bec78aea704b?q=80&w=2864&auto=format&fit=crop', 6, true)\nON CONFLICT DO NOTHING;\n\n-- Insert sample orders\nINSERT INTO orders (customer, items, subtotal, delivery_fee, total, status, payment_method, notes)\nVALUES\n  (\n    '{\"firstName\": \"John\", \"lastName\": \"Doe\", \"email\": \"<EMAIL>\", \"phone\": \"12345678\", \"address\": \"123 Main St\", \"postalCode\": \"0001\", \"city\": \"Oslo\"}',\n    '[{\"id\": 3, \"name\": \"Smoked Brisket\", \"price\": 249, \"quantity\": 1}, {\"id\": 7, \"name\": \"Mac & Cheese\", \"price\": 79, \"quantity\": 1}]',\n    328,\n    49,\n    377,\n    'delivered',\n    'card',\n    'Please include extra sauce'\n  ),\n  (\n    '{\"firstName\": \"Jane\", \"lastName\": \"Smith\", \"email\": \"<EMAIL>\", \"phone\": \"87654321\", \"address\": \"456 Oak Ave\", \"postalCode\": \"0002\", \"city\": \"Oslo\"}',\n    '[{\"id\": 4, \"name\": \"BBQ Ribs Platter\", \"price\": 229, \"quantity\": 2}, {\"id\": 8, \"name\": \"Sweet Potato Fries\", \"price\": 69, \"quantity\": 1}]',\n    527,\n    49,\n    576,\n    'delivered',\n    'card',\n    NULL\n  ),\n  (\n    '{\"firstName\": \"Mike\", \"lastName\": \"Johnson\", \"email\": \"<EMAIL>\", \"phone\": \"55554444\", \"address\": \"789 Pine St\", \"postalCode\": \"0003\", \"city\": \"Oslo\"}',\n    '[{\"id\": 6, \"name\": \"Barbecue Burger\", \"price\": 189, \"quantity\": 1}, {\"id\": 11, \"name\": \"Craft Beer\", \"price\": 89, \"quantity\": 1}]',\n    278,\n    49,\n    327,\n    'in-progress',\n    'cash',\n    'Ring the bell when you arrive'\n  ),\n  (\n    '{\"firstName\": \"Sarah\", \"lastName\": \"Williams\", \"email\": \"<EMAIL>\", \"phone\": \"33332222\", \"address\": \"101 Maple Dr\", \"postalCode\": \"0004\", \"city\": \"Oslo\"}',\n    '[{\"id\": 1, \"name\": \"BBQ Chicken Wings\", \"price\": 129, \"quantity\": 1}, {\"id\": 2, \"name\": \"Loaded Nachos\", \"price\": 119, \"quantity\": 1}, {\"id\": 10, \"name\": \"Chocolate Lava Cake\", \"price\": 109, \"quantity\": 1}]',\n    357,\n    49,\n    406,\n    'pending',\n    'card',\n    NULL\n  )\nON CONFLICT DO NOTHING;\n\n-- Users Table for Authentication\nCREATE TABLE IF NOT EXISTS users (\n  id SERIAL PRIMARY KEY,\n  username VARCHAR(50) NOT NULL UNIQUE,\n  email VARCHAR(100) NOT NULL UNIQUE,\n  password VARCHAR(255) NOT NULL,\n  first_name VARCHAR(100),\n  last_name VARCHAR(100),\n  role VARCHAR(20) NOT NULL DEFAULT 'customer',\n  is_active BOOLEAN NOT NULL DEFAULT true,\n  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,\n  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP\n);\n\n-- Insert staff users with secure passwords\n-- Note: These passwords are hashed versions of secure passwords\nINSERT INTO users (username, email, password, first_name, last_name, role, is_active)\nVALUES\n  ('admin', '<EMAIL>', '$2b$10$rMb4F.1Vd1UQF6QaJZJCm.dSWEJZJ1XGxwXv7jDsQ5Oa7H.6DMTVi', 'Admin', 'User', 'admin', true),\n  ('manager', '<EMAIL>', '$2b$10$rMb4F.1Vd1UQF6QaJZJCm.dSWEJZJ1XGxwXv7jDsQ5Oa7H.6DMTVi', 'Kitchen', 'Manager', 'manager', true),\n  ('driver', '<EMAIL>', '$2b$10$rMb4F.1Vd1UQF6QaJZJCm.dSWEJZJ1XGxwXv7jDsQ5Oa7H.6DMTVi', 'Delivery', 'Driver', 'driver', true)\nON CONFLICT (username) DO NOTHING;"}