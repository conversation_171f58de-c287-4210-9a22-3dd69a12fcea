{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/MenuItemCard.tsx"}, "originalCode": "import { useState } from \"react\";\nimport { Dish } from \"@shared/schema\";\nimport { formatCurrency } from \"@/lib/utils\";\nimport { motion } from \"framer-motion\";\n\ninterface MenuItemCardProps {\n  item: Dish;\n  onCustomize: () => void;\n}\n\nconst MenuItemCard = ({ item, onCustomize }: MenuItemCardProps) => {\n  const [isHovered, setIsHovered] = useState(false);\n\n  // Get dynamic neon colors based on item categories\n  const getNeonColor = () => {\n    // Map categories to colors (with Dish.category backward compatibility)\n    const category = item.category || '';\n\n    if (category.includes('BBQ') || category.includes('Signature'))\n      return {\n        ring: 'ring-cyan-500',\n        glow: 'shadow-[0_0_15px_rgba(0,255,255,0.5)]',\n        textGlow: 'text-cyan-400',\n        gradientFrom: 'from-cyan-500',\n        gradientTo: 'to-blue-700'\n      };\n    else if (category.includes('Starter'))\n      return {\n        ring: 'ring-lime-500',\n        glow: 'shadow-[0_0_15px_rgba(57,255,20,0.5)]',\n        textGlow: 'text-lime-400',\n        gradientFrom: 'from-lime-500',\n        gradientTo: 'to-emerald-700'\n      };\n    else if (category.includes('Dessert'))\n      return {\n        ring: 'ring-fuchsia-500',\n        glow: 'shadow-[0_0_15px_rgba(255,0,255,0.5)]',\n        textGlow: 'text-fuchsia-400',\n        gradientFrom: 'from-fuchsia-500',\n        gradientTo: 'to-purple-700'\n      };\n\n    // Default color scheme\n    return {\n      ring: 'ring-fuchsia-500',\n      glow: 'shadow-[0_0_15px_rgba(255,0,255,0.5)]',\n      textGlow: 'text-fuchsia-400',\n      gradientFrom: 'from-fuchsia-500',\n      gradientTo: 'to-purple-700'\n    };\n  };\n\n  const colors = getNeonColor();\n\n  return (\n    <motion.div\n      className={`bg-black/30 backdrop-blur-sm rounded-xl overflow-hidden\n                border border-gray-800 h-full group\n                hover:border-transparent transition-all duration-300\n                ${isHovered ? `ring-1 ${colors.ring} ${colors.glow}` : ''}`}\n      initial={{ opacity: 0, y: 20 }}\n      animate={{ opacity: 1, y: 0 }}\n      whileHover={{\n        y: -5,\n        transition: { duration: 0.2 }\n      }}\n      onMouseEnter={() => setIsHovered(true)}\n      onMouseLeave={() => setIsHovered(false)}\n    >\n      {/* Image Container with animated overlay */}\n      <div className=\"h-48 overflow-hidden relative\">\n        {/* Gradient Overlay */}\n        <motion.div\n          className={`absolute inset-0 bg-gradient-to-t ${colors.gradientFrom} ${colors.gradientTo} z-10 opacity-0\n                     transition-opacity duration-300 group-hover:opacity-30`}\n        />\n\n        {/* Image with zoom effect */}\n        <motion.img\n          src={item.imageUrl}\n          alt={item.name}\n          className=\"w-full h-full object-cover\"\n          animate={isHovered ? { scale: 1.1 } : { scale: 1 }}\n          transition={{ duration: 0.4 }}\n        />\n\n        {/* Price tag with animation */}\n        <motion.div\n          className={`absolute top-3 right-3 z-20 px-3 py-1.5 rounded-full\n                     bg-black/60 backdrop-blur-sm border border-gray-700\n                     font-bold text-sm ${colors.textGlow}`}\n          animate={isHovered ? {\n            y: [0, -2, 0],\n            boxShadow: [\n              \"0 0 0 rgba(0,0,0,0)\",\n              `0 0 8px ${colors.textGlow.replace('text-', 'rgba(')}`,\n              \"0 0 0 rgba(0,0,0,0)\",\n            ]\n          } : {}}\n          transition={{ duration: 1.5, repeat: isHovered ? Infinity : 0, repeatType: 'reverse' }}\n        >\n          {formatCurrency(item.price)}\n        </motion.div>\n      </div>\n\n      {/* Content */}\n      <div className=\"p-5 relative z-10\">\n        {/* Name with animated underline */}\n        <div className=\"mb-3 pb-1 relative\">\n          <h3 className=\"font-playfair text-xl font-bold text-white group-hover:text-transparent\n                         group-hover:bg-clip-text group-hover:bg-gradient-to-r\n                         group-hover:from-white group-hover:to-gray-300 transition-all duration-500\">\n            {item.name}\n          </h3>\n\n          {/* Animated neon underline */}\n          <motion.div\n            className={`absolute bottom-0 left-0 h-px bg-gradient-to-r ${colors.gradientFrom} ${colors.gradientTo} rounded-full`}\n            initial={{ width: \"0%\" }}\n            animate={isHovered ? { width: \"100%\" } : { width: \"0%\" }}\n            transition={{ duration: 0.3 }}\n          />\n        </div>\n\n        {/* Description */}\n        <p className=\"font-poppins text-gray-400 text-sm mb-4 line-clamp-2\">{item.description}</p>\n\n        {/* Bottom section */}\n        <div className=\"flex justify-between items-center\">\n          <div className=\"flex items-center\">\n            <div className=\"flex space-x-0.5\">\n              {[...Array(5)].map((_, i) => (\n                <motion.span\n                  key={i}\n                  className={`text-sm ${i < Math.round((item.rating || 0) / 20) ? 'text-yellow-400' : 'text-gray-600'}`}\n                  animate={isHovered && i < Math.round((item.rating || 0) / 20) ? {\n                    scale: [1, 1.2, 1],\n                    rotate: [-5, 5, 0],\n                  } : {}}\n                  transition={{\n                    duration: 0.3,\n                    delay: i * 0.1,\n                    repeat: isHovered ? Infinity : 0,\n                    repeatDelay: 2\n                  }}\n                >\n                  <svg\n                    xmlns=\"http://www.w3.org/2000/svg\"\n                    className=\"h-4 w-4\"\n                    viewBox=\"0 0 20 20\"\n                    fill=\"currentColor\"\n                  >\n                    <path d=\"M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z\" />\n                  </svg>\n                </motion.span>\n              ))}\n            </div>\n            <span className=\"font-poppins text-xs text-gray-400 ml-1.5\">\n              ({item.reviews || 0})\n            </span>\n          </div>\n\n          {/* Customize button with animation */}\n          <motion.button\n            onClick={onCustomize}\n            className={`relative overflow-hidden rounded-md px-4 py-1.5 text-sm font-medium\n                     bg-transparent border border-gray-700 text-white transition-all\n                     hover:border-transparent`}\n            whileHover={{ scale: 1.05 }}\n            whileTap={{ scale: 0.95 }}\n          >\n            {/* Button glow on hover */}\n            <motion.span\n              className={`absolute inset-0 w-full h-full bg-gradient-to-r\n                        ${colors.gradientFrom} ${colors.gradientTo} opacity-0\n                        transition-opacity duration-300 group-hover:opacity-100`}\n              initial={{ opacity: 0 }}\n              animate={isHovered ? { opacity: 1 } : { opacity: 0 }}\n            />\n\n            <span className=\"relative z-10 flex items-center space-x-1\">\n              <span>Customize</span>\n              <motion.span\n                animate={isHovered ? { x: [0, 3, 0] } : {}}\n                transition={{ duration: 1, repeat: isHovered ? Infinity : 0 }}\n              >\n                <svg\n                  xmlns=\"http://www.w3.org/2000/svg\"\n                  className=\"h-3.5 w-3.5\"\n                  viewBox=\"0 0 20 20\"\n                  fill=\"currentColor\"\n                >\n                  <path fillRule=\"evenodd\" d=\"M10.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L12.586 11H5a1 1 0 110-2h7.586l-2.293-2.293a1 1 0 010-1.414z\" clipRule=\"evenodd\" />\n                </svg>\n              </motion.span>\n            </span>\n          </motion.button>\n        </div>\n      </div>\n    </motion.div>\n  );\n};\n\nexport default MenuItemCard;", "modifiedCode": "import { useState } from \"react\";\nimport { Dish } from \"@shared/schema\";\nimport { formatCurrency } from \"@/lib/utils\";\nimport { motion } from \"framer-motion\";\n\ninterface MenuItemCardProps {\n  item: Dish;\n  onCustomize: () => void;\n}\n\nconst MenuItemCard = ({ item, onCustomize }: MenuItemCardProps) => {\n  const [isHovered, setIsHovered] = useState(false);\n\n  // Get dynamic neon colors based on item categories\n  const getNeonColor = () => {\n    // Get category name from different possible properties\n    const categoryName = (item as any).category || '';\n    // Get category ID from different possible properties\n    const categoryId = (item as any).categoryId || (item as any).category_id;\n\n    // Try to determine category by name or ID\n    if (categoryName.includes('BBQ') || categoryName.includes('Signature') || categoryId === 1)\n      return {\n        ring: 'ring-cyan-500',\n        glow: 'shadow-[0_0_15px_rgba(0,255,255,0.5)]',\n        textGlow: 'text-cyan-400',\n        gradientFrom: 'from-cyan-500',\n        gradientTo: 'to-blue-700'\n      };\n    else if (categoryName.includes('Starter') || categoryId === 2)\n      return {\n        ring: 'ring-lime-500',\n        glow: 'shadow-[0_0_15px_rgba(57,255,20,0.5)]',\n        textGlow: 'text-lime-400',\n        gradientFrom: 'from-lime-500',\n        gradientTo: 'to-emerald-700'\n      };\n    else if (categoryName.includes('Dessert') || categoryId === 4)\n      return {\n        ring: 'ring-fuchsia-500',\n        glow: 'shadow-[0_0_15px_rgba(255,0,255,0.5)]',\n        textGlow: 'text-fuchsia-400',\n        gradientFrom: 'from-fuchsia-500',\n        gradientTo: 'to-purple-700'\n      };\n\n    // Default color scheme\n    return {\n      ring: 'ring-fuchsia-500',\n      glow: 'shadow-[0_0_15px_rgba(255,0,255,0.5)]',\n      textGlow: 'text-fuchsia-400',\n      gradientFrom: 'from-fuchsia-500',\n      gradientTo: 'to-purple-700'\n    };\n  };\n\n  const colors = getNeonColor();\n\n  return (\n    <motion.div\n      className={`bg-black/30 backdrop-blur-sm rounded-xl overflow-hidden\n                border border-gray-800 h-full group\n                hover:border-transparent transition-all duration-300\n                ${isHovered ? `ring-1 ${colors.ring} ${colors.glow}` : ''}`}\n      initial={{ opacity: 0, y: 20 }}\n      animate={{ opacity: 1, y: 0 }}\n      whileHover={{\n        y: -5,\n        transition: { duration: 0.2 }\n      }}\n      onMouseEnter={() => setIsHovered(true)}\n      onMouseLeave={() => setIsHovered(false)}\n    >\n      {/* Image Container with animated overlay */}\n      <div className=\"h-48 overflow-hidden relative\">\n        {/* Gradient Overlay */}\n        <motion.div\n          className={`absolute inset-0 bg-gradient-to-t ${colors.gradientFrom} ${colors.gradientTo} z-10 opacity-0\n                     transition-opacity duration-300 group-hover:opacity-30`}\n        />\n\n        {/* Image with zoom effect */}\n        <motion.img\n          src={item.imageUrl}\n          alt={item.name}\n          className=\"w-full h-full object-cover\"\n          animate={isHovered ? { scale: 1.1 } : { scale: 1 }}\n          transition={{ duration: 0.4 }}\n        />\n\n        {/* Price tag with animation */}\n        <motion.div\n          className={`absolute top-3 right-3 z-20 px-3 py-1.5 rounded-full\n                     bg-black/60 backdrop-blur-sm border border-gray-700\n                     font-bold text-sm ${colors.textGlow}`}\n          animate={isHovered ? {\n            y: [0, -2, 0],\n            boxShadow: [\n              \"0 0 0 rgba(0,0,0,0)\",\n              `0 0 8px ${colors.textGlow.replace('text-', 'rgba(')}`,\n              \"0 0 0 rgba(0,0,0,0)\",\n            ]\n          } : {}}\n          transition={{ duration: 1.5, repeat: isHovered ? Infinity : 0, repeatType: 'reverse' }}\n        >\n          {formatCurrency(item.price)}\n        </motion.div>\n      </div>\n\n      {/* Content */}\n      <div className=\"p-5 relative z-10\">\n        {/* Name with animated underline */}\n        <div className=\"mb-3 pb-1 relative\">\n          <h3 className=\"font-playfair text-xl font-bold text-white group-hover:text-transparent\n                         group-hover:bg-clip-text group-hover:bg-gradient-to-r\n                         group-hover:from-white group-hover:to-gray-300 transition-all duration-500\">\n            {item.name}\n          </h3>\n\n          {/* Animated neon underline */}\n          <motion.div\n            className={`absolute bottom-0 left-0 h-px bg-gradient-to-r ${colors.gradientFrom} ${colors.gradientTo} rounded-full`}\n            initial={{ width: \"0%\" }}\n            animate={isHovered ? { width: \"100%\" } : { width: \"0%\" }}\n            transition={{ duration: 0.3 }}\n          />\n        </div>\n\n        {/* Description */}\n        <p className=\"font-poppins text-gray-400 text-sm mb-4 line-clamp-2\">{item.description}</p>\n\n        {/* Bottom section */}\n        <div className=\"flex justify-between items-center\">\n          <div className=\"flex items-center\">\n            <div className=\"flex space-x-0.5\">\n              {[...Array(5)].map((_, i) => (\n                <motion.span\n                  key={i}\n                  className={`text-sm ${i < Math.round((item.rating || 0) / 20) ? 'text-yellow-400' : 'text-gray-600'}`}\n                  animate={isHovered && i < Math.round((item.rating || 0) / 20) ? {\n                    scale: [1, 1.2, 1],\n                    rotate: [-5, 5, 0],\n                  } : {}}\n                  transition={{\n                    duration: 0.3,\n                    delay: i * 0.1,\n                    repeat: isHovered ? Infinity : 0,\n                    repeatDelay: 2\n                  }}\n                >\n                  <svg\n                    xmlns=\"http://www.w3.org/2000/svg\"\n                    className=\"h-4 w-4\"\n                    viewBox=\"0 0 20 20\"\n                    fill=\"currentColor\"\n                  >\n                    <path d=\"M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z\" />\n                  </svg>\n                </motion.span>\n              ))}\n            </div>\n            <span className=\"font-poppins text-xs text-gray-400 ml-1.5\">\n              ({item.reviews || 0})\n            </span>\n          </div>\n\n          {/* Customize button with animation */}\n          <motion.button\n            onClick={onCustomize}\n            className={`relative overflow-hidden rounded-md px-4 py-1.5 text-sm font-medium\n                     bg-transparent border border-gray-700 text-white transition-all\n                     hover:border-transparent`}\n            whileHover={{ scale: 1.05 }}\n            whileTap={{ scale: 0.95 }}\n          >\n            {/* Button glow on hover */}\n            <motion.span\n              className={`absolute inset-0 w-full h-full bg-gradient-to-r\n                        ${colors.gradientFrom} ${colors.gradientTo} opacity-0\n                        transition-opacity duration-300 group-hover:opacity-100`}\n              initial={{ opacity: 0 }}\n              animate={isHovered ? { opacity: 1 } : { opacity: 0 }}\n            />\n\n            <span className=\"relative z-10 flex items-center space-x-1\">\n              <span>Customize</span>\n              <motion.span\n                animate={isHovered ? { x: [0, 3, 0] } : {}}\n                transition={{ duration: 1, repeat: isHovered ? Infinity : 0 }}\n              >\n                <svg\n                  xmlns=\"http://www.w3.org/2000/svg\"\n                  className=\"h-3.5 w-3.5\"\n                  viewBox=\"0 0 20 20\"\n                  fill=\"currentColor\"\n                >\n                  <path fillRule=\"evenodd\" d=\"M10.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L12.586 11H5a1 1 0 110-2h7.586l-2.293-2.293a1 1 0 010-1.414z\" clipRule=\"evenodd\" />\n                </svg>\n              </motion.span>\n            </span>\n          </motion.button>\n        </div>\n      </div>\n    </motion.div>\n  );\n};\n\nexport default MenuItemCard;"}