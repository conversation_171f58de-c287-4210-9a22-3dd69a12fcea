{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "server/auth/auth-routes.ts"}, "modifiedCode": "import { Router, Request, Response } from 'express';\nimport { db } from '../db';\nimport { users } from '@shared/schema';\nimport { eq } from 'drizzle-orm';\nimport bcrypt from 'bcrypt';\n\nconst router = Router();\n\n// Login route\nrouter.post('/login', async (req: Request, res: Response) => {\n  try {\n    const { username, password } = req.body;\n\n    if (!username || !password) {\n      return res.status(400).json({ message: 'Username and password are required' });\n    }\n\n    // Find user by username\n    const user = await db.query.users.findFirst({\n      where: eq(users.username, username)\n    });\n\n    if (!user) {\n      return res.status(401).json({ message: 'Invalid username or password' });\n    }\n\n    // Check if user is active\n    if (user.is_active === false) {\n      return res.status(401).json({ message: 'Account is disabled' });\n    }\n\n    // Verify password\n    // For the demo, we're using a fixed password comparison since we're using a hashed password in the schema\n    // In a real application, you would use bcrypt.compare(password, user.password)\n    const isPasswordValid = await bcrypt.compare(password, user.password);\n    \n    if (!isPasswordValid) {\n      return res.status(401).json({ message: 'Invalid username or password' });\n    }\n\n    // Return user data (excluding password)\n    const { password: _, ...userData } = user;\n    \n    return res.status(200).json({\n      id: userData.id,\n      username: userData.username,\n      role: userData.role,\n      firstName: userData.first_name,\n      lastName: userData.last_name\n    });\n  } catch (error) {\n    console.error('Login error:', error);\n    return res.status(500).json({ message: 'An error occurred during login' });\n  }\n});\n\nexport default router;\n"}