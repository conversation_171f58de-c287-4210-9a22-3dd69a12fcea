{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/api/adminApi.ts"}, "originalCode": "// Base URL for API\nconst API_URL = '/api';\n// Base URL for admin-specific API\nconst ADMIN_API_URL = '/api/admin';\n\nconst fetchWrapper = async (url: string, options: RequestInit = {}) => {\n  const response = await fetch(url, options);\n\n  if (!response.ok) {\n    throw new Error(`API request failed: ${response.statusText}`);\n  }\n\n  return await response.json();\n};\n\n// Types\nexport interface AdminSettings {\n  id: number;\n  restaurant_open: boolean;\n  business_hours: Record<string, { open: string; close: string; delivery: boolean }>;\n  delivery_fee: number;\n  estimated_time: string;\n}\n\nexport interface Category {\n  id: number;\n  name: string;\n  image_url: string;\n}\n\nexport interface MenuItem {\n  id: number;\n  name: string;\n  description: string;\n  price: number;\n  image_url: string;\n  category_id: number;\n  available: boolean;\n  category_name?: string;\n}\n\nexport interface AnalyticsSummary {\n  today: number;\n  week: number;\n  month: number;\n  orderCount: number;\n}\n\nexport interface DailyRevenue {\n  date: string;\n  total: number;\n}\n\nexport interface CategoryRevenue {\n  category: string;\n  total: number;\n}\n\n// Admin Settings API\nexport const getAdminSettings = async (): Promise<AdminSettings> => {\n  return await fetchWrapper(`${ADMIN_API_URL}/admin/settings`);\n};\n\nexport const updateAdminSettings = async (settings: Partial<AdminSettings>): Promise<AdminSettings> => {\n  return await fetchWrapper(`${ADMIN_API_URL}/admin/settings`, {\n    method: 'PUT',\n    body: JSON.stringify(settings),\n    headers: {\n      'Content-Type': 'application/json',\n    },\n  });\n};\n\n// Categories API\nexport const getCategories = async (): Promise<Category[]> => {\n  return await fetchWrapper(`${API_URL}/categories`);\n};\n\nexport const createCategory = async (category: Omit<Category, 'id'>): Promise<Category> => {\n  return await fetchWrapper(`${API_URL}/categories`, {\n    method: 'POST',\n    body: JSON.stringify(category),\n    headers: {\n      'Content-Type': 'application/json',\n    },\n  });\n};\n\nexport const updateCategory = async (id: number, category: Partial<Category>): Promise<Category> => {\n  return await fetchWrapper(`${API_URL}/categories/${id}`, {\n    method: 'PUT',\n    body: JSON.stringify(category),\n    headers: {\n      'Content-Type': 'application/json',\n    },\n  });\n};\n\nexport const deleteCategory = async (id: number): Promise<{ message: string; category: Category }> => {\n  return await fetchWrapper(`${API_URL}/categories/${id}`, {\n    method: 'DELETE',\n  });\n};\n\n// Menu Items API\nexport const getMenuItems = async (): Promise<MenuItem[]> => {\n  return await fetchWrapper(`${ADMIN_API_URL}/items`);\n};\n\nexport const createMenuItem = async (item: Omit<MenuItem, 'id'>): Promise<MenuItem> => {\n  return await fetchWrapper(`${ADMIN_API_URL}/items`, {\n    method: 'POST',\n    body: JSON.stringify(item),\n    headers: {\n      'Content-Type': 'application/json',\n    },\n  });\n};\n\nexport const updateMenuItem = async (id: number, item: Partial<MenuItem>): Promise<MenuItem> => {\n  return await fetchWrapper(`${ADMIN_API_URL}/items/${id}`, {\n    method: 'PUT',\n    body: JSON.stringify(item),\n    headers: {\n      'Content-Type': 'application/json',\n    },\n  });\n};\n\nexport const deleteMenuItem = async (id: number): Promise<{ message: string; item: MenuItem }> => {\n  return await fetchWrapper(`${ADMIN_API_URL}/items/${id}`, {\n    method: 'DELETE',\n  });\n};\n\n// Analytics API\nexport const getAnalyticsSummary = async (): Promise<AnalyticsSummary> => {\n  return await fetchWrapper(`${ADMIN_API_URL}/analytics/summary`);\n};\n\nexport const getDailyRevenue = async (): Promise<DailyRevenue[]> => {\n  return await fetchWrapper(`${ADMIN_API_URL}/analytics/daily`);\n};\n\nexport const getCategoryRevenue = async (): Promise<CategoryRevenue[]> => {\n  return await fetchWrapper(`${ADMIN_API_URL}/analytics/by-category`);\n};", "modifiedCode": "// Base URL for API\nconst API_URL = '/api';\n// Base URL for admin-specific API\nconst ADMIN_API_URL = '/api/admin';\n\nconst fetchWrapper = async (url: string, options: RequestInit = {}) => {\n  const response = await fetch(url, options);\n\n  if (!response.ok) {\n    throw new Error(`API request failed: ${response.statusText}`);\n  }\n\n  return await response.json();\n};\n\n// Types\nexport interface AdminSettings {\n  id: number;\n  restaurant_open: boolean;\n  business_hours: Record<string, { open: string; close: string; delivery: boolean }>;\n  delivery_fee: number;\n  estimated_time: string;\n}\n\nexport interface Category {\n  id: number;\n  name: string;\n  image_url: string;\n}\n\nexport interface MenuItem {\n  id: number;\n  name: string;\n  description: string;\n  price: number;\n  image_url: string;\n  category_id: number;\n  available: boolean;\n  category_name?: string;\n}\n\nexport interface AnalyticsSummary {\n  today: number;\n  week: number;\n  month: number;\n  orderCount: number;\n}\n\nexport interface DailyRevenue {\n  date: string;\n  total: number;\n}\n\nexport interface CategoryRevenue {\n  category: string;\n  total: number;\n}\n\n// Admin Settings API\nexport const getAdminSettings = async (): Promise<AdminSettings> => {\n  return await fetchWrapper(`${ADMIN_API_URL}/admin/settings`);\n};\n\nexport const updateAdminSettings = async (settings: Partial<AdminSettings>): Promise<AdminSettings> => {\n  return await fetchWrapper(`${ADMIN_API_URL}/admin/settings`, {\n    method: 'PUT',\n    body: JSON.stringify(settings),\n    headers: {\n      'Content-Type': 'application/json',\n    },\n  });\n};\n\n// Categories API\nexport const getCategories = async (): Promise<Category[]> => {\n  return await fetchWrapper(`${API_URL}/categories`);\n};\n\nexport const createCategory = async (category: Omit<Category, 'id'>): Promise<Category> => {\n  return await fetchWrapper(`${API_URL}/categories`, {\n    method: 'POST',\n    body: JSON.stringify(category),\n    headers: {\n      'Content-Type': 'application/json',\n    },\n  });\n};\n\nexport const updateCategory = async (id: number, category: Partial<Category>): Promise<Category> => {\n  return await fetchWrapper(`${API_URL}/categories/${id}`, {\n    method: 'PUT',\n    body: JSON.stringify(category),\n    headers: {\n      'Content-Type': 'application/json',\n    },\n  });\n};\n\nexport const deleteCategory = async (id: number): Promise<{ message: string; category: Category }> => {\n  return await fetchWrapper(`${API_URL}/categories/${id}`, {\n    method: 'DELETE',\n  });\n};\n\n// Menu Items API\nexport const getMenuItems = async (): Promise<MenuItem[]> => {\n  return await fetchWrapper(`${API_URL}/items`);\n};\n\nexport const createMenuItem = async (item: Omit<MenuItem, 'id'>): Promise<MenuItem> => {\n  return await fetchWrapper(`${API_URL}/items`, {\n    method: 'POST',\n    body: JSON.stringify(item),\n    headers: {\n      'Content-Type': 'application/json',\n    },\n  });\n};\n\nexport const updateMenuItem = async (id: number, item: Partial<MenuItem>): Promise<MenuItem> => {\n  return await fetchWrapper(`${API_URL}/items/${id}`, {\n    method: 'PUT',\n    body: JSON.stringify(item),\n    headers: {\n      'Content-Type': 'application/json',\n    },\n  });\n};\n\nexport const deleteMenuItem = async (id: number): Promise<{ message: string; item: MenuItem }> => {\n  return await fetchWrapper(`${API_URL}/items/${id}`, {\n    method: 'DELETE',\n  });\n};\n\n// Analytics API\nexport const getAnalyticsSummary = async (): Promise<AnalyticsSummary> => {\n  return await fetchWrapper(`${ADMIN_API_URL}/analytics/summary`);\n};\n\nexport const getDailyRevenue = async (): Promise<DailyRevenue[]> => {\n  return await fetchWrapper(`${ADMIN_API_URL}/analytics/daily`);\n};\n\nexport const getCategoryRevenue = async (): Promise<CategoryRevenue[]> => {\n  return await fetchWrapper(`${ADMIN_API_URL}/analytics/by-category`);\n};"}