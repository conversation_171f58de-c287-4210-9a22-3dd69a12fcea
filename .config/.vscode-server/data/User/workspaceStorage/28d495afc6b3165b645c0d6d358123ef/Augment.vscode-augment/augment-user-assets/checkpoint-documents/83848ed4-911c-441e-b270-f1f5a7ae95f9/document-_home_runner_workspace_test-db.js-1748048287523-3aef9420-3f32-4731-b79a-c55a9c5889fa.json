{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "test-db.js"}, "originalCode": "import { Pool } from 'pg';\n\nasync function testDatabase() {\n  console.log('Starting database test...');\n  console.log('DATABASE_URL:', process.env.DATABASE_URL ? 'Set' : 'Not set');\n\n  const pool = new Pool({\n    connectionString: process.env.DATABASE_URL,\n  });\n\n  try {\n    // Check if restaurant_settings table exists\n    const tableCheck = await pool.query(`\n      SELECT EXISTS (\n        SELECT FROM information_schema.tables\n        WHERE table_schema = 'public'\n        AND table_name = 'restaurant_settings'\n      );\n    `);\n\n    console.log('Table exists:', tableCheck.rows[0].exists);\n\n    if (tableCheck.rows[0].exists) {\n      // Get current settings\n      const settings = await pool.query('SELECT * FROM restaurant_settings ORDER BY id');\n      console.log('Current settings:', JSON.stringify(settings.rows, null, 2));\n\n      // Try to update delivery fee\n      const updateResult = await pool.query(\n        'UPDATE restaurant_settings SET delivery_fee = $1 WHERE id = 1 RETURNING *',\n        [100]\n      );\n      console.log('Update result:', JSON.stringify(updateResult.rows, null, 2));\n\n      // Check settings again\n      const newSettings = await pool.query('SELECT * FROM restaurant_settings ORDER BY id');\n      console.log('Settings after update:', JSON.stringify(newSettings.rows, null, 2));\n    }\n  } catch (error) {\n    console.error('Database error:', error.message);\n  } finally {\n    await pool.end();\n  }\n}\n\ntestDatabase();\n"}