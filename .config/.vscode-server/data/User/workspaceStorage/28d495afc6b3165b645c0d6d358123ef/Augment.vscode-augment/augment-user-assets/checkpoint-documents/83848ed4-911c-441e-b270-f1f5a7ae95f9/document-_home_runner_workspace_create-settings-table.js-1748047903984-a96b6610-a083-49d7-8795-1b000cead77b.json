{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "create-settings-table.js"}, "modifiedCode": "import { Pool } from 'pg';\n\nasync function createSettingsTable() {\n  console.log('Creating restaurant_settings table...');\n  \n  const pool = new Pool({\n    connectionString: process.env.DATABASE_URL,\n  });\n\n  try {\n    // Create the table\n    await pool.query(`\n      CREATE TABLE IF NOT EXISTS restaurant_settings (\n        id SERIAL PRIMARY KEY,\n        restaurant_open BOOLEAN DEFAULT true,\n        business_hours JSONB DEFAULT '{}',\n        delivery_fee INTEGER DEFAULT 49,\n        estimated_time VARCHAR(255) DEFAULT '25-35 min'\n      );\n    `);\n    \n    console.log('Table created successfully');\n    \n    // Insert default settings if none exist\n    const result = await pool.query('SELECT COUNT(*) FROM restaurant_settings');\n    const count = parseInt(result.rows[0].count);\n    \n    if (count === 0) {\n      await pool.query(`\n        INSERT INTO restaurant_settings (restaurant_open, business_hours, delivery_fee, estimated_time)\n        VALUES (\n          true,\n          '{\n            \"monday\": {\"open\": \"10:00\", \"close\": \"22:00\", \"delivery\": true},\n            \"tuesday\": {\"open\": \"10:00\", \"close\": \"22:00\", \"delivery\": true},\n            \"wednesday\": {\"open\": \"10:00\", \"close\": \"22:00\", \"delivery\": true},\n            \"thursday\": {\"open\": \"10:00\", \"close\": \"22:00\", \"delivery\": true},\n            \"friday\": {\"open\": \"10:00\", \"close\": \"23:00\", \"delivery\": true},\n            \"saturday\": {\"open\": \"10:00\", \"close\": \"23:00\", \"delivery\": true},\n            \"sunday\": {\"open\": \"12:00\", \"close\": \"21:00\", \"delivery\": true}\n          }',\n          49,\n          '25-35 min'\n        );\n      `);\n      console.log('Default settings inserted');\n    } else {\n      console.log(`Found ${count} existing settings records`);\n    }\n    \n    // Show current settings\n    const settings = await pool.query('SELECT * FROM restaurant_settings ORDER BY id');\n    console.log('Current settings:', JSON.stringify(settings.rows, null, 2));\n    \n  } catch (error) {\n    console.error('Error:', error.message);\n  } finally {\n    await pool.end();\n  }\n}\n\ncreateSettingsTable();\n"}