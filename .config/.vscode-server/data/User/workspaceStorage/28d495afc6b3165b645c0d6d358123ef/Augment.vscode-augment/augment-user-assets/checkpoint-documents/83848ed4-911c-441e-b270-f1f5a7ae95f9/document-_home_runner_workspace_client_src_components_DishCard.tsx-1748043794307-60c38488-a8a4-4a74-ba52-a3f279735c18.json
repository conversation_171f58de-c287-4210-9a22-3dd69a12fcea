{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/DishCard.tsx"}, "originalCode": "import { useState } from 'react';\nimport { useCart } from '@/context/CartContext';\nimport { formatCurrency } from '@/lib/utils';\nimport { Dish } from '@shared/schema';\nimport Button from './Button';\n\ninterface DishCardProps {\n  dish: Dish;\n}\n\nconst DishCard = ({ dish }: DishCardProps) => {\n  const { addToCart } = useCart();\n  const [isAdding, setIsAdding] = useState(false);\n\n  const handleAddToCart = () => {\n    setIsAdding(true);\n\n    // Simulate a small delay for better UX\n    setTimeout(() => {\n      addToCart(dish);\n      setIsAdding(false);\n    }, 300);\n  };\n\n  return (\n    <div className=\"dish-card bg-gray-900 rounded-lg overflow-hidden shadow-lg border border-gray-800 hover:border-secondary transition duration-300 transform hover:-translate-y-1\">\n      <div className=\"relative h-56 overflow-hidden\">\n        <img\n          src={dish.imageUrl}\n          alt={dish.name}\n          className=\"w-full h-full object-cover\"\n        />\n      </div>\n\n      <div className=\"p-6\">\n        <div className=\"flex justify-between items-start mb-3\">\n          <h3 className=\"font-playfair text-xl font-bold text-white\">{dish.name}</h3>\n          <span className=\"font-poppins text-accent font-medium\">{formatCurrency(dish.price)}</span>\n        </div>\n\n        <p className=\"font-poppins text-gray-400 text-sm mb-4 line-clamp-3\">{dish.description}</p>\n\n        <div className=\"flex justify-between items-center\">\n          <div className=\"flex items-center\">\n            <span className=\"text-yellow-400 mr-1\"><i className=\"fas fa-star\"></i></span>\n            <span className=\"font-poppins text-sm text-gray-300\">\n              {dish.rating} ({dish.reviews})\n            </span>\n          </div>\n\n          <Button\n            variant=\"outline-secondary\"\n            size=\"sm\"\n            onClick={handleAddToCart}\n            isLoading={isAdding}\n          >\n            Add to Cart\n          </Button>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default DishCard;\n", "modifiedCode": "import { useState } from 'react';\nimport { useCart } from '@/context/CartContext';\nimport { useRestaurantStatus } from '@/context/RestaurantStatusContext';\nimport { formatCurrency } from '@/lib/utils';\nimport { Dish } from '@shared/schema';\nimport Button from './Button';\n\ninterface DishCardProps {\n  dish: Dish;\n}\n\nconst DishCard = ({ dish }: DishCardProps) => {\n  const { addToCart } = useCart();\n  const [isAdding, setIsAdding] = useState(false);\n\n  const handleAddToCart = () => {\n    setIsAdding(true);\n\n    // Simulate a small delay for better UX\n    setTimeout(() => {\n      addToCart(dish);\n      setIsAdding(false);\n    }, 300);\n  };\n\n  return (\n    <div className=\"dish-card bg-gray-900 rounded-lg overflow-hidden shadow-lg border border-gray-800 hover:border-secondary transition duration-300 transform hover:-translate-y-1\">\n      <div className=\"relative h-56 overflow-hidden\">\n        <img\n          src={dish.imageUrl}\n          alt={dish.name}\n          className=\"w-full h-full object-cover\"\n        />\n      </div>\n\n      <div className=\"p-6\">\n        <div className=\"flex justify-between items-start mb-3\">\n          <h3 className=\"font-playfair text-xl font-bold text-white\">{dish.name}</h3>\n          <span className=\"font-poppins text-accent font-medium\">{formatCurrency(dish.price)}</span>\n        </div>\n\n        <p className=\"font-poppins text-gray-400 text-sm mb-4 line-clamp-3\">{dish.description}</p>\n\n        <div className=\"flex justify-between items-center\">\n          <div className=\"flex items-center\">\n            <span className=\"text-yellow-400 mr-1\"><i className=\"fas fa-star\"></i></span>\n            <span className=\"font-poppins text-sm text-gray-300\">\n              {dish.rating} ({dish.reviews})\n            </span>\n          </div>\n\n          <Button\n            variant=\"outline-secondary\"\n            size=\"sm\"\n            onClick={handleAddToCart}\n            isLoading={isAdding}\n          >\n            Add to Cart\n          </Button>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default DishCard;\n"}