{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "test-customizations.js"}, "originalCode": "// Simple test script to test customization API endpoints\nconst fetch = require('node-fetch');\n\nconst BASE_URL = 'http://localhost:5000';\n\nasync function testCustomizationAPI() {\n  try {\n    console.log('Testing customization API endpoints...');\n\n    // Test getting customization groups\n    console.log('\\n1. Testing GET /api/admin/customization-groups');\n    const groupsResponse = await fetch(`${BASE_URL}/api/admin/customization-groups`);\n    console.log('Status:', groupsResponse.status);\n\n    if (groupsResponse.ok) {\n      const groups = await groupsResponse.json();\n      console.log('Groups:', groups);\n    } else {\n      const error = await groupsResponse.text();\n      console.log('Error:', error);\n    }\n\n    // Test getting customization options\n    console.log('\\n2. Testing GET /api/admin/customization-options');\n    const optionsResponse = await fetch(`${BASE_URL}/api/admin/customization-options`);\n    console.log('Status:', optionsResponse.status);\n\n    if (optionsResponse.ok) {\n      const options = await optionsResponse.json();\n      console.log('Options:', options);\n    } else {\n      const error = await optionsResponse.text();\n      console.log('Error:', error);\n    }\n\n    // Test public customizations endpoint for a menu item\n    console.log('\\n3. Testing GET /api/items/1/customizations');\n    const itemCustomizationsResponse = await fetch(`${BASE_URL}/api/items/1/customizations`);\n    console.log('Status:', itemCustomizationsResponse.status);\n\n    if (itemCustomizationsResponse.ok) {\n      const customizations = await itemCustomizationsResponse.json();\n      console.log('Item customizations:', customizations);\n    } else {\n      const error = await itemCustomizationsResponse.text();\n      console.log('Error:', error);\n    }\n\n  } catch (error) {\n    console.error('Test failed:', error);\n  }\n}\n\ntestCustomizationAPI();\n", "modifiedCode": "// Simple test script to test customization API endpoints\nimport fetch from 'node-fetch';\n\nconst BASE_URL = 'http://localhost:5000';\n\nasync function testCustomizationAPI() {\n  try {\n    console.log('Testing customization API endpoints...');\n\n    // Test getting customization groups\n    console.log('\\n1. Testing GET /api/admin/customization-groups');\n    const groupsResponse = await fetch(`${BASE_URL}/api/admin/customization-groups`);\n    console.log('Status:', groupsResponse.status);\n\n    if (groupsResponse.ok) {\n      const groups = await groupsResponse.json();\n      console.log('Groups:', groups);\n    } else {\n      const error = await groupsResponse.text();\n      console.log('Error:', error);\n    }\n\n    // Test getting customization options\n    console.log('\\n2. Testing GET /api/admin/customization-options');\n    const optionsResponse = await fetch(`${BASE_URL}/api/admin/customization-options`);\n    console.log('Status:', optionsResponse.status);\n\n    if (optionsResponse.ok) {\n      const options = await optionsResponse.json();\n      console.log('Options:', options);\n    } else {\n      const error = await optionsResponse.text();\n      console.log('Error:', error);\n    }\n\n    // Test public customizations endpoint for a menu item\n    console.log('\\n3. Testing GET /api/items/1/customizations');\n    const itemCustomizationsResponse = await fetch(`${BASE_URL}/api/items/1/customizations`);\n    console.log('Status:', itemCustomizationsResponse.status);\n\n    if (itemCustomizationsResponse.ok) {\n      const customizations = await itemCustomizationsResponse.json();\n      console.log('Item customizations:', customizations);\n    } else {\n      const error = await itemCustomizationsResponse.text();\n      console.log('Error:', error);\n    }\n\n  } catch (error) {\n    console.error('Test failed:', error);\n  }\n}\n\ntestCustomizationAPI();\n"}