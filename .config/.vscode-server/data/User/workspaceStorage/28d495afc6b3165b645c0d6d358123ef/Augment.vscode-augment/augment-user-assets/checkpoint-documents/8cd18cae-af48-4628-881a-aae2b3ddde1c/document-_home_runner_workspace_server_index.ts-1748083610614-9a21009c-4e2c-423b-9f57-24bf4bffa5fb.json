{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "server/index.ts"}, "originalCode": "import express, { type Request, Response, NextFunction } from \"express\";\nimport { registerRoutes } from \"./routes\";\nimport { setupVite, serveStatic, log } from \"./vite\";\nimport { initializeDatabase } from \"./init-db\";\n\nconst app = express();\napp.use(express.json());\napp.use(express.urlencoded({ extended: false }));\n\napp.use((req, res, next) => {\n  const start = Date.now();\n  const path = req.path;\n  let capturedJsonResponse: Record<string, any> | undefined = undefined;\n\n  const originalResJson = res.json;\n  res.json = function (bodyJson, ...args) {\n    capturedJsonResponse = bodyJson;\n    return originalResJson.apply(res, [bodyJson, ...args]);\n  };\n\n  res.on(\"finish\", () => {\n    const duration = Date.now() - start;\n    if (path.startsWith(\"/api\")) {\n      let logLine = `${req.method} ${path} ${res.statusCode} in ${duration}ms`;\n      if (capturedJsonResponse) {\n        logLine += ` :: ${JSON.stringify(capturedJsonResponse)}`;\n      }\n\n      if (logLine.length > 80) {\n        logLine = logLine.slice(0, 79) + \"…\";\n      }\n\n      log(logLine);\n    }\n  });\n\n  next();\n});\n\n(async () => {\n  try {\n    // Initialize database before starting the server\n    log(\"Initializing database...\");\n    await initializeDatabase();\n    log(\"Database initialized successfully!\");\n\n    const server = await registerRoutes(app);\n\n    app.use((err: any, _req: Request, res: Response, _next: NextFunction) => {\n      const status = err.status || err.statusCode || 500;\n      const message = err.message || \"Internal Server Error\";\n\n      res.status(status).json({ message });\n      throw err;\n    });\n\n    // importantly only setup vite in development and after\n    // setting up all the other routes so the catch-all route\n    // doesn't interfere with the other routes\n    if (app.get(\"env\") === \"development\") {\n      await setupVite(app, server);\n    } else {\n      serveStatic(app);\n    }\n\n    // ALWAYS serve the app on port 5000\n    // this serves both the API and the client.\n    // It is the only port that is not firewalled.\n    const port = process.env.PORT || 5000;\n    server.listen({\n      port,\n      host: \"0.0.0.0\",\n      reusePort: true,\n    }, () => {\n      log(`serving on port ${port}`);\n    });\n  } catch (error) {\n    console.error(\"Failed to start server:\", error);\n    process.exit(1);\n  }\n})();\n", "modifiedCode": "import express, { type Request, Response, NextFunction } from \"express\";\nimport { registerRoutes } from \"./routes\";\nimport { setupVite, serveStatic, log } from \"./vite\";\nimport { initializeDatabase } from \"./init-db\";\n\nconst app = express();\napp.use(express.json());\napp.use(express.urlencoded({ extended: false }));\n\napp.use((req, res, next) => {\n  const start = Date.now();\n  const path = req.path;\n  let capturedJsonResponse: Record<string, any> | undefined = undefined;\n\n  const originalResJson = res.json;\n  res.json = function (bodyJson, ...args) {\n    capturedJsonResponse = bodyJson;\n    return originalResJson.apply(res, [bodyJson, ...args]);\n  };\n\n  res.on(\"finish\", () => {\n    const duration = Date.now() - start;\n    if (path.startsWith(\"/api\")) {\n      let logLine = `${req.method} ${path} ${res.statusCode} in ${duration}ms`;\n      if (capturedJsonResponse) {\n        logLine += ` :: ${JSON.stringify(capturedJsonResponse)}`;\n      }\n\n      if (logLine.length > 80) {\n        logLine = logLine.slice(0, 79) + \"…\";\n      }\n\n      log(logLine);\n    }\n  });\n\n  next();\n});\n\n(async () => {\n  try {\n    // Initialize database before starting the server\n    log(\"Initializing database...\");\n    await initializeDatabase();\n    log(\"Database initialized successfully!\");\n\n    const server = await registerRoutes(app);\n\n    app.use((err: any, _req: Request, res: Response, _next: NextFunction) => {\n      const status = err.status || err.statusCode || 500;\n      const message = err.message || \"Internal Server Error\";\n\n      res.status(status).json({ message });\n      throw err;\n    });\n\n    // importantly only setup vite in development and after\n    // setting up all the other routes so the catch-all route\n    // doesn't interfere with the other routes\n    if (app.get(\"env\") === \"development\") {\n      await setupVite(app, server);\n    } else {\n      serveStatic(app);\n    }\n\n    // ALWAYS serve the app on port 5000\n    // this serves both the API and the client.\n    // It is the only port that is not firewalled.\n    const port = 5000;\n    server.listen({\n      port,\n      host: \"0.0.0.0\",\n      reusePort: true,\n    }, () => {\n      log(`serving on port ${port}`);\n    });\n  } catch (error) {\n    console.error(\"Failed to start server:\", error);\n    process.exit(1);\n  }\n})();\n"}