{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/pages/driver/DriverPage.tsx"}, "originalCode": "import { useState, useEffect } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport {\n  ShoppingBag,\n  Truck,\n  CheckCircle,\n  Clock,\n  AlertTriangle,\n  PhoneCall,\n  MessageSquare\n} from 'lucide-react';\nimport DriverLayout from './DriverLayout';\nimport { format, parseISO } from 'date-fns';\n\n// Order status constants\nconst STATUS = {\n  READY_FOR_DELIVERY: 'ready_for_delivery',\n  WITH_DRIVER: 'with_driver',\n  DELIVERED: 'delivered'\n};\n\n// Format currency for display\nconst formatCurrency = (amount: number) => {\n  return `$${(amount / 100).toFixed(2)}`;\n};\n\n// Format date for display\nconst formatDate = (dateString: string) => {\n  try {\n    const date = parseISO(dateString);\n    return format(date, 'MMM d, h:mm a');\n  } catch (e) {\n    return dateString;\n  }\n};\n\ninterface OrderItem {\n  id: number;\n  name: string;\n  price: number;\n  quantity: number;\n}\n\ninterface OrderCustomer {\n  firstName: string;\n  lastName: string;\n  phone: string;\n  email: string;\n  address?: string;\n  postalCode?: string;\n  city?: string;\n}\n\ninterface OrderDetails {\n  type?: 'delivery' | 'takeaway';\n  time?: 'asap' | 'scheduled';\n  scheduledTime?: string | null;\n}\n\ninterface Order {\n  id: number;\n  createdAt: string;\n  status: string;\n  items: OrderItem[];\n  customer: OrderCustomer;\n  orderDetails?: OrderDetails;\n  subtotal: number;\n  deliveryFee: number;\n  total: number;\n  paymentMethod: string;\n  notes: string | null;\n}\n\n// Status Badge Component\nconst StatusBadge = ({ status }: { status: string }) => {\n  let color = '';\n  let icon = <AlertTriangle className=\"w-4 h-4\" />;\n  let label = status.replace(/_/g, ' ');\n\n  switch (status) {\n    case STATUS.READY_FOR_DELIVERY:\n      color = 'bg-pink-900/40 border-pink-700/30 text-pink-400';\n      icon = <ShoppingBag className=\"w-4 h-4\" />;\n      label = 'Ready for Delivery';\n      break;\n    case STATUS.WITH_DRIVER:\n      color = 'bg-cyan-900/40 border-cyan-700/30 text-cyan-400';\n      icon = <Truck className=\"w-4 h-4\" />;\n      label = 'With Driver';\n      break;\n    case STATUS.DELIVERED:\n      color = 'bg-green-900/40 border-green-700/30 text-green-400';\n      icon = <CheckCircle className=\"w-4 h-4\" />;\n      label = 'Delivered';\n      break;\n  }\n\n  return (\n    <div className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium border ${color}`}>\n      <span className=\"mr-1.5\">{icon}</span>\n      {label}\n    </div>\n  );\n};\n\n// Action Button Component\nconst ActionButton = ({\n  status,\n  onClick,\n  isLoading\n}: {\n  status: string;\n  onClick: () => void;\n  isLoading: boolean;\n}) => {\n  let color = '';\n  let icon = null;\n  let label = '';\n\n  switch (status) {\n    case STATUS.READY_FOR_DELIVERY:\n      color = 'bg-pink-900/50 hover:bg-pink-800 border-pink-700 text-pink-400 hover:text-white shadow-[0_0_15px_rgba(236,72,153,0.3)]';\n      icon = <Truck className=\"w-5 h-5 mr-2\" />;\n      label = 'Accept Delivery';\n      break;\n    case STATUS.WITH_DRIVER:\n      color = 'bg-cyan-900/50 hover:bg-cyan-800 border-cyan-700 text-cyan-400 hover:text-white shadow-[0_0_15px_rgba(34,211,238,0.3)]';\n      icon = <CheckCircle className=\"w-5 h-5 mr-2\" />;\n      label = 'Mark as Delivered';\n      break;\n    default:\n      return null;\n  }\n\n  return (\n    <motion.button\n      className={`flex items-center justify-center px-6 py-2.5 rounded-lg font-medium\n                  border transition-all ${color} disabled:opacity-50 disabled:cursor-not-allowed\n                  w-full md:w-auto`}\n      onClick={onClick}\n      disabled={isLoading}\n      whileHover={{ scale: 1.03 }}\n      whileTap={{ scale: 0.97 }}\n    >\n      {isLoading ? (\n        <Clock className=\"w-5 h-5 animate-spin mr-2\" />\n      ) : (\n        icon\n      )}\n      {label}\n    </motion.button>\n  );\n};\n\n// Driver Order Card Component\nconst DriverOrderCard = ({\n  order,\n  onStatusUpdate,\n  isUpdating\n}: {\n  order: Order;\n  onStatusUpdate: (orderId: number, newStatus: string) => void;\n  isUpdating: boolean;\n}) => {\n  // Next status to transition to\n  const getNextStatus = () => {\n    switch (order.status) {\n      case STATUS.READY_FOR_DELIVERY:\n        return STATUS.WITH_DRIVER;\n      case STATUS.WITH_DRIVER:\n        return STATUS.DELIVERED;\n      default:\n        return null;\n    }\n  };\n\n  const nextStatus = getNextStatus();\n\n  // Handle status update\n  const handleUpdateStatus = () => {\n    if (nextStatus) {\n      onStatusUpdate(order.id, nextStatus);\n    }\n  };\n\n  // Get customer address\n  const getFullAddress = () => {\n    const { customer } = order;\n    if (!customer.address) return 'No address provided';\n\n    let address = customer.address;\n    if (customer.postalCode) address += `, ${customer.postalCode}`;\n    if (customer.city) address += `, ${customer.city}`;\n\n    return address;\n  };\n\n  // Mock phone call function\n  const handlePhoneCall = (e: React.MouseEvent) => {\n    e.stopPropagation();\n    alert(`Calling customer: ${order.customer.phone}`);\n  };\n\n  // Mock message function\n  const handleMessage = (e: React.MouseEvent) => {\n    e.stopPropagation();\n    alert(`Messaging customer: ${order.customer.phone}`);\n  };\n\n  return (\n    <motion.div\n      className=\"bg-gray-900/70 rounded-xl border border-gray-800 overflow-hidden backdrop-blur-md shadow-lg\"\n      initial={{ opacity: 0, y: 20 }}\n      animate={{ opacity: 1, y: 0 }}\n      exit={{ opacity: 0, y: -20 }}\n      transition={{ duration: 0.3 }}\n    >\n      {/* Header */}\n      <div className=\"bg-gray-900 p-4 border-b border-gray-800 flex justify-between items-center\">\n        <div className=\"flex items-center\">\n          <span className=\"text-lg font-medium text-white mr-3\">Order #{order.id}</span>\n          <StatusBadge status={order.status} />\n        </div>\n        <div className=\"text-sm text-gray-400\">\n          {formatDate(order.createdAt)}\n        </div>\n      </div>\n\n      {/* Body */}\n      <div className=\"p-5 space-y-4\">\n        {/* Customer Info */}\n        <div className=\"bg-gray-800/50 rounded-lg p-4\">\n          <h3 className=\"text-white font-medium mb-3 flex items-center\">\n            <span className=\"inline-block w-2 h-2 bg-pink-500 rounded-full mr-2\"></span>\n            Customer Information\n          </h3>\n\n          <div className=\"flex flex-col md:flex-row justify-between\">\n            <div className=\"space-y-1 mb-4 md:mb-0\">\n              <p className=\"text-white font-medium\">\n                {order.customer.firstName} {order.customer.lastName}\n              </p>\n              <p className=\"text-gray-400\">{order.customer.email}</p>\n              <p className=\"text-cyan-400\">{order.customer.phone}</p>\n            </div>\n\n            <div className=\"flex space-x-2\">\n              <motion.button\n                className=\"p-2.5 rounded-full bg-gray-800 text-cyan-400 hover:bg-cyan-900/40 border border-cyan-800/50\"\n                onClick={handlePhoneCall}\n                whileHover={{ scale: 1.1 }}\n                whileTap={{ scale: 0.9 }}\n              >\n                <PhoneCall className=\"w-5 h-5\" />\n              </motion.button>\n\n              <motion.button\n                className=\"p-2.5 rounded-full bg-gray-800 text-pink-400 hover:bg-pink-900/40 border border-pink-800/50\"\n                onClick={handleMessage}\n                whileHover={{ scale: 1.1 }}\n                whileTap={{ scale: 0.9 }}\n              >\n                <MessageSquare className=\"w-5 h-5\" />\n              </motion.button>\n            </div>\n          </div>\n        </div>\n\n        {/* Delivery Info */}\n        <div className=\"bg-gray-800/50 rounded-lg p-4\">\n          <h3 className=\"text-white font-medium mb-3 flex items-center\">\n            <span className=\"inline-block w-2 h-2 bg-cyan-500 rounded-full mr-2\"></span>\n            Delivery Information\n          </h3>\n\n          <div className=\"space-y-2\">\n            <div className=\"flex justify-between\">\n              <span className=\"text-gray-400\">Address:</span>\n              <span className=\"text-white text-right\">{getFullAddress()}</span>\n            </div>\n            <div className=\"flex justify-between\">\n              <span className=\"text-gray-400\">Order Time:</span>\n              <span className=\"text-white\">\n                {order.orderDetails.time === 'asap'\n                  ? 'ASAP'\n                  : (order.orderDetails.scheduledTime\n                      ? `Scheduled for ${formatDate(order.orderDetails.scheduledTime)}`\n                      : 'Not specified'\n                    )\n                }\n              </span>\n            </div>\n          </div>\n        </div>\n\n        {/* Order Items */}\n        <div className=\"bg-gray-800/50 rounded-lg overflow-hidden\">\n          <h3 className=\"text-white font-medium p-4 border-b border-gray-700 flex items-center\">\n            <span className=\"inline-block w-2 h-2 bg-green-500 rounded-full mr-2\"></span>\n            Order Items\n          </h3>\n\n          <ul className=\"divide-y divide-gray-700\">\n            {order.items.map(item => (\n              <li key={item.id} className=\"p-3 flex justify-between\">\n                <div className=\"flex items-start\">\n                  <span className=\"text-pink-400 font-medium mr-2\">{item.quantity}x</span>\n                  <span className=\"text-white\">{item.name}</span>\n                </div>\n                <span className=\"text-gray-300\">{formatCurrency(item.price * item.quantity)}</span>\n              </li>\n            ))}\n          </ul>\n\n          <div className=\"border-t border-gray-700 p-3 space-y-1\">\n            <div className=\"flex justify-between\">\n              <span className=\"text-gray-400\">Subtotal:</span>\n              <span className=\"text-white\">{formatCurrency(order.subtotal)}</span>\n            </div>\n            <div className=\"flex justify-between\">\n              <span className=\"text-gray-400\">Delivery Fee:</span>\n              <span className=\"text-white\">{formatCurrency(order.deliveryFee)}</span>\n            </div>\n            <div className=\"flex justify-between font-medium pt-1\">\n              <span className=\"text-cyan-400\">Total:</span>\n              <span className=\"text-white text-lg\">{formatCurrency(order.total)}</span>\n            </div>\n          </div>\n        </div>\n\n        {/* Notes */}\n        {order.notes && (\n          <div className=\"bg-gray-800/50 rounded-lg p-4\">\n            <h3 className=\"text-white font-medium mb-2 flex items-center\">\n              <span className=\"inline-block w-2 h-2 bg-yellow-500 rounded-full mr-2\"></span>\n              Special Instructions\n            </h3>\n            <p className=\"text-gray-300\">{order.notes}</p>\n          </div>\n        )}\n\n        {/* Action Buttons */}\n        <div className=\"flex justify-end pt-2\">\n          <ActionButton\n            status={order.status}\n            onClick={handleUpdateStatus}\n            isLoading={isUpdating}\n          />\n        </div>\n      </div>\n    </motion.div>\n  );\n};\n\n// Main Driver Page Component\nconst DriverPage = () => {\n  const [orders, setOrders] = useState<Order[]>([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState<string | null>(null);\n  const [isUpdating, setIsUpdating] = useState(false);\n\n  // Fetch delivery orders\n  useEffect(() => {\n    const fetchOrders = async () => {\n      setLoading(true);\n      try {\n        // In a real app, we'd have a dedicated API endpoint for driver orders\n        // For now, we'll get all orders and filter for delivery ones\n        const response = await fetch('/api/admin/orders');\n        if (!response.ok) {\n          throw new Error(`Failed to fetch orders: ${response.status}`);\n        }\n\n        const allOrders = await response.json();\n\n        // Filter for delivery orders with appropriate status\n        const driverOrders = allOrders.filter((order: Order) =>\n          order.orderDetails.type === 'delivery' &&\n          (order.status === STATUS.READY_FOR_DELIVERY ||\n           order.status === STATUS.WITH_DRIVER)\n        );\n\n        setOrders(driverOrders);\n        setError(null);\n      } catch (err) {\n        console.error('Error fetching driver orders:', err);\n        setError('Failed to load orders. Please try again.');\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    fetchOrders();\n\n    // Periodically refresh orders (every 30 seconds)\n    const interval = setInterval(fetchOrders, 30000);\n    return () => clearInterval(interval);\n  }, []);\n\n  // Update order status\n  const handleUpdateStatus = async (orderId: number, newStatus: string) => {\n    setIsUpdating(true);\n    try {\n      const response = await fetch(`/api/admin/orders/${orderId}/status`, {\n        method: 'PUT',\n        headers: {\n          'Content-Type': 'application/json'\n        },\n        body: JSON.stringify({ newStatus })\n      });\n\n      if (!response.ok) {\n        throw new Error(`Failed to update order status: ${response.status}`);\n      }\n\n      // Update local state\n      setOrders(prevOrders =>\n        prevOrders.map(order =>\n          order.id === orderId\n            ? { ...order, status: newStatus }\n            : order\n        )\n      );\n\n      // If order is now delivered, remove it from the list after a delay\n      if (newStatus === STATUS.DELIVERED) {\n        setTimeout(() => {\n          setOrders(prevOrders => prevOrders.filter(order => order.id !== orderId));\n        }, 2000);\n      }\n\n    } catch (err) {\n      console.error('Error updating order status:', err);\n      setError('Failed to update order status. Please try again.');\n    } finally {\n      setIsUpdating(false);\n    }\n  };\n\n  // Count orders by status\n  const countByStatus = (status: string) => {\n    return orders.filter(order => order.status === status).length;\n  };\n\n  return (\n    <DriverLayout>\n      <div className=\"space-y-6\">\n        <div className=\"flex flex-col md:flex-row md:justify-between md:items-center gap-4\">\n          <div>\n            <h1 className=\"text-2xl font-bold bg-gradient-to-r from-pink-400 via-cyan-400 to-pink-400 text-transparent bg-clip-text mb-2\">\n              Driver Delivery Dashboard\n            </h1>\n            <p className=\"text-gray-400\">Manage your active deliveries and update order statuses</p>\n          </div>\n\n          <div className=\"flex space-x-4\">\n            <div className=\"bg-pink-900/30 border border-pink-700/30 rounded-lg p-3 text-center\">\n              <div className=\"text-pink-400 font-medium\">Ready for Pickup</div>\n              <div className=\"text-2xl font-bold text-white\">{countByStatus(STATUS.READY_FOR_DELIVERY)}</div>\n            </div>\n\n            <div className=\"bg-cyan-900/30 border border-cyan-700/30 rounded-lg p-3 text-center\">\n              <div className=\"text-cyan-400 font-medium\">With Driver</div>\n              <div className=\"text-2xl font-bold text-white\">{countByStatus(STATUS.WITH_DRIVER)}</div>\n            </div>\n          </div>\n        </div>\n\n        {error && (\n          <div className=\"bg-red-900/40 border border-red-700 text-red-400 p-4 rounded-lg\">\n            {error}\n          </div>\n        )}\n\n        {loading ? (\n          <div className=\"flex justify-center items-center py-12\">\n            <div className=\"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-pink-500\"></div>\n          </div>\n        ) : orders.length === 0 ? (\n          <div className=\"bg-gray-900/50 border border-gray-800 rounded-xl p-8 text-center\">\n            <Truck className=\"w-16 h-16 text-gray-700 mx-auto mb-4\" />\n            <h2 className=\"text-xl font-medium text-white mb-2\">No Active Deliveries</h2>\n            <p className=\"text-gray-400\">\n              There are currently no orders ready for delivery or in progress.\n              New orders will appear here automatically.\n            </p>\n          </div>\n        ) : (\n          <div className=\"space-y-6\">\n            <AnimatePresence>\n              {orders.map(order => (\n                <DriverOrderCard\n                  key={order.id}\n                  order={order}\n                  onStatusUpdate={handleUpdateStatus}\n                  isUpdating={isUpdating}\n                />\n              ))}\n            </AnimatePresence>\n          </div>\n        )}\n      </div>\n    </DriverLayout>\n  );\n};\n\nexport default DriverPage;", "modifiedCode": "import { useState, useEffect } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport {\n  ShoppingBag,\n  Truck,\n  CheckCircle,\n  Clock,\n  AlertTriangle,\n  PhoneCall,\n  MessageSquare\n} from 'lucide-react';\nimport DriverLayout from './DriverLayout';\nimport { format, parseISO } from 'date-fns';\n\n// Order status constants\nconst STATUS = {\n  READY_FOR_DELIVERY: 'ready_for_delivery',\n  WITH_DRIVER: 'with_driver',\n  DELIVERED: 'delivered'\n};\n\n// Format currency for display\nconst formatCurrency = (amount: number) => {\n  return `$${(amount / 100).toFixed(2)}`;\n};\n\n// Format date for display\nconst formatDate = (dateString: string) => {\n  try {\n    const date = parseISO(dateString);\n    return format(date, 'MMM d, h:mm a');\n  } catch (e) {\n    return dateString;\n  }\n};\n\ninterface OrderItem {\n  id: number;\n  name: string;\n  price: number;\n  quantity: number;\n}\n\ninterface OrderCustomer {\n  firstName: string;\n  lastName: string;\n  phone: string;\n  email: string;\n  address?: string;\n  postalCode?: string;\n  city?: string;\n}\n\ninterface OrderDetails {\n  type?: 'delivery' | 'takeaway';\n  time?: 'asap' | 'scheduled';\n  scheduledTime?: string | null;\n}\n\ninterface Order {\n  id: number;\n  createdAt: string;\n  status: string;\n  items: OrderItem[];\n  customer: OrderCustomer;\n  orderDetails?: OrderDetails;\n  subtotal: number;\n  deliveryFee: number;\n  total: number;\n  paymentMethod: string;\n  notes: string | null;\n}\n\n// Status Badge Component\nconst StatusBadge = ({ status }: { status: string }) => {\n  let color = '';\n  let icon = <AlertTriangle className=\"w-4 h-4\" />;\n  let label = status.replace(/_/g, ' ');\n\n  switch (status) {\n    case STATUS.READY_FOR_DELIVERY:\n      color = 'bg-pink-900/40 border-pink-700/30 text-pink-400';\n      icon = <ShoppingBag className=\"w-4 h-4\" />;\n      label = 'Ready for Delivery';\n      break;\n    case STATUS.WITH_DRIVER:\n      color = 'bg-cyan-900/40 border-cyan-700/30 text-cyan-400';\n      icon = <Truck className=\"w-4 h-4\" />;\n      label = 'With Driver';\n      break;\n    case STATUS.DELIVERED:\n      color = 'bg-green-900/40 border-green-700/30 text-green-400';\n      icon = <CheckCircle className=\"w-4 h-4\" />;\n      label = 'Delivered';\n      break;\n  }\n\n  return (\n    <div className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium border ${color}`}>\n      <span className=\"mr-1.5\">{icon}</span>\n      {label}\n    </div>\n  );\n};\n\n// Action Button Component\nconst ActionButton = ({\n  status,\n  onClick,\n  isLoading\n}: {\n  status: string;\n  onClick: () => void;\n  isLoading: boolean;\n}) => {\n  let color = '';\n  let icon = null;\n  let label = '';\n\n  switch (status) {\n    case STATUS.READY_FOR_DELIVERY:\n      color = 'bg-pink-900/50 hover:bg-pink-800 border-pink-700 text-pink-400 hover:text-white shadow-[0_0_15px_rgba(236,72,153,0.3)]';\n      icon = <Truck className=\"w-5 h-5 mr-2\" />;\n      label = 'Accept Delivery';\n      break;\n    case STATUS.WITH_DRIVER:\n      color = 'bg-cyan-900/50 hover:bg-cyan-800 border-cyan-700 text-cyan-400 hover:text-white shadow-[0_0_15px_rgba(34,211,238,0.3)]';\n      icon = <CheckCircle className=\"w-5 h-5 mr-2\" />;\n      label = 'Mark as Delivered';\n      break;\n    default:\n      return null;\n  }\n\n  return (\n    <motion.button\n      className={`flex items-center justify-center px-6 py-2.5 rounded-lg font-medium\n                  border transition-all ${color} disabled:opacity-50 disabled:cursor-not-allowed\n                  w-full md:w-auto`}\n      onClick={onClick}\n      disabled={isLoading}\n      whileHover={{ scale: 1.03 }}\n      whileTap={{ scale: 0.97 }}\n    >\n      {isLoading ? (\n        <Clock className=\"w-5 h-5 animate-spin mr-2\" />\n      ) : (\n        icon\n      )}\n      {label}\n    </motion.button>\n  );\n};\n\n// Driver Order Card Component\nconst DriverOrderCard = ({\n  order,\n  onStatusUpdate,\n  isUpdating\n}: {\n  order: Order;\n  onStatusUpdate: (orderId: number, newStatus: string) => void;\n  isUpdating: boolean;\n}) => {\n  // Next status to transition to\n  const getNextStatus = () => {\n    switch (order.status) {\n      case STATUS.READY_FOR_DELIVERY:\n        return STATUS.WITH_DRIVER;\n      case STATUS.WITH_DRIVER:\n        return STATUS.DELIVERED;\n      default:\n        return null;\n    }\n  };\n\n  const nextStatus = getNextStatus();\n\n  // Handle status update\n  const handleUpdateStatus = () => {\n    if (nextStatus) {\n      onStatusUpdate(order.id, nextStatus);\n    }\n  };\n\n  // Get customer address\n  const getFullAddress = () => {\n    const { customer } = order;\n    if (!customer.address) return 'No address provided';\n\n    let address = customer.address;\n    if (customer.postalCode) address += `, ${customer.postalCode}`;\n    if (customer.city) address += `, ${customer.city}`;\n\n    return address;\n  };\n\n  // Mock phone call function\n  const handlePhoneCall = (e: React.MouseEvent) => {\n    e.stopPropagation();\n    alert(`Calling customer: ${order.customer.phone}`);\n  };\n\n  // Mock message function\n  const handleMessage = (e: React.MouseEvent) => {\n    e.stopPropagation();\n    alert(`Messaging customer: ${order.customer.phone}`);\n  };\n\n  return (\n    <motion.div\n      className=\"bg-gray-900/70 rounded-xl border border-gray-800 overflow-hidden backdrop-blur-md shadow-lg\"\n      initial={{ opacity: 0, y: 20 }}\n      animate={{ opacity: 1, y: 0 }}\n      exit={{ opacity: 0, y: -20 }}\n      transition={{ duration: 0.3 }}\n    >\n      {/* Header */}\n      <div className=\"bg-gray-900 p-4 border-b border-gray-800 flex justify-between items-center\">\n        <div className=\"flex items-center\">\n          <span className=\"text-lg font-medium text-white mr-3\">Order #{order.id}</span>\n          <StatusBadge status={order.status} />\n        </div>\n        <div className=\"text-sm text-gray-400\">\n          {formatDate(order.createdAt)}\n        </div>\n      </div>\n\n      {/* Body */}\n      <div className=\"p-5 space-y-4\">\n        {/* Customer Info */}\n        <div className=\"bg-gray-800/50 rounded-lg p-4\">\n          <h3 className=\"text-white font-medium mb-3 flex items-center\">\n            <span className=\"inline-block w-2 h-2 bg-pink-500 rounded-full mr-2\"></span>\n            Customer Information\n          </h3>\n\n          <div className=\"flex flex-col md:flex-row justify-between\">\n            <div className=\"space-y-1 mb-4 md:mb-0\">\n              <p className=\"text-white font-medium\">\n                {order.customer.firstName} {order.customer.lastName}\n              </p>\n              <p className=\"text-gray-400\">{order.customer.email}</p>\n              <p className=\"text-cyan-400\">{order.customer.phone}</p>\n            </div>\n\n            <div className=\"flex space-x-2\">\n              <motion.button\n                className=\"p-2.5 rounded-full bg-gray-800 text-cyan-400 hover:bg-cyan-900/40 border border-cyan-800/50\"\n                onClick={handlePhoneCall}\n                whileHover={{ scale: 1.1 }}\n                whileTap={{ scale: 0.9 }}\n              >\n                <PhoneCall className=\"w-5 h-5\" />\n              </motion.button>\n\n              <motion.button\n                className=\"p-2.5 rounded-full bg-gray-800 text-pink-400 hover:bg-pink-900/40 border border-pink-800/50\"\n                onClick={handleMessage}\n                whileHover={{ scale: 1.1 }}\n                whileTap={{ scale: 0.9 }}\n              >\n                <MessageSquare className=\"w-5 h-5\" />\n              </motion.button>\n            </div>\n          </div>\n        </div>\n\n        {/* Delivery Info */}\n        <div className=\"bg-gray-800/50 rounded-lg p-4\">\n          <h3 className=\"text-white font-medium mb-3 flex items-center\">\n            <span className=\"inline-block w-2 h-2 bg-cyan-500 rounded-full mr-2\"></span>\n            Delivery Information\n          </h3>\n\n          <div className=\"space-y-2\">\n            <div className=\"flex justify-between\">\n              <span className=\"text-gray-400\">Address:</span>\n              <span className=\"text-white text-right\">{getFullAddress()}</span>\n            </div>\n            <div className=\"flex justify-between\">\n              <span className=\"text-gray-400\">Order Time:</span>\n              <span className=\"text-white\">\n                {order.orderDetails.time === 'asap'\n                  ? 'ASAP'\n                  : (order.orderDetails.scheduledTime\n                      ? `Scheduled for ${formatDate(order.orderDetails.scheduledTime)}`\n                      : 'Not specified'\n                    )\n                }\n              </span>\n            </div>\n          </div>\n        </div>\n\n        {/* Order Items */}\n        <div className=\"bg-gray-800/50 rounded-lg overflow-hidden\">\n          <h3 className=\"text-white font-medium p-4 border-b border-gray-700 flex items-center\">\n            <span className=\"inline-block w-2 h-2 bg-green-500 rounded-full mr-2\"></span>\n            Order Items\n          </h3>\n\n          <ul className=\"divide-y divide-gray-700\">\n            {order.items.map(item => (\n              <li key={item.id} className=\"p-3 flex justify-between\">\n                <div className=\"flex items-start\">\n                  <span className=\"text-pink-400 font-medium mr-2\">{item.quantity}x</span>\n                  <span className=\"text-white\">{item.name}</span>\n                </div>\n                <span className=\"text-gray-300\">{formatCurrency(item.price * item.quantity)}</span>\n              </li>\n            ))}\n          </ul>\n\n          <div className=\"border-t border-gray-700 p-3 space-y-1\">\n            <div className=\"flex justify-between\">\n              <span className=\"text-gray-400\">Subtotal:</span>\n              <span className=\"text-white\">{formatCurrency(order.subtotal)}</span>\n            </div>\n            <div className=\"flex justify-between\">\n              <span className=\"text-gray-400\">Delivery Fee:</span>\n              <span className=\"text-white\">{formatCurrency(order.deliveryFee)}</span>\n            </div>\n            <div className=\"flex justify-between font-medium pt-1\">\n              <span className=\"text-cyan-400\">Total:</span>\n              <span className=\"text-white text-lg\">{formatCurrency(order.total)}</span>\n            </div>\n          </div>\n        </div>\n\n        {/* Notes */}\n        {order.notes && (\n          <div className=\"bg-gray-800/50 rounded-lg p-4\">\n            <h3 className=\"text-white font-medium mb-2 flex items-center\">\n              <span className=\"inline-block w-2 h-2 bg-yellow-500 rounded-full mr-2\"></span>\n              Special Instructions\n            </h3>\n            <p className=\"text-gray-300\">{order.notes}</p>\n          </div>\n        )}\n\n        {/* Action Buttons */}\n        <div className=\"flex justify-end pt-2\">\n          <ActionButton\n            status={order.status}\n            onClick={handleUpdateStatus}\n            isLoading={isUpdating}\n          />\n        </div>\n      </div>\n    </motion.div>\n  );\n};\n\n// Main Driver Page Component\nconst DriverPage = () => {\n  const [orders, setOrders] = useState<Order[]>([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState<string | null>(null);\n  const [isUpdating, setIsUpdating] = useState(false);\n\n  // Fetch delivery orders\n  useEffect(() => {\n    const fetchOrders = async () => {\n      setLoading(true);\n      try {\n        // In a real app, we'd have a dedicated API endpoint for driver orders\n        // For now, we'll get all orders and filter for delivery ones\n        const response = await fetch('/api/admin/orders');\n        if (!response.ok) {\n          throw new Error(`Failed to fetch orders: ${response.status}`);\n        }\n\n        const allOrders = await response.json();\n\n        // Filter for delivery orders with appropriate status\n        const driverOrders = allOrders.filter((order: Order) =>\n          order.orderDetails?.type === 'delivery' &&\n          (order.status === STATUS.READY_FOR_DELIVERY ||\n           order.status === STATUS.WITH_DRIVER)\n        );\n\n        setOrders(driverOrders);\n        setError(null);\n      } catch (err) {\n        console.error('Error fetching driver orders:', err);\n        setError('Failed to load orders. Please try again.');\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    fetchOrders();\n\n    // Periodically refresh orders (every 30 seconds)\n    const interval = setInterval(fetchOrders, 30000);\n    return () => clearInterval(interval);\n  }, []);\n\n  // Update order status\n  const handleUpdateStatus = async (orderId: number, newStatus: string) => {\n    setIsUpdating(true);\n    try {\n      const response = await fetch(`/api/admin/orders/${orderId}/status`, {\n        method: 'PUT',\n        headers: {\n          'Content-Type': 'application/json'\n        },\n        body: JSON.stringify({ newStatus })\n      });\n\n      if (!response.ok) {\n        throw new Error(`Failed to update order status: ${response.status}`);\n      }\n\n      // Update local state\n      setOrders(prevOrders =>\n        prevOrders.map(order =>\n          order.id === orderId\n            ? { ...order, status: newStatus }\n            : order\n        )\n      );\n\n      // If order is now delivered, remove it from the list after a delay\n      if (newStatus === STATUS.DELIVERED) {\n        setTimeout(() => {\n          setOrders(prevOrders => prevOrders.filter(order => order.id !== orderId));\n        }, 2000);\n      }\n\n    } catch (err) {\n      console.error('Error updating order status:', err);\n      setError('Failed to update order status. Please try again.');\n    } finally {\n      setIsUpdating(false);\n    }\n  };\n\n  // Count orders by status\n  const countByStatus = (status: string) => {\n    return orders.filter(order => order.status === status).length;\n  };\n\n  return (\n    <DriverLayout>\n      <div className=\"space-y-6\">\n        <div className=\"flex flex-col md:flex-row md:justify-between md:items-center gap-4\">\n          <div>\n            <h1 className=\"text-2xl font-bold bg-gradient-to-r from-pink-400 via-cyan-400 to-pink-400 text-transparent bg-clip-text mb-2\">\n              Driver Delivery Dashboard\n            </h1>\n            <p className=\"text-gray-400\">Manage your active deliveries and update order statuses</p>\n          </div>\n\n          <div className=\"flex space-x-4\">\n            <div className=\"bg-pink-900/30 border border-pink-700/30 rounded-lg p-3 text-center\">\n              <div className=\"text-pink-400 font-medium\">Ready for Pickup</div>\n              <div className=\"text-2xl font-bold text-white\">{countByStatus(STATUS.READY_FOR_DELIVERY)}</div>\n            </div>\n\n            <div className=\"bg-cyan-900/30 border border-cyan-700/30 rounded-lg p-3 text-center\">\n              <div className=\"text-cyan-400 font-medium\">With Driver</div>\n              <div className=\"text-2xl font-bold text-white\">{countByStatus(STATUS.WITH_DRIVER)}</div>\n            </div>\n          </div>\n        </div>\n\n        {error && (\n          <div className=\"bg-red-900/40 border border-red-700 text-red-400 p-4 rounded-lg\">\n            {error}\n          </div>\n        )}\n\n        {loading ? (\n          <div className=\"flex justify-center items-center py-12\">\n            <div className=\"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-pink-500\"></div>\n          </div>\n        ) : orders.length === 0 ? (\n          <div className=\"bg-gray-900/50 border border-gray-800 rounded-xl p-8 text-center\">\n            <Truck className=\"w-16 h-16 text-gray-700 mx-auto mb-4\" />\n            <h2 className=\"text-xl font-medium text-white mb-2\">No Active Deliveries</h2>\n            <p className=\"text-gray-400\">\n              There are currently no orders ready for delivery or in progress.\n              New orders will appear here automatically.\n            </p>\n          </div>\n        ) : (\n          <div className=\"space-y-6\">\n            <AnimatePresence>\n              {orders.map(order => (\n                <DriverOrderCard\n                  key={order.id}\n                  order={order}\n                  onStatusUpdate={handleUpdateStatus}\n                  isUpdating={isUpdating}\n                />\n              ))}\n            </AnimatePresence>\n          </div>\n        )}\n      </div>\n    </DriverLayout>\n  );\n};\n\nexport default DriverPage;"}