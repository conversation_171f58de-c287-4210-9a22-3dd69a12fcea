{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/pages/admin/CustomizationManager.tsx"}, "originalCode": "import { useState, useEffect } from \"react\";\nimport { motion } from \"framer-motion\";\nimport AdminLayout from \"./AdminLayout\";\nimport { CustomizationGroup, CustomizationOption } from \"@shared/schema\";\nimport {\n  getCustomizationGroups,\n  createCustomizationGroup,\n  updateCustomizationGroup,\n  deleteCustomizationGroup,\n  getCustomizationOptions,\n  createCustomizationOption,\n  updateCustomizationOption,\n  deleteCustomizationOption,\n} from \"@/api/customizationApi\";\nimport { useToast } from \"@/hooks/use-toast\";\nimport { Pencil, Trash2, Plus, Settings, ChefHat } from \"lucide-react\";\n\ninterface CustomizationGroupWithOptions extends CustomizationGroup {\n  options: CustomizationOption[];\n}\n\nconst CustomizationManager = () => {\n  const [groups, setGroups] = useState<CustomizationGroupWithOptions[]>([]);\n  const [allOptions, setAllOptions] = useState<CustomizationOption[]>([]);\n  const [loading, setLoading] = useState(true);\n  const [editingGroup, setEditingGroup] = useState<CustomizationGroup | null>(null);\n  const [editingOption, setEditingOption] = useState<CustomizationOption | null>(null);\n  const [showGroupForm, setShowGroupForm] = useState(false);\n  const [showOptionForm, setShowOptionForm] = useState(false);\n  const [selectedGroupId, setSelectedGroupId] = useState<number | null>(null);\n  const { toast } = useToast();\n\n  // Form states\n  const [groupForm, setGroupForm] = useState({ title: \"\" });\n  const [optionForm, setOptionForm] = useState({\n    name: \"\",\n    extraPrice: 0,\n    imageUrl: \"\",\n    groupId: 0,\n  });\n\n  useEffect(() => {\n    fetchData();\n  }, []);\n\n  const fetchData = async () => {\n    try {\n      setLoading(true);\n      const [groupsData, optionsData] = await Promise.all([\n        getCustomizationGroups(),\n        getCustomizationOptions(),\n      ]);\n\n      // Group options by their group ID\n      const groupsWithOptions = groupsData.map(group => ({\n        ...group,\n        options: optionsData.filter(option => option.groupId === group.id),\n      }));\n\n      setGroups(groupsWithOptions);\n      setAllOptions(optionsData);\n    } catch (error) {\n      console.error(\"Error fetching customization data:\", error);\n      toast({\n        title: \"Error\",\n        description: \"Failed to fetch customization data\",\n        variant: \"destructive\",\n      });\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleCreateGroup = async () => {\n    try {\n      if (!groupForm.title.trim()) {\n        toast({\n          title: \"Error\",\n          description: \"Group title is required\",\n          variant: \"destructive\",\n        });\n        return;\n      }\n\n      await createCustomizationGroup(groupForm);\n      toast({\n        title: \"Success\",\n        description: \"Customization group created successfully\",\n      });\n\n      setGroupForm({ title: \"\" });\n      setShowGroupForm(false);\n      fetchData();\n    } catch (error) {\n      console.error(\"Error creating group:\", error);\n      toast({\n        title: \"Error\",\n        description: \"Failed to create customization group\",\n        variant: \"destructive\",\n      });\n    }\n  };\n\n  const handleUpdateGroup = async () => {\n    try {\n      if (!editingGroup || !groupForm.title.trim()) return;\n\n      await updateCustomizationGroup(editingGroup.id, groupForm);\n      toast({\n        title: \"Success\",\n        description: \"Customization group updated successfully\",\n      });\n\n      setEditingGroup(null);\n      setGroupForm({ title: \"\" });\n      fetchData();\n    } catch (error) {\n      console.error(\"Error updating group:\", error);\n      toast({\n        title: \"Error\",\n        description: \"Failed to update customization group\",\n        variant: \"destructive\",\n      });\n    }\n  };\n\n  const handleDeleteGroup = async (id: number) => {\n    try {\n      if (!confirm(\"Are you sure you want to delete this group? This will also delete all options in this group.\")) {\n        return;\n      }\n\n      await deleteCustomizationGroup(id);\n      toast({\n        title: \"Success\",\n        description: \"Customization group deleted successfully\",\n      });\n      fetchData();\n    } catch (error) {\n      console.error(\"Error deleting group:\", error);\n      toast({\n        title: \"Error\",\n        description: \"Failed to delete customization group\",\n        variant: \"destructive\",\n      });\n    }\n  };\n\n  const handleCreateOption = async () => {\n    try {\n      if (!optionForm.name.trim() || !optionForm.groupId) {\n        toast({\n          title: \"Error\",\n          description: \"Option name and group are required\",\n          variant: \"destructive\",\n        });\n        return;\n      }\n\n      await createCustomizationOption(optionForm);\n      toast({\n        title: \"Success\",\n        description: \"Customization option created successfully\",\n      });\n\n      setOptionForm({ name: \"\", extraPrice: 0, imageUrl: \"\", groupId: 0 });\n      setShowOptionForm(false);\n      setSelectedGroupId(null);\n      fetchData();\n    } catch (error) {\n      console.error(\"Error creating option:\", error);\n      toast({\n        title: \"Error\",\n        description: \"Failed to create customization option\",\n        variant: \"destructive\",\n      });\n    }\n  };\n\n  const handleUpdateOption = async () => {\n    try {\n      if (!editingOption || !optionForm.name.trim()) return;\n\n      await updateCustomizationOption(editingOption.id, optionForm);\n      toast({\n        title: \"Success\",\n        description: \"Customization option updated successfully\",\n      });\n\n      setEditingOption(null);\n      setOptionForm({ name: \"\", extraPrice: 0, imageUrl: \"\", groupId: 0 });\n      fetchData();\n    } catch (error) {\n      console.error(\"Error updating option:\", error);\n      toast({\n        title: \"Error\",\n        description: \"Failed to update customization option\",\n        variant: \"destructive\",\n      });\n    }\n  };\n\n  const handleDeleteOption = async (id: number) => {\n    try {\n      if (!confirm(\"Are you sure you want to delete this option?\")) {\n        return;\n      }\n\n      await deleteCustomizationOption(id);\n      toast({\n        title: \"Success\",\n        description: \"Customization option deleted successfully\",\n      });\n      fetchData();\n    } catch (error) {\n      console.error(\"Error deleting option:\", error);\n      toast({\n        title: \"Error\",\n        description: \"Failed to delete customization option\",\n        variant: \"destructive\",\n      });\n    }\n  };\n\n  const startEditingGroup = (group: CustomizationGroup) => {\n    setEditingGroup(group);\n    setGroupForm({ title: group.title });\n    setShowGroupForm(true);\n  };\n\n  const startEditingOption = (option: CustomizationOption) => {\n    setEditingOption(option);\n    setOptionForm({\n      name: option.name,\n      extraPrice: option.extraPrice || 0,\n      imageUrl: option.imageUrl || \"\",\n      groupId: option.groupId,\n    });\n    setShowOptionForm(true);\n  };\n\n  const startAddingOption = (groupId: number) => {\n    setSelectedGroupId(groupId);\n    setOptionForm({ name: \"\", extraPrice: 0, imageUrl: \"\", groupId });\n    setShowOptionForm(true);\n  };\n\n  const cancelForms = () => {\n    setShowGroupForm(false);\n    setShowOptionForm(false);\n    setEditingGroup(null);\n    setEditingOption(null);\n    setSelectedGroupId(null);\n    setGroupForm({ title: \"\" });\n    setOptionForm({ name: \"\", extraPrice: 0, imageUrl: \"\", groupId: 0 });\n  };\n\n  if (loading) {\n    return (\n      <AdminLayout>\n        <div className=\"flex items-center justify-center h-64\">\n          <div className=\"animate-spin rounded-full h-32 w-32 border-b-2 border-purple-600\"></div>\n        </div>\n      </AdminLayout>\n    );\n  }\n\n  return (\n    <AdminLayout>\n      <div className=\"space-y-8\">\n        {/* Header */}\n        <div className=\"flex items-center justify-between\">\n          <div className=\"flex items-center space-x-3\">\n            <div className=\"p-3 bg-gradient-to-br from-purple-600 to-purple-800 rounded-xl\">\n              <ChefHat className=\"w-8 h-8 text-white\" />\n            </div>\n            <div>\n              <h1 className=\"text-3xl font-bold text-white\">Customization Manager</h1>\n              <p className=\"text-gray-400\">Manage menu item customization groups and options</p>\n            </div>\n          </div>\n\n          <button\n            onClick={() => setShowGroupForm(true)}\n            className=\"px-6 py-3 bg-gradient-to-r from-purple-600 to-purple-800 hover:from-purple-500 hover:to-purple-700\n                     text-white rounded-lg transition-all duration-300 shadow-lg hover:shadow-purple-500/30 flex items-center\"\n          >\n            <Plus className=\"w-5 h-5 mr-2\" />\n            Add Group\n          </button>\n        </div>\n\n        {/* Groups and Options */}\n        <div className=\"grid gap-6\">\n          {groups.map((group) => (\n            <motion.div\n              key={group.id}\n              initial={{ opacity: 0, y: 20 }}\n              animate={{ opacity: 1, y: 0 }}\n              className=\"bg-gray-800/50 backdrop-blur-sm rounded-xl border border-gray-700/50 p-6\"\n            >\n              {/* Group Header */}\n              <div className=\"flex items-center justify-between mb-4\">\n                <div className=\"flex items-center space-x-3\">\n                  <Settings className=\"w-6 h-6 text-purple-400\" />\n                  <h3 className=\"text-xl font-semibold text-white\">{group.title}</h3>\n                  <span className=\"px-2 py-1 bg-purple-600/20 text-purple-300 rounded-full text-sm\">\n                    {group.options.length} options\n                  </span>\n                </div>\n\n                <div className=\"flex items-center space-x-2\">\n                  <button\n                    onClick={() => startAddingOption(group.id)}\n                    className=\"px-3 py-2 bg-cyan-600/20 hover:bg-cyan-600/30 text-cyan-300 rounded-lg transition-colors\"\n                  >\n                    <Plus className=\"w-4 h-4\" />\n                  </button>\n                  <button\n                    onClick={() => startEditingGroup(group)}\n                    className=\"px-3 py-2 bg-blue-600/20 hover:bg-blue-600/30 text-blue-300 rounded-lg transition-colors\"\n                  >\n                    <Pencil className=\"w-4 h-4\" />\n                  </button>\n                  <button\n                    onClick={() => handleDeleteGroup(group.id)}\n                    className=\"px-3 py-2 bg-red-600/20 hover:bg-red-600/30 text-red-300 rounded-lg transition-colors\"\n                  >\n                    <Trash2 className=\"w-4 h-4\" />\n                  </button>\n                </div>\n              </div>\n\n              {/* Options Grid */}\n              <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\">\n                {group.options.map((option) => (\n                  <div\n                    key={option.id}\n                    className=\"bg-gray-700/30 rounded-lg p-4 border border-gray-600/30\"\n                  >\n                    <div className=\"flex items-center justify-between mb-2\">\n                      <h4 className=\"font-medium text-white\">{option.name}</h4>\n                      <div className=\"flex items-center space-x-1\">\n                        <button\n                          onClick={() => startEditingOption(option)}\n                          className=\"p-1 text-blue-400 hover:text-blue-300 transition-colors\"\n                        >\n                          <Pencil className=\"w-4 h-4\" />\n                        </button>\n                        <button\n                          onClick={() => handleDeleteOption(option.id)}\n                          className=\"p-1 text-red-400 hover:text-red-300 transition-colors\"\n                        >\n                          <Trash2 className=\"w-4 h-4\" />\n                        </button>\n                      </div>\n                    </div>\n                    <p className=\"text-sm text-gray-400\">\n                      {option.extraPrice ? `+${option.extraPrice} kr` : 'Included'}\n                    </p>\n                  </div>\n                ))}\n              </div>\n            </motion.div>\n          ))}\n        </div>\n      </div>\n    </AdminLayout>\n  );\n};\n\nexport default CustomizationManager;\n", "modifiedCode": "import { useState, useEffect } from \"react\";\nimport { motion } from \"framer-motion\";\nimport AdminLayout from \"./AdminLayout\";\nimport { CustomizationGroup, CustomizationOption } from \"@shared/schema\";\nimport {\n  getCustomizationGroups,\n  createCustomizationGroup,\n  updateCustomizationGroup,\n  deleteCustomizationGroup,\n  getCustomizationOptions,\n  createCustomizationOption,\n  updateCustomizationOption,\n  deleteCustomizationOption,\n} from \"@/api/customizationApi\";\nimport { useToast } from \"@/hooks/use-toast\";\nimport { Pencil, Trash2, Plus, Settings, ChefHat } from \"lucide-react\";\n\ninterface CustomizationGroupWithOptions extends CustomizationGroup {\n  options: CustomizationOption[];\n}\n\nconst CustomizationManager = () => {\n  const [groups, setGroups] = useState<CustomizationGroupWithOptions[]>([]);\n  const [allOptions, setAllOptions] = useState<CustomizationOption[]>([]);\n  const [loading, setLoading] = useState(true);\n  const [editingGroup, setEditingGroup] = useState<CustomizationGroup | null>(null);\n  const [editingOption, setEditingOption] = useState<CustomizationOption | null>(null);\n  const [showGroupForm, setShowGroupForm] = useState(false);\n  const [showOptionForm, setShowOptionForm] = useState(false);\n  const [selectedGroupId, setSelectedGroupId] = useState<number | null>(null);\n  const { toast } = useToast();\n\n  // Form states\n  const [groupForm, setGroupForm] = useState({ title: \"\" });\n  const [optionForm, setOptionForm] = useState({\n    name: \"\",\n    extraPrice: 0,\n    imageUrl: \"\",\n    groupId: 0,\n  });\n\n  useEffect(() => {\n    fetchData();\n  }, []);\n\n  const fetchData = async () => {\n    try {\n      setLoading(true);\n      const [groupsData, optionsData] = await Promise.all([\n        getCustomizationGroups(),\n        getCustomizationOptions(),\n      ]);\n\n      // Group options by their group ID\n      const groupsWithOptions = groupsData.map(group => ({\n        ...group,\n        options: optionsData.filter(option => option.groupId === group.id),\n      }));\n\n      setGroups(groupsWithOptions);\n      setAllOptions(optionsData);\n    } catch (error) {\n      console.error(\"Error fetching customization data:\", error);\n      toast({\n        title: \"Error\",\n        description: \"Failed to fetch customization data\",\n        variant: \"destructive\",\n      });\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleCreateGroup = async () => {\n    try {\n      if (!groupForm.title.trim()) {\n        toast({\n          title: \"Error\",\n          description: \"Group title is required\",\n          variant: \"destructive\",\n        });\n        return;\n      }\n\n      await createCustomizationGroup(groupForm);\n      toast({\n        title: \"Success\",\n        description: \"Customization group created successfully\",\n      });\n\n      setGroupForm({ title: \"\" });\n      setShowGroupForm(false);\n      fetchData();\n    } catch (error) {\n      console.error(\"Error creating group:\", error);\n      toast({\n        title: \"Error\",\n        description: \"Failed to create customization group\",\n        variant: \"destructive\",\n      });\n    }\n  };\n\n  const handleUpdateGroup = async () => {\n    try {\n      if (!editingGroup || !groupForm.title.trim()) return;\n\n      await updateCustomizationGroup(editingGroup.id, groupForm);\n      toast({\n        title: \"Success\",\n        description: \"Customization group updated successfully\",\n      });\n\n      setEditingGroup(null);\n      setGroupForm({ title: \"\" });\n      fetchData();\n    } catch (error) {\n      console.error(\"Error updating group:\", error);\n      toast({\n        title: \"Error\",\n        description: \"Failed to update customization group\",\n        variant: \"destructive\",\n      });\n    }\n  };\n\n  const handleDeleteGroup = async (id: number) => {\n    try {\n      if (!confirm(\"Are you sure you want to delete this group? This will also delete all options in this group.\")) {\n        return;\n      }\n\n      await deleteCustomizationGroup(id);\n      toast({\n        title: \"Success\",\n        description: \"Customization group deleted successfully\",\n      });\n      fetchData();\n    } catch (error) {\n      console.error(\"Error deleting group:\", error);\n      toast({\n        title: \"Error\",\n        description: \"Failed to delete customization group\",\n        variant: \"destructive\",\n      });\n    }\n  };\n\n  const handleCreateOption = async () => {\n    try {\n      if (!optionForm.name.trim() || !optionForm.groupId) {\n        toast({\n          title: \"Error\",\n          description: \"Option name and group are required\",\n          variant: \"destructive\",\n        });\n        return;\n      }\n\n      await createCustomizationOption(optionForm);\n      toast({\n        title: \"Success\",\n        description: \"Customization option created successfully\",\n      });\n\n      setOptionForm({ name: \"\", extraPrice: 0, imageUrl: \"\", groupId: 0 });\n      setShowOptionForm(false);\n      setSelectedGroupId(null);\n      fetchData();\n    } catch (error) {\n      console.error(\"Error creating option:\", error);\n      toast({\n        title: \"Error\",\n        description: \"Failed to create customization option\",\n        variant: \"destructive\",\n      });\n    }\n  };\n\n  const handleUpdateOption = async () => {\n    try {\n      if (!editingOption || !optionForm.name.trim()) return;\n\n      await updateCustomizationOption(editingOption.id, optionForm);\n      toast({\n        title: \"Success\",\n        description: \"Customization option updated successfully\",\n      });\n\n      setEditingOption(null);\n      setOptionForm({ name: \"\", extraPrice: 0, imageUrl: \"\", groupId: 0 });\n      fetchData();\n    } catch (error) {\n      console.error(\"Error updating option:\", error);\n      toast({\n        title: \"Error\",\n        description: \"Failed to update customization option\",\n        variant: \"destructive\",\n      });\n    }\n  };\n\n  const handleDeleteOption = async (id: number) => {\n    try {\n      if (!confirm(\"Are you sure you want to delete this option?\")) {\n        return;\n      }\n\n      await deleteCustomizationOption(id);\n      toast({\n        title: \"Success\",\n        description: \"Customization option deleted successfully\",\n      });\n      fetchData();\n    } catch (error) {\n      console.error(\"Error deleting option:\", error);\n      toast({\n        title: \"Error\",\n        description: \"Failed to delete customization option\",\n        variant: \"destructive\",\n      });\n    }\n  };\n\n  const startEditingGroup = (group: CustomizationGroup) => {\n    setEditingGroup(group);\n    setGroupForm({ title: group.title });\n    setShowGroupForm(true);\n  };\n\n  const startEditingOption = (option: CustomizationOption) => {\n    setEditingOption(option);\n    setOptionForm({\n      name: option.name,\n      extraPrice: option.extraPrice || 0,\n      imageUrl: option.imageUrl || \"\",\n      groupId: option.groupId,\n    });\n    setShowOptionForm(true);\n  };\n\n  const startAddingOption = (groupId: number) => {\n    setSelectedGroupId(groupId);\n    setOptionForm({ name: \"\", extraPrice: 0, imageUrl: \"\", groupId });\n    setShowOptionForm(true);\n  };\n\n  const cancelForms = () => {\n    setShowGroupForm(false);\n    setShowOptionForm(false);\n    setEditingGroup(null);\n    setEditingOption(null);\n    setSelectedGroupId(null);\n    setGroupForm({ title: \"\" });\n    setOptionForm({ name: \"\", extraPrice: 0, imageUrl: \"\", groupId: 0 });\n  };\n\n  if (loading) {\n    return (\n      <AdminLayout>\n        <div className=\"flex items-center justify-center h-64\">\n          <div className=\"animate-spin rounded-full h-32 w-32 border-b-2 border-purple-600\"></div>\n        </div>\n      </AdminLayout>\n    );\n  }\n\n  return (\n    <AdminLayout>\n      <div className=\"space-y-8\">\n        {/* Header */}\n        <div className=\"flex items-center justify-between\">\n          <div className=\"flex items-center space-x-3\">\n            <div className=\"p-3 bg-gradient-to-br from-purple-600 to-purple-800 rounded-xl\">\n              <ChefHat className=\"w-8 h-8 text-white\" />\n            </div>\n            <div>\n              <h1 className=\"text-3xl font-bold text-white\">Customization Manager</h1>\n              <p className=\"text-gray-400\">Manage menu item customization groups and options</p>\n            </div>\n          </div>\n\n          <button\n            onClick={() => setShowGroupForm(true)}\n            className=\"px-6 py-3 bg-gradient-to-r from-purple-600 to-purple-800 hover:from-purple-500 hover:to-purple-700\n                     text-white rounded-lg transition-all duration-300 shadow-lg hover:shadow-purple-500/30 flex items-center\"\n          >\n            <Plus className=\"w-5 h-5 mr-2\" />\n            Add Group\n          </button>\n        </div>\n\n        {/* Groups and Options */}\n        <div className=\"grid gap-6\">\n          {groups.map((group) => (\n            <motion.div\n              key={group.id}\n              initial={{ opacity: 0, y: 20 }}\n              animate={{ opacity: 1, y: 0 }}\n              className=\"bg-gray-800/50 backdrop-blur-sm rounded-xl border border-gray-700/50 p-6\"\n            >\n              {/* Group Header */}\n              <div className=\"flex items-center justify-between mb-4\">\n                <div className=\"flex items-center space-x-3\">\n                  <Settings className=\"w-6 h-6 text-purple-400\" />\n                  <h3 className=\"text-xl font-semibold text-white\">{group.title}</h3>\n                  <span className=\"px-2 py-1 bg-purple-600/20 text-purple-300 rounded-full text-sm\">\n                    {group.options.length} options\n                  </span>\n                </div>\n\n                <div className=\"flex items-center space-x-2\">\n                  <button\n                    onClick={() => startAddingOption(group.id)}\n                    className=\"px-3 py-2 bg-cyan-600/20 hover:bg-cyan-600/30 text-cyan-300 rounded-lg transition-colors\"\n                  >\n                    <Plus className=\"w-4 h-4\" />\n                  </button>\n                  <button\n                    onClick={() => startEditingGroup(group)}\n                    className=\"px-3 py-2 bg-blue-600/20 hover:bg-blue-600/30 text-blue-300 rounded-lg transition-colors\"\n                  >\n                    <Pencil className=\"w-4 h-4\" />\n                  </button>\n                  <button\n                    onClick={() => handleDeleteGroup(group.id)}\n                    className=\"px-3 py-2 bg-red-600/20 hover:bg-red-600/30 text-red-300 rounded-lg transition-colors\"\n                  >\n                    <Trash2 className=\"w-4 h-4\" />\n                  </button>\n                </div>\n              </div>\n\n              {/* Options Grid */}\n              <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\">\n                {group.options.map((option) => (\n                  <div\n                    key={option.id}\n                    className=\"bg-gray-700/30 rounded-lg p-4 border border-gray-600/30\"\n                  >\n                    <div className=\"flex items-center justify-between mb-2\">\n                      <h4 className=\"font-medium text-white\">{option.name}</h4>\n                      <div className=\"flex items-center space-x-1\">\n                        <button\n                          onClick={() => startEditingOption(option)}\n                          className=\"p-1 text-blue-400 hover:text-blue-300 transition-colors\"\n                        >\n                          <Pencil className=\"w-4 h-4\" />\n                        </button>\n                        <button\n                          onClick={() => handleDeleteOption(option.id)}\n                          className=\"p-1 text-red-400 hover:text-red-300 transition-colors\"\n                        >\n                          <Trash2 className=\"w-4 h-4\" />\n                        </button>\n                      </div>\n                    </div>\n                    <p className=\"text-sm text-gray-400\">\n                      {option.extraPrice ? `+${option.extraPrice} kr` : 'Included'}\n                    </p>\n                  </div>\n                ))}\n              </div>\n            </motion.div>\n          ))}\n        </div>\n\n        {/* Group Form Modal */}\n        {showGroupForm && (\n          <div className=\"fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4\">\n            <motion.div\n              initial={{ opacity: 0, scale: 0.95 }}\n              animate={{ opacity: 1, scale: 1 }}\n              className=\"bg-gray-800 rounded-xl border border-gray-700 p-6 w-full max-w-md\"\n            >\n              <h3 className=\"text-xl font-semibold text-white mb-4\">\n                {editingGroup ? 'Edit Group' : 'Add New Group'}\n              </h3>\n\n              <div className=\"space-y-4\">\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-300 mb-2\">\n                    Group Title\n                  </label>\n                  <input\n                    type=\"text\"\n                    value={groupForm.title}\n                    onChange={(e) => setGroupForm({ title: e.target.value })}\n                    className=\"w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-purple-500\"\n                    placeholder=\"e.g., Sauce Options, Protein Choices\"\n                  />\n                </div>\n              </div>\n\n              <div className=\"flex justify-end space-x-3 mt-6\">\n                <button\n                  onClick={cancelForms}\n                  className=\"px-4 py-2 text-gray-300 hover:text-white transition-colors\"\n                >\n                  Cancel\n                </button>\n                <button\n                  onClick={editingGroup ? handleUpdateGroup : handleCreateGroup}\n                  className=\"px-4 py-2 bg-purple-600 hover:bg-purple-700 text-white rounded-lg transition-colors\"\n                >\n                  {editingGroup ? 'Update' : 'Create'}\n                </button>\n              </div>\n            </motion.div>\n          </div>\n        )}\n\n        {/* Option Form Modal */}\n        {showOptionForm && (\n          <div className=\"fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4\">\n            <motion.div\n              initial={{ opacity: 0, scale: 0.95 }}\n              animate={{ opacity: 1, scale: 1 }}\n              className=\"bg-gray-800 rounded-xl border border-gray-700 p-6 w-full max-w-md\"\n            >\n              <h3 className=\"text-xl font-semibold text-white mb-4\">\n                {editingOption ? 'Edit Option' : 'Add New Option'}\n              </h3>\n\n              <div className=\"space-y-4\">\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-300 mb-2\">\n                    Option Name\n                  </label>\n                  <input\n                    type=\"text\"\n                    value={optionForm.name}\n                    onChange={(e) => setOptionForm({ ...optionForm, name: e.target.value })}\n                    className=\"w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-purple-500\"\n                    placeholder=\"e.g., Extra Cheese, Spicy Sauce\"\n                  />\n                </div>\n\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-300 mb-2\">\n                    Extra Price (kr)\n                  </label>\n                  <input\n                    type=\"number\"\n                    value={optionForm.extraPrice}\n                    onChange={(e) => setOptionForm({ ...optionForm, extraPrice: parseInt(e.target.value) || 0 })}\n                    className=\"w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-purple-500\"\n                    placeholder=\"0\"\n                    min=\"0\"\n                  />\n                </div>\n\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-300 mb-2\">\n                    Image URL (optional)\n                  </label>\n                  <input\n                    type=\"text\"\n                    value={optionForm.imageUrl}\n                    onChange={(e) => setOptionForm({ ...optionForm, imageUrl: e.target.value })}\n                    className=\"w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-purple-500\"\n                    placeholder=\"https://...\"\n                  />\n                </div>\n\n                {!editingOption && (\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-300 mb-2\">\n                      Group\n                    </label>\n                    <select\n                      value={optionForm.groupId}\n                      onChange={(e) => setOptionForm({ ...optionForm, groupId: parseInt(e.target.value) })}\n                      className=\"w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-purple-500\"\n                    >\n                      <option value={0}>Select a group</option>\n                      {groups.map((group) => (\n                        <option key={group.id} value={group.id}>\n                          {group.title}\n                        </option>\n                      ))}\n                    </select>\n                  </div>\n                )}\n              </div>\n\n              <div className=\"flex justify-end space-x-3 mt-6\">\n                <button\n                  onClick={cancelForms}\n                  className=\"px-4 py-2 text-gray-300 hover:text-white transition-colors\"\n                >\n                  Cancel\n                </button>\n                <button\n                  onClick={editingOption ? handleUpdateOption : handleCreateOption}\n                  className=\"px-4 py-2 bg-purple-600 hover:bg-purple-700 text-white rounded-lg transition-colors\"\n                >\n                  {editingOption ? 'Update' : 'Create'}\n                </button>\n              </div>\n            </motion.div>\n          </div>\n        )}\n      </div>\n    </AdminLayout>\n  );\n};\n\nexport default CustomizationManager;\n"}