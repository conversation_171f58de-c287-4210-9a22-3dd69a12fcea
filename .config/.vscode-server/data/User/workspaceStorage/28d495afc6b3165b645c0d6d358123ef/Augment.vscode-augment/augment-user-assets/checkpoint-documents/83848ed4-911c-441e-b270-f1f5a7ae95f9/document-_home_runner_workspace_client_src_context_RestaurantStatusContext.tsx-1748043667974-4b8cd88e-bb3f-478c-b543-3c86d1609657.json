{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/context/RestaurantStatusContext.tsx"}, "modifiedCode": "import { createContext, useContext, useState, ReactNode, useEffect } from \"react\";\nimport { AdminSettings } from \"@/api/adminApi\";\n\ninterface RestaurantStatusContextType {\n  isOpen: boolean;\n  isLoading: boolean;\n  businessHours: Record<string, { open: string; close: string; delivery: boolean }>;\n  deliveryFee: number;\n  estimatedTime: string;\n  refreshStatus: () => Promise<void>;\n}\n\nconst RestaurantStatusContext = createContext<RestaurantStatusContextType | undefined>(undefined);\n\nexport const useRestaurantStatus = () => {\n  const context = useContext(RestaurantStatusContext);\n  if (!context) {\n    throw new Error(\"useRestaurantStatus must be used within a RestaurantStatusProvider\");\n  }\n  return context;\n};\n\ninterface RestaurantStatusProviderProps {\n  children: ReactNode;\n}\n\nexport const RestaurantStatusProvider = ({ children }: RestaurantStatusProviderProps) => {\n  const [isOpen, setIsOpen] = useState<boolean>(true);\n  const [isLoading, setIsLoading] = useState<boolean>(true);\n  const [businessHours, setBusinessHours] = useState<Record<string, { open: string; close: string; delivery: boolean }>>({});\n  const [deliveryFee, setDeliveryFee] = useState<number>(49);\n  const [estimatedTime, setEstimatedTime] = useState<string>(\"25-35 min\");\n\n  const fetchRestaurantStatus = async () => {\n    setIsLoading(true);\n    try {\n      const response = await fetch('/api/admin/settings');\n      if (!response.ok) {\n        throw new Error('Failed to fetch restaurant status');\n      }\n      \n      const data: AdminSettings = await response.json();\n      setIsOpen(data.restaurant_open);\n      setBusinessHours(data.business_hours);\n      setDeliveryFee(data.delivery_fee);\n      setEstimatedTime(data.estimated_time);\n    } catch (error) {\n      console.error('Error fetching restaurant status:', error);\n      // Use default values if API call fails\n      setIsOpen(true);\n      setBusinessHours({\n        monday: { open: \"10:00\", close: \"22:00\", delivery: true },\n        tuesday: { open: \"10:00\", close: \"22:00\", delivery: true },\n        wednesday: { open: \"10:00\", close: \"22:00\", delivery: true },\n        thursday: { open: \"10:00\", close: \"22:00\", delivery: true },\n        friday: { open: \"10:00\", close: \"23:00\", delivery: true },\n        saturday: { open: \"10:00\", close: \"23:00\", delivery: true },\n        sunday: { open: \"12:00\", close: \"21:00\", delivery: true }\n      });\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  // Fetch restaurant status on component mount\n  useEffect(() => {\n    fetchRestaurantStatus();\n  }, []);\n\n  const value = {\n    isOpen,\n    isLoading,\n    businessHours,\n    deliveryFee,\n    estimatedTime,\n    refreshStatus: fetchRestaurantStatus\n  };\n\n  return (\n    <RestaurantStatusContext.Provider value={value}>\n      {children}\n    </RestaurantStatusContext.Provider>\n  );\n};\n"}