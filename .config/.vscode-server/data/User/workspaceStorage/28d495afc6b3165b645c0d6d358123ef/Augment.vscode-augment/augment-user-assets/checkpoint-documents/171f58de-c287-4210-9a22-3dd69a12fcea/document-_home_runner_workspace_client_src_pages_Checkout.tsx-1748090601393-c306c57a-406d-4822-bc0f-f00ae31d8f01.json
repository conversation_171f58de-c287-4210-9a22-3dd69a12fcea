{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/pages/Checkout.tsx"}, "originalCode": "import { useState, useEffect } from \"react\";\nimport { useLocation, Link } from \"wouter\";\nimport { useForm } from \"react-hook-form\";\nimport { zodResolver } from \"@hookform/resolvers/zod\";\nimport { z } from \"zod\";\nimport { useCart } from \"@/context/CartContext\";\nimport { useRestaurantStatus } from \"@/context/RestaurantStatusContext\";\nimport { formatCurrency } from \"@/lib/utils\";\nimport { createOrder } from \"@/api/api\";\nimport Button from \"@/components/Button\";\nimport { motion, AnimatePresence } from \"framer-motion\";\nimport { useInView } from \"react-intersection-observer\";\n\nconst checkoutSchema = z.object({\n  firstName: z.string().min(2, \"First name is required\"),\n  email: z.string().email(\"Please enter a valid email\").optional().or(z.literal(\"\")),\n  phone: z.string()\n    .min(1, \"Phone number is required\")\n    .regex(/^\\+47\\s?\\d{2}\\s?\\d{2}\\s?\\d{2}\\s?\\d{2}$/, \"Please enter a valid Norwegian phone number (+47 XX XX XX XX)\"),\n  orderType: z.enum([\"delivery\", \"takeaway\"]),\n  deliveryTime: z.enum([\"asap\", \"scheduled\"]),\n  scheduledTime: z.string().optional(),\n  address: z.string().optional(),\n  postalCode: z.string().optional(),\n  city: z.string().optional(),\n  notes: z.string().optional(),\n  paymentMethod: z.enum([\"card\", \"vipps\", \"cash\"])\n}).refine(data => {\n  // If delivery is selected, address is required\n  if (data.orderType === \"delivery\") {\n    return !!data.address && !!data.postalCode && !!data.city;\n  }\n  return true;\n}, {\n  message: \"Address details are required for delivery\",\n  path: [\"address\"]\n}).refine(data => {\n  // If scheduled time is selected, it must be provided\n  if (data.deliveryTime === \"scheduled\") {\n    return !!data.scheduledTime;\n  }\n  return true;\n}, {\n  message: \"Please select a time for scheduled delivery/pickup\",\n  path: [\"scheduledTime\"]\n});\n\ntype CheckoutFormData = z.infer<typeof checkoutSchema>;\n\nconst Checkout = () => {\n  const { cartItems, clearCart } = useCart();\n  const { isOpen } = useRestaurantStatus();\n  const [, setLocation] = useLocation();\n  const [isSubmitting, setIsSubmitting] = useState(false);\n  const [submitSuccess, setSubmitSuccess] = useState(false);\n  const [animatedTotal, setAnimatedTotal] = useState(0);\n  const [focusedField, setFocusedField] = useState<string | null>(null);\n\n  // Animation refs for sections\n  const [headerRef, headerInView] = useInView({\n    triggerOnce: true,\n    threshold: 0.1,\n  });\n\n  const [formRef, formInView] = useInView({\n    triggerOnce: true,\n    threshold: 0.1,\n  });\n\n  const [summaryRef, summaryInView] = useInView({\n    triggerOnce: true,\n    threshold: 0.1,\n  });\n\n  // Animation variants\n  const containerVariants = {\n    hidden: { opacity: 0 },\n    visible: {\n      opacity: 1,\n      transition: {\n        staggerChildren: 0.1\n      }\n    },\n    exit: {\n      opacity: 0,\n      transition: {\n        duration: 0.3\n      }\n    }\n  };\n\n  const itemVariants = {\n    hidden: { y: 20, opacity: 0 },\n    visible: {\n      y: 0,\n      opacity: 1,\n      transition: {\n        type: \"spring\",\n        stiffness: 100,\n        damping: 12\n      }\n    }\n  };\n\n  const successVariants = {\n    hidden: { scale: 0.8, opacity: 0 },\n    visible: {\n      scale: 1,\n      opacity: 1,\n      transition: {\n        type: \"spring\",\n        stiffness: 200,\n        damping: 20,\n        duration: 0.5\n      }\n    }\n  };\n\n  const formInputVariants = {\n    initial: { scale: 1, boxShadow: \"0 0 0px rgba(0, 0, 0, 0)\" },\n    focused: {\n      scale: 1.02,\n      boxShadow: \"0 0 15px rgba(0, 255, 255, 0.3)\",\n      transition: {\n        type: \"spring\",\n        stiffness: 500,\n        damping: 20,\n        duration: 0.2\n      }\n    },\n    paymentFocused: {\n      scale: 1.02,\n      boxShadow: \"0 0 15px rgba(57, 255, 20, 0.3)\",\n      transition: {\n        type: \"spring\",\n        stiffness: 500,\n        damping: 20,\n        duration: 0.2\n      }\n    },\n    error: {\n      scale: [1, 1.02, 1],\n      boxShadow: \"0 0 15px rgba(255, 0, 0, 0.3)\",\n      transition: {\n        scale: {\n          duration: 0.3,\n          repeat: 2\n        }\n      }\n    }\n  };\n\n  const {\n    register,\n    handleSubmit,\n    watch,\n    setValue,\n    formState: { errors, touchedFields, isValid }\n  } = useForm<CheckoutFormData>({\n    resolver: zodResolver(checkoutSchema),\n    defaultValues: {\n      paymentMethod: \"card\",\n      orderType: \"delivery\",\n      deliveryTime: \"asap\",\n      phone: \"+47 \"\n    },\n    mode: \"onChange\"\n  });\n\n  // Watch form values for conditional rendering\n  const orderType = watch(\"orderType\");\n  const deliveryTime = watch(\"deliveryTime\");\n\n  // State for delivery fee and settings\n  const [deliveryFee, setDeliveryFee] = useState(89); // Default fallback\n  const [estimatedTime, setEstimatedTime] = useState(\"30-45 minutes\");\n  const [isLoadingSettings, setIsLoadingSettings] = useState(true);\n\n  // Fetch delivery fee from API\n  useEffect(() => {\n    const fetchSettings = async () => {\n      try {\n        const response = await fetch('/api/settings');\n        if (response.ok) {\n          const settings = await response.json();\n          setDeliveryFee(settings.delivery_fee);\n          setEstimatedTime(settings.estimated_time);\n        }\n      } catch (error) {\n        console.error('Failed to fetch settings:', error);\n        // Keep default values on error\n      } finally {\n        setIsLoadingSettings(false);\n      }\n    };\n\n    fetchSettings();\n  }, []);\n\n  const subtotal = cartItems.reduce((total, item) => total + (item.price * item.quantity), 0);\n  const actualDeliveryFee = cartItems.length > 0 ? deliveryFee : 0;\n  const VAT = subtotal * 0.25; // 25% VAT\n  const total = subtotal + actualDeliveryFee + VAT;\n\n  // Use effect for animated total - much faster animation\n  useEffect(() => {\n    const duration = 200; // Much faster animation (was 800ms)\n    const steps = 5;      // Fewer steps (was 20)\n    const stepDuration = duration / steps;\n    const increment = (total - animatedTotal) / steps;\n\n    // If the difference is very small, just set it directly\n    if (Math.abs(total - animatedTotal) < 2) {\n      setAnimatedTotal(total);\n      return;\n    }\n\n    const timer = setTimeout(() => {\n      setAnimatedTotal(prev => {\n        const next = prev + increment;\n        // If we're close enough, just set the exact value\n        if (Math.abs(next - total) < 2) return total;\n        return next;\n      });\n    }, stepDuration);\n\n    return () => clearTimeout(timer);\n  }, [total, animatedTotal]);\n\n  // Add a missing state\n  const [isUpdating, setIsUpdating] = useState<number | null>(null);\n\n  // Geolocation states\n  const [isGettingLocation, setIsGettingLocation] = useState(false);\n  const [locationError, setLocationError] = useState<string | null>(null);\n\n  // Clear location error when order type changes or address field is modified\n  useEffect(() => {\n    setLocationError(null);\n  }, [orderType]);\n\n  // Clear location error when user starts typing in address field\n  const addressValue = watch('address');\n  useEffect(() => {\n    if (addressValue && locationError) {\n      setLocationError(null);\n    }\n  }, [addressValue, locationError]);\n\n  // Custom register to track focus state\n  const registerWithFocus = (name: keyof CheckoutFormData) => {\n    return {\n      ...register(name),\n      onFocus: () => setFocusedField(name),\n      onBlur: () => setFocusedField(null)\n    };\n  };\n\n  const onSubmit = async (data: CheckoutFormData) => {\n    // Check if cart is empty\n    if (cartItems.length === 0) {\n      setLocation(\"/menu\");\n      return;\n    }\n\n    // Check if restaurant is open\n    if (!isOpen) {\n      alert(\"Sorry, the restaurant is currently closed. Please try again during business hours.\");\n      return;\n    }\n\n    setIsSubmitting(true);\n\n    try {\n      const orderData = {\n        customer: {\n          firstName: data.firstName,\n          lastName: data.lastName,\n          email: data.email,\n          phone: data.phone,\n          address: data.address,\n          postalCode: data.postalCode,\n          city: data.city\n        },\n        orderDetails: {\n          type: data.orderType,\n          time: data.deliveryTime,\n          scheduledTime: data.scheduledTime || null\n        },\n        items: cartItems,\n        subtotal,\n        deliveryFee: data.orderType === \"delivery\" ? actualDeliveryFee : 0,\n        total: data.orderType === \"delivery\" ? total : subtotal + VAT,\n        paymentMethod: data.paymentMethod,\n        notes: data.notes || \"\",\n        status: \"pending\"\n      };\n\n      const createdOrder = await createOrder(orderData);\n\n      // Clear cart after successful order\n      clearCart();\n\n      // Navigate to order confirmation with the order ID\n      const orderId = createdOrder.id || `BBC${Math.floor(Math.random() * 100000)}`;\n      setLocation(`/order-confirmation/${orderId}`);\n\n    } catch (error) {\n      console.error(\"Order submission failed:\", error);\n      alert(\"There was an error processing your order. Please try again.\");\n      setIsSubmitting(false);\n    }\n  };\n\n  // Geolocation function\n  const getCurrentLocation = async () => {\n    console.log('getCurrentLocation called');\n    setIsGettingLocation(true);\n    setLocationError(null);\n\n    try {\n      // Check if geolocation is supported\n      if (!navigator.geolocation) {\n        throw new Error('Geolocation is not supported by this browser');\n      }\n\n      console.log('Requesting geolocation...');\n      // Get current position\n      const position = await new Promise<GeolocationPosition>((resolve, reject) => {\n        navigator.geolocation.getCurrentPosition(\n          resolve,\n          reject,\n          {\n            enableHighAccuracy: true,\n            timeout: 10000,\n            maximumAge: 300000 // 5 minutes\n          }\n        );\n      });\n\n      const { latitude, longitude } = position.coords;\n      console.log('Got coordinates:', latitude, longitude);\n\n      // Use Nominatim (OpenStreetMap) for reverse geocoding (free alternative to Google Maps)\n      const response = await fetch(\n        `https://nominatim.openstreetmap.org/reverse?format=json&lat=${latitude}&lon=${longitude}&addressdetails=1&accept-language=no,en`\n      );\n\n      if (!response.ok) {\n        throw new Error('Failed to get address from coordinates');\n      }\n\n      const data = await response.json();\n      console.log('Reverse geocoding response:', data);\n\n      if (!data || !data.address) {\n        throw new Error('No address found for this location');\n      }\n\n      const address = data.address;\n\n      // Extract Norwegian address components\n      const streetNumber = address.house_number || '';\n      const streetName = address.road || address.pedestrian || address.footway || '';\n      const fullStreetAddress = streetNumber && streetName\n        ? `${streetName} ${streetNumber}`\n        : streetName || address.display_name?.split(',')[0] || '';\n\n      const postalCode = address.postcode || '';\n      const city = address.city || address.town || address.village || address.municipality || '';\n\n      console.log('Extracted address components:', {\n        fullStreetAddress,\n        postalCode,\n        city,\n        countryCode: address.country_code\n      });\n\n      // Validate that we have essential Norwegian address components\n      if (!fullStreetAddress || !postalCode || !city) {\n        throw new Error('Could not determine complete address. Please enter manually.');\n      }\n\n      // Check if the location is in Norway (basic validation)\n      if (address.country_code !== 'no') {\n        throw new Error('Location appears to be outside Norway. Please enter a Norwegian address.');\n      }\n\n      console.log('Setting form values...');\n      // Auto-fill the form fields with proper validation\n      setValue('address', fullStreetAddress, { shouldValidate: true, shouldDirty: true });\n      setValue('postalCode', postalCode, { shouldValidate: true, shouldDirty: true });\n      setValue('city', city, { shouldValidate: true, shouldDirty: true });\n\n      // Clear any existing errors for these fields\n      clearErrors(['address', 'postalCode', 'city']);\n\n      // Show success message briefly\n      setLocationError(null);\n      console.log('Address fields populated successfully');\n\n    } catch (error: any) {\n      console.error('Geolocation error:', error);\n\n      let errorMessage = 'Unable to get your location. ';\n\n      if (error.code === 1) {\n        errorMessage += 'Location access denied. Please enable location services and try again.';\n      } else if (error.code === 2) {\n        errorMessage += 'Location unavailable. Please check your connection and try again.';\n      } else if (error.code === 3) {\n        errorMessage += 'Location request timed out. Please try again.';\n      } else {\n        errorMessage += error.message || 'Please enter your address manually.';\n      }\n\n      setLocationError(errorMessage);\n    } finally {\n      setIsGettingLocation(false);\n    }\n  };\n\n  // Success Confirmation Component\n  if (submitSuccess) {\n    return (\n      <section className=\"min-h-screen py-32 bg-black relative overflow-hidden\">\n        {/* Animated Gradient Background */}\n        <div\n          className=\"absolute inset-0 z-0\"\n          style={{\n            background: \"radial-gradient(circle at 20% 80%, rgba(57, 255, 20, 0.03), transparent 33%), \" +\n                      \"radial-gradient(circle at 80% 20%, rgba(0, 255, 255, 0.03), transparent 33%), \" +\n                      \"radial-gradient(circle at 50% 50%, rgba(255, 0, 255, 0.03), rgba(0, 0, 0, 1) 100%)\",\n          }}\n        ></div>\n\n        {/* Animated success message */}\n        <div className=\"container mx-auto px-4 z-10 relative\">\n          <motion.div\n            className=\"max-w-2xl mx-auto\"\n            variants={successVariants}\n            initial=\"hidden\"\n            animate=\"visible\"\n          >\n            <div className=\"bg-black/30 backdrop-blur-sm rounded-2xl p-8 md:p-12 border border-lime-800/30\n                          shadow-[0_0_30px_rgba(57,255,20,0.2)]\">\n              <div className=\"text-center\">\n                <motion.div\n                  className=\"w-24 h-24 mx-auto mb-6 rounded-full bg-gradient-to-br from-lime-500 to-green-700\n                           flex items-center justify-center text-white\"\n                  animate={{\n                    boxShadow: [\n                      \"0 0 0px rgba(57, 255, 20, 0)\",\n                      \"0 0 30px rgba(57, 255, 20, 0.8)\",\n                      \"0 0 10px rgba(57, 255, 20, 0.4)\"\n                    ],\n                    scale: [1, 1.1, 1]\n                  }}\n                  transition={{ duration: 2, repeat: Infinity, repeatType: \"reverse\" }}\n                >\n                  <svg className=\"w-12 h-12\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={3} d=\"M5 13l4 4L19 7\" />\n                  </svg>\n                </motion.div>\n\n                <motion.h2\n                  className=\"font-playfair text-3xl md:text-4xl font-bold mb-4 text-white\"\n                  initial={{ opacity: 0, y: 20 }}\n                  animate={{ opacity: 1, y: 0 }}\n                  transition={{ delay: 0.3, duration: 0.5 }}\n                >\n                  Order Confirmed!\n                </motion.h2>\n\n                <motion.div\n                  className=\"text-gray-300 mb-8 space-y-2\"\n                  initial={{ opacity: 0 }}\n                  animate={{ opacity: 1 }}\n                  transition={{ delay: 0.5, duration: 0.5 }}\n                >\n                  <p className=\"text-lg\">Your delicious BBQ is on the way!</p>\n                  <p>You will receive a confirmation email shortly.</p>\n                </motion.div>\n\n                {/* Confetti animation */}\n                <div className=\"absolute inset-0 pointer-events-none\">\n                  {[...Array(20)].map((_, i) => (\n                    <motion.div\n                      key={i}\n                      className=\"absolute w-4 h-4 rounded-full\"\n                      style={{\n                        backgroundColor:\n                          i % 3 === 0 ? 'rgba(57, 255, 20, 0.7)' :\n                          i % 3 === 1 ? 'rgba(0, 255, 255, 0.7)' :\n                          'rgba(255, 0, 255, 0.7)',\n                        top: `${Math.random() * 100}%`,\n                        left: `${Math.random() * 100}%`,\n                      }}\n                      initial={{\n                        y: -20,\n                        opacity: 0,\n                        scale: 0\n                      }}\n                      animate={{\n                        y: [0, 100 + Math.random() * 300],\n                        opacity: [1, 0],\n                        scale: [1, 0.5],\n                        rotate: [0, Math.random() * 360]\n                      }}\n                      transition={{\n                        duration: 2 + Math.random() * 3,\n                        delay: Math.random() * 1,\n                        ease: \"easeOut\"\n                      }}\n                    />\n                  ))}\n                </div>\n\n                <motion.p\n                  className=\"text-sm text-gray-500 mb-8\"\n                  initial={{ opacity: 0 }}\n                  animate={{ opacity: 1 }}\n                  transition={{ delay: 0.8, duration: 0.5 }}\n                >\n                  Redirecting to home page...\n                </motion.p>\n\n                <div className=\"flex justify-center\">\n                  <motion.div\n                    initial={{ opacity: 0, y: 20 }}\n                    animate={{ opacity: 1, y: 0 }}\n                    transition={{ delay: 1, duration: 0.5 }}\n                  >\n                    <Link href=\"/\">\n                      <Button variant=\"outline-primary\">\n                        Return to Home\n                      </Button>\n                    </Link>\n                  </motion.div>\n                </div>\n              </div>\n            </div>\n          </motion.div>\n        </div>\n      </section>\n    );\n  }\n\n  // Empty Cart State\n  if (cartItems.length === 0) {\n    return (\n      <section className=\"min-h-screen py-32 bg-black relative overflow-hidden\">\n        {/* Animated Gradient Background */}\n        <div\n          className=\"absolute inset-0 z-0\"\n          style={{\n            background: \"radial-gradient(circle at 20% 80%, rgba(0, 255, 255, 0.03), transparent 33%), \" +\n                      \"radial-gradient(circle at 80% 20%, rgba(255, 0, 255, 0.03), transparent 33%), \" +\n                      \"radial-gradient(circle at 50% 50%, rgba(57, 255, 20, 0.01), rgba(0, 0, 0, 1) 100%)\",\n          }}\n        ></div>\n\n        {/* Neon Grid Overlay */}\n        <div className=\"absolute inset-0 z-0 opacity-[0.02]\"\n             style={{\n               backgroundImage: \"linear-gradient(to right, #FF00FF 1px, transparent 1px), linear-gradient(to bottom, #FF00FF 1px, transparent 1px)\",\n               backgroundSize: \"40px 40px\"\n             }}>\n        </div>\n\n        <div className=\"container mx-auto px-4 relative z-10\">\n          <motion.div\n            className=\"text-center mb-16\"\n            initial={{ opacity: 0, y: -20 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.8 }}\n          >\n            <motion.h2\n              className=\"font-playfair text-4xl md:text-5xl font-bold mb-4 text-white\"\n              initial={{ opacity: 0 }}\n              animate={{ opacity: 1 }}\n              transition={{ duration: 0.5, delay: 0.2 }}\n            >\n              <span className=\"text-white\">Your </span>\n              <motion.span\n                className=\"text-transparent bg-clip-text bg-gradient-to-r from-cyan-400 to-fuchsia-500\"\n                animate={{\n                  textShadow: [\n                    \"0 0 5px rgba(0, 255, 255, 0.7), 0 0 10px rgba(0, 255, 255, 0.5), 0 0 15px rgba(0, 255, 255, 0.3)\",\n                    \"0 0 5px rgba(255, 0, 255, 0.7), 0 0 10px rgba(255, 0, 255, 0.5), 0 0 15px rgba(255, 0, 255, 0.3)\",\n                    \"0 0 5px rgba(0, 255, 255, 0.7), 0 0 10px rgba(0, 255, 255, 0.5), 0 0 15px rgba(0, 255, 255, 0.3)\",\n                  ]\n                }}\n                transition={{ duration: 3, repeat: Infinity }}\n              >\n                Checkout\n              </motion.span>\n            </motion.h2>\n          </motion.div>\n\n          <motion.div\n            className=\"max-w-2xl mx-auto\"\n            initial={{ opacity: 0, y: 20 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.5, delay: 0.3 }}\n          >\n            <div className=\"backdrop-blur-sm bg-black/20 rounded-xl p-10 border border-gray-800\">\n              <div className=\"text-center\">\n                {/* Animated Empty Cart Icon */}\n                <motion.div\n                  className=\"w-32 h-32 mx-auto mb-6 relative\"\n                  animate={{\n                    rotateY: [0, 10, 0, -10, 0],\n                  }}\n                  transition={{ duration: 4, repeat: Infinity }}\n                >\n                  <svg\n                    className=\"w-full h-full text-gray-600\"\n                    viewBox=\"0 0 24 24\"\n                    fill=\"none\"\n                    xmlns=\"http://www.w3.org/2000/svg\"\n                  >\n                    <motion.path\n                      d=\"M3 3h2l.5 3M7 13h10l4-8H5.5M7 13L5.5 6M7 13l-2.3 2.3c-.4.4-.1 1.7 1.1 1.7H17\"\n                      stroke=\"currentColor\"\n                      strokeWidth=\"1.5\"\n                      strokeLinecap=\"round\"\n                      strokeLinejoin=\"round\"\n                      initial={{ pathLength: 0 }}\n                      animate={{ pathLength: 1 }}\n                      transition={{ duration: 2 }}\n                    />\n                    <motion.path\n                      d=\"M17 18a2 2 0 100 4 2 2 0 000-4zM7 18a2 2 0 100 4 2 2 0 000-4z\"\n                      stroke=\"currentColor\"\n                      strokeWidth=\"1.5\"\n                      strokeLinecap=\"round\"\n                      strokeLinejoin=\"round\"\n                      initial={{ pathLength: 0 }}\n                      animate={{ pathLength: 1 }}\n                      transition={{ duration: 1.5, delay: 1.5 }}\n                    />\n                  </svg>\n\n                  {/* Animated empty indicator */}\n                  <motion.div\n                    className=\"absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 w-16 h-0.5 bg-gray-600\"\n                    initial={{ rotate: 45, scale: 0 }}\n                    animate={{ rotate: 45, scale: 1 }}\n                    transition={{ delay: 2.5, duration: 0.5 }}\n                  />\n                </motion.div>\n\n                <motion.h3\n                  className=\"font-playfair text-2xl font-bold text-white mb-3\"\n                  initial={{ opacity: 0, y: 10 }}\n                  animate={{ opacity: 1, y: 0 }}\n                  transition={{ delay: 0.5, duration: 0.5 }}\n                >\n                  Your cart is empty\n                </motion.h3>\n\n                <motion.p\n                  className=\"font-poppins text-gray-400 mb-8 max-w-md mx-auto\"\n                  initial={{ opacity: 0 }}\n                  animate={{ opacity: 1 }}\n                  transition={{ delay: 0.7, duration: 0.5 }}\n                >\n                  Your culinary journey awaits! Visit our menu to discover premium BBQ dishes crafted to perfection.\n                </motion.p>\n\n                <motion.div\n                  initial={{ opacity: 0, y: 20 }}\n                  animate={{ opacity: 1, y: 0 }}\n                  transition={{ delay: 0.9, duration: 0.5 }}\n                >\n                  <Link href=\"/menu\">\n                    <motion.button\n                      className=\"relative overflow-hidden rounded-md px-8 py-3 bg-transparent\"\n                      whileHover={{ scale: 1.03 }}\n                      whileTap={{ scale: 0.97 }}\n                    >\n                      {/* Button Background */}\n                      <span className=\"absolute inset-0 w-full h-full bg-gradient-to-r from-cyan-800/70 to-cyan-600/70\"></span>\n\n                      {/* Button Glow Effect */}\n                      <span className=\"absolute inset-0 w-full h-full transition-all duration-300\n                                      bg-gradient-to-r from-cyan-600 via-blue-600 to-cyan-600\n                                      opacity-0 hover:opacity-100 hover:blur-md\"></span>\n\n                      {/* Button Border */}\n                      <span className=\"absolute inset-0 w-full h-full border border-cyan-500 rounded-md\"></span>\n\n                      {/* Button Text */}\n                      <span className=\"relative z-10 flex items-center font-medium text-white\">\n                        <svg className=\"w-5 h-5 mr-2\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M4 6h16M4 12h8m-8 6h16\" />\n                        </svg>\n                        Browse Menu\n                      </span>\n                    </motion.button>\n                  </Link>\n                </motion.div>\n              </div>\n            </div>\n          </motion.div>\n        </div>\n      </section>\n    );\n  }\n\n  return (\n    <section className=\"min-h-screen py-32 bg-black relative overflow-hidden\">\n      {/* Animated Gradient Background */}\n      <div\n        className=\"absolute inset-0 z-0\"\n        style={{\n          background: \"radial-gradient(circle at 20% 80%, rgba(0, 255, 255, 0.03), transparent 33%), \" +\n                    \"radial-gradient(circle at 80% 20%, rgba(255, 0, 255, 0.03), transparent 33%), \" +\n                    \"radial-gradient(circle at 50% 50%, rgba(57, 255, 20, 0.01), rgba(0, 0, 0, 1) 100%)\",\n        }}\n      ></div>\n\n      {/* Neon Grid Overlay */}\n      <div className=\"absolute inset-0 z-0 opacity-[0.02]\"\n           style={{\n             backgroundImage: \"linear-gradient(to right, #FF00FF 1px, transparent 1px), linear-gradient(to bottom, #FF00FF 1px, transparent 1px)\",\n             backgroundSize: \"40px 40px\"\n           }}>\n      </div>\n\n      <div className=\"container mx-auto px-4 relative z-10\">\n        {/* Header Section */}\n        <motion.div\n          ref={headerRef}\n          initial={{ opacity: 0, y: -20 }}\n          animate={headerInView ? { opacity: 1, y: 0 } : {}}\n          transition={{ duration: 0.8 }}\n          className=\"text-center mb-16\"\n        >\n          <motion.h2\n            className=\"font-playfair text-4xl md:text-5xl font-bold mb-4 text-white inline-block\"\n            initial={{ opacity: 0 }}\n            animate={headerInView ? { opacity: 1 } : {}}\n            transition={{ duration: 0.5, delay: 0.2 }}\n          >\n            <span className=\"text-white\">Complete </span>\n            <motion.span\n              className=\"text-transparent bg-clip-text bg-gradient-to-r from-lime-400 to-green-500\"\n              animate={{\n                textShadow: [\n                  \"0 0 5px rgba(57, 255, 20, 0.7), 0 0 10px rgba(57, 255, 20, 0.5), 0 0 15px rgba(57, 255, 20, 0.3)\",\n                  \"0 0 5px rgba(0, 255, 255, 0.7), 0 0 10px rgba(0, 255, 255, 0.5), 0 0 15px rgba(0, 255, 255, 0.3)\",\n                  \"0 0 5px rgba(57, 255, 20, 0.7), 0 0 10px rgba(57, 255, 20, 0.5), 0 0 15px rgba(57, 255, 20, 0.3)\"\n                ]\n              }}\n              transition={{ duration: 3, repeat: Infinity }}\n            >\n              Your Order\n            </motion.span>\n          </motion.h2>\n\n          <motion.p\n            className=\"font-poppins text-lg text-gray-400 max-w-2xl mx-auto\"\n            initial={{ opacity: 0, y: 20 }}\n            animate={headerInView ? { opacity: 1, y: 0 } : {}}\n            transition={{ duration: 0.6, delay: 0.4 }}\n          >\n            We're just a few details away from your premium BBQ experience\n          </motion.p>\n        </motion.div>\n\n        <motion.div\n          ref={formRef}\n          variants={containerVariants}\n          initial=\"hidden\"\n          animate={formInView ? \"visible\" : \"hidden\"}\n          className=\"max-w-6xl mx-auto\"\n        >\n          <div className=\"lg:grid lg:grid-cols-3 lg:gap-10\">\n            {/* Checkout Form (2/3 width on desktop) */}\n            <motion.div\n              className=\"lg:col-span-2 mb-10 lg:mb-0\"\n              variants={itemVariants}\n            >\n              <div className=\"backdrop-blur-sm bg-black/20 rounded-xl border border-gray-800\n                             overflow-hidden shadow-[0_10px_30px_rgba(0,0,0,0.3)]\">\n                {/* Form Header */}\n                <div className=\"relative\">\n                  <div className=\"h-3 w-full bg-gradient-to-r from-cyan-500 via-fuchsia-500 to-lime-500\"></div>\n                  <div className=\"px-8 py-6 border-b border-gray-800\">\n                    <h3 className=\"font-playfair text-2xl font-bold text-white flex items-center\">\n                      <svg className=\"w-6 h-6 mr-2 text-cyan-400\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z\" />\n                      </svg>\n                      Delivery Information\n                    </h3>\n                  </div>\n                </div>\n\n                {/* Form Content */}\n                <div className=\"p-8\">\n                  <form onSubmit={handleSubmit(onSubmit)}>\n                    {/* Order Type Selection */}\n                    <div className=\"mb-8\">\n                      <label className=\"block font-medium text-gray-300 mb-4\">\n                        <span>Order Type</span>\n                      </label>\n\n                      <div className=\"grid grid-cols-2 gap-4\">\n                        <motion.label\n                          className={`flex flex-col items-center justify-center bg-black/40 p-6 rounded-lg border\n                            ${orderType === \"delivery\"\n                              ? 'border-cyan-500 shadow-[0_0_15px_rgba(0,255,255,0.2)]'\n                              : 'border-gray-700'}\n                            cursor-pointer transition-all duration-300 hover:border-cyan-700`}\n                          whileHover={{ scale: 1.02 }}\n                          whileTap={{ scale: 0.98 }}\n                        >\n                          <input\n                            type=\"radio\"\n                            value=\"delivery\"\n                            className=\"sr-only\"\n                            {...register(\"orderType\")}\n                          />\n                          <div className=\"w-14 h-14 mb-3 rounded-full bg-gradient-to-br from-cyan-500/40 to-cyan-700/40\n                                        flex items-center justify-center text-cyan-400\">\n                            <svg className=\"w-8 h-8\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={1.5}\n                                d=\"M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z\" />\n                              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={1.5}\n                                d=\"M15 11a3 3 0 11-6 0 3 3 0 016 0z\" />\n                            </svg>\n                          </div>\n                          <span className=\"font-medium text-white text-lg\">Delivery</span>\n                          <span className=\"text-gray-400 text-sm mt-1\">\n                            To your address\n                          </span>\n\n                          {orderType === \"delivery\" && (\n                            <motion.div\n                              className=\"absolute top-2 right-2 w-4 h-4 rounded-full bg-cyan-500\"\n                              initial={{ scale: 0 }}\n                              animate={{ scale: 1 }}\n                              transition={{ type: \"spring\", stiffness: 500, damping: 15 }}\n                            />\n                          )}\n                        </motion.label>\n\n                        <motion.label\n                          className={`flex flex-col items-center justify-center bg-black/40 p-6 rounded-lg border\n                            ${orderType === \"takeaway\"\n                              ? 'border-cyan-500 shadow-[0_0_15px_rgba(0,255,255,0.2)]'\n                              : 'border-gray-700'}\n                            cursor-pointer transition-all duration-300 hover:border-cyan-700`}\n                          whileHover={{ scale: 1.02 }}\n                          whileTap={{ scale: 0.98 }}\n                        >\n                          <input\n                            type=\"radio\"\n                            value=\"takeaway\"\n                            className=\"sr-only\"\n                            {...register(\"orderType\")}\n                          />\n                          <div className=\"w-14 h-14 mb-3 rounded-full bg-gradient-to-br from-fuchsia-500/40 to-fuchsia-700/40\n                                        flex items-center justify-center text-fuchsia-400\">\n                            <svg className=\"w-8 h-8\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={1.5}\n                                d=\"M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 002 2z\" />\n                            </svg>\n                          </div>\n                          <span className=\"font-medium text-white text-lg\">Takeaway</span>\n                          <span className=\"text-gray-400 text-sm mt-1\">\n                            Pick up at restaurant\n                          </span>\n\n                          {orderType === \"takeaway\" && (\n                            <motion.div\n                              className=\"absolute top-2 right-2 w-4 h-4 rounded-full bg-fuchsia-500\"\n                              initial={{ scale: 0 }}\n                              animate={{ scale: 1 }}\n                              transition={{ type: \"spring\", stiffness: 500, damping: 15 }}\n                            />\n                          )}\n                        </motion.label>\n                      </div>\n                    </div>\n\n                    {/* Delivery/Pickup Time */}\n                    <div className=\"mb-8\">\n                      <label className=\"block font-medium text-gray-300 mb-4\">\n                        <span>{orderType === \"delivery\" ? \"Delivery\" : \"Pickup\"} Time</span>\n                      </label>\n\n                      <div className=\"grid grid-cols-2 gap-4 mb-4\">\n                        <motion.label\n                          className={`flex flex-col items-center justify-center bg-black/40 p-5 rounded-lg border\n                            ${deliveryTime === \"asap\"\n                              ? 'border-lime-500 shadow-[0_0_15px_rgba(57,255,20,0.2)]'\n                              : 'border-gray-700'}\n                            cursor-pointer transition-all duration-300 hover:border-lime-700`}\n                          whileHover={{ scale: 1.02 }}\n                          whileTap={{ scale: 0.98 }}\n                        >\n                          <input\n                            type=\"radio\"\n                            value=\"asap\"\n                            className=\"sr-only\"\n                            {...register(\"deliveryTime\")}\n                          />\n                          <div className=\"w-12 h-12 mb-2 rounded-full bg-gradient-to-br from-lime-500/40 to-lime-700/40\n                                        flex items-center justify-center text-lime-400\">\n                            <svg className=\"w-7 h-7\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={1.5}\n                                d=\"M13 10V3L4 14h7v7l9-11h-7z\" />\n                            </svg>\n                          </div>\n                          <span className=\"font-medium text-white\">As Soon As Possible</span>\n                          <span className=\"text-gray-400 text-xs mt-1\">\n                            30-45 min estimated\n                          </span>\n\n                          {deliveryTime === \"asap\" && (\n                            <motion.div\n                              className=\"absolute top-2 right-2 w-4 h-4 rounded-full bg-lime-500\"\n                              initial={{ scale: 0 }}\n                              animate={{ scale: 1 }}\n                              transition={{ type: \"spring\", stiffness: 500, damping: 15 }}\n                            />\n                          )}\n                        </motion.label>\n\n                        <motion.label\n                          className={`flex flex-col items-center justify-center bg-black/40 p-5 rounded-lg border\n                            ${deliveryTime === \"scheduled\"\n                              ? 'border-lime-500 shadow-[0_0_15px_rgba(57,255,20,0.2)]'\n                              : 'border-gray-700'}\n                            cursor-pointer transition-all duration-300 hover:border-lime-700`}\n                          whileHover={{ scale: 1.02 }}\n                          whileTap={{ scale: 0.98 }}\n                        >\n                          <input\n                            type=\"radio\"\n                            value=\"scheduled\"\n                            className=\"sr-only\"\n                            {...register(\"deliveryTime\")}\n                          />\n                          <div className=\"w-12 h-12 mb-2 rounded-full bg-gradient-to-br from-lime-500/40 to-lime-700/40\n                                        flex items-center justify-center text-lime-400\">\n                            <svg className=\"w-7 h-7\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={1.5}\n                                d=\"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z\" />\n                            </svg>\n                          </div>\n                          <span className=\"font-medium text-white\">Schedule for Later</span>\n                          <span className=\"text-gray-400 text-xs mt-1\">\n                            Select a time\n                          </span>\n\n                          {deliveryTime === \"scheduled\" && (\n                            <motion.div\n                              className=\"absolute top-2 right-2 w-4 h-4 rounded-full bg-lime-500\"\n                              initial={{ scale: 0 }}\n                              animate={{ scale: 1 }}\n                              transition={{ type: \"spring\", stiffness: 500, damping: 15 }}\n                            />\n                          )}\n                        </motion.label>\n                      </div>\n\n                      {/* Scheduled Time Selector */}\n                      <AnimatePresence>\n                        {deliveryTime === \"scheduled\" && (\n                          <motion.div\n                            initial={{ opacity: 0, height: 0 }}\n                            animate={{ opacity: 1, height: \"auto\" }}\n                            exit={{ opacity: 0, height: 0 }}\n                            transition={{ duration: 0.3 }}\n                            className=\"overflow-hidden\"\n                          >\n                            <div className=\"pt-3\">\n                              <motion.div\n                                animate={errors.scheduledTime\n                                  ? \"error\"\n                                  : focusedField === \"scheduledTime\"\n                                    ? \"focused\"\n                                    : \"initial\"\n                                }\n                                variants={formInputVariants}\n                                custom=\"scheduledTime\"\n                                className=\"relative\"\n                              >\n                                <div className=\"relative\">\n                                  <div className=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\">\n                                    <svg className=\"h-5 w-5 text-gray-500\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={1.5}\n                                        d=\"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z\" />\n                                    </svg>\n                                  </div>\n                                  <select\n                                    id=\"scheduledTime\"\n                                    className={`w-full bg-black/40 border ${\n                                      errors.scheduledTime\n                                        ? 'border-red-500 focus:border-red-400'\n                                        : focusedField === \"scheduledTime\"\n                                          ? 'border-lime-500'\n                                          : 'border-gray-700 focus:border-lime-700'\n                                    } rounded-lg pl-10 px-4 py-3 font-poppins text-white focus:outline-none transition-all duration-300 appearance-none`}\n                                    {...registerWithFocus(\"scheduledTime\")}\n                                  >\n                                    <option value=\"\">Select a time</option>\n                                    {/* Dynamic time options based on current time */}\n                                    <option value=\"12:00\">Today, 12:00 PM</option>\n                                    <option value=\"12:30\">Today, 12:30 PM</option>\n                                    <option value=\"13:00\">Today, 1:00 PM</option>\n                                    <option value=\"13:30\">Today, 1:30 PM</option>\n                                    <option value=\"14:00\">Today, 2:00 PM</option>\n                                    <option value=\"18:00\">Today, 6:00 PM</option>\n                                    <option value=\"18:30\">Today, 6:30 PM</option>\n                                    <option value=\"19:00\">Today, 7:00 PM</option>\n                                    <option value=\"19:30\">Today, 7:30 PM</option>\n                                    <option value=\"20:00\">Today, 8:00 PM</option>\n                                  </select>\n                                  <div className=\"absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none\">\n                                    <svg className=\"h-5 w-5 text-gray-500\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={1.5} d=\"M19 9l-7 7-7-7\" />\n                                    </svg>\n                                  </div>\n                                </div>\n                                {focusedField === \"scheduledTime\" && (\n                                  <motion.span\n                                    className=\"absolute inset-0 rounded-lg ring-2 ring-lime-500 pointer-events-none\"\n                                    initial={{ opacity: 0 }}\n                                    animate={{ opacity: 1 }}\n                                    exit={{ opacity: 0 }}\n                                  />\n                                )}\n                              </motion.div>\n                              {errors.scheduledTime && (\n                                <motion.p\n                                  initial={{ opacity: 0, y: -10 }}\n                                  animate={{ opacity: 1, y: 0 }}\n                                  className=\"mt-1 font-poppins text-red-500 text-sm\"\n                                >\n                                  {errors.scheduledTime.message}\n                                </motion.p>\n                              )}\n                            </div>\n                          </motion.div>\n                        )}\n                      </AnimatePresence>\n                    </div>\n\n                    {/* Personal Information Section Divider */}\n                    <div className=\"relative flex py-5 items-center mb-6\">\n                      <div className=\"flex-grow border-t border-gray-800\"></div>\n                      <span className=\"flex-shrink mx-3 text-gray-400 font-medium\">\n                        Personal Information\n                      </span>\n                      <div className=\"flex-grow border-t border-gray-800\"></div>\n                    </div>\n\n                    {/* First Name */}\n                    <div className=\"mb-8\">\n                      <label htmlFor=\"firstName\" className=\"block font-medium text-gray-300 mb-2 flex items-center\">\n                        <span>First Name</span>\n                        {errors.firstName && (\n                          <motion.span\n                            initial={{ opacity: 0, scale: 0.5 }}\n                            animate={{ opacity: 1, scale: 1 }}\n                            className=\"ml-2 text-red-500 text-xs\"\n                          >\n                            Required\n                          </motion.span>\n                        )}\n                      </label>\n                      <motion.div\n                        animate={errors.firstName\n                          ? \"error\"\n                          : focusedField === \"firstName\"\n                            ? \"focused\"\n                            : \"initial\"\n                        }\n                        variants={formInputVariants}\n                        custom=\"firstName\"\n                        className=\"relative\"\n                      >\n                        <input\n                          id=\"firstName\"\n                          className={`w-full bg-black/40 border ${\n                            errors.firstName\n                              ? 'border-red-500 focus:border-red-400'\n                              : focusedField === \"firstName\"\n                                ? 'border-cyan-500'\n                                : 'border-gray-700 focus:border-cyan-700'\n                          } rounded-lg px-4 py-3 font-poppins text-white focus:outline-none transition-all duration-300`}\n                          placeholder=\"John\"\n                          {...registerWithFocus(\"firstName\")}\n                        />\n                        {focusedField === \"firstName\" && (\n                          <motion.span\n                            className=\"absolute inset-0 rounded-lg ring-2 ring-cyan-500 pointer-events-none\"\n                            initial={{ opacity: 0 }}\n                            animate={{ opacity: 1 }}\n                            exit={{ opacity: 0 }}\n                          />\n                        )}\n                      </motion.div>\n                      {errors.firstName && (\n                        <motion.p\n                          initial={{ opacity: 0, y: -10 }}\n                          animate={{ opacity: 1, y: 0 }}\n                          className=\"mt-1 font-poppins text-red-500 text-sm\"\n                        >\n                          {errors.firstName.message}\n                        </motion.p>\n                      )}\n                    </div>\n\n                    {/* Email Address */}\n                    <div className=\"mb-8\">\n                      <label htmlFor=\"email\" className=\"block font-medium text-gray-300 mb-2 flex items-center\">\n                        <span>Email Address (Optional)</span>\n                      </label>\n                      <motion.div\n                        animate={errors.email\n                          ? \"error\"\n                          : focusedField === \"email\"\n                            ? \"focused\"\n                            : \"initial\"\n                        }\n                        variants={formInputVariants}\n                        custom=\"email\"\n                        className=\"relative\"\n                      >\n                        <div className=\"relative\">\n                          <div className=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\">\n                            <svg className=\"h-5 w-5 text-gray-500\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={1.5} d=\"M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z\" />\n                            </svg>\n                          </div>\n                          <input\n                            id=\"email\"\n                            type=\"email\"\n                            className={`w-full bg-black/40 border ${\n                              errors.email\n                                ? 'border-red-500 focus:border-red-400'\n                                : focusedField === \"email\"\n                                  ? 'border-cyan-500'\n                                  : 'border-gray-700 focus:border-cyan-700'\n                            } rounded-lg pl-10 px-4 py-3 font-poppins text-white focus:outline-none transition-all duration-300`}\n                            placeholder=\"<EMAIL> (optional)\"\n                            {...registerWithFocus(\"email\")}\n                          />\n                        </div>\n                        {focusedField === \"email\" && (\n                          <motion.span\n                            className=\"absolute inset-0 rounded-lg ring-2 ring-cyan-500 pointer-events-none\"\n                            initial={{ opacity: 0 }}\n                            animate={{ opacity: 1 }}\n                            exit={{ opacity: 0 }}\n                          />\n                        )}\n                      </motion.div>\n                      {errors.email && (\n                        <motion.p\n                          initial={{ opacity: 0, y: -10 }}\n                          animate={{ opacity: 1, y: 0 }}\n                          className=\"mt-1 font-poppins text-red-500 text-sm\"\n                        >\n                          {errors.email.message}\n                        </motion.p>\n                      )}\n                    </div>\n\n                    {/* Phone Number */}\n                    <div className=\"mb-8\">\n                      <label htmlFor=\"phone\" className=\"block font-medium text-gray-300 mb-2 flex items-center\">\n                        <span>Phone Number</span>\n                        {errors.phone && (\n                          <motion.span\n                            initial={{ opacity: 0, scale: 0.5 }}\n                            animate={{ opacity: 1, scale: 1 }}\n                            className=\"ml-2 text-red-500 text-xs\"\n                          >\n                            Required\n                          </motion.span>\n                        )}\n                      </label>\n                      <motion.div\n                        animate={errors.phone\n                          ? \"error\"\n                          : focusedField === \"phone\"\n                            ? \"focused\"\n                            : \"initial\"\n                        }\n                        variants={formInputVariants}\n                        custom=\"phone\"\n                        className=\"relative\"\n                      >\n                        <div className=\"relative\">\n                          <div className=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\">\n                            <svg className=\"h-5 w-5 text-gray-500\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={1.5} d=\"M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z\" />\n                            </svg>\n                          </div>\n                          <input\n                            id=\"phone\"\n                            type=\"tel\"\n                            className={`w-full bg-black/40 border ${\n                              errors.phone\n                                ? 'border-red-500 focus:border-red-400'\n                                : focusedField === \"phone\"\n                                  ? 'border-cyan-500'\n                                  : 'border-gray-700 focus:border-cyan-700'\n                            } rounded-lg pl-10 px-4 py-3 font-poppins text-white focus:outline-none transition-all duration-300`}\n                            placeholder=\"+47 12 34 56 78\"\n                            {...registerWithFocus(\"phone\")}\n                          />\n                        </div>\n                        {focusedField === \"phone\" && (\n                          <motion.span\n                            className=\"absolute inset-0 rounded-lg ring-2 ring-cyan-500 pointer-events-none\"\n                            initial={{ opacity: 0 }}\n                            animate={{ opacity: 1 }}\n                            exit={{ opacity: 0 }}\n                          />\n                        )}\n                      </motion.div>\n                      {errors.phone && (\n                        <motion.p\n                          initial={{ opacity: 0, y: -10 }}\n                          animate={{ opacity: 1, y: 0 }}\n                          className=\"mt-1 font-poppins text-red-500 text-sm\"\n                        >\n                          {errors.phone.message}\n                        </motion.p>\n                      )}\n                    </div>\n\n                    {/* Delivery Section Divider */}\n                    <div className=\"relative flex py-5 items-center mb-6\">\n                      <div className=\"flex-grow border-t border-gray-800\"></div>\n                      <span className=\"flex-shrink mx-3 text-gray-400 font-medium\">\n                        {orderType === \"delivery\" ? \"Delivery Address\" : \"Additional Details\"}\n                      </span>\n                      <div className=\"flex-grow border-t border-gray-800\"></div>\n                    </div>\n\n                    {/* Address Fields - Only shown for delivery */}\n                    <AnimatePresence>\n                      {orderType === \"delivery\" && (\n                        <motion.div\n                          initial={{ opacity: 0, height: 0 }}\n                          animate={{ opacity: 1, height: \"auto\" }}\n                          exit={{ opacity: 0, height: 0 }}\n                          transition={{ duration: 0.3 }}\n                          className=\"space-y-8 overflow-hidden\"\n                        >\n                          {/* Address */}\n                          <div className=\"mb-8\">\n                            <label htmlFor=\"address\" className=\"block font-medium text-gray-300 mb-2 flex items-center\">\n                              <span>Delivery Address</span>\n                              {errors.address && (\n                                <motion.span\n                                  initial={{ opacity: 0, scale: 0.5 }}\n                                  animate={{ opacity: 1, scale: 1 }}\n                                  className=\"ml-2 text-red-500 text-xs\"\n                                >\n                                  Required\n                                </motion.span>\n                              )}\n                            </label>\n                            <motion.div\n                              animate={errors.address\n                                ? \"error\"\n                                : focusedField === \"address\"\n                                  ? \"focused\"\n                                  : \"initial\"\n                              }\n                              variants={formInputVariants}\n                              custom=\"address\"\n                              className=\"relative\"\n                            >\n                              <div className=\"relative\">\n                                <div className=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\">\n                                  <svg className=\"h-5 w-5 text-gray-500\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={1.5} d=\"M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z\" />\n                                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={1.5} d=\"M15 11a3 3 0 11-6 0 3 3 0 016 0z\" />\n                                  </svg>\n                                </div>\n                                <input\n                                  id=\"address\"\n                                  className={`w-full bg-black/40 border ${\n                                    errors.address\n                                      ? 'border-red-500 focus:border-red-400'\n                                      : focusedField === \"address\"\n                                        ? 'border-cyan-500'\n                                        : 'border-gray-700 focus:border-cyan-700'\n                                  } rounded-lg pl-10 px-4 py-3 font-poppins text-white focus:outline-none transition-all duration-300`}\n                                  placeholder=\"Street address\"\n                                  {...registerWithFocus(\"address\")}\n                                />\n                              </div>\n                              {focusedField === \"address\" && (\n                                <motion.span\n                                  className=\"absolute inset-0 rounded-lg ring-2 ring-cyan-500 pointer-events-none\"\n                                  initial={{ opacity: 0 }}\n                                  animate={{ opacity: 1 }}\n                                  exit={{ opacity: 0 }}\n                                />\n                              )}\n                            </motion.div>\n                            {errors.address && (\n                              <motion.p\n                                initial={{ opacity: 0, y: -10 }}\n                                animate={{ opacity: 1, y: 0 }}\n                                className=\"mt-1 font-poppins text-red-500 text-sm\"\n                              >\n                                {errors.address.message}\n                              </motion.p>\n                            )}\n\n                            {/* Geolocation Button - Only show for delivery */}\n                            {orderType === \"delivery\" && (\n                              <motion.div\n                                className=\"mt-3 mb-6\"\n                                initial={{ opacity: 0, y: -10 }}\n                                animate={{ opacity: 1, y: 0 }}\n                                exit={{ opacity: 0, y: -10 }}\n                                transition={{ duration: 0.3 }}\n                              >\n                                <motion.button\n                                  type=\"button\"\n                                  onClick={getCurrentLocation}\n                                  disabled={isGettingLocation}\n                                  className={`flex items-center justify-center px-4 py-2 rounded-lg border transition-all duration-300\n                                    ${isGettingLocation\n                                      ? 'bg-gray-800/50 border-gray-600 cursor-not-allowed'\n                                      : 'bg-black/40 border-gray-700 hover:border-cyan-600 hover:bg-black/60'\n                                    } text-white font-poppins text-sm`}\n                                  whileHover={!isGettingLocation ? { scale: 1.02 } : {}}\n                                  whileTap={!isGettingLocation ? { scale: 0.98 } : {}}\n                                >\n                                  {isGettingLocation ? (\n                                    <>\n                                      <svg className=\"animate-spin -ml-1 mr-2 h-4 w-4 text-cyan-400\" fill=\"none\" viewBox=\"0 0 24 24\">\n                                        <circle className=\"opacity-25\" cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\" strokeWidth=\"4\"></circle>\n                                        <path className=\"opacity-75\" fill=\"currentColor\" d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"></path>\n                                      </svg>\n                                      Getting location...\n                                    </>\n                                  ) : (\n                                    <>\n                                      <svg className=\"w-4 h-4 mr-2 text-cyan-400\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                                        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={1.5}\n                                          d=\"M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z\" />\n                                        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={1.5}\n                                          d=\"M15 11a3 3 0 11-6 0 3 3 0 016 0z\" />\n                                      </svg>\n                                      Use Current Location\n                                    </>\n                                  )}\n                                </motion.button>\n\n                                {/* Location Error Message */}\n                                <AnimatePresence>\n                                  {locationError && (\n                                    <motion.div\n                                      initial={{ opacity: 0, y: -10 }}\n                                      animate={{ opacity: 1, y: 0 }}\n                                      exit={{ opacity: 0, y: -10 }}\n                                      className=\"mt-2 p-3 bg-red-900/20 border border-red-800/30 rounded-lg\"\n                                    >\n                                      <div className=\"flex items-start\">\n                                        <svg className=\"w-4 h-4 text-red-400 mt-0.5 mr-2 flex-shrink-0\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                                          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={1.5}\n                                            d=\"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z\" />\n                                        </svg>\n                                        <p className=\"text-red-300 text-xs font-poppins\">{locationError}</p>\n                                      </div>\n                                    </motion.div>\n                                  )}\n                                </AnimatePresence>\n\n                                {/* Helper Text */}\n                                <p className=\"mt-2 text-xs text-gray-500 font-poppins\">\n                                  Click to automatically fill your address using your current location\n                                </p>\n                              </motion.div>\n                            )}\n                          </div>\n\n                          <div className=\"grid grid-cols-1 md:grid-cols-3 gap-6\">\n                            {/* Postal Code */}\n                            <div>\n                              <label htmlFor=\"postalCode\" className=\"block font-medium text-gray-300 mb-2 flex items-center\">\n                                <span>Postal Code</span>\n                                {errors.postalCode && (\n                                  <motion.span\n                                    initial={{ opacity: 0, scale: 0.5 }}\n                                    animate={{ opacity: 1, scale: 1 }}\n                                    className=\"ml-2 text-red-500 text-xs\"\n                                  >\n                                    Required\n                                  </motion.span>\n                                )}\n                              </label>\n                              <motion.div\n                                animate={errors.postalCode\n                                  ? \"error\"\n                                  : focusedField === \"postalCode\"\n                                    ? \"focused\"\n                                    : \"initial\"\n                                }\n                                variants={formInputVariants}\n                                custom=\"postalCode\"\n                                className=\"relative\"\n                              >\n                                <input\n                                  id=\"postalCode\"\n                                  className={`w-full bg-black/40 border ${\n                                    errors.postalCode\n                                      ? 'border-red-500 focus:border-red-400'\n                                      : focusedField === \"postalCode\"\n                                        ? 'border-cyan-500'\n                                        : 'border-gray-700 focus:border-cyan-700'\n                                  } rounded-lg px-4 py-3 font-poppins text-white focus:outline-none transition-all duration-300`}\n                                  placeholder=\"0000\"\n                                  {...registerWithFocus(\"postalCode\")}\n                                />\n                                {focusedField === \"postalCode\" && (\n                                  <motion.span\n                                    className=\"absolute inset-0 rounded-lg ring-2 ring-cyan-500 pointer-events-none\"\n                                    initial={{ opacity: 0 }}\n                                    animate={{ opacity: 1 }}\n                                    exit={{ opacity: 0 }}\n                                  />\n                                )}\n                              </motion.div>\n                              {errors.postalCode && (\n                                <motion.p\n                                  initial={{ opacity: 0, y: -10 }}\n                                  animate={{ opacity: 1, y: 0 }}\n                                  className=\"mt-1 font-poppins text-red-500 text-sm\"\n                                >\n                                  {errors.postalCode.message}\n                                </motion.p>\n                              )}\n                            </div>\n\n                            {/* City */}\n                            <div className=\"md:col-span-2\">\n                              <label htmlFor=\"city\" className=\"block font-medium text-gray-300 mb-2 flex items-center\">\n                                <span>City</span>\n                                {errors.city && (\n                                  <motion.span\n                                    initial={{ opacity: 0, scale: 0.5 }}\n                                    animate={{ opacity: 1, scale: 1 }}\n                                    className=\"ml-2 text-red-500 text-xs\"\n                                  >\n                                    Required\n                                  </motion.span>\n                                )}\n                              </label>\n                              <motion.div\n                                animate={errors.city\n                                  ? \"error\"\n                                  : focusedField === \"city\"\n                                    ? \"focused\"\n                                    : \"initial\"\n                                }\n                                variants={formInputVariants}\n                                custom=\"city\"\n                                className=\"relative\"\n                              >\n                                <input\n                                  id=\"city\"\n                                  className={`w-full bg-black/40 border ${\n                                    errors.city\n                                      ? 'border-red-500 focus:border-red-400'\n                                      : focusedField === \"city\"\n                                        ? 'border-cyan-500'\n                                        : 'border-gray-700 focus:border-cyan-700'\n                                  } rounded-lg px-4 py-3 font-poppins text-white focus:outline-none transition-all duration-300`}\n                                  placeholder=\"Oslo\"\n                                  {...registerWithFocus(\"city\")}\n                                />\n                                {focusedField === \"city\" && (\n                                  <motion.span\n                                    className=\"absolute inset-0 rounded-lg ring-2 ring-cyan-500 pointer-events-none\"\n                                    initial={{ opacity: 0 }}\n                                    animate={{ opacity: 1 }}\n                                    exit={{ opacity: 0 }}\n                                  />\n                                )}\n                              </motion.div>\n                              {errors.city && (\n                                <motion.p\n                                  initial={{ opacity: 0, y: -10 }}\n                                  animate={{ opacity: 1, y: 0 }}\n                                  className=\"mt-1 font-poppins text-red-500 text-sm\"\n                                >\n                                  {errors.city.message}\n                                </motion.p>\n                              )}\n                            </div>\n                          </div>\n                        </motion.div>\n                      )}\n                    </AnimatePresence>\n\n                    {/* Special Instructions */}\n                    <div className=\"mb-8\">\n                      <label htmlFor=\"notes\" className=\"block font-medium text-gray-300 mb-2 flex items-center\">\n                        <span>Special Instructions (Optional)</span>\n                      </label>\n                      <motion.div\n                        animate={focusedField === \"notes\" ? \"focused\" : \"initial\"}\n                        variants={formInputVariants}\n                        custom=\"notes\"\n                        className=\"relative\"\n                      >\n                        <div className=\"relative\">\n                          <div className=\"absolute top-3 left-3 flex items-start pointer-events-none\">\n                            <svg className=\"h-5 w-5 text-gray-500\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={1.5} d=\"M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z\" />\n                            </svg>\n                          </div>\n                          <textarea\n                            id=\"notes\"\n                            rows={3}\n                            className={`w-full bg-black/40 border border-gray-700 rounded-lg pl-10 px-4 py-3 font-poppins text-white focus:outline-none focus:border-cyan-700 transition-all duration-300`}\n                            placeholder=\"Any special requests or delivery instructions\"\n                            {...registerWithFocus(\"notes\")}\n                          ></textarea>\n                        </div>\n                        {focusedField === \"notes\" && (\n                          <motion.span\n                            className=\"absolute inset-0 rounded-lg ring-2 ring-cyan-500 pointer-events-none\"\n                            initial={{ opacity: 0 }}\n                            animate={{ opacity: 1 }}\n                            exit={{ opacity: 0 }}\n                          />\n                        )}\n                      </motion.div>\n                    </div>\n\n                    {/* Order Button */}\n                    <motion.div\n                      initial={{ opacity: 0, y: 20 }}\n                      animate={{ opacity: 1, y: 0 }}\n                      transition={{ delay: 0.4, duration: 0.5 }}\n                      className=\"mt-10\"\n                    >\n                      <motion.button\n                        type=\"submit\"\n                        disabled={isSubmitting || !isValid || !isOpen}\n                        className={`relative w-full overflow-hidden rounded-lg p-4 flex items-center justify-center\n                                 font-bold text-white tracking-wide text-lg\n                                 ${isValid && isOpen ? 'opacity-100' : 'opacity-70'}`}\n                        whileHover={isValid && isOpen ? { scale: 1.02 } : {}}\n                        whileTap={isValid && isOpen ? { scale: 0.98 } : {}}\n                        title={!isOpen ? \"Restaurant is currently closed\" : \"\"}\n                      >\n                        {/* Button Background */}\n                        <span className=\"absolute inset-0 w-full h-full bg-gradient-to-r from-lime-600 to-green-700\"></span>\n\n                        {/* Animated Glow Effect */}\n                        <motion.span\n                          className=\"absolute inset-0 w-full h-full bg-gradient-to-r from-lime-500 to-green-600 opacity-0\"\n                          animate={{\n                            opacity: [0, 0.5, 0],\n                            scale: [1, 1.05, 1],\n                            boxShadow: [\n                              \"0 0 0 rgba(57, 255, 20, 0)\",\n                              \"0 0 20px rgba(57, 255, 20, 0.5)\",\n                              \"0 0 0 rgba(57, 255, 20, 0)\"\n                            ]\n                          }}\n                          transition={{ duration: 2.5, repeat: Infinity }}\n                        />\n\n                        {/* Button Text */}\n                        <span className=\"relative z-10 flex items-center\">\n                          {isSubmitting ? (\n                            <>\n                              <svg className=\"animate-spin -ml-1 mr-3 h-5 w-5 text-white\" fill=\"none\" viewBox=\"0 0 24 24\">\n                                <circle className=\"opacity-25\" cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\" strokeWidth=\"4\"></circle>\n                                <path className=\"opacity-75\" fill=\"currentColor\" d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"></path>\n                              </svg>\n                              Processing...\n                            </>\n                          ) : !isOpen ? (\n                            <>\n                              <svg className=\"w-6 h-6 mr-2\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z\" />\n                              </svg>\n                              Restaurant Closed\n                            </>\n                          ) : (\n                            <>\n                              Place Order\n                              <svg className=\"w-6 h-6 ml-2\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M14 5l7 7m0 0l-7 7m7-7H3\" />\n                              </svg>\n                            </>\n                          )}\n                        </span>\n                      </motion.button>\n                      <p className=\"text-gray-500 text-xs text-center mt-3\">\n                        By placing an order you agree to our Terms and Conditions\n                      </p>\n                    </motion.div>\n                  </form>\n                </div>\n              </div>\n            </motion.div>\n\n            {/* Order Summary (1/3 width on desktop) */}\n            <motion.div\n              variants={itemVariants}\n              ref={summaryRef}\n              className=\"lg:block\"\n            >\n              <div className=\"backdrop-blur-sm bg-black/30 rounded-xl border border-gray-800\n                            shadow-[0_0_25px_rgba(0,0,0,0.2)] sticky top-24\">\n\n                {/* Glowing top border */}\n                <div className=\"h-1 w-full bg-gradient-to-r from-fuchsia-500 via-cyan-500 to-fuchsia-500 rounded-t-xl\"></div>\n\n                <div className=\"p-6\">\n                  <h3 className=\"font-playfair text-xl font-bold text-white mb-6 flex items-center\">\n                    <svg className=\"w-5 h-5 mr-2 text-fuchsia-400\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M3 3h2l.4 2M7 13h10l4-8H5.4M7 13L5.4 5M7 13l-2.293 2.293c-.63.63-.184 1.707.707 1.707H17m0 0a2 2 0 100 4 2 2 0 000-4zm-8 2a2 2 0 11-4 0 2 2 0 014 0z\" />\n                    </svg>\n                    Order Summary\n                  </h3>\n\n                  <div className=\"space-y-5 mb-6\">\n                    {/* Order Items Summary */}\n                    <div className=\"border-b border-gray-800 pb-4\">\n                      <motion.div className=\"space-y-3\">\n                        <AnimatePresence>\n                          {cartItems.map((item, index) => (\n                            <motion.div\n                              key={item.id}\n                              initial={{ opacity: 0, y: 10 }}\n                              animate={{ opacity: 1, y: 0 }}\n                              exit={{ opacity: 0, y: -10 }}\n                              transition={{ delay: index * 0.1 }}\n                              className=\"flex items-start gap-3\"\n                            >\n                              <div className=\"relative w-10 h-10 rounded overflow-hidden flex-shrink-0\">\n                                <img\n                                  src={item.imageUrl}\n                                  alt={item.name}\n                                  className=\"w-full h-full object-cover\"\n                                />\n                              </div>\n                              <div className=\"flex-1 min-w-0\">\n                                <div className=\"flex justify-between\">\n                                  <p className=\"font-poppins font-medium text-sm text-white truncate pr-2\">\n                                    {item.name}\n                                  </p>\n                                  <div className=\"text-xs font-medium text-gray-400 whitespace-nowrap\">\n                                    × {item.quantity}\n                                  </div>\n                                </div>\n                                <motion.div\n                                  className=\"font-poppins text-sm text-transparent bg-clip-text bg-gradient-to-r from-fuchsia-400 to-cyan-400\"\n                                  animate={{\n                                    textShadow: ['0 0 3px rgba(255, 0, 255, 0.2)', '0 0 5px rgba(255, 0, 255, 0.3)', '0 0 3px rgba(255, 0, 255, 0.2)']\n                                  }}\n                                  transition={{ duration: 1.5, repeat: Infinity }}\n                                >\n                                  {formatCurrency(item.price * item.quantity)}\n                                </motion.div>\n                              </div>\n                            </motion.div>\n                          ))}\n                        </AnimatePresence>\n                      </motion.div>\n                    </div>\n\n                    {/* Costs Breakdown */}\n                    <div className=\"border-b border-gray-800 pb-4 space-y-3\">\n                      <motion.div\n                        className=\"flex justify-between\"\n                        initial={{ opacity: 0 }}\n                        animate={{ opacity: 1 }}\n                        transition={{ delay: 0.2 }}\n                      >\n                        <span className=\"font-poppins text-gray-400\">Subtotal</span>\n                        <motion.span\n                          className=\"font-poppins font-medium text-white\"\n                          animate={\n                            isUpdating !== null\n                              ? { scale: [1, 1.05, 1] }\n                              : {}\n                          }\n                          transition={{ duration: 0.3 }}\n                        >\n                          {formatCurrency(subtotal)}\n                        </motion.span>\n                      </motion.div>\n\n                      <motion.div\n                        className=\"flex justify-between\"\n                        initial={{ opacity: 0 }}\n                        animate={{ opacity: 1 }}\n                        transition={{ delay: 0.3 }}\n                      >\n                        <span className=\"font-poppins text-gray-400\">VAT (25%)</span>\n                        <span className=\"font-poppins font-medium text-white\">\n                          {formatCurrency(VAT)}\n                        </span>\n                      </motion.div>\n\n                      <motion.div\n                        className=\"flex justify-between\"\n                        initial={{ opacity: 0 }}\n                        animate={{ opacity: 1 }}\n                        transition={{ delay: 0.4 }}\n                      >\n                        <span className=\"font-poppins text-gray-400\">Delivery Fee</span>\n                        <span className=\"font-poppins font-medium text-white\">{formatCurrency(actualDeliveryFee)}</span>\n                      </motion.div>\n                    </div>\n\n                    {/* Total */}\n                    <motion.div\n                      className=\"flex justify-between items-center\"\n                      initial={{ opacity: 0 }}\n                      animate={{ opacity: 1 }}\n                      transition={{ delay: 0.5 }}\n                    >\n                      <span className=\"font-poppins font-medium text-lg text-white\">Total</span>\n                      <motion.span\n                        className=\"font-poppins font-bold text-transparent bg-clip-text bg-gradient-to-r from-lime-400 to-green-500 text-xl\"\n                        animate={{\n                          textShadow: ['0 0 5px rgba(57, 255, 20, 0.4)', '0 0 10px rgba(57, 255, 20, 0.6)', '0 0 5px rgba(57, 255, 20, 0.4)']\n                        }}\n                        transition={{ duration: 0.5, repeat: Infinity }}\n                      >\n                        {formatCurrency(animatedTotal)}\n                      </motion.span>\n                    </motion.div>\n                  </div>\n\n                  {/* Payment Method Selection */}\n                  <motion.div\n                    className=\"mb-6\"\n                    initial={{ opacity: 0, y: 20 }}\n                    animate={{ opacity: 1, y: 0 }}\n                    transition={{ delay: 0.6, duration: 0.5 }}\n                  >\n                    <h4 className=\"font-playfair text-lg font-medium text-white mb-3 flex items-center\">\n                      <svg className=\"w-5 h-5 mr-2 text-lime-400\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z\" />\n                      </svg>\n                      Payment Method\n                    </h4>\n\n                    <div className=\"space-y-3\">\n                      <motion.label\n                        className={`flex items-center bg-black/40 p-3 rounded-lg border border-gray-700 cursor-pointer\n                                  ${focusedField === \"paymentMethod\" ? 'border-lime-600 shadow-[0_0_10px_rgba(57,255,20,0.2)]' : 'hover:border-gray-600'}`}\n                        whileHover={{ scale: 1.01 }}\n                        whileTap={{ scale: 0.99 }}\n                      >\n                        <input\n                          type=\"radio\"\n                          value=\"card\"\n                          className=\"mr-3 accent-lime-500\"\n                          {...registerWithFocus(\"paymentMethod\")}\n                        />\n                        <span className=\"font-poppins text-white\">Credit Card</span>\n                        <div className=\"ml-auto flex space-x-2\">\n                          <span className=\"text-blue-400 opacity-80\">\n                            <svg className=\"w-6 h-6\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\n                              <path d=\"M21.5,9.5h-19C1.7,9.5,1,8.8,1,8s0.7-1.5,1.5-1.5h19c0.8,0,1.5,0.7,1.5,1.5S22.3,9.5,21.5,9.5z M21.5,5h-19 C1.7,5,1,4.3,1,3.5S1.7,2,2.5,2h19C22.3,2,23,2.7,23,3.5S22.3,5,21.5,5z M21.5,14h-19c-0.8,0-1.5-0.7-1.5-1.5s0.7-1.5,1.5-1.5h19 c0.8,0,1.5,0.7,1.5,1.5S22.3,14,21.5,14z M21.5,22h-19c-0.8,0-1.5-0.7-1.5-1.5S1.7,19,2.5,19h19c0.8,0,1.5,0.7,1.5,1.5 S22.3,22,21.5,22z\"/>\n                            </svg>\n                          </span>\n                          <span className=\"text-red-400 opacity-80\">\n                            <svg className=\"w-6 h-6\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\n                              <path d=\"M16,9C13.2,9,11,6.8,11,4s2.2-4,5-4s5,2.2,5,4S18.8,9,16,9z M16,2c-1.1,0-2,0.9-2,2s0.9,2,2,2s2-0.9,2-2S17.1,2,16,2z  M8,9C5.2,9,3,6.8,3,4s2.2-4,5-4s5,2.2,5,4S10.8,9,8,9z M8,2C6.9,2,6,2.9,6,4s0.9,2,2,2s2-0.9,2-2S9.1,2,8,2z\"/>\n                            </svg>\n                          </span>\n                        </div>\n                      </motion.label>\n\n                      <motion.label\n                        className={`flex items-center bg-black/40 p-3 rounded-lg border border-gray-700 cursor-pointer\n                                 ${focusedField === \"paymentMethod\" ? 'border-lime-600 shadow-[0_0_10px_rgba(57,255,20,0.2)]' : 'hover:border-gray-600'}`}\n                        whileHover={{ scale: 1.01 }}\n                        whileTap={{ scale: 0.99 }}\n                      >\n                        <input\n                          type=\"radio\"\n                          value=\"vipps\"\n                          className=\"mr-3 accent-lime-500\"\n                          {...registerWithFocus(\"paymentMethod\")}\n                        />\n                        <span className=\"font-poppins text-white\">Vipps</span>\n                      </motion.label>\n\n                      <motion.label\n                        className={`flex items-center bg-black/40 p-3 rounded-lg border border-gray-700 cursor-pointer\n                                 ${focusedField === \"paymentMethod\" ? 'border-lime-600 shadow-[0_0_10px_rgba(57,255,20,0.2)]' : 'hover:border-gray-600'}`}\n                        whileHover={{ scale: 1.01 }}\n                        whileTap={{ scale: 0.99 }}\n                      >\n                        <input\n                          type=\"radio\"\n                          value=\"cash\"\n                          className=\"mr-3 accent-lime-500\"\n                          {...registerWithFocus(\"paymentMethod\")}\n                        />\n                        <span className=\"font-poppins text-white\">Cash on Delivery</span>\n                      </motion.label>\n                    </div>\n                  </motion.div>\n\n                  {/* Estimated Delivery */}\n                  <motion.div\n                    className=\"bg-black/40 p-4 rounded-lg border border-lime-800/30\n                             shadow-[0_0_15px_rgba(57,255,20,0.1)]\"\n                    initial={{ opacity: 0, y: 20 }}\n                    animate={{ opacity: 1, y: 0 }}\n                    transition={{ delay: 0.7, duration: 0.5 }}\n                  >\n                    <div className=\"flex items-center mb-2\">\n                      <svg className=\"w-5 h-5 mr-2 text-lime-400\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\" />\n                      </svg>\n                      <span className=\"font-medium text-white\">Estimated Delivery</span>\n                    </div>\n                    <p className=\"text-gray-400 text-sm\">{estimatedTime} after order confirmation</p>\n                  </motion.div>\n                </div>\n              </div>\n            </motion.div>\n          </div>\n        </motion.div>\n      </div>\n    </section>\n  );\n};\n\nexport default Checkout;\n", "modifiedCode": "import { useState, useEffect } from \"react\";\nimport { useLocation, Link } from \"wouter\";\nimport { useForm } from \"react-hook-form\";\nimport { zodResolver } from \"@hookform/resolvers/zod\";\nimport { z } from \"zod\";\nimport { useCart } from \"@/context/CartContext\";\nimport { useRestaurantStatus } from \"@/context/RestaurantStatusContext\";\nimport { formatCurrency } from \"@/lib/utils\";\nimport { createOrder } from \"@/api/api\";\nimport Button from \"@/components/Button\";\nimport { motion, AnimatePresence } from \"framer-motion\";\nimport { useInView } from \"react-intersection-observer\";\n\nconst checkoutSchema = z.object({\n  firstName: z.string().min(2, \"First name is required\"),\n  email: z.string().email(\"Please enter a valid email\").optional().or(z.literal(\"\")),\n  phone: z.string()\n    .min(1, \"Phone number is required\")\n    .regex(/^\\+47\\s?\\d{2}\\s?\\d{2}\\s?\\d{2}\\s?\\d{2}$/, \"Please enter a valid Norwegian phone number (+47 XX XX XX XX)\"),\n  orderType: z.enum([\"delivery\", \"takeaway\"]),\n  deliveryTime: z.enum([\"asap\", \"scheduled\"]),\n  scheduledTime: z.string().optional(),\n  address: z.string().optional(),\n  postalCode: z.string().optional(),\n  city: z.string().optional(),\n  notes: z.string().optional(),\n  paymentMethod: z.enum([\"card\", \"vipps\", \"cash\"])\n}).refine(data => {\n  // If delivery is selected, address is required\n  if (data.orderType === \"delivery\") {\n    return !!data.address && !!data.postalCode && !!data.city;\n  }\n  return true;\n}, {\n  message: \"Address details are required for delivery\",\n  path: [\"address\"]\n}).refine(data => {\n  // If scheduled time is selected, it must be provided\n  if (data.deliveryTime === \"scheduled\") {\n    return !!data.scheduledTime;\n  }\n  return true;\n}, {\n  message: \"Please select a time for scheduled delivery/pickup\",\n  path: [\"scheduledTime\"]\n});\n\ntype CheckoutFormData = z.infer<typeof checkoutSchema>;\n\nconst Checkout = () => {\n  const { cartItems, clearCart } = useCart();\n  const { isOpen } = useRestaurantStatus();\n  const [, setLocation] = useLocation();\n  const [isSubmitting, setIsSubmitting] = useState(false);\n  const [submitSuccess, setSubmitSuccess] = useState(false);\n  const [animatedTotal, setAnimatedTotal] = useState(0);\n  const [focusedField, setFocusedField] = useState<string | null>(null);\n\n  // Animation refs for sections\n  const [headerRef, headerInView] = useInView({\n    triggerOnce: true,\n    threshold: 0.1,\n  });\n\n  const [formRef, formInView] = useInView({\n    triggerOnce: true,\n    threshold: 0.1,\n  });\n\n  const [summaryRef, summaryInView] = useInView({\n    triggerOnce: true,\n    threshold: 0.1,\n  });\n\n  // Animation variants\n  const containerVariants = {\n    hidden: { opacity: 0 },\n    visible: {\n      opacity: 1,\n      transition: {\n        staggerChildren: 0.1\n      }\n    },\n    exit: {\n      opacity: 0,\n      transition: {\n        duration: 0.3\n      }\n    }\n  };\n\n  const itemVariants = {\n    hidden: { y: 20, opacity: 0 },\n    visible: {\n      y: 0,\n      opacity: 1,\n      transition: {\n        type: \"spring\",\n        stiffness: 100,\n        damping: 12\n      }\n    }\n  };\n\n  const successVariants = {\n    hidden: { scale: 0.8, opacity: 0 },\n    visible: {\n      scale: 1,\n      opacity: 1,\n      transition: {\n        type: \"spring\",\n        stiffness: 200,\n        damping: 20,\n        duration: 0.5\n      }\n    }\n  };\n\n  const formInputVariants = {\n    initial: { scale: 1, boxShadow: \"0 0 0px rgba(0, 0, 0, 0)\" },\n    focused: {\n      scale: 1.02,\n      boxShadow: \"0 0 15px rgba(0, 255, 255, 0.3)\",\n      transition: {\n        type: \"spring\",\n        stiffness: 500,\n        damping: 20,\n        duration: 0.2\n      }\n    },\n    paymentFocused: {\n      scale: 1.02,\n      boxShadow: \"0 0 15px rgba(57, 255, 20, 0.3)\",\n      transition: {\n        type: \"spring\",\n        stiffness: 500,\n        damping: 20,\n        duration: 0.2\n      }\n    },\n    error: {\n      scale: [1, 1.02, 1],\n      boxShadow: \"0 0 15px rgba(255, 0, 0, 0.3)\",\n      transition: {\n        scale: {\n          duration: 0.3,\n          repeat: 2\n        }\n      }\n    }\n  };\n\n  const {\n    register,\n    handleSubmit,\n    watch,\n    setValue,\n    clearErrors,\n    formState: { errors, touchedFields, isValid }\n  } = useForm<CheckoutFormData>({\n    resolver: zodResolver(checkoutSchema),\n    defaultValues: {\n      paymentMethod: \"card\",\n      orderType: \"delivery\",\n      deliveryTime: \"asap\",\n      phone: \"+47 \"\n    },\n    mode: \"onChange\"\n  });\n\n  // Watch form values for conditional rendering\n  const orderType = watch(\"orderType\");\n  const deliveryTime = watch(\"deliveryTime\");\n\n  // State for delivery fee and settings\n  const [deliveryFee, setDeliveryFee] = useState(89); // Default fallback\n  const [estimatedTime, setEstimatedTime] = useState(\"30-45 minutes\");\n  const [isLoadingSettings, setIsLoadingSettings] = useState(true);\n\n  // Fetch delivery fee from API\n  useEffect(() => {\n    const fetchSettings = async () => {\n      try {\n        const response = await fetch('/api/settings');\n        if (response.ok) {\n          const settings = await response.json();\n          setDeliveryFee(settings.delivery_fee);\n          setEstimatedTime(settings.estimated_time);\n        }\n      } catch (error) {\n        console.error('Failed to fetch settings:', error);\n        // Keep default values on error\n      } finally {\n        setIsLoadingSettings(false);\n      }\n    };\n\n    fetchSettings();\n  }, []);\n\n  const subtotal = cartItems.reduce((total, item) => total + (item.price * item.quantity), 0);\n  const actualDeliveryFee = cartItems.length > 0 ? deliveryFee : 0;\n  const VAT = subtotal * 0.25; // 25% VAT\n  const total = subtotal + actualDeliveryFee + VAT;\n\n  // Use effect for animated total - much faster animation\n  useEffect(() => {\n    const duration = 200; // Much faster animation (was 800ms)\n    const steps = 5;      // Fewer steps (was 20)\n    const stepDuration = duration / steps;\n    const increment = (total - animatedTotal) / steps;\n\n    // If the difference is very small, just set it directly\n    if (Math.abs(total - animatedTotal) < 2) {\n      setAnimatedTotal(total);\n      return;\n    }\n\n    const timer = setTimeout(() => {\n      setAnimatedTotal(prev => {\n        const next = prev + increment;\n        // If we're close enough, just set the exact value\n        if (Math.abs(next - total) < 2) return total;\n        return next;\n      });\n    }, stepDuration);\n\n    return () => clearTimeout(timer);\n  }, [total, animatedTotal]);\n\n  // Add a missing state\n  const [isUpdating, setIsUpdating] = useState<number | null>(null);\n\n  // Geolocation states\n  const [isGettingLocation, setIsGettingLocation] = useState(false);\n  const [locationError, setLocationError] = useState<string | null>(null);\n\n  // Clear location error when order type changes or address field is modified\n  useEffect(() => {\n    setLocationError(null);\n  }, [orderType]);\n\n  // Clear location error when user starts typing in address field\n  const addressValue = watch('address');\n  useEffect(() => {\n    if (addressValue && locationError) {\n      setLocationError(null);\n    }\n  }, [addressValue, locationError]);\n\n  // Custom register to track focus state\n  const registerWithFocus = (name: keyof CheckoutFormData) => {\n    return {\n      ...register(name),\n      onFocus: () => setFocusedField(name),\n      onBlur: () => setFocusedField(null)\n    };\n  };\n\n  const onSubmit = async (data: CheckoutFormData) => {\n    // Check if cart is empty\n    if (cartItems.length === 0) {\n      setLocation(\"/menu\");\n      return;\n    }\n\n    // Check if restaurant is open\n    if (!isOpen) {\n      alert(\"Sorry, the restaurant is currently closed. Please try again during business hours.\");\n      return;\n    }\n\n    setIsSubmitting(true);\n\n    try {\n      const orderData = {\n        customer: {\n          firstName: data.firstName,\n          lastName: data.lastName,\n          email: data.email,\n          phone: data.phone,\n          address: data.address,\n          postalCode: data.postalCode,\n          city: data.city\n        },\n        orderDetails: {\n          type: data.orderType,\n          time: data.deliveryTime,\n          scheduledTime: data.scheduledTime || null\n        },\n        items: cartItems,\n        subtotal,\n        deliveryFee: data.orderType === \"delivery\" ? actualDeliveryFee : 0,\n        total: data.orderType === \"delivery\" ? total : subtotal + VAT,\n        paymentMethod: data.paymentMethod,\n        notes: data.notes || \"\",\n        status: \"pending\"\n      };\n\n      const createdOrder = await createOrder(orderData);\n\n      // Clear cart after successful order\n      clearCart();\n\n      // Navigate to order confirmation with the order ID\n      const orderId = createdOrder.id || `BBC${Math.floor(Math.random() * 100000)}`;\n      setLocation(`/order-confirmation/${orderId}`);\n\n    } catch (error) {\n      console.error(\"Order submission failed:\", error);\n      alert(\"There was an error processing your order. Please try again.\");\n      setIsSubmitting(false);\n    }\n  };\n\n  // Geolocation function\n  const getCurrentLocation = async () => {\n    console.log('getCurrentLocation called');\n    setIsGettingLocation(true);\n    setLocationError(null);\n\n    try {\n      // Check if geolocation is supported\n      if (!navigator.geolocation) {\n        throw new Error('Geolocation is not supported by this browser');\n      }\n\n      console.log('Requesting geolocation...');\n      // Get current position\n      const position = await new Promise<GeolocationPosition>((resolve, reject) => {\n        navigator.geolocation.getCurrentPosition(\n          resolve,\n          reject,\n          {\n            enableHighAccuracy: true,\n            timeout: 10000,\n            maximumAge: 300000 // 5 minutes\n          }\n        );\n      });\n\n      const { latitude, longitude } = position.coords;\n      console.log('Got coordinates:', latitude, longitude);\n\n      // Use Nominatim (OpenStreetMap) for reverse geocoding (free alternative to Google Maps)\n      const response = await fetch(\n        `https://nominatim.openstreetmap.org/reverse?format=json&lat=${latitude}&lon=${longitude}&addressdetails=1&accept-language=no,en`\n      );\n\n      if (!response.ok) {\n        throw new Error('Failed to get address from coordinates');\n      }\n\n      const data = await response.json();\n      console.log('Reverse geocoding response:', data);\n\n      if (!data || !data.address) {\n        throw new Error('No address found for this location');\n      }\n\n      const address = data.address;\n\n      // Extract Norwegian address components\n      const streetNumber = address.house_number || '';\n      const streetName = address.road || address.pedestrian || address.footway || '';\n      const fullStreetAddress = streetNumber && streetName\n        ? `${streetName} ${streetNumber}`\n        : streetName || address.display_name?.split(',')[0] || '';\n\n      const postalCode = address.postcode || '';\n      const city = address.city || address.town || address.village || address.municipality || '';\n\n      console.log('Extracted address components:', {\n        fullStreetAddress,\n        postalCode,\n        city,\n        countryCode: address.country_code\n      });\n\n      // Validate that we have essential Norwegian address components\n      if (!fullStreetAddress || !postalCode || !city) {\n        throw new Error('Could not determine complete address. Please enter manually.');\n      }\n\n      // Check if the location is in Norway (basic validation)\n      if (address.country_code !== 'no') {\n        throw new Error('Location appears to be outside Norway. Please enter a Norwegian address.');\n      }\n\n      console.log('Setting form values...');\n      // Auto-fill the form fields with proper validation\n      setValue('address', fullStreetAddress, { shouldValidate: true, shouldDirty: true });\n      setValue('postalCode', postalCode, { shouldValidate: true, shouldDirty: true });\n      setValue('city', city, { shouldValidate: true, shouldDirty: true });\n\n      // Clear any existing errors for these fields\n      clearErrors(['address', 'postalCode', 'city']);\n\n      // Show success message briefly\n      setLocationError(null);\n      console.log('Address fields populated successfully');\n\n    } catch (error: any) {\n      console.error('Geolocation error:', error);\n\n      let errorMessage = 'Unable to get your location. ';\n\n      if (error.code === 1) {\n        errorMessage += 'Location access denied. Please enable location services and try again.';\n      } else if (error.code === 2) {\n        errorMessage += 'Location unavailable. Please check your connection and try again.';\n      } else if (error.code === 3) {\n        errorMessage += 'Location request timed out. Please try again.';\n      } else {\n        errorMessage += error.message || 'Please enter your address manually.';\n      }\n\n      setLocationError(errorMessage);\n    } finally {\n      setIsGettingLocation(false);\n    }\n  };\n\n  // Success Confirmation Component\n  if (submitSuccess) {\n    return (\n      <section className=\"min-h-screen py-32 bg-black relative overflow-hidden\">\n        {/* Animated Gradient Background */}\n        <div\n          className=\"absolute inset-0 z-0\"\n          style={{\n            background: \"radial-gradient(circle at 20% 80%, rgba(57, 255, 20, 0.03), transparent 33%), \" +\n                      \"radial-gradient(circle at 80% 20%, rgba(0, 255, 255, 0.03), transparent 33%), \" +\n                      \"radial-gradient(circle at 50% 50%, rgba(255, 0, 255, 0.03), rgba(0, 0, 0, 1) 100%)\",\n          }}\n        ></div>\n\n        {/* Animated success message */}\n        <div className=\"container mx-auto px-4 z-10 relative\">\n          <motion.div\n            className=\"max-w-2xl mx-auto\"\n            variants={successVariants}\n            initial=\"hidden\"\n            animate=\"visible\"\n          >\n            <div className=\"bg-black/30 backdrop-blur-sm rounded-2xl p-8 md:p-12 border border-lime-800/30\n                          shadow-[0_0_30px_rgba(57,255,20,0.2)]\">\n              <div className=\"text-center\">\n                <motion.div\n                  className=\"w-24 h-24 mx-auto mb-6 rounded-full bg-gradient-to-br from-lime-500 to-green-700\n                           flex items-center justify-center text-white\"\n                  animate={{\n                    boxShadow: [\n                      \"0 0 0px rgba(57, 255, 20, 0)\",\n                      \"0 0 30px rgba(57, 255, 20, 0.8)\",\n                      \"0 0 10px rgba(57, 255, 20, 0.4)\"\n                    ],\n                    scale: [1, 1.1, 1]\n                  }}\n                  transition={{ duration: 2, repeat: Infinity, repeatType: \"reverse\" }}\n                >\n                  <svg className=\"w-12 h-12\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={3} d=\"M5 13l4 4L19 7\" />\n                  </svg>\n                </motion.div>\n\n                <motion.h2\n                  className=\"font-playfair text-3xl md:text-4xl font-bold mb-4 text-white\"\n                  initial={{ opacity: 0, y: 20 }}\n                  animate={{ opacity: 1, y: 0 }}\n                  transition={{ delay: 0.3, duration: 0.5 }}\n                >\n                  Order Confirmed!\n                </motion.h2>\n\n                <motion.div\n                  className=\"text-gray-300 mb-8 space-y-2\"\n                  initial={{ opacity: 0 }}\n                  animate={{ opacity: 1 }}\n                  transition={{ delay: 0.5, duration: 0.5 }}\n                >\n                  <p className=\"text-lg\">Your delicious BBQ is on the way!</p>\n                  <p>You will receive a confirmation email shortly.</p>\n                </motion.div>\n\n                {/* Confetti animation */}\n                <div className=\"absolute inset-0 pointer-events-none\">\n                  {[...Array(20)].map((_, i) => (\n                    <motion.div\n                      key={i}\n                      className=\"absolute w-4 h-4 rounded-full\"\n                      style={{\n                        backgroundColor:\n                          i % 3 === 0 ? 'rgba(57, 255, 20, 0.7)' :\n                          i % 3 === 1 ? 'rgba(0, 255, 255, 0.7)' :\n                          'rgba(255, 0, 255, 0.7)',\n                        top: `${Math.random() * 100}%`,\n                        left: `${Math.random() * 100}%`,\n                      }}\n                      initial={{\n                        y: -20,\n                        opacity: 0,\n                        scale: 0\n                      }}\n                      animate={{\n                        y: [0, 100 + Math.random() * 300],\n                        opacity: [1, 0],\n                        scale: [1, 0.5],\n                        rotate: [0, Math.random() * 360]\n                      }}\n                      transition={{\n                        duration: 2 + Math.random() * 3,\n                        delay: Math.random() * 1,\n                        ease: \"easeOut\"\n                      }}\n                    />\n                  ))}\n                </div>\n\n                <motion.p\n                  className=\"text-sm text-gray-500 mb-8\"\n                  initial={{ opacity: 0 }}\n                  animate={{ opacity: 1 }}\n                  transition={{ delay: 0.8, duration: 0.5 }}\n                >\n                  Redirecting to home page...\n                </motion.p>\n\n                <div className=\"flex justify-center\">\n                  <motion.div\n                    initial={{ opacity: 0, y: 20 }}\n                    animate={{ opacity: 1, y: 0 }}\n                    transition={{ delay: 1, duration: 0.5 }}\n                  >\n                    <Link href=\"/\">\n                      <Button variant=\"outline-primary\">\n                        Return to Home\n                      </Button>\n                    </Link>\n                  </motion.div>\n                </div>\n              </div>\n            </div>\n          </motion.div>\n        </div>\n      </section>\n    );\n  }\n\n  // Empty Cart State\n  if (cartItems.length === 0) {\n    return (\n      <section className=\"min-h-screen py-32 bg-black relative overflow-hidden\">\n        {/* Animated Gradient Background */}\n        <div\n          className=\"absolute inset-0 z-0\"\n          style={{\n            background: \"radial-gradient(circle at 20% 80%, rgba(0, 255, 255, 0.03), transparent 33%), \" +\n                      \"radial-gradient(circle at 80% 20%, rgba(255, 0, 255, 0.03), transparent 33%), \" +\n                      \"radial-gradient(circle at 50% 50%, rgba(57, 255, 20, 0.01), rgba(0, 0, 0, 1) 100%)\",\n          }}\n        ></div>\n\n        {/* Neon Grid Overlay */}\n        <div className=\"absolute inset-0 z-0 opacity-[0.02]\"\n             style={{\n               backgroundImage: \"linear-gradient(to right, #FF00FF 1px, transparent 1px), linear-gradient(to bottom, #FF00FF 1px, transparent 1px)\",\n               backgroundSize: \"40px 40px\"\n             }}>\n        </div>\n\n        <div className=\"container mx-auto px-4 relative z-10\">\n          <motion.div\n            className=\"text-center mb-16\"\n            initial={{ opacity: 0, y: -20 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.8 }}\n          >\n            <motion.h2\n              className=\"font-playfair text-4xl md:text-5xl font-bold mb-4 text-white\"\n              initial={{ opacity: 0 }}\n              animate={{ opacity: 1 }}\n              transition={{ duration: 0.5, delay: 0.2 }}\n            >\n              <span className=\"text-white\">Your </span>\n              <motion.span\n                className=\"text-transparent bg-clip-text bg-gradient-to-r from-cyan-400 to-fuchsia-500\"\n                animate={{\n                  textShadow: [\n                    \"0 0 5px rgba(0, 255, 255, 0.7), 0 0 10px rgba(0, 255, 255, 0.5), 0 0 15px rgba(0, 255, 255, 0.3)\",\n                    \"0 0 5px rgba(255, 0, 255, 0.7), 0 0 10px rgba(255, 0, 255, 0.5), 0 0 15px rgba(255, 0, 255, 0.3)\",\n                    \"0 0 5px rgba(0, 255, 255, 0.7), 0 0 10px rgba(0, 255, 255, 0.5), 0 0 15px rgba(0, 255, 255, 0.3)\",\n                  ]\n                }}\n                transition={{ duration: 3, repeat: Infinity }}\n              >\n                Checkout\n              </motion.span>\n            </motion.h2>\n          </motion.div>\n\n          <motion.div\n            className=\"max-w-2xl mx-auto\"\n            initial={{ opacity: 0, y: 20 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.5, delay: 0.3 }}\n          >\n            <div className=\"backdrop-blur-sm bg-black/20 rounded-xl p-10 border border-gray-800\">\n              <div className=\"text-center\">\n                {/* Animated Empty Cart Icon */}\n                <motion.div\n                  className=\"w-32 h-32 mx-auto mb-6 relative\"\n                  animate={{\n                    rotateY: [0, 10, 0, -10, 0],\n                  }}\n                  transition={{ duration: 4, repeat: Infinity }}\n                >\n                  <svg\n                    className=\"w-full h-full text-gray-600\"\n                    viewBox=\"0 0 24 24\"\n                    fill=\"none\"\n                    xmlns=\"http://www.w3.org/2000/svg\"\n                  >\n                    <motion.path\n                      d=\"M3 3h2l.5 3M7 13h10l4-8H5.5M7 13L5.5 6M7 13l-2.3 2.3c-.4.4-.1 1.7 1.1 1.7H17\"\n                      stroke=\"currentColor\"\n                      strokeWidth=\"1.5\"\n                      strokeLinecap=\"round\"\n                      strokeLinejoin=\"round\"\n                      initial={{ pathLength: 0 }}\n                      animate={{ pathLength: 1 }}\n                      transition={{ duration: 2 }}\n                    />\n                    <motion.path\n                      d=\"M17 18a2 2 0 100 4 2 2 0 000-4zM7 18a2 2 0 100 4 2 2 0 000-4z\"\n                      stroke=\"currentColor\"\n                      strokeWidth=\"1.5\"\n                      strokeLinecap=\"round\"\n                      strokeLinejoin=\"round\"\n                      initial={{ pathLength: 0 }}\n                      animate={{ pathLength: 1 }}\n                      transition={{ duration: 1.5, delay: 1.5 }}\n                    />\n                  </svg>\n\n                  {/* Animated empty indicator */}\n                  <motion.div\n                    className=\"absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 w-16 h-0.5 bg-gray-600\"\n                    initial={{ rotate: 45, scale: 0 }}\n                    animate={{ rotate: 45, scale: 1 }}\n                    transition={{ delay: 2.5, duration: 0.5 }}\n                  />\n                </motion.div>\n\n                <motion.h3\n                  className=\"font-playfair text-2xl font-bold text-white mb-3\"\n                  initial={{ opacity: 0, y: 10 }}\n                  animate={{ opacity: 1, y: 0 }}\n                  transition={{ delay: 0.5, duration: 0.5 }}\n                >\n                  Your cart is empty\n                </motion.h3>\n\n                <motion.p\n                  className=\"font-poppins text-gray-400 mb-8 max-w-md mx-auto\"\n                  initial={{ opacity: 0 }}\n                  animate={{ opacity: 1 }}\n                  transition={{ delay: 0.7, duration: 0.5 }}\n                >\n                  Your culinary journey awaits! Visit our menu to discover premium BBQ dishes crafted to perfection.\n                </motion.p>\n\n                <motion.div\n                  initial={{ opacity: 0, y: 20 }}\n                  animate={{ opacity: 1, y: 0 }}\n                  transition={{ delay: 0.9, duration: 0.5 }}\n                >\n                  <Link href=\"/menu\">\n                    <motion.button\n                      className=\"relative overflow-hidden rounded-md px-8 py-3 bg-transparent\"\n                      whileHover={{ scale: 1.03 }}\n                      whileTap={{ scale: 0.97 }}\n                    >\n                      {/* Button Background */}\n                      <span className=\"absolute inset-0 w-full h-full bg-gradient-to-r from-cyan-800/70 to-cyan-600/70\"></span>\n\n                      {/* Button Glow Effect */}\n                      <span className=\"absolute inset-0 w-full h-full transition-all duration-300\n                                      bg-gradient-to-r from-cyan-600 via-blue-600 to-cyan-600\n                                      opacity-0 hover:opacity-100 hover:blur-md\"></span>\n\n                      {/* Button Border */}\n                      <span className=\"absolute inset-0 w-full h-full border border-cyan-500 rounded-md\"></span>\n\n                      {/* Button Text */}\n                      <span className=\"relative z-10 flex items-center font-medium text-white\">\n                        <svg className=\"w-5 h-5 mr-2\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M4 6h16M4 12h8m-8 6h16\" />\n                        </svg>\n                        Browse Menu\n                      </span>\n                    </motion.button>\n                  </Link>\n                </motion.div>\n              </div>\n            </div>\n          </motion.div>\n        </div>\n      </section>\n    );\n  }\n\n  return (\n    <section className=\"min-h-screen py-32 bg-black relative overflow-hidden\">\n      {/* Animated Gradient Background */}\n      <div\n        className=\"absolute inset-0 z-0\"\n        style={{\n          background: \"radial-gradient(circle at 20% 80%, rgba(0, 255, 255, 0.03), transparent 33%), \" +\n                    \"radial-gradient(circle at 80% 20%, rgba(255, 0, 255, 0.03), transparent 33%), \" +\n                    \"radial-gradient(circle at 50% 50%, rgba(57, 255, 20, 0.01), rgba(0, 0, 0, 1) 100%)\",\n        }}\n      ></div>\n\n      {/* Neon Grid Overlay */}\n      <div className=\"absolute inset-0 z-0 opacity-[0.02]\"\n           style={{\n             backgroundImage: \"linear-gradient(to right, #FF00FF 1px, transparent 1px), linear-gradient(to bottom, #FF00FF 1px, transparent 1px)\",\n             backgroundSize: \"40px 40px\"\n           }}>\n      </div>\n\n      <div className=\"container mx-auto px-4 relative z-10\">\n        {/* Header Section */}\n        <motion.div\n          ref={headerRef}\n          initial={{ opacity: 0, y: -20 }}\n          animate={headerInView ? { opacity: 1, y: 0 } : {}}\n          transition={{ duration: 0.8 }}\n          className=\"text-center mb-16\"\n        >\n          <motion.h2\n            className=\"font-playfair text-4xl md:text-5xl font-bold mb-4 text-white inline-block\"\n            initial={{ opacity: 0 }}\n            animate={headerInView ? { opacity: 1 } : {}}\n            transition={{ duration: 0.5, delay: 0.2 }}\n          >\n            <span className=\"text-white\">Complete </span>\n            <motion.span\n              className=\"text-transparent bg-clip-text bg-gradient-to-r from-lime-400 to-green-500\"\n              animate={{\n                textShadow: [\n                  \"0 0 5px rgba(57, 255, 20, 0.7), 0 0 10px rgba(57, 255, 20, 0.5), 0 0 15px rgba(57, 255, 20, 0.3)\",\n                  \"0 0 5px rgba(0, 255, 255, 0.7), 0 0 10px rgba(0, 255, 255, 0.5), 0 0 15px rgba(0, 255, 255, 0.3)\",\n                  \"0 0 5px rgba(57, 255, 20, 0.7), 0 0 10px rgba(57, 255, 20, 0.5), 0 0 15px rgba(57, 255, 20, 0.3)\"\n                ]\n              }}\n              transition={{ duration: 3, repeat: Infinity }}\n            >\n              Your Order\n            </motion.span>\n          </motion.h2>\n\n          <motion.p\n            className=\"font-poppins text-lg text-gray-400 max-w-2xl mx-auto\"\n            initial={{ opacity: 0, y: 20 }}\n            animate={headerInView ? { opacity: 1, y: 0 } : {}}\n            transition={{ duration: 0.6, delay: 0.4 }}\n          >\n            We're just a few details away from your premium BBQ experience\n          </motion.p>\n        </motion.div>\n\n        <motion.div\n          ref={formRef}\n          variants={containerVariants}\n          initial=\"hidden\"\n          animate={formInView ? \"visible\" : \"hidden\"}\n          className=\"max-w-6xl mx-auto\"\n        >\n          <div className=\"lg:grid lg:grid-cols-3 lg:gap-10\">\n            {/* Checkout Form (2/3 width on desktop) */}\n            <motion.div\n              className=\"lg:col-span-2 mb-10 lg:mb-0\"\n              variants={itemVariants}\n            >\n              <div className=\"backdrop-blur-sm bg-black/20 rounded-xl border border-gray-800\n                             overflow-hidden shadow-[0_10px_30px_rgba(0,0,0,0.3)]\">\n                {/* Form Header */}\n                <div className=\"relative\">\n                  <div className=\"h-3 w-full bg-gradient-to-r from-cyan-500 via-fuchsia-500 to-lime-500\"></div>\n                  <div className=\"px-8 py-6 border-b border-gray-800\">\n                    <h3 className=\"font-playfair text-2xl font-bold text-white flex items-center\">\n                      <svg className=\"w-6 h-6 mr-2 text-cyan-400\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z\" />\n                      </svg>\n                      Delivery Information\n                    </h3>\n                  </div>\n                </div>\n\n                {/* Form Content */}\n                <div className=\"p-8\">\n                  <form onSubmit={handleSubmit(onSubmit)}>\n                    {/* Order Type Selection */}\n                    <div className=\"mb-8\">\n                      <label className=\"block font-medium text-gray-300 mb-4\">\n                        <span>Order Type</span>\n                      </label>\n\n                      <div className=\"grid grid-cols-2 gap-4\">\n                        <motion.label\n                          className={`flex flex-col items-center justify-center bg-black/40 p-6 rounded-lg border\n                            ${orderType === \"delivery\"\n                              ? 'border-cyan-500 shadow-[0_0_15px_rgba(0,255,255,0.2)]'\n                              : 'border-gray-700'}\n                            cursor-pointer transition-all duration-300 hover:border-cyan-700`}\n                          whileHover={{ scale: 1.02 }}\n                          whileTap={{ scale: 0.98 }}\n                        >\n                          <input\n                            type=\"radio\"\n                            value=\"delivery\"\n                            className=\"sr-only\"\n                            {...register(\"orderType\")}\n                          />\n                          <div className=\"w-14 h-14 mb-3 rounded-full bg-gradient-to-br from-cyan-500/40 to-cyan-700/40\n                                        flex items-center justify-center text-cyan-400\">\n                            <svg className=\"w-8 h-8\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={1.5}\n                                d=\"M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z\" />\n                              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={1.5}\n                                d=\"M15 11a3 3 0 11-6 0 3 3 0 016 0z\" />\n                            </svg>\n                          </div>\n                          <span className=\"font-medium text-white text-lg\">Delivery</span>\n                          <span className=\"text-gray-400 text-sm mt-1\">\n                            To your address\n                          </span>\n\n                          {orderType === \"delivery\" && (\n                            <motion.div\n                              className=\"absolute top-2 right-2 w-4 h-4 rounded-full bg-cyan-500\"\n                              initial={{ scale: 0 }}\n                              animate={{ scale: 1 }}\n                              transition={{ type: \"spring\", stiffness: 500, damping: 15 }}\n                            />\n                          )}\n                        </motion.label>\n\n                        <motion.label\n                          className={`flex flex-col items-center justify-center bg-black/40 p-6 rounded-lg border\n                            ${orderType === \"takeaway\"\n                              ? 'border-cyan-500 shadow-[0_0_15px_rgba(0,255,255,0.2)]'\n                              : 'border-gray-700'}\n                            cursor-pointer transition-all duration-300 hover:border-cyan-700`}\n                          whileHover={{ scale: 1.02 }}\n                          whileTap={{ scale: 0.98 }}\n                        >\n                          <input\n                            type=\"radio\"\n                            value=\"takeaway\"\n                            className=\"sr-only\"\n                            {...register(\"orderType\")}\n                          />\n                          <div className=\"w-14 h-14 mb-3 rounded-full bg-gradient-to-br from-fuchsia-500/40 to-fuchsia-700/40\n                                        flex items-center justify-center text-fuchsia-400\">\n                            <svg className=\"w-8 h-8\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={1.5}\n                                d=\"M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 002 2z\" />\n                            </svg>\n                          </div>\n                          <span className=\"font-medium text-white text-lg\">Takeaway</span>\n                          <span className=\"text-gray-400 text-sm mt-1\">\n                            Pick up at restaurant\n                          </span>\n\n                          {orderType === \"takeaway\" && (\n                            <motion.div\n                              className=\"absolute top-2 right-2 w-4 h-4 rounded-full bg-fuchsia-500\"\n                              initial={{ scale: 0 }}\n                              animate={{ scale: 1 }}\n                              transition={{ type: \"spring\", stiffness: 500, damping: 15 }}\n                            />\n                          )}\n                        </motion.label>\n                      </div>\n                    </div>\n\n                    {/* Delivery/Pickup Time */}\n                    <div className=\"mb-8\">\n                      <label className=\"block font-medium text-gray-300 mb-4\">\n                        <span>{orderType === \"delivery\" ? \"Delivery\" : \"Pickup\"} Time</span>\n                      </label>\n\n                      <div className=\"grid grid-cols-2 gap-4 mb-4\">\n                        <motion.label\n                          className={`flex flex-col items-center justify-center bg-black/40 p-5 rounded-lg border\n                            ${deliveryTime === \"asap\"\n                              ? 'border-lime-500 shadow-[0_0_15px_rgba(57,255,20,0.2)]'\n                              : 'border-gray-700'}\n                            cursor-pointer transition-all duration-300 hover:border-lime-700`}\n                          whileHover={{ scale: 1.02 }}\n                          whileTap={{ scale: 0.98 }}\n                        >\n                          <input\n                            type=\"radio\"\n                            value=\"asap\"\n                            className=\"sr-only\"\n                            {...register(\"deliveryTime\")}\n                          />\n                          <div className=\"w-12 h-12 mb-2 rounded-full bg-gradient-to-br from-lime-500/40 to-lime-700/40\n                                        flex items-center justify-center text-lime-400\">\n                            <svg className=\"w-7 h-7\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={1.5}\n                                d=\"M13 10V3L4 14h7v7l9-11h-7z\" />\n                            </svg>\n                          </div>\n                          <span className=\"font-medium text-white\">As Soon As Possible</span>\n                          <span className=\"text-gray-400 text-xs mt-1\">\n                            30-45 min estimated\n                          </span>\n\n                          {deliveryTime === \"asap\" && (\n                            <motion.div\n                              className=\"absolute top-2 right-2 w-4 h-4 rounded-full bg-lime-500\"\n                              initial={{ scale: 0 }}\n                              animate={{ scale: 1 }}\n                              transition={{ type: \"spring\", stiffness: 500, damping: 15 }}\n                            />\n                          )}\n                        </motion.label>\n\n                        <motion.label\n                          className={`flex flex-col items-center justify-center bg-black/40 p-5 rounded-lg border\n                            ${deliveryTime === \"scheduled\"\n                              ? 'border-lime-500 shadow-[0_0_15px_rgba(57,255,20,0.2)]'\n                              : 'border-gray-700'}\n                            cursor-pointer transition-all duration-300 hover:border-lime-700`}\n                          whileHover={{ scale: 1.02 }}\n                          whileTap={{ scale: 0.98 }}\n                        >\n                          <input\n                            type=\"radio\"\n                            value=\"scheduled\"\n                            className=\"sr-only\"\n                            {...register(\"deliveryTime\")}\n                          />\n                          <div className=\"w-12 h-12 mb-2 rounded-full bg-gradient-to-br from-lime-500/40 to-lime-700/40\n                                        flex items-center justify-center text-lime-400\">\n                            <svg className=\"w-7 h-7\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={1.5}\n                                d=\"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z\" />\n                            </svg>\n                          </div>\n                          <span className=\"font-medium text-white\">Schedule for Later</span>\n                          <span className=\"text-gray-400 text-xs mt-1\">\n                            Select a time\n                          </span>\n\n                          {deliveryTime === \"scheduled\" && (\n                            <motion.div\n                              className=\"absolute top-2 right-2 w-4 h-4 rounded-full bg-lime-500\"\n                              initial={{ scale: 0 }}\n                              animate={{ scale: 1 }}\n                              transition={{ type: \"spring\", stiffness: 500, damping: 15 }}\n                            />\n                          )}\n                        </motion.label>\n                      </div>\n\n                      {/* Scheduled Time Selector */}\n                      <AnimatePresence>\n                        {deliveryTime === \"scheduled\" && (\n                          <motion.div\n                            initial={{ opacity: 0, height: 0 }}\n                            animate={{ opacity: 1, height: \"auto\" }}\n                            exit={{ opacity: 0, height: 0 }}\n                            transition={{ duration: 0.3 }}\n                            className=\"overflow-hidden\"\n                          >\n                            <div className=\"pt-3\">\n                              <motion.div\n                                animate={errors.scheduledTime\n                                  ? \"error\"\n                                  : focusedField === \"scheduledTime\"\n                                    ? \"focused\"\n                                    : \"initial\"\n                                }\n                                variants={formInputVariants}\n                                custom=\"scheduledTime\"\n                                className=\"relative\"\n                              >\n                                <div className=\"relative\">\n                                  <div className=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\">\n                                    <svg className=\"h-5 w-5 text-gray-500\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={1.5}\n                                        d=\"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z\" />\n                                    </svg>\n                                  </div>\n                                  <select\n                                    id=\"scheduledTime\"\n                                    className={`w-full bg-black/40 border ${\n                                      errors.scheduledTime\n                                        ? 'border-red-500 focus:border-red-400'\n                                        : focusedField === \"scheduledTime\"\n                                          ? 'border-lime-500'\n                                          : 'border-gray-700 focus:border-lime-700'\n                                    } rounded-lg pl-10 px-4 py-3 font-poppins text-white focus:outline-none transition-all duration-300 appearance-none`}\n                                    {...registerWithFocus(\"scheduledTime\")}\n                                  >\n                                    <option value=\"\">Select a time</option>\n                                    {/* Dynamic time options based on current time */}\n                                    <option value=\"12:00\">Today, 12:00 PM</option>\n                                    <option value=\"12:30\">Today, 12:30 PM</option>\n                                    <option value=\"13:00\">Today, 1:00 PM</option>\n                                    <option value=\"13:30\">Today, 1:30 PM</option>\n                                    <option value=\"14:00\">Today, 2:00 PM</option>\n                                    <option value=\"18:00\">Today, 6:00 PM</option>\n                                    <option value=\"18:30\">Today, 6:30 PM</option>\n                                    <option value=\"19:00\">Today, 7:00 PM</option>\n                                    <option value=\"19:30\">Today, 7:30 PM</option>\n                                    <option value=\"20:00\">Today, 8:00 PM</option>\n                                  </select>\n                                  <div className=\"absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none\">\n                                    <svg className=\"h-5 w-5 text-gray-500\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={1.5} d=\"M19 9l-7 7-7-7\" />\n                                    </svg>\n                                  </div>\n                                </div>\n                                {focusedField === \"scheduledTime\" && (\n                                  <motion.span\n                                    className=\"absolute inset-0 rounded-lg ring-2 ring-lime-500 pointer-events-none\"\n                                    initial={{ opacity: 0 }}\n                                    animate={{ opacity: 1 }}\n                                    exit={{ opacity: 0 }}\n                                  />\n                                )}\n                              </motion.div>\n                              {errors.scheduledTime && (\n                                <motion.p\n                                  initial={{ opacity: 0, y: -10 }}\n                                  animate={{ opacity: 1, y: 0 }}\n                                  className=\"mt-1 font-poppins text-red-500 text-sm\"\n                                >\n                                  {errors.scheduledTime.message}\n                                </motion.p>\n                              )}\n                            </div>\n                          </motion.div>\n                        )}\n                      </AnimatePresence>\n                    </div>\n\n                    {/* Personal Information Section Divider */}\n                    <div className=\"relative flex py-5 items-center mb-6\">\n                      <div className=\"flex-grow border-t border-gray-800\"></div>\n                      <span className=\"flex-shrink mx-3 text-gray-400 font-medium\">\n                        Personal Information\n                      </span>\n                      <div className=\"flex-grow border-t border-gray-800\"></div>\n                    </div>\n\n                    {/* First Name */}\n                    <div className=\"mb-8\">\n                      <label htmlFor=\"firstName\" className=\"block font-medium text-gray-300 mb-2 flex items-center\">\n                        <span>First Name</span>\n                        {errors.firstName && (\n                          <motion.span\n                            initial={{ opacity: 0, scale: 0.5 }}\n                            animate={{ opacity: 1, scale: 1 }}\n                            className=\"ml-2 text-red-500 text-xs\"\n                          >\n                            Required\n                          </motion.span>\n                        )}\n                      </label>\n                      <motion.div\n                        animate={errors.firstName\n                          ? \"error\"\n                          : focusedField === \"firstName\"\n                            ? \"focused\"\n                            : \"initial\"\n                        }\n                        variants={formInputVariants}\n                        custom=\"firstName\"\n                        className=\"relative\"\n                      >\n                        <input\n                          id=\"firstName\"\n                          className={`w-full bg-black/40 border ${\n                            errors.firstName\n                              ? 'border-red-500 focus:border-red-400'\n                              : focusedField === \"firstName\"\n                                ? 'border-cyan-500'\n                                : 'border-gray-700 focus:border-cyan-700'\n                          } rounded-lg px-4 py-3 font-poppins text-white focus:outline-none transition-all duration-300`}\n                          placeholder=\"John\"\n                          {...registerWithFocus(\"firstName\")}\n                        />\n                        {focusedField === \"firstName\" && (\n                          <motion.span\n                            className=\"absolute inset-0 rounded-lg ring-2 ring-cyan-500 pointer-events-none\"\n                            initial={{ opacity: 0 }}\n                            animate={{ opacity: 1 }}\n                            exit={{ opacity: 0 }}\n                          />\n                        )}\n                      </motion.div>\n                      {errors.firstName && (\n                        <motion.p\n                          initial={{ opacity: 0, y: -10 }}\n                          animate={{ opacity: 1, y: 0 }}\n                          className=\"mt-1 font-poppins text-red-500 text-sm\"\n                        >\n                          {errors.firstName.message}\n                        </motion.p>\n                      )}\n                    </div>\n\n                    {/* Email Address */}\n                    <div className=\"mb-8\">\n                      <label htmlFor=\"email\" className=\"block font-medium text-gray-300 mb-2 flex items-center\">\n                        <span>Email Address (Optional)</span>\n                      </label>\n                      <motion.div\n                        animate={errors.email\n                          ? \"error\"\n                          : focusedField === \"email\"\n                            ? \"focused\"\n                            : \"initial\"\n                        }\n                        variants={formInputVariants}\n                        custom=\"email\"\n                        className=\"relative\"\n                      >\n                        <div className=\"relative\">\n                          <div className=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\">\n                            <svg className=\"h-5 w-5 text-gray-500\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={1.5} d=\"M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z\" />\n                            </svg>\n                          </div>\n                          <input\n                            id=\"email\"\n                            type=\"email\"\n                            className={`w-full bg-black/40 border ${\n                              errors.email\n                                ? 'border-red-500 focus:border-red-400'\n                                : focusedField === \"email\"\n                                  ? 'border-cyan-500'\n                                  : 'border-gray-700 focus:border-cyan-700'\n                            } rounded-lg pl-10 px-4 py-3 font-poppins text-white focus:outline-none transition-all duration-300`}\n                            placeholder=\"<EMAIL> (optional)\"\n                            {...registerWithFocus(\"email\")}\n                          />\n                        </div>\n                        {focusedField === \"email\" && (\n                          <motion.span\n                            className=\"absolute inset-0 rounded-lg ring-2 ring-cyan-500 pointer-events-none\"\n                            initial={{ opacity: 0 }}\n                            animate={{ opacity: 1 }}\n                            exit={{ opacity: 0 }}\n                          />\n                        )}\n                      </motion.div>\n                      {errors.email && (\n                        <motion.p\n                          initial={{ opacity: 0, y: -10 }}\n                          animate={{ opacity: 1, y: 0 }}\n                          className=\"mt-1 font-poppins text-red-500 text-sm\"\n                        >\n                          {errors.email.message}\n                        </motion.p>\n                      )}\n                    </div>\n\n                    {/* Phone Number */}\n                    <div className=\"mb-8\">\n                      <label htmlFor=\"phone\" className=\"block font-medium text-gray-300 mb-2 flex items-center\">\n                        <span>Phone Number</span>\n                        {errors.phone && (\n                          <motion.span\n                            initial={{ opacity: 0, scale: 0.5 }}\n                            animate={{ opacity: 1, scale: 1 }}\n                            className=\"ml-2 text-red-500 text-xs\"\n                          >\n                            Required\n                          </motion.span>\n                        )}\n                      </label>\n                      <motion.div\n                        animate={errors.phone\n                          ? \"error\"\n                          : focusedField === \"phone\"\n                            ? \"focused\"\n                            : \"initial\"\n                        }\n                        variants={formInputVariants}\n                        custom=\"phone\"\n                        className=\"relative\"\n                      >\n                        <div className=\"relative\">\n                          <div className=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\">\n                            <svg className=\"h-5 w-5 text-gray-500\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={1.5} d=\"M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z\" />\n                            </svg>\n                          </div>\n                          <input\n                            id=\"phone\"\n                            type=\"tel\"\n                            className={`w-full bg-black/40 border ${\n                              errors.phone\n                                ? 'border-red-500 focus:border-red-400'\n                                : focusedField === \"phone\"\n                                  ? 'border-cyan-500'\n                                  : 'border-gray-700 focus:border-cyan-700'\n                            } rounded-lg pl-10 px-4 py-3 font-poppins text-white focus:outline-none transition-all duration-300`}\n                            placeholder=\"+47 12 34 56 78\"\n                            {...registerWithFocus(\"phone\")}\n                          />\n                        </div>\n                        {focusedField === \"phone\" && (\n                          <motion.span\n                            className=\"absolute inset-0 rounded-lg ring-2 ring-cyan-500 pointer-events-none\"\n                            initial={{ opacity: 0 }}\n                            animate={{ opacity: 1 }}\n                            exit={{ opacity: 0 }}\n                          />\n                        )}\n                      </motion.div>\n                      {errors.phone && (\n                        <motion.p\n                          initial={{ opacity: 0, y: -10 }}\n                          animate={{ opacity: 1, y: 0 }}\n                          className=\"mt-1 font-poppins text-red-500 text-sm\"\n                        >\n                          {errors.phone.message}\n                        </motion.p>\n                      )}\n                    </div>\n\n                    {/* Delivery Section Divider */}\n                    <div className=\"relative flex py-5 items-center mb-6\">\n                      <div className=\"flex-grow border-t border-gray-800\"></div>\n                      <span className=\"flex-shrink mx-3 text-gray-400 font-medium\">\n                        {orderType === \"delivery\" ? \"Delivery Address\" : \"Additional Details\"}\n                      </span>\n                      <div className=\"flex-grow border-t border-gray-800\"></div>\n                    </div>\n\n                    {/* Address Fields - Only shown for delivery */}\n                    <AnimatePresence>\n                      {orderType === \"delivery\" && (\n                        <motion.div\n                          initial={{ opacity: 0, height: 0 }}\n                          animate={{ opacity: 1, height: \"auto\" }}\n                          exit={{ opacity: 0, height: 0 }}\n                          transition={{ duration: 0.3 }}\n                          className=\"space-y-8 overflow-hidden\"\n                        >\n                          {/* Address */}\n                          <div className=\"mb-8\">\n                            <label htmlFor=\"address\" className=\"block font-medium text-gray-300 mb-2 flex items-center\">\n                              <span>Delivery Address</span>\n                              {errors.address && (\n                                <motion.span\n                                  initial={{ opacity: 0, scale: 0.5 }}\n                                  animate={{ opacity: 1, scale: 1 }}\n                                  className=\"ml-2 text-red-500 text-xs\"\n                                >\n                                  Required\n                                </motion.span>\n                              )}\n                            </label>\n                            <motion.div\n                              animate={errors.address\n                                ? \"error\"\n                                : focusedField === \"address\"\n                                  ? \"focused\"\n                                  : \"initial\"\n                              }\n                              variants={formInputVariants}\n                              custom=\"address\"\n                              className=\"relative\"\n                            >\n                              <div className=\"relative\">\n                                <div className=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\">\n                                  <svg className=\"h-5 w-5 text-gray-500\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={1.5} d=\"M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z\" />\n                                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={1.5} d=\"M15 11a3 3 0 11-6 0 3 3 0 016 0z\" />\n                                  </svg>\n                                </div>\n                                <input\n                                  id=\"address\"\n                                  className={`w-full bg-black/40 border ${\n                                    errors.address\n                                      ? 'border-red-500 focus:border-red-400'\n                                      : focusedField === \"address\"\n                                        ? 'border-cyan-500'\n                                        : 'border-gray-700 focus:border-cyan-700'\n                                  } rounded-lg pl-10 px-4 py-3 font-poppins text-white focus:outline-none transition-all duration-300`}\n                                  placeholder=\"Street address\"\n                                  {...registerWithFocus(\"address\")}\n                                />\n                              </div>\n                              {focusedField === \"address\" && (\n                                <motion.span\n                                  className=\"absolute inset-0 rounded-lg ring-2 ring-cyan-500 pointer-events-none\"\n                                  initial={{ opacity: 0 }}\n                                  animate={{ opacity: 1 }}\n                                  exit={{ opacity: 0 }}\n                                />\n                              )}\n                            </motion.div>\n                            {errors.address && (\n                              <motion.p\n                                initial={{ opacity: 0, y: -10 }}\n                                animate={{ opacity: 1, y: 0 }}\n                                className=\"mt-1 font-poppins text-red-500 text-sm\"\n                              >\n                                {errors.address.message}\n                              </motion.p>\n                            )}\n\n                            {/* Geolocation Button - Only show for delivery */}\n                            {orderType === \"delivery\" && (\n                              <motion.div\n                                className=\"mt-3 mb-6\"\n                                initial={{ opacity: 0, y: -10 }}\n                                animate={{ opacity: 1, y: 0 }}\n                                exit={{ opacity: 0, y: -10 }}\n                                transition={{ duration: 0.3 }}\n                              >\n                                <motion.button\n                                  type=\"button\"\n                                  onClick={getCurrentLocation}\n                                  disabled={isGettingLocation}\n                                  className={`flex items-center justify-center px-4 py-2 rounded-lg border transition-all duration-300\n                                    ${isGettingLocation\n                                      ? 'bg-gray-800/50 border-gray-600 cursor-not-allowed'\n                                      : 'bg-black/40 border-gray-700 hover:border-cyan-600 hover:bg-black/60'\n                                    } text-white font-poppins text-sm`}\n                                  whileHover={!isGettingLocation ? { scale: 1.02 } : {}}\n                                  whileTap={!isGettingLocation ? { scale: 0.98 } : {}}\n                                >\n                                  {isGettingLocation ? (\n                                    <>\n                                      <svg className=\"animate-spin -ml-1 mr-2 h-4 w-4 text-cyan-400\" fill=\"none\" viewBox=\"0 0 24 24\">\n                                        <circle className=\"opacity-25\" cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\" strokeWidth=\"4\"></circle>\n                                        <path className=\"opacity-75\" fill=\"currentColor\" d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"></path>\n                                      </svg>\n                                      Getting location...\n                                    </>\n                                  ) : (\n                                    <>\n                                      <svg className=\"w-4 h-4 mr-2 text-cyan-400\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                                        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={1.5}\n                                          d=\"M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z\" />\n                                        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={1.5}\n                                          d=\"M15 11a3 3 0 11-6 0 3 3 0 016 0z\" />\n                                      </svg>\n                                      Use Current Location\n                                    </>\n                                  )}\n                                </motion.button>\n\n                                {/* Location Error Message */}\n                                <AnimatePresence>\n                                  {locationError && (\n                                    <motion.div\n                                      initial={{ opacity: 0, y: -10 }}\n                                      animate={{ opacity: 1, y: 0 }}\n                                      exit={{ opacity: 0, y: -10 }}\n                                      className=\"mt-2 p-3 bg-red-900/20 border border-red-800/30 rounded-lg\"\n                                    >\n                                      <div className=\"flex items-start\">\n                                        <svg className=\"w-4 h-4 text-red-400 mt-0.5 mr-2 flex-shrink-0\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                                          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={1.5}\n                                            d=\"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z\" />\n                                        </svg>\n                                        <p className=\"text-red-300 text-xs font-poppins\">{locationError}</p>\n                                      </div>\n                                    </motion.div>\n                                  )}\n                                </AnimatePresence>\n\n                                {/* Helper Text */}\n                                <p className=\"mt-2 text-xs text-gray-500 font-poppins\">\n                                  Click to automatically fill your address using your current location\n                                </p>\n                              </motion.div>\n                            )}\n                          </div>\n\n                          <div className=\"grid grid-cols-1 md:grid-cols-3 gap-6\">\n                            {/* Postal Code */}\n                            <div>\n                              <label htmlFor=\"postalCode\" className=\"block font-medium text-gray-300 mb-2 flex items-center\">\n                                <span>Postal Code</span>\n                                {errors.postalCode && (\n                                  <motion.span\n                                    initial={{ opacity: 0, scale: 0.5 }}\n                                    animate={{ opacity: 1, scale: 1 }}\n                                    className=\"ml-2 text-red-500 text-xs\"\n                                  >\n                                    Required\n                                  </motion.span>\n                                )}\n                              </label>\n                              <motion.div\n                                animate={errors.postalCode\n                                  ? \"error\"\n                                  : focusedField === \"postalCode\"\n                                    ? \"focused\"\n                                    : \"initial\"\n                                }\n                                variants={formInputVariants}\n                                custom=\"postalCode\"\n                                className=\"relative\"\n                              >\n                                <input\n                                  id=\"postalCode\"\n                                  className={`w-full bg-black/40 border ${\n                                    errors.postalCode\n                                      ? 'border-red-500 focus:border-red-400'\n                                      : focusedField === \"postalCode\"\n                                        ? 'border-cyan-500'\n                                        : 'border-gray-700 focus:border-cyan-700'\n                                  } rounded-lg px-4 py-3 font-poppins text-white focus:outline-none transition-all duration-300`}\n                                  placeholder=\"0000\"\n                                  {...registerWithFocus(\"postalCode\")}\n                                />\n                                {focusedField === \"postalCode\" && (\n                                  <motion.span\n                                    className=\"absolute inset-0 rounded-lg ring-2 ring-cyan-500 pointer-events-none\"\n                                    initial={{ opacity: 0 }}\n                                    animate={{ opacity: 1 }}\n                                    exit={{ opacity: 0 }}\n                                  />\n                                )}\n                              </motion.div>\n                              {errors.postalCode && (\n                                <motion.p\n                                  initial={{ opacity: 0, y: -10 }}\n                                  animate={{ opacity: 1, y: 0 }}\n                                  className=\"mt-1 font-poppins text-red-500 text-sm\"\n                                >\n                                  {errors.postalCode.message}\n                                </motion.p>\n                              )}\n                            </div>\n\n                            {/* City */}\n                            <div className=\"md:col-span-2\">\n                              <label htmlFor=\"city\" className=\"block font-medium text-gray-300 mb-2 flex items-center\">\n                                <span>City</span>\n                                {errors.city && (\n                                  <motion.span\n                                    initial={{ opacity: 0, scale: 0.5 }}\n                                    animate={{ opacity: 1, scale: 1 }}\n                                    className=\"ml-2 text-red-500 text-xs\"\n                                  >\n                                    Required\n                                  </motion.span>\n                                )}\n                              </label>\n                              <motion.div\n                                animate={errors.city\n                                  ? \"error\"\n                                  : focusedField === \"city\"\n                                    ? \"focused\"\n                                    : \"initial\"\n                                }\n                                variants={formInputVariants}\n                                custom=\"city\"\n                                className=\"relative\"\n                              >\n                                <input\n                                  id=\"city\"\n                                  className={`w-full bg-black/40 border ${\n                                    errors.city\n                                      ? 'border-red-500 focus:border-red-400'\n                                      : focusedField === \"city\"\n                                        ? 'border-cyan-500'\n                                        : 'border-gray-700 focus:border-cyan-700'\n                                  } rounded-lg px-4 py-3 font-poppins text-white focus:outline-none transition-all duration-300`}\n                                  placeholder=\"Oslo\"\n                                  {...registerWithFocus(\"city\")}\n                                />\n                                {focusedField === \"city\" && (\n                                  <motion.span\n                                    className=\"absolute inset-0 rounded-lg ring-2 ring-cyan-500 pointer-events-none\"\n                                    initial={{ opacity: 0 }}\n                                    animate={{ opacity: 1 }}\n                                    exit={{ opacity: 0 }}\n                                  />\n                                )}\n                              </motion.div>\n                              {errors.city && (\n                                <motion.p\n                                  initial={{ opacity: 0, y: -10 }}\n                                  animate={{ opacity: 1, y: 0 }}\n                                  className=\"mt-1 font-poppins text-red-500 text-sm\"\n                                >\n                                  {errors.city.message}\n                                </motion.p>\n                              )}\n                            </div>\n                          </div>\n                        </motion.div>\n                      )}\n                    </AnimatePresence>\n\n                    {/* Special Instructions */}\n                    <div className=\"mb-8\">\n                      <label htmlFor=\"notes\" className=\"block font-medium text-gray-300 mb-2 flex items-center\">\n                        <span>Special Instructions (Optional)</span>\n                      </label>\n                      <motion.div\n                        animate={focusedField === \"notes\" ? \"focused\" : \"initial\"}\n                        variants={formInputVariants}\n                        custom=\"notes\"\n                        className=\"relative\"\n                      >\n                        <div className=\"relative\">\n                          <div className=\"absolute top-3 left-3 flex items-start pointer-events-none\">\n                            <svg className=\"h-5 w-5 text-gray-500\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={1.5} d=\"M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z\" />\n                            </svg>\n                          </div>\n                          <textarea\n                            id=\"notes\"\n                            rows={3}\n                            className={`w-full bg-black/40 border border-gray-700 rounded-lg pl-10 px-4 py-3 font-poppins text-white focus:outline-none focus:border-cyan-700 transition-all duration-300`}\n                            placeholder=\"Any special requests or delivery instructions\"\n                            {...registerWithFocus(\"notes\")}\n                          ></textarea>\n                        </div>\n                        {focusedField === \"notes\" && (\n                          <motion.span\n                            className=\"absolute inset-0 rounded-lg ring-2 ring-cyan-500 pointer-events-none\"\n                            initial={{ opacity: 0 }}\n                            animate={{ opacity: 1 }}\n                            exit={{ opacity: 0 }}\n                          />\n                        )}\n                      </motion.div>\n                    </div>\n\n                    {/* Order Button */}\n                    <motion.div\n                      initial={{ opacity: 0, y: 20 }}\n                      animate={{ opacity: 1, y: 0 }}\n                      transition={{ delay: 0.4, duration: 0.5 }}\n                      className=\"mt-10\"\n                    >\n                      <motion.button\n                        type=\"submit\"\n                        disabled={isSubmitting || !isValid || !isOpen}\n                        className={`relative w-full overflow-hidden rounded-lg p-4 flex items-center justify-center\n                                 font-bold text-white tracking-wide text-lg\n                                 ${isValid && isOpen ? 'opacity-100' : 'opacity-70'}`}\n                        whileHover={isValid && isOpen ? { scale: 1.02 } : {}}\n                        whileTap={isValid && isOpen ? { scale: 0.98 } : {}}\n                        title={!isOpen ? \"Restaurant is currently closed\" : \"\"}\n                      >\n                        {/* Button Background */}\n                        <span className=\"absolute inset-0 w-full h-full bg-gradient-to-r from-lime-600 to-green-700\"></span>\n\n                        {/* Animated Glow Effect */}\n                        <motion.span\n                          className=\"absolute inset-0 w-full h-full bg-gradient-to-r from-lime-500 to-green-600 opacity-0\"\n                          animate={{\n                            opacity: [0, 0.5, 0],\n                            scale: [1, 1.05, 1],\n                            boxShadow: [\n                              \"0 0 0 rgba(57, 255, 20, 0)\",\n                              \"0 0 20px rgba(57, 255, 20, 0.5)\",\n                              \"0 0 0 rgba(57, 255, 20, 0)\"\n                            ]\n                          }}\n                          transition={{ duration: 2.5, repeat: Infinity }}\n                        />\n\n                        {/* Button Text */}\n                        <span className=\"relative z-10 flex items-center\">\n                          {isSubmitting ? (\n                            <>\n                              <svg className=\"animate-spin -ml-1 mr-3 h-5 w-5 text-white\" fill=\"none\" viewBox=\"0 0 24 24\">\n                                <circle className=\"opacity-25\" cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\" strokeWidth=\"4\"></circle>\n                                <path className=\"opacity-75\" fill=\"currentColor\" d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"></path>\n                              </svg>\n                              Processing...\n                            </>\n                          ) : !isOpen ? (\n                            <>\n                              <svg className=\"w-6 h-6 mr-2\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z\" />\n                              </svg>\n                              Restaurant Closed\n                            </>\n                          ) : (\n                            <>\n                              Place Order\n                              <svg className=\"w-6 h-6 ml-2\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M14 5l7 7m0 0l-7 7m7-7H3\" />\n                              </svg>\n                            </>\n                          )}\n                        </span>\n                      </motion.button>\n                      <p className=\"text-gray-500 text-xs text-center mt-3\">\n                        By placing an order you agree to our Terms and Conditions\n                      </p>\n                    </motion.div>\n                  </form>\n                </div>\n              </div>\n            </motion.div>\n\n            {/* Order Summary (1/3 width on desktop) */}\n            <motion.div\n              variants={itemVariants}\n              ref={summaryRef}\n              className=\"lg:block\"\n            >\n              <div className=\"backdrop-blur-sm bg-black/30 rounded-xl border border-gray-800\n                            shadow-[0_0_25px_rgba(0,0,0,0.2)] sticky top-24\">\n\n                {/* Glowing top border */}\n                <div className=\"h-1 w-full bg-gradient-to-r from-fuchsia-500 via-cyan-500 to-fuchsia-500 rounded-t-xl\"></div>\n\n                <div className=\"p-6\">\n                  <h3 className=\"font-playfair text-xl font-bold text-white mb-6 flex items-center\">\n                    <svg className=\"w-5 h-5 mr-2 text-fuchsia-400\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M3 3h2l.4 2M7 13h10l4-8H5.4M7 13L5.4 5M7 13l-2.293 2.293c-.63.63-.184 1.707.707 1.707H17m0 0a2 2 0 100 4 2 2 0 000-4zm-8 2a2 2 0 11-4 0 2 2 0 014 0z\" />\n                    </svg>\n                    Order Summary\n                  </h3>\n\n                  <div className=\"space-y-5 mb-6\">\n                    {/* Order Items Summary */}\n                    <div className=\"border-b border-gray-800 pb-4\">\n                      <motion.div className=\"space-y-3\">\n                        <AnimatePresence>\n                          {cartItems.map((item, index) => (\n                            <motion.div\n                              key={item.id}\n                              initial={{ opacity: 0, y: 10 }}\n                              animate={{ opacity: 1, y: 0 }}\n                              exit={{ opacity: 0, y: -10 }}\n                              transition={{ delay: index * 0.1 }}\n                              className=\"flex items-start gap-3\"\n                            >\n                              <div className=\"relative w-10 h-10 rounded overflow-hidden flex-shrink-0\">\n                                <img\n                                  src={item.imageUrl}\n                                  alt={item.name}\n                                  className=\"w-full h-full object-cover\"\n                                />\n                              </div>\n                              <div className=\"flex-1 min-w-0\">\n                                <div className=\"flex justify-between\">\n                                  <p className=\"font-poppins font-medium text-sm text-white truncate pr-2\">\n                                    {item.name}\n                                  </p>\n                                  <div className=\"text-xs font-medium text-gray-400 whitespace-nowrap\">\n                                    × {item.quantity}\n                                  </div>\n                                </div>\n                                <motion.div\n                                  className=\"font-poppins text-sm text-transparent bg-clip-text bg-gradient-to-r from-fuchsia-400 to-cyan-400\"\n                                  animate={{\n                                    textShadow: ['0 0 3px rgba(255, 0, 255, 0.2)', '0 0 5px rgba(255, 0, 255, 0.3)', '0 0 3px rgba(255, 0, 255, 0.2)']\n                                  }}\n                                  transition={{ duration: 1.5, repeat: Infinity }}\n                                >\n                                  {formatCurrency(item.price * item.quantity)}\n                                </motion.div>\n                              </div>\n                            </motion.div>\n                          ))}\n                        </AnimatePresence>\n                      </motion.div>\n                    </div>\n\n                    {/* Costs Breakdown */}\n                    <div className=\"border-b border-gray-800 pb-4 space-y-3\">\n                      <motion.div\n                        className=\"flex justify-between\"\n                        initial={{ opacity: 0 }}\n                        animate={{ opacity: 1 }}\n                        transition={{ delay: 0.2 }}\n                      >\n                        <span className=\"font-poppins text-gray-400\">Subtotal</span>\n                        <motion.span\n                          className=\"font-poppins font-medium text-white\"\n                          animate={\n                            isUpdating !== null\n                              ? { scale: [1, 1.05, 1] }\n                              : {}\n                          }\n                          transition={{ duration: 0.3 }}\n                        >\n                          {formatCurrency(subtotal)}\n                        </motion.span>\n                      </motion.div>\n\n                      <motion.div\n                        className=\"flex justify-between\"\n                        initial={{ opacity: 0 }}\n                        animate={{ opacity: 1 }}\n                        transition={{ delay: 0.3 }}\n                      >\n                        <span className=\"font-poppins text-gray-400\">VAT (25%)</span>\n                        <span className=\"font-poppins font-medium text-white\">\n                          {formatCurrency(VAT)}\n                        </span>\n                      </motion.div>\n\n                      <motion.div\n                        className=\"flex justify-between\"\n                        initial={{ opacity: 0 }}\n                        animate={{ opacity: 1 }}\n                        transition={{ delay: 0.4 }}\n                      >\n                        <span className=\"font-poppins text-gray-400\">Delivery Fee</span>\n                        <span className=\"font-poppins font-medium text-white\">{formatCurrency(actualDeliveryFee)}</span>\n                      </motion.div>\n                    </div>\n\n                    {/* Total */}\n                    <motion.div\n                      className=\"flex justify-between items-center\"\n                      initial={{ opacity: 0 }}\n                      animate={{ opacity: 1 }}\n                      transition={{ delay: 0.5 }}\n                    >\n                      <span className=\"font-poppins font-medium text-lg text-white\">Total</span>\n                      <motion.span\n                        className=\"font-poppins font-bold text-transparent bg-clip-text bg-gradient-to-r from-lime-400 to-green-500 text-xl\"\n                        animate={{\n                          textShadow: ['0 0 5px rgba(57, 255, 20, 0.4)', '0 0 10px rgba(57, 255, 20, 0.6)', '0 0 5px rgba(57, 255, 20, 0.4)']\n                        }}\n                        transition={{ duration: 0.5, repeat: Infinity }}\n                      >\n                        {formatCurrency(animatedTotal)}\n                      </motion.span>\n                    </motion.div>\n                  </div>\n\n                  {/* Payment Method Selection */}\n                  <motion.div\n                    className=\"mb-6\"\n                    initial={{ opacity: 0, y: 20 }}\n                    animate={{ opacity: 1, y: 0 }}\n                    transition={{ delay: 0.6, duration: 0.5 }}\n                  >\n                    <h4 className=\"font-playfair text-lg font-medium text-white mb-3 flex items-center\">\n                      <svg className=\"w-5 h-5 mr-2 text-lime-400\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z\" />\n                      </svg>\n                      Payment Method\n                    </h4>\n\n                    <div className=\"space-y-3\">\n                      <motion.label\n                        className={`flex items-center bg-black/40 p-3 rounded-lg border border-gray-700 cursor-pointer\n                                  ${focusedField === \"paymentMethod\" ? 'border-lime-600 shadow-[0_0_10px_rgba(57,255,20,0.2)]' : 'hover:border-gray-600'}`}\n                        whileHover={{ scale: 1.01 }}\n                        whileTap={{ scale: 0.99 }}\n                      >\n                        <input\n                          type=\"radio\"\n                          value=\"card\"\n                          className=\"mr-3 accent-lime-500\"\n                          {...registerWithFocus(\"paymentMethod\")}\n                        />\n                        <span className=\"font-poppins text-white\">Credit Card</span>\n                        <div className=\"ml-auto flex space-x-2\">\n                          <span className=\"text-blue-400 opacity-80\">\n                            <svg className=\"w-6 h-6\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\n                              <path d=\"M21.5,9.5h-19C1.7,9.5,1,8.8,1,8s0.7-1.5,1.5-1.5h19c0.8,0,1.5,0.7,1.5,1.5S22.3,9.5,21.5,9.5z M21.5,5h-19 C1.7,5,1,4.3,1,3.5S1.7,2,2.5,2h19C22.3,2,23,2.7,23,3.5S22.3,5,21.5,5z M21.5,14h-19c-0.8,0-1.5-0.7-1.5-1.5s0.7-1.5,1.5-1.5h19 c0.8,0,1.5,0.7,1.5,1.5S22.3,14,21.5,14z M21.5,22h-19c-0.8,0-1.5-0.7-1.5-1.5S1.7,19,2.5,19h19c0.8,0,1.5,0.7,1.5,1.5 S22.3,22,21.5,22z\"/>\n                            </svg>\n                          </span>\n                          <span className=\"text-red-400 opacity-80\">\n                            <svg className=\"w-6 h-6\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\n                              <path d=\"M16,9C13.2,9,11,6.8,11,4s2.2-4,5-4s5,2.2,5,4S18.8,9,16,9z M16,2c-1.1,0-2,0.9-2,2s0.9,2,2,2s2-0.9,2-2S17.1,2,16,2z  M8,9C5.2,9,3,6.8,3,4s2.2-4,5-4s5,2.2,5,4S10.8,9,8,9z M8,2C6.9,2,6,2.9,6,4s0.9,2,2,2s2-0.9,2-2S9.1,2,8,2z\"/>\n                            </svg>\n                          </span>\n                        </div>\n                      </motion.label>\n\n                      <motion.label\n                        className={`flex items-center bg-black/40 p-3 rounded-lg border border-gray-700 cursor-pointer\n                                 ${focusedField === \"paymentMethod\" ? 'border-lime-600 shadow-[0_0_10px_rgba(57,255,20,0.2)]' : 'hover:border-gray-600'}`}\n                        whileHover={{ scale: 1.01 }}\n                        whileTap={{ scale: 0.99 }}\n                      >\n                        <input\n                          type=\"radio\"\n                          value=\"vipps\"\n                          className=\"mr-3 accent-lime-500\"\n                          {...registerWithFocus(\"paymentMethod\")}\n                        />\n                        <span className=\"font-poppins text-white\">Vipps</span>\n                      </motion.label>\n\n                      <motion.label\n                        className={`flex items-center bg-black/40 p-3 rounded-lg border border-gray-700 cursor-pointer\n                                 ${focusedField === \"paymentMethod\" ? 'border-lime-600 shadow-[0_0_10px_rgba(57,255,20,0.2)]' : 'hover:border-gray-600'}`}\n                        whileHover={{ scale: 1.01 }}\n                        whileTap={{ scale: 0.99 }}\n                      >\n                        <input\n                          type=\"radio\"\n                          value=\"cash\"\n                          className=\"mr-3 accent-lime-500\"\n                          {...registerWithFocus(\"paymentMethod\")}\n                        />\n                        <span className=\"font-poppins text-white\">Cash on Delivery</span>\n                      </motion.label>\n                    </div>\n                  </motion.div>\n\n                  {/* Estimated Delivery */}\n                  <motion.div\n                    className=\"bg-black/40 p-4 rounded-lg border border-lime-800/30\n                             shadow-[0_0_15px_rgba(57,255,20,0.1)]\"\n                    initial={{ opacity: 0, y: 20 }}\n                    animate={{ opacity: 1, y: 0 }}\n                    transition={{ delay: 0.7, duration: 0.5 }}\n                  >\n                    <div className=\"flex items-center mb-2\">\n                      <svg className=\"w-5 h-5 mr-2 text-lime-400\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\" />\n                      </svg>\n                      <span className=\"font-medium text-white\">Estimated Delivery</span>\n                    </div>\n                    <p className=\"text-gray-400 text-sm\">{estimatedTime} after order confirmation</p>\n                  </motion.div>\n                </div>\n              </div>\n            </motion.div>\n          </div>\n        </motion.div>\n      </div>\n    </section>\n  );\n};\n\nexport default Checkout;\n"}