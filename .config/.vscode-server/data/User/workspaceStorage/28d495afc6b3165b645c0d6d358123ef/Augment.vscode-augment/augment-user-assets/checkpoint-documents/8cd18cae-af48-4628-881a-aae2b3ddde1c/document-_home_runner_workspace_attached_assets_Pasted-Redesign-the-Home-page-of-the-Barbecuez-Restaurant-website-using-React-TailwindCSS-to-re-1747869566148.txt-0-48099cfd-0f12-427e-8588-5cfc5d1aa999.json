{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "attached_assets/Pasted-Redesign-the-Home-page-of-the-Barbecuez-Restaurant-website-using-React-TailwindCSS-to-re-1747869566148.txt"}, "originalCode": "Redesign the **Home page** of the \"Barbecuez Restaurant\" website using **React + TailwindCSS** to reflect a more **luxurious, modern, and animated visual identity**.\r\n\r\n### ✨ Visual and Design Goals:\r\n- Create a **visually immersive homepage** with **dark luxury aesthetics**.\r\n- Use a **full-screen background video** or animated gradient background with motion.\r\n- Apply **3D or pseudo-3D effects** for major elements (hero text, images, buttons).\r\n- Animate key sections on scroll using **Framer Motion** or similar animation libraries.\r\n- Maintain a dark palette with neon accents:\r\n  - Primary background: `#000000`\r\n  - Neon highlight colors: \r\n    - Electric Blue `#00FFFF`\r\n    - Neon Pink `#FF00FF`\r\n    - Lime Green `#39FF14`\r\n\r\n### 🧩 Hero Section (Above the Fold):\r\n- Large elegant logo + animated tagline.\r\n- Hero heading using neon gradient text with subtle glowing effects.\r\n- A 3D animated barbecue smoke or grill fire effect (WebGL / Lottie / Three.js if possible).\r\n- CTA button (“Explore Menu”) with glowing edge and hover animation.\r\n\r\n### 📜 Below Hero:\r\n- Add an **\"Our Story\"** section with animated cards or split layout.\r\n- Use image parallax or subtle depth effect on food images.\r\n- Add a \"Why Choose Us?\" section with neon glowing icons for:\r\n  - Years of Experience\r\n  - Signature Dishes\r\n  - 5-Star Rating\r\n- Animate each metric count with smooth fade/slide.\r\n\r\n### 🖼️ Featured Dishes Carousel:\r\n- Horizontal scrollable section or 3D card slider (like React Slick / Swiper.js).\r\n- Each card should glow slightly and scale on hover.\r\n- Dishes should be highlighted with price (in NOK), image, and an “Add to Cart” button.\r\n\r\n### 📐 Technical:\r\n- Use TailwindCSS with custom configuration for neon glowing styles and dark gradients.\r\n- Use **Framer Motion** for animating:\r\n  - Scroll-in sections\r\n  - Button hover/focus states\r\n  - Page transitions\r\n- Optionally integrate **Three.js or R3F** (React Three Fiber) for 3D props like fire, utensils, smoke.\r\n- All components should be responsive and mobile-friendly.\r\n- Write the code in modular React components for reuse.\r\n\r\n### ⚠️ Important:\r\n- The homepage must give a strong impression of exclusivity, richness, and modern design.\r\n- Avoid looking like a basic dark site — focus on a **high-end, cinematic experience**.\r\n- Include smooth scroll cues, animated transitions, and micro-interactions where useful.\r\n", "modifiedCode": "Redesign the **Home page** of the \"Barbecuez Restaurant\" website using **React + TailwindCSS** to reflect a more **luxurious, modern, and animated visual identity**.\r\n\r\n### ✨ Visual and Design Goals:\r\n- Create a **visually immersive homepage** with **dark luxury aesthetics**.\r\n- Use a **full-screen background video** or animated gradient background with motion.\r\n- Apply **3D or pseudo-3D effects** for major elements (hero text, images, buttons).\r\n- Animate key sections on scroll using **Framer Motion** or similar animation libraries.\r\n- Maintain a dark palette with neon accents:\r\n  - Primary background: `#000000`\r\n  - Neon highlight colors: \r\n    - Electric Blue `#00FFFF`\r\n    - Neon Pink `#FF00FF`\r\n    - Lime Green `#39FF14`\r\n\r\n### 🧩 Hero Section (Above the Fold):\r\n- Large elegant logo + animated tagline.\r\n- Hero heading using neon gradient text with subtle glowing effects.\r\n- A 3D animated barbecue smoke or grill fire effect (WebGL / Lottie / Three.js if possible).\r\n- CTA button (“Explore Menu”) with glowing edge and hover animation.\r\n\r\n### 📜 Below Hero:\r\n- Add an **\"Our Story\"** section with animated cards or split layout.\r\n- Use image parallax or subtle depth effect on food images.\r\n- Add a \"Why Choose Us?\" section with neon glowing icons for:\r\n  - Years of Experience\r\n  - Signature Dishes\r\n  - 5-Star Rating\r\n- Animate each metric count with smooth fade/slide.\r\n\r\n### 🖼️ Featured Dishes Carousel:\r\n- Horizontal scrollable section or 3D card slider (like React Slick / Swiper.js).\r\n- Each card should glow slightly and scale on hover.\r\n- Dishes should be highlighted with price (in NOK), image, and an “Add to Cart” button.\r\n\r\n### 📐 Technical:\r\n- Use TailwindCSS with custom configuration for neon glowing styles and dark gradients.\r\n- Use **Framer Motion** for animating:\r\n  - Scroll-in sections\r\n  - Button hover/focus states\r\n  - Page transitions\r\n- Optionally integrate **Three.js or R3F** (React Three Fiber) for 3D props like fire, utensils, smoke.\r\n- All components should be responsive and mobile-friendly.\r\n- Write the code in modular React components for reuse.\r\n\r\n### ⚠️ Important:\r\n- The homepage must give a strong impression of exclusivity, richness, and modern design.\r\n- Avoid looking like a basic dark site — focus on a **high-end, cinematic experience**.\r\n- Include smooth scroll cues, animated transitions, and micro-interactions where useful.\r\n"}