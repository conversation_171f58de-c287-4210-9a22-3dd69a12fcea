{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "migrations/003_add_order_details.sql"}, "modifiedCode": "-- Migration: Add orderDetails column to orders table\n-- This migration adds the missing orderDetails JSONB column to the orders table\n\n-- Add the orderDetails column\nALTER TABLE orders ADD COLUMN IF NOT EXISTS order_details JSONB;\n\n-- Update existing orders to have default orderDetails\n-- For existing orders, we'll set default values based on common patterns\nUPDATE orders \nSET order_details = '{\"type\": \"delivery\", \"time\": \"asap\", \"scheduledTime\": null}'::jsonb\nWHERE order_details IS NULL;\n\n-- Make the column NOT NULL after setting default values\nALTER TABLE orders ALTER COLUMN order_details SET NOT NULL;\n"}