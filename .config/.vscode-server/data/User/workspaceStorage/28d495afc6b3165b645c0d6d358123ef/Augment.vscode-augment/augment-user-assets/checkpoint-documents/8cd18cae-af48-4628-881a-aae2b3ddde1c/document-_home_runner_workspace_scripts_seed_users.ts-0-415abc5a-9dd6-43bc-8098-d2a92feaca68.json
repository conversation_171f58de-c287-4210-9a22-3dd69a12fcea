{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "scripts/seed_users.ts"}, "originalCode": "import { db } from '../server/db';\nimport { users } from '../shared/schema';\nimport * as crypto from 'crypto';\n\n// Hash password function (same as in auth.ts)\nasync function hashPassword(password: string): Promise<string> {\n  const salt = crypto.randomBytes(16).toString(\"hex\");\n  return new Promise((resolve, reject) => {\n    crypto.scrypt(password, salt, 64, (err, derivedKey) => {\n      if (err) reject(err);\n      resolve(`${derivedKey.toString(\"hex\")}.${salt}`);\n    });\n  });\n}\n\nasync function seedUsers() {\n  console.log(\"🌱 Seeding users...\");\n  \n  // Check if users already exist\n  const existingUsers = await db.select().from(users);\n  \n  if (existingUsers.length > 0) {\n    console.log(\"Users already exist in the database. Skipping seeding.\");\n    return;\n  }\n  \n  // Hash passwords\n  const adminPassword = await hashPassword(\"admin123\");\n  const managerPassword = await hashPassword(\"manager123\");\n  const driverPassword = await hashPassword(\"driver123\");\n  \n  // Insert staff users\n  await db.insert(users).values([\n    {\n      username: \"admin\",\n      email: \"<EMAIL>\",\n      password: adminPassword,\n      first_name: \"Admin\",\n      last_name: \"User\",\n      role: \"admin\",\n    },\n    {\n      username: \"manager\",\n      email: \"<EMAIL>\",\n      password: managerPassword,\n      first_name: \"Kitchen\",\n      last_name: \"Manager\",\n      role: \"manager\",\n    },\n    {\n      username: \"driver\",\n      email: \"<EMAIL>\",\n      password: driverPassword,\n      first_name: \"Delivery\",\n      last_name: \"Driver\",\n      role: \"driver\",\n    }\n  ]);\n  \n  console.log(\"✅ Users seeded successfully\");\n}\n\n// Run the seed function\nseedUsers()\n  .then(() => {\n    console.log(\"User seeding completed.\");\n    process.exit(0);\n  })\n  .catch(error => {\n    console.error(\"Error seeding users:\", error);\n    process.exit(1);\n  });", "modifiedCode": "import { db } from '../server/db';\nimport { users } from '../shared/schema';\nimport * as crypto from 'crypto';\n\n// Hash password function (same as in auth.ts)\nasync function hashPassword(password: string): Promise<string> {\n  const salt = crypto.randomBytes(16).toString(\"hex\");\n  return new Promise((resolve, reject) => {\n    crypto.scrypt(password, salt, 64, (err, derivedKey) => {\n      if (err) reject(err);\n      resolve(`${derivedKey.toString(\"hex\")}.${salt}`);\n    });\n  });\n}\n\nasync function seedUsers() {\n  console.log(\"🌱 Seeding users...\");\n  \n  // Check if users already exist\n  const existingUsers = await db.select().from(users);\n  \n  if (existingUsers.length > 0) {\n    console.log(\"Users already exist in the database. Skipping seeding.\");\n    return;\n  }\n  \n  // Hash passwords\n  const adminPassword = await hashPassword(\"admin123\");\n  const managerPassword = await hashPassword(\"manager123\");\n  const driverPassword = await hashPassword(\"driver123\");\n  \n  // Insert staff users\n  await db.insert(users).values([\n    {\n      username: \"admin\",\n      email: \"<EMAIL>\",\n      password: adminPassword,\n      first_name: \"Admin\",\n      last_name: \"User\",\n      role: \"admin\",\n    },\n    {\n      username: \"manager\",\n      email: \"<EMAIL>\",\n      password: managerPassword,\n      first_name: \"Kitchen\",\n      last_name: \"Manager\",\n      role: \"manager\",\n    },\n    {\n      username: \"driver\",\n      email: \"<EMAIL>\",\n      password: driverPassword,\n      first_name: \"Delivery\",\n      last_name: \"Driver\",\n      role: \"driver\",\n    }\n  ]);\n  \n  console.log(\"✅ Users seeded successfully\");\n}\n\n// Run the seed function\nseedUsers()\n  .then(() => {\n    console.log(\"User seeding completed.\");\n    process.exit(0);\n  })\n  .catch(error => {\n    console.error(\"Error seeding users:\", error);\n    process.exit(1);\n  });"}