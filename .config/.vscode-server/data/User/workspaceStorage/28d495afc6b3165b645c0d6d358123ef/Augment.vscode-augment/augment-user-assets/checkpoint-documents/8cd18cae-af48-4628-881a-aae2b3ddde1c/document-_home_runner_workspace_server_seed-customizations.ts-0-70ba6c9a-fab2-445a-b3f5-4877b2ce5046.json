{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "server/seed-customizations.ts"}, "originalCode": "import { db } from \"./db\";\nimport { customizationGroups, customizationOptions } from \"@shared/schema\";\n\nasync function seedCustomizations() {\n  try {\n    console.log(\"Seeding customization data...\");\n\n    // Check if customization groups already exist\n    const existingGroups = await db.select().from(customizationGroups).limit(1);\n    \n    if (existingGroups.length > 0) {\n      console.log(\"Customization groups already exist. Skipping seeding.\");\n      return;\n    }\n\n    // Insert customization groups\n    const sampleGroups = [\n      { title: \"Saus & Topping\" },\n      { title: \"Ost\" },\n      { title: \"Ekstra Produkter\" },\n      { title: \"<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\" }\n    ];\n\n    const insertedGroups = await db.insert(customizationGroups).values(sampleGroups).returning();\n    console.log(`Inserted ${insertedGroups.length} customization groups`);\n\n    // Insert customization options\n    const sampleOptions = [\n      { name: \"BBQ Sauce\", extraPrice: 10, imageUrl: \"\", groupId: insertedGroups[0].id },\n      { name: \"Hot Sauce\", extraPrice: 10, imageUrl: \"\", groupId: insertedGroups[0].id },\n      { name: \"<PERSON>\", extraPrice: 10, imageUrl: \"\", groupId: insertedGroups[0].id },\n      { name: \"Honey Mustard\", extraPrice: 15, imageUrl: \"\", groupId: insertedGroups[0].id },\n      \n      { name: \"Cheddar\", extraPrice: 15, imageUrl: \"\", groupId: insertedGroups[1].id },\n      { name: \"Blue Cheese\", extraPrice: 20, imageUrl: \"\", groupId: insertedGroups[1].id },\n      { name: \"Mozzarella\", extraPrice: 15, imageUrl: \"\", groupId: insertedGroups[1].id },\n      \n      { name: \"Bacon\", extraPrice: 25, imageUrl: \"\", groupId: insertedGroups[2].id },\n      { name: \"Double Meat\", extraPrice: 40, imageUrl: \"\", groupId: insertedGroups[2].id },\n      { name: \"Fried Egg\", extraPrice: 15, imageUrl: \"\", groupId: insertedGroups[2].id },\n      \n      { name: \"Lettuce\", extraPrice: 0, imageUrl: \"\", groupId: insertedGroups[3].id },\n      { name: \"Tomato\", extraPrice: 0, imageUrl: \"\", groupId: insertedGroups[3].id },\n      { name: \"Onion\", extraPrice: 0, imageUrl: \"\", groupId: insertedGroups[3].id },\n      { name: \"Cucumber\", extraPrice: 0, imageUrl: \"\", groupId: insertedGroups[3].id },\n      { name: \"Avocado\", extraPrice: 20, imageUrl: \"\", groupId: insertedGroups[3].id }\n    ];\n\n    const insertedOptions = await db.insert(customizationOptions).values(sampleOptions).returning();\n    console.log(`Inserted ${insertedOptions.length} customization options`);\n\n    console.log(\"Customization data seeded successfully!\");\n\n  } catch (error) {\n    console.error(\"Error seeding customizations:\", error);\n    throw error;\n  }\n}\n\n// Run if this file is executed directly\nif (import.meta.url === `file://${process.argv[1]}`) {\n  seedCustomizations()\n    .then(() => {\n      console.log(\"Customization seeding completed!\");\n      process.exit(0);\n    })\n    .catch((error) => {\n      console.error(\"Customization seeding failed:\", error);\n      process.exit(1);\n    });\n}\n\nexport { seedCustomizations };\n", "modifiedCode": "import { db } from \"./db\";\nimport { customizationGroups, customizationOptions } from \"@shared/schema\";\n\nasync function seedCustomizations() {\n  try {\n    console.log(\"Seeding customization data...\");\n\n    // Check if customization groups already exist\n    const existingGroups = await db.select().from(customizationGroups).limit(1);\n    \n    if (existingGroups.length > 0) {\n      console.log(\"Customization groups already exist. Skipping seeding.\");\n      return;\n    }\n\n    // Insert customization groups\n    const sampleGroups = [\n      { title: \"Saus & Topping\" },\n      { title: \"Ost\" },\n      { title: \"Ekstra Produkter\" },\n      { title: \"<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\" }\n    ];\n\n    const insertedGroups = await db.insert(customizationGroups).values(sampleGroups).returning();\n    console.log(`Inserted ${insertedGroups.length} customization groups`);\n\n    // Insert customization options\n    const sampleOptions = [\n      { name: \"BBQ Sauce\", extraPrice: 10, imageUrl: \"\", groupId: insertedGroups[0].id },\n      { name: \"Hot Sauce\", extraPrice: 10, imageUrl: \"\", groupId: insertedGroups[0].id },\n      { name: \"<PERSON>\", extraPrice: 10, imageUrl: \"\", groupId: insertedGroups[0].id },\n      { name: \"Honey Mustard\", extraPrice: 15, imageUrl: \"\", groupId: insertedGroups[0].id },\n      \n      { name: \"Cheddar\", extraPrice: 15, imageUrl: \"\", groupId: insertedGroups[1].id },\n      { name: \"Blue Cheese\", extraPrice: 20, imageUrl: \"\", groupId: insertedGroups[1].id },\n      { name: \"Mozzarella\", extraPrice: 15, imageUrl: \"\", groupId: insertedGroups[1].id },\n      \n      { name: \"Bacon\", extraPrice: 25, imageUrl: \"\", groupId: insertedGroups[2].id },\n      { name: \"Double Meat\", extraPrice: 40, imageUrl: \"\", groupId: insertedGroups[2].id },\n      { name: \"Fried Egg\", extraPrice: 15, imageUrl: \"\", groupId: insertedGroups[2].id },\n      \n      { name: \"Lettuce\", extraPrice: 0, imageUrl: \"\", groupId: insertedGroups[3].id },\n      { name: \"Tomato\", extraPrice: 0, imageUrl: \"\", groupId: insertedGroups[3].id },\n      { name: \"Onion\", extraPrice: 0, imageUrl: \"\", groupId: insertedGroups[3].id },\n      { name: \"Cucumber\", extraPrice: 0, imageUrl: \"\", groupId: insertedGroups[3].id },\n      { name: \"Avocado\", extraPrice: 20, imageUrl: \"\", groupId: insertedGroups[3].id }\n    ];\n\n    const insertedOptions = await db.insert(customizationOptions).values(sampleOptions).returning();\n    console.log(`Inserted ${insertedOptions.length} customization options`);\n\n    console.log(\"Customization data seeded successfully!\");\n\n  } catch (error) {\n    console.error(\"Error seeding customizations:\", error);\n    throw error;\n  }\n}\n\n// Run if this file is executed directly\nif (import.meta.url === `file://${process.argv[1]}`) {\n  seedCustomizations()\n    .then(() => {\n      console.log(\"Customization seeding completed!\");\n      process.exit(0);\n    })\n    .catch((error) => {\n      console.error(\"Customization seeding failed:\", error);\n      process.exit(1);\n    });\n}\n\nexport { seedCustomizations };\n"}