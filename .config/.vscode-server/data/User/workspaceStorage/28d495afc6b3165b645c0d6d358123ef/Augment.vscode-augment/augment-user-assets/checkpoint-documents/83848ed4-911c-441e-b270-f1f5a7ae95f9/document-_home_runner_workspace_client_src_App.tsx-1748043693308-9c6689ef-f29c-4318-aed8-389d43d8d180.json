{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/App.tsx"}, "originalCode": "import { useEffect } from \"react\";\nimport { Route, Switch, useLocation } from \"wouter\";\nimport { QueryClientProvider } from \"@tanstack/react-query\";\nimport { queryClient } from \"./lib/queryClient\";\nimport { Toaster } from \"@/components/ui/toaster\";\nimport { TooltipProvider } from \"@/components/ui/tooltip\";\n\nimport Header from \"@/components/Header\";\nimport Footer from \"@/components/Footer\";\nimport Home from \"@/pages/Home\";\nimport Menu from \"@/pages/Menu\";\nimport Cart from \"@/pages/Cart\";\nimport Checkout from \"@/pages/Checkout\";\nimport Contact from \"@/pages/Contact\";\nimport OrderConfirmation from \"@/pages/OrderConfirmation\";\nimport OrderTracker from \"@/pages/OrderTracker\";\nimport AdminSettings from \"@/pages/admin/Settings\";\nimport AdminMenu from \"@/pages/admin/Menu\";\nimport AdminAnalytics from \"@/pages/admin/Analytics\";\nimport OrderManager from \"@/pages/admin/OrderManager\";\nimport DriverPage from \"./pages/driver/DriverPage\";\nimport ManagerPage from \"./pages/manager/ManagerPage\";\nimport AuthPage from \"./pages/auth/AuthPage\";\nimport LogoutPage from \"./pages/auth/LogoutPage\";\nimport NotFound from \"@/pages/not-found\";\nimport { CartProvider } from \"@/context/CartContext\";\nimport Loader from \"@/components/Loader\";\n\nfunction Router() {\n  const [location] = useLocation();\n\n  // Check if current route is an admin, manager or driver route\n  const isInternalPage =\n    location.startsWith('/admin') ||\n    location.startsWith('/driver') ||\n    location.startsWith('/manager') ||\n    location.startsWith('/auth') ||\n    location === '/logout';\n\n  return (\n    <div className=\"flex flex-col min-h-screen\">\n      {!isInternalPage && <Header />}\n      <main className={`flex-grow ${!isInternalPage ? 'pt-20' : ''}`}>\n        <Switch>\n          <Route path=\"/\" component={Home} />\n          <Route path=\"/menu\" component={Menu} />\n          <Route path=\"/cart\" component={Cart} />\n          <Route path=\"/checkout\" component={Checkout} />\n          <Route path=\"/contact\" component={Contact} />\n          <Route path=\"/order-confirmation/:orderId?\" component={OrderConfirmation} />\n          <Route path=\"/order-tracker/:orderId?\" component={OrderTracker} />\n          <Route path=\"/auth\" component={AuthPage} />\n          <Route path=\"/logout\" component={LogoutPage} />\n          <Route path=\"/admin/settings\" component={AdminSettings} />\n          <Route path=\"/admin/menu\" component={AdminMenu} />\n          <Route path=\"/admin/analytics\" component={AdminAnalytics} />\n          <Route path=\"/admin/orders\" component={OrderManager} />\n          <Route path=\"/driver\" component={DriverPage} />\n          <Route path=\"/manager\" component={ManagerPage} />\n          <Route component={NotFound} />\n        </Switch>\n      </main>\n      {!isInternalPage && <Footer />}\n    </div>\n  );\n}\n\nfunction App() {\n  useEffect(() => {\n    // Set page title\n    document.title = \"Barbecuez Restaurant | Premium BBQ Experience\";\n  }, []);\n\n  return (\n    <QueryClientProvider client={queryClient}>\n      <CartProvider>\n        <TooltipProvider>\n          <Toaster />\n          <Router />\n          <Loader />\n        </TooltipProvider>\n      </CartProvider>\n    </QueryClientProvider>\n  );\n}\n\nexport default App;\n", "modifiedCode": "import { useEffect } from \"react\";\nimport { Route, Switch, useLocation } from \"wouter\";\nimport { QueryClientProvider } from \"@tanstack/react-query\";\nimport { queryClient } from \"./lib/queryClient\";\nimport { Toaster } from \"@/components/ui/toaster\";\nimport { TooltipProvider } from \"@/components/ui/tooltip\";\n\nimport Header from \"@/components/Header\";\nimport Footer from \"@/components/Footer\";\nimport Home from \"@/pages/Home\";\nimport Menu from \"@/pages/Menu\";\nimport Cart from \"@/pages/Cart\";\nimport Checkout from \"@/pages/Checkout\";\nimport Contact from \"@/pages/Contact\";\nimport OrderConfirmation from \"@/pages/OrderConfirmation\";\nimport OrderTracker from \"@/pages/OrderTracker\";\nimport AdminSettings from \"@/pages/admin/Settings\";\nimport AdminMenu from \"@/pages/admin/Menu\";\nimport AdminAnalytics from \"@/pages/admin/Analytics\";\nimport OrderManager from \"@/pages/admin/OrderManager\";\nimport DriverPage from \"./pages/driver/DriverPage\";\nimport ManagerPage from \"./pages/manager/ManagerPage\";\nimport AuthPage from \"./pages/auth/AuthPage\";\nimport LogoutPage from \"./pages/auth/LogoutPage\";\nimport NotFound from \"@/pages/not-found\";\nimport { CartProvider } from \"@/context/CartContext\";\nimport { RestaurantStatusProvider } from \"@/context/RestaurantStatusContext\";\nimport Loader from \"@/components/Loader\";\n\nfunction Router() {\n  const [location] = useLocation();\n\n  // Check if current route is an admin, manager or driver route\n  const isInternalPage =\n    location.startsWith('/admin') ||\n    location.startsWith('/driver') ||\n    location.startsWith('/manager') ||\n    location.startsWith('/auth') ||\n    location === '/logout';\n\n  return (\n    <div className=\"flex flex-col min-h-screen\">\n      {!isInternalPage && <Header />}\n      <main className={`flex-grow ${!isInternalPage ? 'pt-20' : ''}`}>\n        <Switch>\n          <Route path=\"/\" component={Home} />\n          <Route path=\"/menu\" component={Menu} />\n          <Route path=\"/cart\" component={Cart} />\n          <Route path=\"/checkout\" component={Checkout} />\n          <Route path=\"/contact\" component={Contact} />\n          <Route path=\"/order-confirmation/:orderId?\" component={OrderConfirmation} />\n          <Route path=\"/order-tracker/:orderId?\" component={OrderTracker} />\n          <Route path=\"/auth\" component={AuthPage} />\n          <Route path=\"/logout\" component={LogoutPage} />\n          <Route path=\"/admin/settings\" component={AdminSettings} />\n          <Route path=\"/admin/menu\" component={AdminMenu} />\n          <Route path=\"/admin/analytics\" component={AdminAnalytics} />\n          <Route path=\"/admin/orders\" component={OrderManager} />\n          <Route path=\"/driver\" component={DriverPage} />\n          <Route path=\"/manager\" component={ManagerPage} />\n          <Route component={NotFound} />\n        </Switch>\n      </main>\n      {!isInternalPage && <Footer />}\n    </div>\n  );\n}\n\nfunction App() {\n  useEffect(() => {\n    // Set page title\n    document.title = \"Barbecuez Restaurant | Premium BBQ Experience\";\n  }, []);\n\n  return (\n    <QueryClientProvider client={queryClient}>\n      <CartProvider>\n        <TooltipProvider>\n          <Toaster />\n          <Router />\n          <Loader />\n        </TooltipProvider>\n      </CartProvider>\n    </QueryClientProvider>\n  );\n}\n\nexport default App;\n"}