{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/api/customizationApi.ts"}, "originalCode": "import { CustomizationGroup, CustomizationOption, ItemCustomizationMap } from '@shared/schema';\n\nconst API_BASE = '/api/admin';\n\n// Customization Groups API\nexport const getCustomizationGroups = async (): Promise<CustomizationGroup[]> => {\n  const response = await fetch(`${API_BASE}/customization-groups`, {\n    credentials: 'include'\n  });\n  if (!response.ok) {\n    throw new Error('Failed to fetch customization groups');\n  }\n  return response.json();\n};\n\nexport const createCustomizationGroup = async (group: { title: string }): Promise<CustomizationGroup> => {\n  const response = await fetch(`${API_BASE}/customization-groups`, {\n    method: 'POST',\n    headers: {\n      'Content-Type': 'application/json',\n    },\n    credentials: 'include',\n    body: JSON.stringify(group),\n  });\n\n  if (!response.ok) {\n    throw new Error('Failed to create customization group');\n  }\n  return response.json();\n};\n\nexport const updateCustomizationGroup = async (id: number, group: { title: string }): Promise<CustomizationGroup> => {\n  const response = await fetch(`${API_BASE}/customization-groups/${id}`, {\n    method: 'PUT',\n    headers: {\n      'Content-Type': 'application/json',\n    },\n    credentials: 'include',\n    body: JSON.stringify(group),\n  });\n\n  if (!response.ok) {\n    throw new Error('Failed to update customization group');\n  }\n  return response.json();\n};\n\nexport const deleteCustomizationGroup = async (id: number): Promise<void> => {\n  const response = await fetch(`${API_BASE}/customization-groups/${id}`, {\n    method: 'DELETE',\n  });\n\n  if (!response.ok) {\n    throw new Error('Failed to delete customization group');\n  }\n};\n\n// Customization Options API\nexport const getCustomizationOptions = async (): Promise<CustomizationOption[]> => {\n  const response = await fetch(`${API_BASE}/customization-options`);\n  if (!response.ok) {\n    throw new Error('Failed to fetch customization options');\n  }\n  return response.json();\n};\n\nexport const getCustomizationOptionsByGroup = async (groupId: number): Promise<CustomizationOption[]> => {\n  const response = await fetch(`${API_BASE}/customization-options/group/${groupId}`);\n  if (!response.ok) {\n    throw new Error('Failed to fetch customization options for group');\n  }\n  return response.json();\n};\n\nexport const createCustomizationOption = async (option: {\n  name: string;\n  extraPrice?: number;\n  imageUrl?: string;\n  groupId: number;\n}): Promise<CustomizationOption> => {\n  const response = await fetch(`${API_BASE}/customization-options`, {\n    method: 'POST',\n    headers: {\n      'Content-Type': 'application/json',\n    },\n    body: JSON.stringify(option),\n  });\n\n  if (!response.ok) {\n    throw new Error('Failed to create customization option');\n  }\n  return response.json();\n};\n\nexport const updateCustomizationOption = async (\n  id: number,\n  option: {\n    name?: string;\n    extraPrice?: number;\n    imageUrl?: string;\n    groupId?: number;\n  }\n): Promise<CustomizationOption> => {\n  const response = await fetch(`${API_BASE}/customization-options/${id}`, {\n    method: 'PUT',\n    headers: {\n      'Content-Type': 'application/json',\n    },\n    body: JSON.stringify(option),\n  });\n\n  if (!response.ok) {\n    throw new Error('Failed to update customization option');\n  }\n  return response.json();\n};\n\nexport const deleteCustomizationOption = async (id: number): Promise<void> => {\n  const response = await fetch(`${API_BASE}/customization-options/${id}`, {\n    method: 'DELETE',\n  });\n\n  if (!response.ok) {\n    throw new Error('Failed to delete customization option');\n  }\n};\n\n// Item Customization Mapping API\nexport const getCustomizationsForMenuItem = async (itemId: number): Promise<{\n  group: CustomizationGroup;\n  options: CustomizationOption[];\n}[]> => {\n  const response = await fetch(`${API_BASE}/items/${itemId}/customizations`);\n  if (!response.ok) {\n    throw new Error('Failed to fetch customizations for menu item');\n  }\n  return response.json();\n};\n\nexport const mapCustomizationToMenuItem = async (itemId: number, optionId: number): Promise<ItemCustomizationMap> => {\n  const response = await fetch(`${API_BASE}/items/${itemId}/customizations/${optionId}`, {\n    method: 'POST',\n  });\n\n  if (!response.ok) {\n    throw new Error('Failed to map customization to menu item');\n  }\n  return response.json();\n};\n\nexport const unmapCustomizationFromMenuItem = async (itemId: number, optionId: number): Promise<void> => {\n  const response = await fetch(`${API_BASE}/items/${itemId}/customizations/${optionId}`, {\n    method: 'DELETE',\n  });\n\n  if (!response.ok) {\n    throw new Error('Failed to unmap customization from menu item');\n  }\n};\n", "modifiedCode": "import { CustomizationGroup, CustomizationOption, ItemCustomizationMap } from '@shared/schema';\n\nconst API_BASE = '/api/admin';\n\n// Customization Groups API\nexport const getCustomizationGroups = async (): Promise<CustomizationGroup[]> => {\n  const response = await fetch(`${API_BASE}/customization-groups`, {\n    credentials: 'include'\n  });\n  if (!response.ok) {\n    throw new Error('Failed to fetch customization groups');\n  }\n  return response.json();\n};\n\nexport const createCustomizationGroup = async (group: { title: string }): Promise<CustomizationGroup> => {\n  const response = await fetch(`${API_BASE}/customization-groups`, {\n    method: 'POST',\n    headers: {\n      'Content-Type': 'application/json',\n    },\n    credentials: 'include',\n    body: JSON.stringify(group),\n  });\n\n  if (!response.ok) {\n    throw new Error('Failed to create customization group');\n  }\n  return response.json();\n};\n\nexport const updateCustomizationGroup = async (id: number, group: { title: string }): Promise<CustomizationGroup> => {\n  const response = await fetch(`${API_BASE}/customization-groups/${id}`, {\n    method: 'PUT',\n    headers: {\n      'Content-Type': 'application/json',\n    },\n    credentials: 'include',\n    body: JSON.stringify(group),\n  });\n\n  if (!response.ok) {\n    throw new Error('Failed to update customization group');\n  }\n  return response.json();\n};\n\nexport const deleteCustomizationGroup = async (id: number): Promise<void> => {\n  const response = await fetch(`${API_BASE}/customization-groups/${id}`, {\n    method: 'DELETE',\n    credentials: 'include',\n  });\n\n  if (!response.ok) {\n    throw new Error('Failed to delete customization group');\n  }\n};\n\n// Customization Options API\nexport const getCustomizationOptions = async (): Promise<CustomizationOption[]> => {\n  const response = await fetch(`${API_BASE}/customization-options`);\n  if (!response.ok) {\n    throw new Error('Failed to fetch customization options');\n  }\n  return response.json();\n};\n\nexport const getCustomizationOptionsByGroup = async (groupId: number): Promise<CustomizationOption[]> => {\n  const response = await fetch(`${API_BASE}/customization-options/group/${groupId}`);\n  if (!response.ok) {\n    throw new Error('Failed to fetch customization options for group');\n  }\n  return response.json();\n};\n\nexport const createCustomizationOption = async (option: {\n  name: string;\n  extraPrice?: number;\n  imageUrl?: string;\n  groupId: number;\n}): Promise<CustomizationOption> => {\n  const response = await fetch(`${API_BASE}/customization-options`, {\n    method: 'POST',\n    headers: {\n      'Content-Type': 'application/json',\n    },\n    body: JSON.stringify(option),\n  });\n\n  if (!response.ok) {\n    throw new Error('Failed to create customization option');\n  }\n  return response.json();\n};\n\nexport const updateCustomizationOption = async (\n  id: number,\n  option: {\n    name?: string;\n    extraPrice?: number;\n    imageUrl?: string;\n    groupId?: number;\n  }\n): Promise<CustomizationOption> => {\n  const response = await fetch(`${API_BASE}/customization-options/${id}`, {\n    method: 'PUT',\n    headers: {\n      'Content-Type': 'application/json',\n    },\n    body: JSON.stringify(option),\n  });\n\n  if (!response.ok) {\n    throw new Error('Failed to update customization option');\n  }\n  return response.json();\n};\n\nexport const deleteCustomizationOption = async (id: number): Promise<void> => {\n  const response = await fetch(`${API_BASE}/customization-options/${id}`, {\n    method: 'DELETE',\n  });\n\n  if (!response.ok) {\n    throw new Error('Failed to delete customization option');\n  }\n};\n\n// Item Customization Mapping API\nexport const getCustomizationsForMenuItem = async (itemId: number): Promise<{\n  group: CustomizationGroup;\n  options: CustomizationOption[];\n}[]> => {\n  const response = await fetch(`${API_BASE}/items/${itemId}/customizations`);\n  if (!response.ok) {\n    throw new Error('Failed to fetch customizations for menu item');\n  }\n  return response.json();\n};\n\nexport const mapCustomizationToMenuItem = async (itemId: number, optionId: number): Promise<ItemCustomizationMap> => {\n  const response = await fetch(`${API_BASE}/items/${itemId}/customizations/${optionId}`, {\n    method: 'POST',\n  });\n\n  if (!response.ok) {\n    throw new Error('Failed to map customization to menu item');\n  }\n  return response.json();\n};\n\nexport const unmapCustomizationFromMenuItem = async (itemId: number, optionId: number): Promise<void> => {\n  const response = await fetch(`${API_BASE}/items/${itemId}/customizations/${optionId}`, {\n    method: 'DELETE',\n  });\n\n  if (!response.ok) {\n    throw new Error('Failed to unmap customization from menu item');\n  }\n};\n"}