{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "attached_assets/Pasted-Build-the-Manager-Kitchen-Page-for-Order-Status-Control-React-TailwindCSS-Framer-Motion-Obj-1747874559621.txt"}, "originalCode": "Build the Manager Kitchen Page for Order Status Control (React + TailwindCSS + Framer Motion)\n🎯 Objective:\nCreate a powerful \"Manager\" page for the kitchen to manage all incoming orders. This page should allow updating the order status dynamically, with specific rules depending on whether the order is Takeaway or Delivery.\n\n🧩 Main Features & Logic Rules\n✅ General Layout\nDark, dashboard-style interface optimized for kitchen use\n\nResponsive grid/table of active orders\n\nEach order card/row includes:\n\nOrder ID\n\nOrder type (Takeaway or Delivery)\n\nItems summary\n\nTimestamp (order time)\n\nCurrent status badge (with color and glow)\n\nNext action button depending on type and status\n\nIf Delivery → includes \"Send to Driver\" logic at final kitchen stage\n\n🔁 Status Flow Logic\n🥡 If order type is TAKEAWAY\nConfirmed\n\n→ Preparing\n\n→ Complete ✅ (final status)\n\n🚚 If order type is DELIVERY\nConfirmed\n\n→ Preparing\n\n→ Ready for Delivery ✅ (final status in manager panel)\n\nManager clicks “Send to Driver” → triggers handoff to Driver Page\n\nOrder becomes visible in Driver system with status = With Driver\n\n📋 Order Card UI\nEach order should display:\n\nOrder ID, Time\n\nType badge: \"Takeaway\" or \"Delivery\" (colored neon)\n\nCustomer name & phone (optional)\n\nOrdered items list (names + quantities)\n\nCurrent status indicator\n\nAction button:\n\nLabel and function depend on current status & type\n\nBut<PERSON> uses glowing neon style and scales on hover\n\n⚙️ Logic Handling\nOrders should be pulled via:\n\nGET /api/orders?status=in_progress\n\nOrder status update:\n\nPUT /api/orders/:id/status\n\njson\nCopy\nEdit\n{ \"newStatus\": \"preparing\" }\nUpon final delivery-ready status (ready for delivery), a separate API call should be triggered:\n\nPOST /api/dispatch/to-driver\n\njson\nCopy\nEdit\n{ \"orderId\": 123 }\n🖼️ Design & Animation (UX)\nUse Framer Motion to animate:\n\nStatus change (e.g., fade out old badge, fade in new one)\n\nButton press (scale + glow)\n\nCards entering the screen (staggered motion)\n\nTailwind Styles:\n\nNeon glowing status labels\n\nBackground: #000000 with glassmorphism card containers\n\nColor-coded statuses:\n\nConfirmed: Neutral glow\n\nPreparing: Orange glow\n\nComplete: Green glow\n\nReady for Delivery: Lime\n\nWith Driver: Blue\n\n📁 Component Structure Suggestion\nbash\nCopy\nEdit\n/pages\n └── ManagerPage.jsx\n\n/components\n └── OrderCard.jsx\n └── StatusBadge.jsx\n └── ActionButton.jsx\n✅ Output\nA fully working, animated Manager control page that:\n\nDistinguishes clearly between Takeaway and Delivery logic\n\nAllows kitchen staff to update order statuses progressively\n\nTriggers dispatch logic only for delivery orders\n\nPrepares delivery orders for driver handoff\n\n🧩 Optional Enhancements\nAuto-refresh orders every 30 seconds\n\nSound alert when new order is confirmed\n\nFilter by status or type (tabs or dropdown)\n\nGroup orders into columns by status (e.g., Kanban-style)\n\n", "modifiedCode": "Build the Manager Kitchen Page for Order Status Control (React + TailwindCSS + Framer Motion)\n🎯 Objective:\nCreate a powerful \"Manager\" page for the kitchen to manage all incoming orders. This page should allow updating the order status dynamically, with specific rules depending on whether the order is Takeaway or Delivery.\n\n🧩 Main Features & Logic Rules\n✅ General Layout\nDark, dashboard-style interface optimized for kitchen use\n\nResponsive grid/table of active orders\n\nEach order card/row includes:\n\nOrder ID\n\nOrder type (Takeaway or Delivery)\n\nItems summary\n\nTimestamp (order time)\n\nCurrent status badge (with color and glow)\n\nNext action button depending on type and status\n\nIf Delivery → includes \"Send to Driver\" logic at final kitchen stage\n\n🔁 Status Flow Logic\n🥡 If order type is TAKEAWAY\nConfirmed\n\n→ Preparing\n\n→ Complete ✅ (final status)\n\n🚚 If order type is DELIVERY\nConfirmed\n\n→ Preparing\n\n→ Ready for Delivery ✅ (final status in manager panel)\n\nManager clicks “Send to Driver” → triggers handoff to Driver Page\n\nOrder becomes visible in Driver system with status = With Driver\n\n📋 Order Card UI\nEach order should display:\n\nOrder ID, Time\n\nType badge: \"Takeaway\" or \"Delivery\" (colored neon)\n\nCustomer name & phone (optional)\n\nOrdered items list (names + quantities)\n\nCurrent status indicator\n\nAction button:\n\nLabel and function depend on current status & type\n\nBut<PERSON> uses glowing neon style and scales on hover\n\n⚙️ Logic Handling\nOrders should be pulled via:\n\nGET /api/orders?status=in_progress\n\nOrder status update:\n\nPUT /api/orders/:id/status\n\njson\nCopy\nEdit\n{ \"newStatus\": \"preparing\" }\nUpon final delivery-ready status (ready for delivery), a separate API call should be triggered:\n\nPOST /api/dispatch/to-driver\n\njson\nCopy\nEdit\n{ \"orderId\": 123 }\n🖼️ Design & Animation (UX)\nUse Framer Motion to animate:\n\nStatus change (e.g., fade out old badge, fade in new one)\n\nButton press (scale + glow)\n\nCards entering the screen (staggered motion)\n\nTailwind Styles:\n\nNeon glowing status labels\n\nBackground: #000000 with glassmorphism card containers\n\nColor-coded statuses:\n\nConfirmed: Neutral glow\n\nPreparing: Orange glow\n\nComplete: Green glow\n\nReady for Delivery: Lime\n\nWith Driver: Blue\n\n📁 Component Structure Suggestion\nbash\nCopy\nEdit\n/pages\n └── ManagerPage.jsx\n\n/components\n └── OrderCard.jsx\n └── StatusBadge.jsx\n └── ActionButton.jsx\n✅ Output\nA fully working, animated Manager control page that:\n\nDistinguishes clearly between Takeaway and Delivery logic\n\nAllows kitchen staff to update order statuses progressively\n\nTriggers dispatch logic only for delivery orders\n\nPrepares delivery orders for driver handoff\n\n🧩 Optional Enhancements\nAuto-refresh orders every 30 seconds\n\nSound alert when new order is confirmed\n\nFilter by status or type (tabs or dropdown)\n\nGroup orders into columns by status (e.g., Kanban-style)\n\n"}