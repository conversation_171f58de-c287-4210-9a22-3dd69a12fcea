{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/RestaurantStatusBanner.tsx"}, "modifiedCode": "import { useRestaurantStatus } from \"@/context/RestaurantStatusContext\";\n\nconst RestaurantStatusBanner = () => {\n  const { isOpen, isLoading } = useRestaurantStatus();\n\n  // Don't show anything while loading\n  if (isLoading) {\n    return null;\n  }\n\n  // Only show the banner when the restaurant is closed\n  if (isOpen) {\n    return null;\n  }\n\n  return (\n    <div className=\"bg-red-900/80 text-white py-2 px-4 text-center border-b border-red-700 sticky top-0 z-50\">\n      <p className=\"flex items-center justify-center\">\n        <span className=\"inline-block w-2 h-2 bg-red-500 rounded-full mr-2 animate-pulse\"></span>\n        Restaurant is currently CLOSED - Online ordering is unavailable\n      </p>\n    </div>\n  );\n};\n\nexport default RestaurantStatusBanner;\n"}