{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "attached_assets/Pasted--Build-Admin-Panel-for-Restaurant-Settings-Menu-Management-Revenue-Analytics-React-TailwindCSS-1747871652816.txt"}, "originalCode": " Build Admin Panel for Restaurant Settings, Menu Management & Revenue Analytics (React + TailwindCSS + Framer Motion)\n🎯 Objective:\nCreate a clean, intuitive Admin Panel to allow restaurant staff or owner to:\n\nManage settings like business hours, delivery status, and restaurant availability\n\nAdd/edit/remove menu items and categories\n\nView earnings data (e.g., daily/weekly revenue)\n\nControl whether the restaurant accepts orders (Open/Closed switch)\n\n📁 Page Structure & Routing\n/admin/settings → General restaurant settings\n\n/admin/menu → Add/edit/remove items and categories\n\n/admin/analytics → Revenue dashboard\n\nUse React Router with nested routing for /admin.\n\n🧩 1. Settings Page Prompt\nPrompt:\n\nsql\nCopy\nEdit\nCreate an Admin Settings page for restaurant control using React + TailwindCSS.\n\nFeatures:\n- Toggle: Restaurant is Open / Closed\n  - When set to “Closed”, customers cannot place orders\n  - Reflects visually on the frontend (e.g., red banner)\n\n- Business Hours Configuration:\n  - Time pickers (from/to) for each day of the week\n  - Delivery availability toggle per day\n  - Store in backend as JSON object per day\n\n- Delivery Fee & Estimated Time:\n  - Editable values (e.g., \"Delivery Time: 25–35 min\", \"Fee: 49 NOK\")\n\n- Save Changes Button with glowing confirmation\n\nUse Tailwind’s form utilities with glassmorphism + neon highlight focus.\n🍔 2. Menu Management Page Prompt\nPrompt:\n\nmarkdown\nCopy\nEdit\nCreate a Menu Management interface with two main tabs:\n\n### A. Categories Management\n- Display all current categories in cards (name + image)\n- Add new category form:\n  - Upload image (or use URL temporarily)\n  - Enter name\n  - Neon “Add Category” button\n\n### B. Items Management\n- Show all items in a table or grid\n- Each row shows:\n  - Image thumbnail\n  - Name, price (NOK), category name\n  - Button to “Edit” or “Delete”\n- Add Item Form:\n  - Select category\n  - Upload image\n  - Name\n  - Description\n  - Base price (NOK)\n  - Optional: availability toggle\n\n- Add/Edit forms open in modal with glowing borders\n- Use Framer Motion for modal transitions and card animations\n📊 3. Analytics Page Prompt\nPrompt:\n\nsql\nCopy\nEdit\nCreate a Revenue Analytics Dashboard with elegant dark visuals and glowing graphs.\n\nFeatures:\n- Line chart for revenue (e.g., last 7 or 30 days)\n- Total income today, this week, this month\n- Number of orders (optional)\n- Revenue per category breakdown (pie or bar)\n\nUse a chart library (e.g., Recharts or Chart.js with Tailwind-compatible styling).\n\nNeon glowing cards for:\n- “Today’s Income”\n- “This Week”\n- “This Month”\n- Style each card with animated counters\n\nLayout:\n- Left: metrics\n- Right: interactive chart with filters (daily/weekly/monthly)\n⚙️ Technical & Functional Notes\nAll forms should use React Hook Form or basic controlled inputs\n\nUse Tailwind form styling with focus-ring and dark/glass UI\n\nFramer Motion for:\n\nTab transitions\n\nModal animations\n\nButton interactions\n\nAxios for API integration:\n\nGET /admin/settings\n\nPUT /admin/settings\n\nGET/POST/DELETE /admin/categories\n\nGET/POST/PUT/DELETE /admin/items\n\nGET /admin/analytics\n\nDisplay a warning banner on all pages if restaurantOpen = false\n\nStore open/closed status in database and serve via /api/settings\n\n✅ Output:\nA fully functional Admin Panel that:\n\nLets the restaurant team manage every aspect (hours, delivery, items)\n\nProvides visual insights on income\n\nControls availability of ordering system\n\nMatches the luxury + glowing identity of the main website\n\n", "modifiedCode": " Build Admin Panel for Restaurant Settings, Menu Management & Revenue Analytics (React + TailwindCSS + Framer Motion)\n🎯 Objective:\nCreate a clean, intuitive Admin Panel to allow restaurant staff or owner to:\n\nManage settings like business hours, delivery status, and restaurant availability\n\nAdd/edit/remove menu items and categories\n\nView earnings data (e.g., daily/weekly revenue)\n\nControl whether the restaurant accepts orders (Open/Closed switch)\n\n📁 Page Structure & Routing\n/admin/settings → General restaurant settings\n\n/admin/menu → Add/edit/remove items and categories\n\n/admin/analytics → Revenue dashboard\n\nUse React Router with nested routing for /admin.\n\n🧩 1. Settings Page Prompt\nPrompt:\n\nsql\nCopy\nEdit\nCreate an Admin Settings page for restaurant control using React + TailwindCSS.\n\nFeatures:\n- Toggle: Restaurant is Open / Closed\n  - When set to “Closed”, customers cannot place orders\n  - Reflects visually on the frontend (e.g., red banner)\n\n- Business Hours Configuration:\n  - Time pickers (from/to) for each day of the week\n  - Delivery availability toggle per day\n  - Store in backend as JSON object per day\n\n- Delivery Fee & Estimated Time:\n  - Editable values (e.g., \"Delivery Time: 25–35 min\", \"Fee: 49 NOK\")\n\n- Save Changes Button with glowing confirmation\n\nUse Tailwind’s form utilities with glassmorphism + neon highlight focus.\n🍔 2. Menu Management Page Prompt\nPrompt:\n\nmarkdown\nCopy\nEdit\nCreate a Menu Management interface with two main tabs:\n\n### A. Categories Management\n- Display all current categories in cards (name + image)\n- Add new category form:\n  - Upload image (or use URL temporarily)\n  - Enter name\n  - Neon “Add Category” button\n\n### B. Items Management\n- Show all items in a table or grid\n- Each row shows:\n  - Image thumbnail\n  - Name, price (NOK), category name\n  - Button to “Edit” or “Delete”\n- Add Item Form:\n  - Select category\n  - Upload image\n  - Name\n  - Description\n  - Base price (NOK)\n  - Optional: availability toggle\n\n- Add/Edit forms open in modal with glowing borders\n- Use Framer Motion for modal transitions and card animations\n📊 3. Analytics Page Prompt\nPrompt:\n\nsql\nCopy\nEdit\nCreate a Revenue Analytics Dashboard with elegant dark visuals and glowing graphs.\n\nFeatures:\n- Line chart for revenue (e.g., last 7 or 30 days)\n- Total income today, this week, this month\n- Number of orders (optional)\n- Revenue per category breakdown (pie or bar)\n\nUse a chart library (e.g., Recharts or Chart.js with Tailwind-compatible styling).\n\nNeon glowing cards for:\n- “Today’s Income”\n- “This Week”\n- “This Month”\n- Style each card with animated counters\n\nLayout:\n- Left: metrics\n- Right: interactive chart with filters (daily/weekly/monthly)\n⚙️ Technical & Functional Notes\nAll forms should use React Hook Form or basic controlled inputs\n\nUse Tailwind form styling with focus-ring and dark/glass UI\n\nFramer Motion for:\n\nTab transitions\n\nModal animations\n\nButton interactions\n\nAxios for API integration:\n\nGET /admin/settings\n\nPUT /admin/settings\n\nGET/POST/DELETE /admin/categories\n\nGET/POST/PUT/DELETE /admin/items\n\nGET /admin/analytics\n\nDisplay a warning banner on all pages if restaurantOpen = false\n\nStore open/closed status in database and serve via /api/settings\n\n✅ Output:\nA fully functional Admin Panel that:\n\nLets the restaurant team manage every aspect (hours, delivery, items)\n\nProvides visual insights on income\n\nControls availability of ordering system\n\nMatches the luxury + glowing identity of the main website\n\n"}