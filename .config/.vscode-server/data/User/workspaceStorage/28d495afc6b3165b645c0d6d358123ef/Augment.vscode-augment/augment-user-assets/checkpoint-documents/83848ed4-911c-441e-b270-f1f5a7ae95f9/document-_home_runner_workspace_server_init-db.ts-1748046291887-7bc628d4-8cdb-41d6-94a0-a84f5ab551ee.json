{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "server/init-db.ts"}, "originalCode": "import { db } from \"./db\";\nimport {\n  users, categories, menuItems, orders, contactMessages,\n  customizationGroups, customizationOptions, itemCustomizationMap\n} from \"@shared/schema\";\nimport * as crypto from \"crypto\";\n\n// Password hashing utility functions\nasync function hashPassword(password: string): Promise<string> {\n  const salt = crypto.randomBytes(16).toString(\"hex\");\n  return new Promise((resolve, reject) => {\n    crypto.scrypt(password, salt, 64, (err, derivedKey) => {\n      if (err) reject(err);\n      resolve(`${derivedKey.toString(\"hex\")}.${salt}`);\n    });\n  });\n}\n\nasync function initializeDatabase() {\n  try {\n    console.log(\"Initializing database...\");\n\n    // Check if we have any categories, if not, initialize with sample data\n    const existingCategories = await db.select().from(categories).limit(1);\n\n    if (existingCategories.length === 0) {\n      console.log(\"Database is empty. Seeding with initial data...\");\n      await seedDatabase();\n    } else {\n      console.log(\"Database already contains data. Skipping seeding.\");\n    }\n\n    console.log(\"Database initialization completed!\");\n  } catch (error) {\n    console.error(\"Error initializing database:\", error);\n    throw error;\n  }\n}\n\nasync function seedDatabase() {\n  try {\n    // Insert sample categories\n    const sampleCategories = [\n      {\n        name: \"Signature BBQ\",\n        imageUrl: \"https://images.unsplash.com/photo-1529193591184-b1d58069ecdd?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&h=400\",\n      },\n      {\n        name: \"Starters\",\n        imageUrl: \"https://images.unsplash.com/photo-1527477396000-e27163b481c2?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&h=400\",\n      },\n      {\n        name: \"Main Course\",\n        imageUrl: \"https://images.unsplash.com/photo-1519708227418-c8fd9a32b7a2?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&h=400\",\n      },\n      {\n        name: \"Desserts\",\n        imageUrl: \"https://images.unsplash.com/photo-1624353365286-3f8d62daad51?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&h=400\",\n      },\n      {\n        name: \"BurgerZ\",\n        imageUrl: \"https://images.unsplash.com/photo-1568901346375-23c9450c58cd?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&h=400\",\n      },\n      {\n        name: \"SandwichZ\",\n        imageUrl: \"https://images.unsplash.com/photo-1621996346565-e3dbc646d9a9?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&h=400\",\n      }\n    ];\n\n    const insertedCategories = await db.insert(categories).values(sampleCategories).returning();\n    console.log(`Inserted ${insertedCategories.length} categories`);\n\n    // Insert sample menu items\n    const sampleMenuItems = [\n      {\n        name: \"Smoked Beef Brisket\",\n        description: \"24-hour smoked premium beef brisket with our signature spice rub, served with homemade BBQ sauce and pickled vegetables.\",\n        price: 329,\n        imageUrl: \"https://images.unsplash.com/photo-1529193591184-b1d58069ecdd?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&h=400\",\n        categoryId: insertedCategories[0].id, // Signature BBQ\n        available: true,\n        rating: 49,\n        reviews: 120\n      },\n      {\n        name: \"BBQ Pulled Pork\",\n        description: \"Slow-cooked pulled pork shoulder with tangy BBQ sauce, served on a brioche bun with coleslaw.\",\n        price: 269,\n        imageUrl: \"https://images.unsplash.com/photo-1544025162-d76694265947?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&h=400\",\n        categoryId: insertedCategories[0].id, // Signature BBQ\n        available: true,\n        rating: 46,\n        reviews: 89\n      },\n      {\n        name: \"Loaded Nachos\",\n        description: \"Crispy tortilla chips topped with melted cheese, jalapeños, sour cream, and guacamole.\",\n        price: 189,\n        imageUrl: \"https://images.unsplash.com/photo-1513456852971-30c0b8199d4d?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&h=400\",\n        categoryId: insertedCategories[1].id, // Starters\n        available: true,\n        rating: 42,\n        reviews: 67\n      },\n      {\n        name: \"BBQ Wings\",\n        description: \"Crispy chicken wings tossed in our signature BBQ sauce, served with ranch dipping sauce.\",\n        price: 219,\n        imageUrl: \"https://images.unsplash.com/photo-1527477396000-e27163b481c2?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&h=400\",\n        categoryId: insertedCategories[1].id, // Starters\n        available: true,\n        rating: 44,\n        reviews: 78\n      },\n      {\n        name: \"Classic Cheeseburger\",\n        description: \"Juicy beef patty with melted cheese, lettuce, tomato, and our special sauce on a toasted bun.\",\n        price: 249,\n        imageUrl: \"https://images.unsplash.com/photo-1568901346375-23c9450c58cd?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&h=400\",\n        categoryId: insertedCategories[4].id, // BurgerZ\n        available: true,\n        rating: 47,\n        reviews: 156\n      },\n      {\n        name: \"BBQ Sandwich\",\n        description: \"Tender pulled pork with BBQ sauce, pickles, and coleslaw on a fresh sandwich roll.\",\n        price: 229,\n        imageUrl: \"https://images.unsplash.com/photo-1621996346565-e3dbc646d9a9?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&h=400\",\n        categoryId: insertedCategories[5].id, // SandwichZ\n        available: true,\n        rating: 45,\n        reviews: 92\n      }\n    ];\n\n    const insertedMenuItems = await db.insert(menuItems).values(sampleMenuItems).returning();\n    console.log(`Inserted ${insertedMenuItems.length} menu items`);\n\n    // Insert customization groups\n    const sampleGroups = [\n      { title: \"Saus & Topping\" },\n      { title: \"Ost\" },\n      { title: \"Ekstra Produkter\" },\n      { title: \"Grønnsaker\" }\n    ];\n\n    const insertedGroups = await db.insert(customizationGroups).values(sampleGroups).returning();\n    console.log(`Inserted ${insertedGroups.length} customization groups`);\n\n    // Insert customization options\n    const sampleOptions = [\n      { name: \"BBQ Sauce\", extraPrice: 10, imageUrl: \"\", groupId: insertedGroups[0].id },\n      { name: \"Hot Sauce\", extraPrice: 10, imageUrl: \"\", groupId: insertedGroups[0].id },\n      { name: \"Mayo\", extraPrice: 10, imageUrl: \"\", groupId: insertedGroups[0].id },\n      { name: \"Honey Mustard\", extraPrice: 15, imageUrl: \"\", groupId: insertedGroups[0].id },\n\n      { name: \"Cheddar\", extraPrice: 15, imageUrl: \"\", groupId: insertedGroups[1].id },\n      { name: \"Blue Cheese\", extraPrice: 20, imageUrl: \"\", groupId: insertedGroups[1].id },\n      { name: \"Mozzarella\", extraPrice: 15, imageUrl: \"\", groupId: insertedGroups[1].id },\n\n      { name: \"Bacon\", extraPrice: 25, imageUrl: \"\", groupId: insertedGroups[2].id },\n      { name: \"Double Meat\", extraPrice: 40, imageUrl: \"\", groupId: insertedGroups[2].id },\n      { name: \"Fried Egg\", extraPrice: 15, imageUrl: \"\", groupId: insertedGroups[2].id },\n\n      { name: \"Lettuce\", extraPrice: 0, imageUrl: \"\", groupId: insertedGroups[3].id },\n      { name: \"Tomato\", extraPrice: 0, imageUrl: \"\", groupId: insertedGroups[3].id },\n      { name: \"Onion\", extraPrice: 0, imageUrl: \"\", groupId: insertedGroups[3].id },\n      { name: \"Cucumber\", extraPrice: 0, imageUrl: \"\", groupId: insertedGroups[3].id },\n      { name: \"Avocado\", extraPrice: 20, imageUrl: \"\", groupId: insertedGroups[3].id }\n    ];\n\n    const insertedOptions = await db.insert(customizationOptions).values(sampleOptions).returning();\n    console.log(`Inserted ${insertedOptions.length} customization options`);\n\n    // Create a default admin user\n    const hashedPassword = await hashPassword(\"admin123\");\n    const adminUser = {\n      username: \"admin\",\n      password: hashedPassword,\n      role: \"admin\" as const\n    };\n\n    const insertedUsers = await db.insert(users).values([adminUser]).returning();\n    console.log(`Created admin user: ${insertedUsers[0].username}`);\n\n    console.log(\"Database seeded successfully!\");\n  } catch (error) {\n    console.error(\"Error seeding database:\", error);\n    throw error;\n  }\n}\n\n// Run initialization if this file is executed directly\nif (require.main === module) {\n  initializeDatabase()\n    .then(() => {\n      console.log(\"Database initialization completed!\");\n      process.exit(0);\n    })\n    .catch((error) => {\n      console.error(\"Database initialization failed:\", error);\n      process.exit(1);\n    });\n}\n\nexport { initializeDatabase, seedDatabase };\n", "modifiedCode": "import { db } from \"./db\";\nimport {\n  users, categories, menuItems, orders, contactMessages,\n  customizationGroups, customizationOptions, itemCustomizationMap\n} from \"@shared/schema\";\nimport * as crypto from \"crypto\";\n\n// Password hashing utility functions\nasync function hashPassword(password: string): Promise<string> {\n  const salt = crypto.randomBytes(16).toString(\"hex\");\n  return new Promise((resolve, reject) => {\n    crypto.scrypt(password, salt, 64, (err, derivedKey) => {\n      if (err) reject(err);\n      resolve(`${derivedKey.toString(\"hex\")}.${salt}`);\n    });\n  });\n}\n\nasync function initializeDatabase() {\n  try {\n    console.log(\"Initializing database...\");\n\n    // Check if we have any categories, if not, initialize with sample data\n    const existingCategories = await db.select().from(categories).limit(1);\n\n    if (existingCategories.length === 0) {\n      console.log(\"Database is empty. Seeding with initial data...\");\n      await seedDatabase();\n    } else {\n      console.log(\"Database already contains data. Skipping seeding.\");\n    }\n\n    console.log(\"Database initialization completed!\");\n  } catch (error) {\n    console.error(\"Error initializing database:\", error);\n    throw error;\n  }\n}\n\nasync function seedDatabase() {\n  try {\n    // Insert sample categories\n    const sampleCategories = [\n      {\n        name: \"Signature BBQ\",\n        imageUrl: \"https://images.unsplash.com/photo-1529193591184-b1d58069ecdd?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&h=400\",\n      },\n      {\n        name: \"Starters\",\n        imageUrl: \"https://images.unsplash.com/photo-1527477396000-e27163b481c2?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&h=400\",\n      },\n      {\n        name: \"Main Course\",\n        imageUrl: \"https://images.unsplash.com/photo-1519708227418-c8fd9a32b7a2?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&h=400\",\n      },\n      {\n        name: \"Desserts\",\n        imageUrl: \"https://images.unsplash.com/photo-1624353365286-3f8d62daad51?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&h=400\",\n      },\n      {\n        name: \"BurgerZ\",\n        imageUrl: \"https://images.unsplash.com/photo-1568901346375-23c9450c58cd?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&h=400\",\n      },\n      {\n        name: \"SandwichZ\",\n        imageUrl: \"https://images.unsplash.com/photo-1621996346565-e3dbc646d9a9?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&h=400\",\n      }\n    ];\n\n    const insertedCategories = await db.insert(categories).values(sampleCategories).returning();\n    console.log(`Inserted ${insertedCategories.length} categories`);\n\n    // Insert sample menu items\n    const sampleMenuItems = [\n      {\n        name: \"Smoked Beef Brisket\",\n        description: \"24-hour smoked premium beef brisket with our signature spice rub, served with homemade BBQ sauce and pickled vegetables.\",\n        price: 329,\n        imageUrl: \"https://images.unsplash.com/photo-1529193591184-b1d58069ecdd?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&h=400\",\n        categoryId: insertedCategories[0].id, // Signature BBQ\n        available: true,\n        rating: 49,\n        reviews: 120\n      },\n      {\n        name: \"BBQ Pulled Pork\",\n        description: \"Slow-cooked pulled pork shoulder with tangy BBQ sauce, served on a brioche bun with coleslaw.\",\n        price: 269,\n        imageUrl: \"https://images.unsplash.com/photo-1544025162-d76694265947?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&h=400\",\n        categoryId: insertedCategories[0].id, // Signature BBQ\n        available: true,\n        rating: 46,\n        reviews: 89\n      },\n      {\n        name: \"Loaded Nachos\",\n        description: \"Crispy tortilla chips topped with melted cheese, jalapeños, sour cream, and guacamole.\",\n        price: 189,\n        imageUrl: \"https://images.unsplash.com/photo-1513456852971-30c0b8199d4d?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&h=400\",\n        categoryId: insertedCategories[1].id, // Starters\n        available: true,\n        rating: 42,\n        reviews: 67\n      },\n      {\n        name: \"BBQ Wings\",\n        description: \"Crispy chicken wings tossed in our signature BBQ sauce, served with ranch dipping sauce.\",\n        price: 219,\n        imageUrl: \"https://images.unsplash.com/photo-1527477396000-e27163b481c2?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&h=400\",\n        categoryId: insertedCategories[1].id, // Starters\n        available: true,\n        rating: 44,\n        reviews: 78\n      },\n      {\n        name: \"Classic Cheeseburger\",\n        description: \"Juicy beef patty with melted cheese, lettuce, tomato, and our special sauce on a toasted bun.\",\n        price: 249,\n        imageUrl: \"https://images.unsplash.com/photo-1568901346375-23c9450c58cd?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&h=400\",\n        categoryId: insertedCategories[4].id, // BurgerZ\n        available: true,\n        rating: 47,\n        reviews: 156\n      },\n      {\n        name: \"BBQ Sandwich\",\n        description: \"Tender pulled pork with BBQ sauce, pickles, and coleslaw on a fresh sandwich roll.\",\n        price: 229,\n        imageUrl: \"https://images.unsplash.com/photo-1621996346565-e3dbc646d9a9?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&h=400\",\n        categoryId: insertedCategories[5].id, // SandwichZ\n        available: true,\n        rating: 45,\n        reviews: 92\n      }\n    ];\n\n    const insertedMenuItems = await db.insert(menuItems).values(sampleMenuItems).returning();\n    console.log(`Inserted ${insertedMenuItems.length} menu items`);\n\n    // Insert customization groups\n    const sampleGroups = [\n      { title: \"Saus & Topping\" },\n      { title: \"Ost\" },\n      { title: \"Ekstra Produkter\" },\n      { title: \"Grønnsaker\" }\n    ];\n\n    const insertedGroups = await db.insert(customizationGroups).values(sampleGroups).returning();\n    console.log(`Inserted ${insertedGroups.length} customization groups`);\n\n    // Insert customization options\n    const sampleOptions = [\n      { name: \"BBQ Sauce\", extraPrice: 10, imageUrl: \"\", groupId: insertedGroups[0].id },\n      { name: \"Hot Sauce\", extraPrice: 10, imageUrl: \"\", groupId: insertedGroups[0].id },\n      { name: \"Mayo\", extraPrice: 10, imageUrl: \"\", groupId: insertedGroups[0].id },\n      { name: \"Honey Mustard\", extraPrice: 15, imageUrl: \"\", groupId: insertedGroups[0].id },\n\n      { name: \"Cheddar\", extraPrice: 15, imageUrl: \"\", groupId: insertedGroups[1].id },\n      { name: \"Blue Cheese\", extraPrice: 20, imageUrl: \"\", groupId: insertedGroups[1].id },\n      { name: \"Mozzarella\", extraPrice: 15, imageUrl: \"\", groupId: insertedGroups[1].id },\n\n      { name: \"Bacon\", extraPrice: 25, imageUrl: \"\", groupId: insertedGroups[2].id },\n      { name: \"Double Meat\", extraPrice: 40, imageUrl: \"\", groupId: insertedGroups[2].id },\n      { name: \"Fried Egg\", extraPrice: 15, imageUrl: \"\", groupId: insertedGroups[2].id },\n\n      { name: \"Lettuce\", extraPrice: 0, imageUrl: \"\", groupId: insertedGroups[3].id },\n      { name: \"Tomato\", extraPrice: 0, imageUrl: \"\", groupId: insertedGroups[3].id },\n      { name: \"Onion\", extraPrice: 0, imageUrl: \"\", groupId: insertedGroups[3].id },\n      { name: \"Cucumber\", extraPrice: 0, imageUrl: \"\", groupId: insertedGroups[3].id },\n      { name: \"Avocado\", extraPrice: 20, imageUrl: \"\", groupId: insertedGroups[3].id }\n    ];\n\n    const insertedOptions = await db.insert(customizationOptions).values(sampleOptions).returning();\n    console.log(`Inserted ${insertedOptions.length} customization options`);\n\n    // Create a default admin user\n    const hashedPassword = await hashPassword(\"admin123\");\n    const adminUser = {\n      username: \"admin\",\n      password: hashedPassword,\n      role: \"admin\" as const\n    };\n\n    const insertedUsers = await db.insert(users).values([adminUser]).returning();\n    console.log(`Created admin user: ${insertedUsers[0].username}`);\n\n    console.log(\"Database seeded successfully!\");\n  } catch (error) {\n    console.error(\"Error seeding database:\", error);\n    throw error;\n  }\n}\n\n// Run initialization if this file is executed directly\nif (import.meta.url === `file://${process.argv[1]}`) {\n  initializeDatabase()\n    .then(() => {\n      console.log(\"Database initialization completed!\");\n      process.exit(0);\n    })\n    .catch((error) => {\n      console.error(\"Database initialization failed:\", error);\n      process.exit(1);\n    });\n}\n\nexport { initializeDatabase, seedDatabase };\n"}