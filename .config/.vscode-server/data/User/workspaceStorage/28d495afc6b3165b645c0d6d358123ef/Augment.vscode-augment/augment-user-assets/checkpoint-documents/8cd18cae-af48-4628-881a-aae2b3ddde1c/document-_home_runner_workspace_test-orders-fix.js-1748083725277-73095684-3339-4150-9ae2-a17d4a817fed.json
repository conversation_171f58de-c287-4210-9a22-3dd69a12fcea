{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "test-orders-fix.js"}, "modifiedCode": "// Test script to verify the orders table fix\nimport { Pool, neonConfig } from '@neondatabase/serverless';\nimport ws from 'ws';\nimport dotenv from 'dotenv';\n\n// Configure WebSocket for Neon\nneonConfig.webSocketConstructor = ws;\n\ndotenv.config();\n\nconst connectionString = process.env.DATABASE_URL;\n\nif (!connectionString) {\n  console.error(\"DATABASE_URL must be set\");\n  process.exit(1);\n}\n\nasync function testOrdersTable() {\n  const client = new Pool({ connectionString });\n  \n  try {\n    console.log(\"Testing orders table...\");\n    \n    // Test selecting from orders table with order_details column\n    const result = await client.query(`\n      SELECT id, customer, items, order_details, subtotal, delivery_fee, total, status, payment_method, notes, created_at\n      FROM orders \n      LIMIT 5;\n    `);\n    \n    console.log(\"✅ Orders table query successful!\");\n    console.log(`Found ${result.rows.length} orders`);\n    \n    if (result.rows.length > 0) {\n      console.log(\"Sample order:\", {\n        id: result.rows[0].id,\n        customer: result.rows[0].customer,\n        order_details: result.rows[0].order_details,\n        status: result.rows[0].status\n      });\n    }\n    \n    // Test inserting a new order with order_details\n    console.log(\"\\nTesting order insertion...\");\n    const insertResult = await client.query(`\n      INSERT INTO orders (customer, items, order_details, subtotal, delivery_fee, total, status, payment_method, notes)\n      VALUES (\n        '{\"firstName\": \"Test\", \"lastName\": \"User\", \"email\": \"<EMAIL>\", \"phone\": \"12345678\", \"address\": \"Test St\", \"postalCode\": \"0001\", \"city\": \"Oslo\"}',\n        '[{\"id\": 1, \"name\": \"Test Item\", \"price\": 100, \"quantity\": 1}]',\n        '{\"type\": \"delivery\", \"time\": \"asap\", \"scheduledTime\": null}',\n        100,\n        49,\n        149,\n        'pending',\n        'card',\n        'Test order'\n      )\n      RETURNING id;\n    `);\n    \n    console.log(\"✅ Order insertion successful! New order ID:\", insertResult.rows[0].id);\n    \n    // Clean up test order\n    await client.query(`DELETE FROM orders WHERE id = $1`, [insertResult.rows[0].id]);\n    console.log(\"✅ Test order cleaned up\");\n    \n  } catch (error) {\n    console.error(\"❌ Error testing orders table:\", error);\n  } finally {\n    await client.end();\n  }\n}\n\ntestOrdersTable();\n"}