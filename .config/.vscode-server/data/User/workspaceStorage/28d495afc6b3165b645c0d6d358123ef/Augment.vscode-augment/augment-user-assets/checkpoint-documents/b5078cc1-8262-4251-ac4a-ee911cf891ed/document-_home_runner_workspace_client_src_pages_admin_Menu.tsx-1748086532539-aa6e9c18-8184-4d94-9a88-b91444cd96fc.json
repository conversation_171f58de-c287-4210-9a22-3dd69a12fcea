{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/pages/admin/Menu.tsx"}, "originalCode": "import { useState, useEffect } from \"react\";\nimport { motion } from \"framer-motion\";\nimport AdminLayout from \"./AdminLayout\";\nimport { getCategories, getMenuItems, createMenuItem, updateMenuItem, deleteMenuItem, createCategory, updateCategory, deleteCategory, Category, MenuItem } from \"@/api/adminApi\";\nimport { useToast } from \"@/hooks/use-toast\";\n\nconst Menu = () => {\n  const { toast } = useToast();\n\n  // State for menu data\n  const [categories, setCategories] = useState<Category[]>([]);\n  const [menuItems, setMenuItems] = useState<MenuItem[]>([]);\n  const [isLoading, setIsLoading] = useState(true);\n\n  // State for editing\n  const [selectedCategory, setSelectedCategory] = useState<Category | null>(null);\n  const [selectedMenuItem, setSelectedMenuItem] = useState<MenuItem | null>(null);\n  const [showCategoryForm, setShowCategoryForm] = useState(false);\n  const [showMenuItemForm, setShowMenuItemForm] = useState(false);\n\n  // Form data\n  const [categoryForm, setCategoryForm] = useState<Omit<Category, 'id'>>({\n    name: '',\n    image_url: ''\n  });\n\n  const [menuItemForm, setMenuItemForm] = useState<Omit<MenuItem, 'id' | 'category_name'>>({\n    name: '',\n    description: '',\n    price: 0,\n    imageUrl: '',\n    categoryId: 0,\n    available: true\n  });\n\n  // Load data on component mount\n  useEffect(() => {\n    const loadData = async () => {\n      try {\n        setIsLoading(true);\n\n        // Fetch categories and menu items in parallel\n        const [categoriesData, menuItemsData] = await Promise.all([\n          fetch('/api/categories').then(res => res.json()),\n          fetch('/api/items').then(res => res.json())\n        ]);\n\n        setCategories(categoriesData);\n        setMenuItems(menuItemsData);\n      } catch (error) {\n        console.error('Error loading menu data:', error);\n        toast({\n          title: \"Error\",\n          description: \"Failed to load menu data\",\n          variant: \"destructive\"\n        });\n      } finally {\n        setIsLoading(false);\n      }\n    };\n\n    loadData();\n  }, [toast]);\n\n  // Reset forms\n  const resetForms = () => {\n    setCategoryForm({ name: '', image_url: '' });\n    setMenuItemForm({\n      name: '',\n      description: '',\n      price: 0,\n      image_url: '',\n      category_id: categories.length > 0 ? categories[0].id : 0,\n      available: true\n    });\n    setSelectedCategory(null);\n    setSelectedMenuItem(null);\n  };\n\n  // Open category form\n  const handleAddCategory = () => {\n    resetForms();\n    setShowCategoryForm(true);\n    setShowMenuItemForm(false);\n  };\n\n  // Open menu item form\n  const handleAddMenuItem = () => {\n    resetForms();\n    setMenuItemForm({\n      ...menuItemForm,\n      category_id: categories.length > 0 ? categories[0].id : 0\n    });\n    setShowMenuItemForm(true);\n    setShowCategoryForm(false);\n  };\n\n  // Edit category\n  const handleEditCategory = (category: Category) => {\n    setSelectedCategory(category);\n    setCategoryForm({\n      name: category.name,\n      image_url: category.image_url || ''\n    });\n    setShowCategoryForm(true);\n    setShowMenuItemForm(false);\n  };\n\n  // Edit menu item\n  const handleEditMenuItem = (item: MenuItem) => {\n    setSelectedMenuItem(item);\n    setMenuItemForm({\n      name: item.name,\n      description: item.description || '',\n      price: item.price,\n      image_url: item.image_url || '',\n      category_id: item.category_id,\n      available: item.available\n    });\n    setShowMenuItemForm(true);\n    setShowCategoryForm(false);\n  };\n\n  // Save category\n  const handleSaveCategory = async (e: React.FormEvent) => {\n    e.preventDefault();\n\n    try {\n      if (!categoryForm.name) {\n        toast({\n          title: \"Validation Error\",\n          description: \"Category name is required\",\n          variant: \"destructive\"\n        });\n        return;\n      }\n\n      if (selectedCategory) {\n        // Update existing category\n        const updatedCategory = await updateCategory(selectedCategory.id, categoryForm);\n\n        // Update the categories list\n        setCategories(prev =>\n          prev.map(cat => cat.id === selectedCategory.id ? updatedCategory : cat)\n        );\n\n        toast({\n          title: \"Success\",\n          description: \"Category updated successfully\",\n          variant: \"default\"\n        });\n      } else {\n        // Create new category\n        console.log('Creating new category:', categoryForm);\n        const newCategory = await createCategory(categoryForm);\n        console.log('New category created:', newCategory);\n\n        // Add to categories list\n        setCategories(prev => [...prev, newCategory]);\n\n        toast({\n          title: \"Success\",\n          description: \"Category created successfully\",\n          variant: \"default\"\n        });\n      }\n\n      // Reset and close form\n      resetForms();\n      setShowCategoryForm(false);\n    } catch (error) {\n      console.error('Error saving category:', error);\n\n      // More detailed error message\n      let errorMessage = \"Failed to save category\";\n      if (error instanceof Error) {\n        errorMessage += `: ${error.message}`;\n      }\n\n      toast({\n        title: \"Error\",\n        description: errorMessage,\n        variant: \"destructive\"\n      });\n    }\n  };\n\n  // Save menu item\n  const handleSaveMenuItem = async (e: React.FormEvent) => {\n    e.preventDefault();\n\n    try {\n      if (!menuItemForm.name || menuItemForm.price <= 0) {\n        toast({\n          title: \"Validation Error\",\n          description: \"Name and a valid price are required\",\n          variant: \"destructive\"\n        });\n        return;\n      }\n\n      if (selectedMenuItem) {\n        // Update existing menu item\n        const updatedItem = await updateMenuItem(selectedMenuItem.id, menuItemForm);\n\n        // Update the menu items list\n        setMenuItems(prev =>\n          prev.map(item => item.id === selectedMenuItem.id ? updatedItem : item)\n        );\n\n        toast({\n          title: \"Success\",\n          description: \"Menu item updated successfully\",\n          variant: \"default\"\n        });\n      } else {\n        // Create new menu item\n        console.log('Creating new menu item:', menuItemForm);\n        const newItem = await createMenuItem(menuItemForm);\n        console.log('New menu item created:', newItem);\n\n        // Add to menu items list with category name\n        const category = categories.find(c => c.id === menuItemForm.category_id);\n        const itemWithCategory = {\n          ...newItem,\n          category_name: category ? category.name : 'Uncategorized'\n        };\n\n        setMenuItems(prev => [...prev, itemWithCategory]);\n\n        toast({\n          title: \"Success\",\n          description: \"Menu item created successfully\",\n          variant: \"default\"\n        });\n      }\n\n      // Reset and close form\n      resetForms();\n      setShowMenuItemForm(false);\n    } catch (error) {\n      console.error('Error saving menu item:', error);\n\n      // More detailed error message\n      let errorMessage = \"Failed to save menu item\";\n      if (error instanceof Error) {\n        errorMessage += `: ${error.message}`;\n      }\n\n      toast({\n        title: \"Error\",\n        description: errorMessage,\n        variant: \"destructive\"\n      });\n    }\n  };\n\n  // Delete category\n  const handleDeleteCategory = async (id: number) => {\n    if (!confirm(\"Are you sure you want to delete this category? This will remove the category from all menu items.\")) {\n      return;\n    }\n\n    try {\n      const result = await deleteCategory(id);\n\n      // Remove from categories list\n      setCategories(prev => prev.filter(cat => cat.id !== id));\n\n      // Update menu items\n      setMenuItems(prev =>\n        prev.map(item =>\n          item.category_id === id\n            ? { ...item, category_id: 0, category_name: 'Uncategorized' }\n            : item\n        )\n      );\n\n      toast({\n        title: \"Success\",\n        description: \"Category deleted successfully\",\n        variant: \"default\"\n      });\n    } catch (error) {\n      console.error('Error deleting category:', error);\n\n      // More detailed error message\n      let errorMessage = \"Failed to delete category\";\n      if (error instanceof Error) {\n        errorMessage += `: ${error.message}`;\n      }\n\n      toast({\n        title: \"Error\",\n        description: errorMessage,\n        variant: \"destructive\"\n      });\n    }\n  };\n\n  // Delete menu item\n  const handleDeleteMenuItem = async (id: number) => {\n    if (!confirm(\"Are you sure you want to delete this menu item?\")) {\n      return;\n    }\n\n    try {\n      const result = await deleteMenuItem(id);\n\n      // Remove from menu items list\n      setMenuItems(prev => prev.filter(item => item.id !== id));\n\n      toast({\n        title: \"Success\",\n        description: \"Menu item deleted successfully\",\n        variant: \"default\"\n      });\n    } catch (error) {\n      console.error('Error deleting menu item:', error);\n\n      // More detailed error message\n      let errorMessage = \"Failed to delete menu item\";\n      if (error instanceof Error) {\n        errorMessage += `: ${error.message}`;\n      }\n\n      toast({\n        title: \"Error\",\n        description: errorMessage,\n        variant: \"destructive\"\n      });\n    }\n  };\n\n  return (\n    <AdminLayout>\n      <div className=\"max-w-6xl mx-auto\">\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.5 }}\n        >\n          <div className=\"flex justify-between items-center mb-2\">\n            <h1 className=\"text-3xl font-bold bg-gradient-to-r from-purple-400 via-cyan-400 to-purple-400 text-transparent bg-clip-text\">\n              Menu Management\n            </h1>\n\n            {/* Action Buttons - Redesigned for better visibility */}\n            <div className=\"flex items-center space-x-4\">\n              <button\n                onClick={handleAddCategory}\n                className=\"px-5 py-2.5 bg-gradient-to-r from-purple-600 to-purple-800 hover:from-purple-500 hover:to-purple-700\n                        text-white rounded-lg transition-all duration-300 shadow-lg hover:shadow-purple-500/30 flex items-center\"\n              >\n                <svg className=\"w-5 h-5 mr-2\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\" xmlns=\"http://www.w3.org/2000/svg\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 6v6m0 0v6m0-6h6m-6 0H6\"></path>\n                </svg>\n                Add Category\n              </button>\n              <button\n                onClick={handleAddMenuItem}\n                className=\"px-5 py-2.5 bg-gradient-to-r from-cyan-600 to-cyan-800 hover:from-cyan-500 hover:to-cyan-700\n                        text-white rounded-lg transition-all duration-300 shadow-lg hover:shadow-cyan-500/30 flex items-center\"\n              >\n                <svg className=\"w-5 h-5 mr-2\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\" xmlns=\"http://www.w3.org/2000/svg\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 6v6m0 0v6m0-6h6m-6 0H6\"></path>\n                </svg>\n                Add Menu Item\n              </button>\n            </div>\n          </div>\n\n          <p className=\"text-gray-400 mb-6\">\n            Easily manage your restaurant's menu categories and items below.\n          </p>\n\n          {/* Category Form */}\n          {showCategoryForm && (\n            <motion.div\n              initial={{ opacity: 0, scale: 0.95 }}\n              animate={{ opacity: 1, scale: 1 }}\n              className=\"mb-8 bg-gray-900/50 backdrop-blur-sm rounded-xl p-6 border border-gray-800\"\n            >\n              <h2 className=\"text-xl font-bold mb-4 text-white\">\n                {selectedCategory ? 'Edit Category' : 'Add New Category'}\n              </h2>\n\n              <form onSubmit={handleSaveCategory}>\n                <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n                  <div>\n                    <label htmlFor=\"categoryName\" className=\"block text-gray-300 mb-2\">Category Name*</label>\n                    <input\n                      id=\"categoryName\"\n                      type=\"text\"\n                      value={categoryForm.name}\n                      onChange={(e) => setCategoryForm({...categoryForm, name: e.target.value})}\n                      className=\"bg-gray-800 border border-gray-700 text-white rounded px-3 py-2 w-full\n                              focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent\"\n                      required\n                    />\n                  </div>\n\n                  <div>\n                    <label htmlFor=\"categoryImage\" className=\"block text-gray-300 mb-2\">Image URL</label>\n                    <input\n                      id=\"categoryImage\"\n                      type=\"text\"\n                      value={categoryForm.image_url}\n                      onChange={(e) => setCategoryForm({...categoryForm, image_url: e.target.value})}\n                      className=\"bg-gray-800 border border-gray-700 text-white rounded px-3 py-2 w-full\n                              focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent\"\n                    />\n                  </div>\n                </div>\n\n                <div className=\"mt-6 flex space-x-4\">\n                  <button\n                    type=\"submit\"\n                    className=\"px-4 py-2 bg-gradient-to-r from-purple-600 to-purple-800 hover:from-purple-700 hover:to-purple-900\n                            text-white rounded-md transition-all duration-300 shadow-lg hover:shadow-purple-500/20\"\n                  >\n                    {selectedCategory ? 'Update Category' : 'Add Category'}\n                  </button>\n                  <button\n                    type=\"button\"\n                    onClick={() => {\n                      setShowCategoryForm(false);\n                      resetForms();\n                    }}\n                    className=\"px-4 py-2 bg-gray-800 hover:bg-gray-700\n                            text-white rounded-md transition-all duration-300\"\n                  >\n                    Cancel\n                  </button>\n                </div>\n              </form>\n            </motion.div>\n          )}\n\n          {/* Menu Item Form */}\n          {showMenuItemForm && (\n            <motion.div\n              initial={{ opacity: 0, scale: 0.95 }}\n              animate={{ opacity: 1, scale: 1 }}\n              className=\"mb-8 bg-gray-900/50 backdrop-blur-sm rounded-xl p-6 border border-gray-800\"\n            >\n              <h2 className=\"text-xl font-bold mb-4 text-white\">\n                {selectedMenuItem ? 'Edit Menu Item' : 'Add New Menu Item'}\n              </h2>\n\n              <form onSubmit={handleSaveMenuItem}>\n                <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n                  <div>\n                    <label htmlFor=\"itemName\" className=\"block text-gray-300 mb-2\">Item Name*</label>\n                    <input\n                      id=\"itemName\"\n                      type=\"text\"\n                      value={menuItemForm.name}\n                      onChange={(e) => setMenuItemForm({...menuItemForm, name: e.target.value})}\n                      className=\"bg-gray-800 border border-gray-700 text-white rounded px-3 py-2 w-full\n                              focus:outline-none focus:ring-2 focus:ring-cyan-500 focus:border-transparent\"\n                      required\n                    />\n                  </div>\n\n                  <div>\n                    <label htmlFor=\"itemCategory\" className=\"block text-gray-300 mb-2\">Category*</label>\n                    <select\n                      id=\"itemCategory\"\n                      value={menuItemForm.category_id}\n                      onChange={(e) => setMenuItemForm({...menuItemForm, category_id: Number(e.target.value)})}\n                      className=\"bg-gray-800 border border-gray-700 text-white rounded px-3 py-2 w-full\n                              focus:outline-none focus:ring-2 focus:ring-cyan-500 focus:border-transparent\"\n                      required\n                    >\n                      <option value=\"\">Select a category</option>\n                      {categories.map(category => (\n                        <option key={category.id} value={category.id}>{category.name}</option>\n                      ))}\n                    </select>\n                  </div>\n\n                  <div>\n                    <label htmlFor=\"itemPrice\" className=\"block text-gray-300 mb-2\">Price (NOK)*</label>\n                    <input\n                      id=\"itemPrice\"\n                      type=\"number\"\n                      value={menuItemForm.price}\n                      onChange={(e) => setMenuItemForm({...menuItemForm, price: Number(e.target.value)})}\n                      className=\"bg-gray-800 border border-gray-700 text-white rounded px-3 py-2 w-full\n                              focus:outline-none focus:ring-2 focus:ring-cyan-500 focus:border-transparent\"\n                      required\n                      min=\"0\"\n                    />\n                  </div>\n\n                  <div>\n                    <label htmlFor=\"itemImage\" className=\"block text-gray-300 mb-2\">Image URL</label>\n                    <input\n                      id=\"itemImage\"\n                      type=\"text\"\n                      value={menuItemForm.image_url}\n                      onChange={(e) => setMenuItemForm({...menuItemForm, image_url: e.target.value})}\n                      className=\"bg-gray-800 border border-gray-700 text-white rounded px-3 py-2 w-full\n                              focus:outline-none focus:ring-2 focus:ring-cyan-500 focus:border-transparent\"\n                    />\n                  </div>\n\n                  <div className=\"md:col-span-2\">\n                    <label htmlFor=\"itemDescription\" className=\"block text-gray-300 mb-2\">Description</label>\n                    <textarea\n                      id=\"itemDescription\"\n                      value={menuItemForm.description}\n                      onChange={(e) => setMenuItemForm({...menuItemForm, description: e.target.value})}\n                      className=\"bg-gray-800 border border-gray-700 text-white rounded px-3 py-2 w-full h-24\n                              focus:outline-none focus:ring-2 focus:ring-cyan-500 focus:border-transparent\"\n                    />\n                  </div>\n\n                  <div>\n                    <label className=\"flex items-center space-x-2 text-gray-300\">\n                      <input\n                        type=\"checkbox\"\n                        checked={menuItemForm.available}\n                        onChange={(e) => setMenuItemForm({...menuItemForm, available: e.target.checked})}\n                        className=\"w-4 h-4 rounded border-gray-700 text-cyan-600 focus:ring-cyan-500\"\n                      />\n                      <span>Available</span>\n                    </label>\n                  </div>\n                </div>\n\n                <div className=\"mt-6 flex space-x-4\">\n                  <button\n                    type=\"submit\"\n                    className=\"px-4 py-2 bg-gradient-to-r from-cyan-600 to-cyan-800 hover:from-cyan-700 hover:to-cyan-900\n                            text-white rounded-md transition-all duration-300 shadow-lg hover:shadow-cyan-500/20\"\n                  >\n                    {selectedMenuItem ? 'Update Menu Item' : 'Add Menu Item'}\n                  </button>\n                  <button\n                    type=\"button\"\n                    onClick={() => {\n                      setShowMenuItemForm(false);\n                      resetForms();\n                    }}\n                    className=\"px-4 py-2 bg-gray-800 hover:bg-gray-700\n                            text-white rounded-md transition-all duration-300\"\n                  >\n                    Cancel\n                  </button>\n                </div>\n              </form>\n            </motion.div>\n          )}\n\n          {/* Categories List */}\n          <div className=\"mb-10 bg-gray-900/50 backdrop-blur-sm rounded-xl p-6 border border-gray-800\">\n            <h2 className=\"text-xl font-bold mb-4 text-white\">Categories</h2>\n\n            {isLoading ? (\n              <div className=\"p-4 text-center text-gray-400\">Loading categories...</div>\n            ) : categories.length === 0 ? (\n              <div className=\"p-4 text-center text-gray-400\">No categories found. Add your first category!</div>\n            ) : (\n              <div className=\"overflow-x-auto\">\n                <table className=\"w-full\">\n                  <thead>\n                    <tr className=\"border-b border-gray-800\">\n                      <th className=\"py-3 text-left text-gray-400\">ID</th>\n                      <th className=\"py-3 text-left text-gray-400\">Name</th>\n                      <th className=\"py-3 text-left text-gray-400\">Image</th>\n                      <th className=\"py-3 text-left text-gray-400\">Actions</th>\n                    </tr>\n                  </thead>\n                  <tbody>\n                    {categories.map(category => (\n                      <tr key={category.id} className=\"border-b border-gray-800/50\">\n                        <td className=\"py-4 text-gray-300\">{category.id}</td>\n                        <td className=\"py-4 font-medium text-white\">{category.name}</td>\n                        <td className=\"py-4\">\n                          {category.image_url && (\n                            <div className=\"w-16 h-16 rounded overflow-hidden\">\n                              <img\n                                src={category.image_url}\n                                alt={category.name}\n                                className=\"w-full h-full object-cover\"\n                                onError={(e) => (e.target as HTMLImageElement).src = 'https://placehold.co/64x64/gray/white?text=NA'}\n                              />\n                            </div>\n                          )}\n                        </td>\n                        <td className=\"py-4\">\n                          <div className=\"flex space-x-2\">\n                            <button\n                              onClick={() => handleEditCategory(category)}\n                              className=\"px-3 py-1 bg-amber-700 hover:bg-amber-600 text-white rounded-md text-sm transition-colors\"\n                            >\n                              Edit\n                            </button>\n                            <button\n                              onClick={() => handleDeleteCategory(category.id)}\n                              className=\"px-3 py-1 bg-red-800 hover:bg-red-700 text-white rounded-md text-sm transition-colors\"\n                            >\n                              Delete\n                            </button>\n                          </div>\n                        </td>\n                      </tr>\n                    ))}\n                  </tbody>\n                </table>\n              </div>\n            )}\n          </div>\n\n          {/* Menu Items List */}\n          <div className=\"mb-10 bg-gray-900/50 backdrop-blur-sm rounded-xl p-6 border border-gray-800\">\n            <h2 className=\"text-xl font-bold mb-4 text-white\">Menu Items</h2>\n\n            {isLoading ? (\n              <div className=\"p-4 text-center text-gray-400\">Loading menu items...</div>\n            ) : menuItems.length === 0 ? (\n              <div className=\"p-4 text-center text-gray-400\">No menu items found. Add your first menu item!</div>\n            ) : (\n              <div className=\"overflow-x-auto\">\n                <table className=\"w-full\">\n                  <thead>\n                    <tr className=\"border-b border-gray-800\">\n                      <th className=\"py-3 text-left text-gray-400\">ID</th>\n                      <th className=\"py-3 text-left text-gray-400\">Name</th>\n                      <th className=\"py-3 text-left text-gray-400\">Category</th>\n                      <th className=\"py-3 text-left text-gray-400\">Price</th>\n                      <th className=\"py-3 text-left text-gray-400\">Available</th>\n                      <th className=\"py-3 text-left text-gray-400\">Image</th>\n                      <th className=\"py-3 text-left text-gray-400\">Actions</th>\n                    </tr>\n                  </thead>\n                  <tbody>\n                    {menuItems.map(item => (\n                      <tr key={item.id} className=\"border-b border-gray-800/50\">\n                        <td className=\"py-4 text-gray-300\">{item.id}</td>\n                        <td className=\"py-4 font-medium text-white\">{item.name}</td>\n                        <td className=\"py-4 text-gray-300\">{item.category_name || 'Uncategorized'}</td>\n                        <td className=\"py-4 text-gray-300\">{item.price} NOK</td>\n                        <td className=\"py-4\">\n                          <span className={`px-2 py-1 rounded-full text-xs ${item.available ? 'bg-green-900/50 text-green-300' : 'bg-red-900/50 text-red-300'}`}>\n                            {item.available ? 'Yes' : 'No'}\n                          </span>\n                        </td>\n                        <td className=\"py-4\">\n                          {item.image_url && (\n                            <div className=\"w-16 h-16 rounded overflow-hidden\">\n                              <img\n                                src={item.image_url}\n                                alt={item.name}\n                                className=\"w-full h-full object-cover\"\n                                onError={(e) => (e.target as HTMLImageElement).src = 'https://placehold.co/64x64/gray/white?text=NA'}\n                              />\n                            </div>\n                          )}\n                        </td>\n                        <td className=\"py-4\">\n                          <div className=\"flex space-x-2\">\n                            <button\n                              onClick={() => handleEditMenuItem(item)}\n                              className=\"px-3 py-1 bg-amber-700 hover:bg-amber-600 text-white rounded-md text-sm transition-colors\"\n                            >\n                              Edit\n                            </button>\n                            <button\n                              onClick={() => handleDeleteMenuItem(item.id)}\n                              className=\"px-3 py-1 bg-red-800 hover:bg-red-700 text-white rounded-md text-sm transition-colors\"\n                            >\n                              Delete\n                            </button>\n                          </div>\n                        </td>\n                      </tr>\n                    ))}\n                  </tbody>\n                </table>\n              </div>\n            )}\n          </div>\n        </motion.div>\n      </div>\n    </AdminLayout>\n  );\n};\n\nexport default Menu;", "modifiedCode": "import { useState, useEffect } from \"react\";\nimport { motion } from \"framer-motion\";\nimport AdminLayout from \"./AdminLayout\";\nimport { getCategories, getMenuItems, createMenuItem, updateMenuItem, deleteMenuItem, createCategory, updateCategory, deleteCategory, Category, MenuItem } from \"@/api/adminApi\";\nimport { useToast } from \"@/hooks/use-toast\";\n\nconst Menu = () => {\n  const { toast } = useToast();\n\n  // State for menu data\n  const [categories, setCategories] = useState<Category[]>([]);\n  const [menuItems, setMenuItems] = useState<MenuItem[]>([]);\n  const [isLoading, setIsLoading] = useState(true);\n\n  // State for editing\n  const [selectedCategory, setSelectedCategory] = useState<Category | null>(null);\n  const [selectedMenuItem, setSelectedMenuItem] = useState<MenuItem | null>(null);\n  const [showCategoryForm, setShowCategoryForm] = useState(false);\n  const [showMenuItemForm, setShowMenuItemForm] = useState(false);\n\n  // Form data\n  const [categoryForm, setCategoryForm] = useState<Omit<Category, 'id'>>({\n    name: '',\n    image_url: ''\n  });\n\n  const [menuItemForm, setMenuItemForm] = useState<Omit<MenuItem, 'id' | 'category_name'>>({\n    name: '',\n    description: '',\n    price: 0,\n    imageUrl: '',\n    categoryId: 0,\n    available: true\n  });\n\n  // Load data on component mount\n  useEffect(() => {\n    const loadData = async () => {\n      try {\n        setIsLoading(true);\n\n        // Fetch categories and menu items in parallel\n        const [categoriesData, menuItemsData] = await Promise.all([\n          fetch('/api/categories').then(res => res.json()),\n          fetch('/api/items').then(res => res.json())\n        ]);\n\n        setCategories(categoriesData);\n        setMenuItems(menuItemsData);\n      } catch (error) {\n        console.error('Error loading menu data:', error);\n        toast({\n          title: \"Error\",\n          description: \"Failed to load menu data\",\n          variant: \"destructive\"\n        });\n      } finally {\n        setIsLoading(false);\n      }\n    };\n\n    loadData();\n  }, [toast]);\n\n  // Reset forms\n  const resetForms = () => {\n    setCategoryForm({ name: '', image_url: '' });\n    setMenuItemForm({\n      name: '',\n      description: '',\n      price: 0,\n      image_url: '',\n      category_id: categories.length > 0 ? categories[0].id : 0,\n      available: true\n    });\n    setSelectedCategory(null);\n    setSelectedMenuItem(null);\n  };\n\n  // Open category form\n  const handleAddCategory = () => {\n    resetForms();\n    setShowCategoryForm(true);\n    setShowMenuItemForm(false);\n  };\n\n  // Open menu item form\n  const handleAddMenuItem = () => {\n    resetForms();\n    setMenuItemForm({\n      ...menuItemForm,\n      category_id: categories.length > 0 ? categories[0].id : 0\n    });\n    setShowMenuItemForm(true);\n    setShowCategoryForm(false);\n  };\n\n  // Edit category\n  const handleEditCategory = (category: Category) => {\n    setSelectedCategory(category);\n    setCategoryForm({\n      name: category.name,\n      image_url: category.image_url || ''\n    });\n    setShowCategoryForm(true);\n    setShowMenuItemForm(false);\n  };\n\n  // Edit menu item\n  const handleEditMenuItem = (item: MenuItem) => {\n    setSelectedMenuItem(item);\n    setMenuItemForm({\n      name: item.name,\n      description: item.description || '',\n      price: item.price,\n      imageUrl: item.imageUrl || '',\n      categoryId: item.categoryId,\n      available: item.available\n    });\n    setShowMenuItemForm(true);\n    setShowCategoryForm(false);\n  };\n\n  // Save category\n  const handleSaveCategory = async (e: React.FormEvent) => {\n    e.preventDefault();\n\n    try {\n      if (!categoryForm.name) {\n        toast({\n          title: \"Validation Error\",\n          description: \"Category name is required\",\n          variant: \"destructive\"\n        });\n        return;\n      }\n\n      if (selectedCategory) {\n        // Update existing category\n        const updatedCategory = await updateCategory(selectedCategory.id, categoryForm);\n\n        // Update the categories list\n        setCategories(prev =>\n          prev.map(cat => cat.id === selectedCategory.id ? updatedCategory : cat)\n        );\n\n        toast({\n          title: \"Success\",\n          description: \"Category updated successfully\",\n          variant: \"default\"\n        });\n      } else {\n        // Create new category\n        console.log('Creating new category:', categoryForm);\n        const newCategory = await createCategory(categoryForm);\n        console.log('New category created:', newCategory);\n\n        // Add to categories list\n        setCategories(prev => [...prev, newCategory]);\n\n        toast({\n          title: \"Success\",\n          description: \"Category created successfully\",\n          variant: \"default\"\n        });\n      }\n\n      // Reset and close form\n      resetForms();\n      setShowCategoryForm(false);\n    } catch (error) {\n      console.error('Error saving category:', error);\n\n      // More detailed error message\n      let errorMessage = \"Failed to save category\";\n      if (error instanceof Error) {\n        errorMessage += `: ${error.message}`;\n      }\n\n      toast({\n        title: \"Error\",\n        description: errorMessage,\n        variant: \"destructive\"\n      });\n    }\n  };\n\n  // Save menu item\n  const handleSaveMenuItem = async (e: React.FormEvent) => {\n    e.preventDefault();\n\n    try {\n      if (!menuItemForm.name || menuItemForm.price <= 0) {\n        toast({\n          title: \"Validation Error\",\n          description: \"Name and a valid price are required\",\n          variant: \"destructive\"\n        });\n        return;\n      }\n\n      if (selectedMenuItem) {\n        // Update existing menu item\n        const updatedItem = await updateMenuItem(selectedMenuItem.id, menuItemForm);\n\n        // Update the menu items list\n        setMenuItems(prev =>\n          prev.map(item => item.id === selectedMenuItem.id ? updatedItem : item)\n        );\n\n        toast({\n          title: \"Success\",\n          description: \"Menu item updated successfully\",\n          variant: \"default\"\n        });\n      } else {\n        // Create new menu item\n        console.log('Creating new menu item:', menuItemForm);\n        const newItem = await createMenuItem(menuItemForm);\n        console.log('New menu item created:', newItem);\n\n        // Add to menu items list with category name\n        const category = categories.find(c => c.id === menuItemForm.category_id);\n        const itemWithCategory = {\n          ...newItem,\n          category_name: category ? category.name : 'Uncategorized'\n        };\n\n        setMenuItems(prev => [...prev, itemWithCategory]);\n\n        toast({\n          title: \"Success\",\n          description: \"Menu item created successfully\",\n          variant: \"default\"\n        });\n      }\n\n      // Reset and close form\n      resetForms();\n      setShowMenuItemForm(false);\n    } catch (error) {\n      console.error('Error saving menu item:', error);\n\n      // More detailed error message\n      let errorMessage = \"Failed to save menu item\";\n      if (error instanceof Error) {\n        errorMessage += `: ${error.message}`;\n      }\n\n      toast({\n        title: \"Error\",\n        description: errorMessage,\n        variant: \"destructive\"\n      });\n    }\n  };\n\n  // Delete category\n  const handleDeleteCategory = async (id: number) => {\n    if (!confirm(\"Are you sure you want to delete this category? This will remove the category from all menu items.\")) {\n      return;\n    }\n\n    try {\n      const result = await deleteCategory(id);\n\n      // Remove from categories list\n      setCategories(prev => prev.filter(cat => cat.id !== id));\n\n      // Update menu items\n      setMenuItems(prev =>\n        prev.map(item =>\n          item.category_id === id\n            ? { ...item, category_id: 0, category_name: 'Uncategorized' }\n            : item\n        )\n      );\n\n      toast({\n        title: \"Success\",\n        description: \"Category deleted successfully\",\n        variant: \"default\"\n      });\n    } catch (error) {\n      console.error('Error deleting category:', error);\n\n      // More detailed error message\n      let errorMessage = \"Failed to delete category\";\n      if (error instanceof Error) {\n        errorMessage += `: ${error.message}`;\n      }\n\n      toast({\n        title: \"Error\",\n        description: errorMessage,\n        variant: \"destructive\"\n      });\n    }\n  };\n\n  // Delete menu item\n  const handleDeleteMenuItem = async (id: number) => {\n    if (!confirm(\"Are you sure you want to delete this menu item?\")) {\n      return;\n    }\n\n    try {\n      const result = await deleteMenuItem(id);\n\n      // Remove from menu items list\n      setMenuItems(prev => prev.filter(item => item.id !== id));\n\n      toast({\n        title: \"Success\",\n        description: \"Menu item deleted successfully\",\n        variant: \"default\"\n      });\n    } catch (error) {\n      console.error('Error deleting menu item:', error);\n\n      // More detailed error message\n      let errorMessage = \"Failed to delete menu item\";\n      if (error instanceof Error) {\n        errorMessage += `: ${error.message}`;\n      }\n\n      toast({\n        title: \"Error\",\n        description: errorMessage,\n        variant: \"destructive\"\n      });\n    }\n  };\n\n  return (\n    <AdminLayout>\n      <div className=\"max-w-6xl mx-auto\">\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.5 }}\n        >\n          <div className=\"flex justify-between items-center mb-2\">\n            <h1 className=\"text-3xl font-bold bg-gradient-to-r from-purple-400 via-cyan-400 to-purple-400 text-transparent bg-clip-text\">\n              Menu Management\n            </h1>\n\n            {/* Action Buttons - Redesigned for better visibility */}\n            <div className=\"flex items-center space-x-4\">\n              <button\n                onClick={handleAddCategory}\n                className=\"px-5 py-2.5 bg-gradient-to-r from-purple-600 to-purple-800 hover:from-purple-500 hover:to-purple-700\n                        text-white rounded-lg transition-all duration-300 shadow-lg hover:shadow-purple-500/30 flex items-center\"\n              >\n                <svg className=\"w-5 h-5 mr-2\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\" xmlns=\"http://www.w3.org/2000/svg\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 6v6m0 0v6m0-6h6m-6 0H6\"></path>\n                </svg>\n                Add Category\n              </button>\n              <button\n                onClick={handleAddMenuItem}\n                className=\"px-5 py-2.5 bg-gradient-to-r from-cyan-600 to-cyan-800 hover:from-cyan-500 hover:to-cyan-700\n                        text-white rounded-lg transition-all duration-300 shadow-lg hover:shadow-cyan-500/30 flex items-center\"\n              >\n                <svg className=\"w-5 h-5 mr-2\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\" xmlns=\"http://www.w3.org/2000/svg\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 6v6m0 0v6m0-6h6m-6 0H6\"></path>\n                </svg>\n                Add Menu Item\n              </button>\n            </div>\n          </div>\n\n          <p className=\"text-gray-400 mb-6\">\n            Easily manage your restaurant's menu categories and items below.\n          </p>\n\n          {/* Category Form */}\n          {showCategoryForm && (\n            <motion.div\n              initial={{ opacity: 0, scale: 0.95 }}\n              animate={{ opacity: 1, scale: 1 }}\n              className=\"mb-8 bg-gray-900/50 backdrop-blur-sm rounded-xl p-6 border border-gray-800\"\n            >\n              <h2 className=\"text-xl font-bold mb-4 text-white\">\n                {selectedCategory ? 'Edit Category' : 'Add New Category'}\n              </h2>\n\n              <form onSubmit={handleSaveCategory}>\n                <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n                  <div>\n                    <label htmlFor=\"categoryName\" className=\"block text-gray-300 mb-2\">Category Name*</label>\n                    <input\n                      id=\"categoryName\"\n                      type=\"text\"\n                      value={categoryForm.name}\n                      onChange={(e) => setCategoryForm({...categoryForm, name: e.target.value})}\n                      className=\"bg-gray-800 border border-gray-700 text-white rounded px-3 py-2 w-full\n                              focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent\"\n                      required\n                    />\n                  </div>\n\n                  <div>\n                    <label htmlFor=\"categoryImage\" className=\"block text-gray-300 mb-2\">Image URL</label>\n                    <input\n                      id=\"categoryImage\"\n                      type=\"text\"\n                      value={categoryForm.image_url}\n                      onChange={(e) => setCategoryForm({...categoryForm, image_url: e.target.value})}\n                      className=\"bg-gray-800 border border-gray-700 text-white rounded px-3 py-2 w-full\n                              focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent\"\n                    />\n                  </div>\n                </div>\n\n                <div className=\"mt-6 flex space-x-4\">\n                  <button\n                    type=\"submit\"\n                    className=\"px-4 py-2 bg-gradient-to-r from-purple-600 to-purple-800 hover:from-purple-700 hover:to-purple-900\n                            text-white rounded-md transition-all duration-300 shadow-lg hover:shadow-purple-500/20\"\n                  >\n                    {selectedCategory ? 'Update Category' : 'Add Category'}\n                  </button>\n                  <button\n                    type=\"button\"\n                    onClick={() => {\n                      setShowCategoryForm(false);\n                      resetForms();\n                    }}\n                    className=\"px-4 py-2 bg-gray-800 hover:bg-gray-700\n                            text-white rounded-md transition-all duration-300\"\n                  >\n                    Cancel\n                  </button>\n                </div>\n              </form>\n            </motion.div>\n          )}\n\n          {/* Menu Item Form */}\n          {showMenuItemForm && (\n            <motion.div\n              initial={{ opacity: 0, scale: 0.95 }}\n              animate={{ opacity: 1, scale: 1 }}\n              className=\"mb-8 bg-gray-900/50 backdrop-blur-sm rounded-xl p-6 border border-gray-800\"\n            >\n              <h2 className=\"text-xl font-bold mb-4 text-white\">\n                {selectedMenuItem ? 'Edit Menu Item' : 'Add New Menu Item'}\n              </h2>\n\n              <form onSubmit={handleSaveMenuItem}>\n                <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n                  <div>\n                    <label htmlFor=\"itemName\" className=\"block text-gray-300 mb-2\">Item Name*</label>\n                    <input\n                      id=\"itemName\"\n                      type=\"text\"\n                      value={menuItemForm.name}\n                      onChange={(e) => setMenuItemForm({...menuItemForm, name: e.target.value})}\n                      className=\"bg-gray-800 border border-gray-700 text-white rounded px-3 py-2 w-full\n                              focus:outline-none focus:ring-2 focus:ring-cyan-500 focus:border-transparent\"\n                      required\n                    />\n                  </div>\n\n                  <div>\n                    <label htmlFor=\"itemCategory\" className=\"block text-gray-300 mb-2\">Category*</label>\n                    <select\n                      id=\"itemCategory\"\n                      value={menuItemForm.category_id}\n                      onChange={(e) => setMenuItemForm({...menuItemForm, category_id: Number(e.target.value)})}\n                      className=\"bg-gray-800 border border-gray-700 text-white rounded px-3 py-2 w-full\n                              focus:outline-none focus:ring-2 focus:ring-cyan-500 focus:border-transparent\"\n                      required\n                    >\n                      <option value=\"\">Select a category</option>\n                      {categories.map(category => (\n                        <option key={category.id} value={category.id}>{category.name}</option>\n                      ))}\n                    </select>\n                  </div>\n\n                  <div>\n                    <label htmlFor=\"itemPrice\" className=\"block text-gray-300 mb-2\">Price (NOK)*</label>\n                    <input\n                      id=\"itemPrice\"\n                      type=\"number\"\n                      value={menuItemForm.price}\n                      onChange={(e) => setMenuItemForm({...menuItemForm, price: Number(e.target.value)})}\n                      className=\"bg-gray-800 border border-gray-700 text-white rounded px-3 py-2 w-full\n                              focus:outline-none focus:ring-2 focus:ring-cyan-500 focus:border-transparent\"\n                      required\n                      min=\"0\"\n                    />\n                  </div>\n\n                  <div>\n                    <label htmlFor=\"itemImage\" className=\"block text-gray-300 mb-2\">Image URL</label>\n                    <input\n                      id=\"itemImage\"\n                      type=\"text\"\n                      value={menuItemForm.image_url}\n                      onChange={(e) => setMenuItemForm({...menuItemForm, image_url: e.target.value})}\n                      className=\"bg-gray-800 border border-gray-700 text-white rounded px-3 py-2 w-full\n                              focus:outline-none focus:ring-2 focus:ring-cyan-500 focus:border-transparent\"\n                    />\n                  </div>\n\n                  <div className=\"md:col-span-2\">\n                    <label htmlFor=\"itemDescription\" className=\"block text-gray-300 mb-2\">Description</label>\n                    <textarea\n                      id=\"itemDescription\"\n                      value={menuItemForm.description}\n                      onChange={(e) => setMenuItemForm({...menuItemForm, description: e.target.value})}\n                      className=\"bg-gray-800 border border-gray-700 text-white rounded px-3 py-2 w-full h-24\n                              focus:outline-none focus:ring-2 focus:ring-cyan-500 focus:border-transparent\"\n                    />\n                  </div>\n\n                  <div>\n                    <label className=\"flex items-center space-x-2 text-gray-300\">\n                      <input\n                        type=\"checkbox\"\n                        checked={menuItemForm.available}\n                        onChange={(e) => setMenuItemForm({...menuItemForm, available: e.target.checked})}\n                        className=\"w-4 h-4 rounded border-gray-700 text-cyan-600 focus:ring-cyan-500\"\n                      />\n                      <span>Available</span>\n                    </label>\n                  </div>\n                </div>\n\n                <div className=\"mt-6 flex space-x-4\">\n                  <button\n                    type=\"submit\"\n                    className=\"px-4 py-2 bg-gradient-to-r from-cyan-600 to-cyan-800 hover:from-cyan-700 hover:to-cyan-900\n                            text-white rounded-md transition-all duration-300 shadow-lg hover:shadow-cyan-500/20\"\n                  >\n                    {selectedMenuItem ? 'Update Menu Item' : 'Add Menu Item'}\n                  </button>\n                  <button\n                    type=\"button\"\n                    onClick={() => {\n                      setShowMenuItemForm(false);\n                      resetForms();\n                    }}\n                    className=\"px-4 py-2 bg-gray-800 hover:bg-gray-700\n                            text-white rounded-md transition-all duration-300\"\n                  >\n                    Cancel\n                  </button>\n                </div>\n              </form>\n            </motion.div>\n          )}\n\n          {/* Categories List */}\n          <div className=\"mb-10 bg-gray-900/50 backdrop-blur-sm rounded-xl p-6 border border-gray-800\">\n            <h2 className=\"text-xl font-bold mb-4 text-white\">Categories</h2>\n\n            {isLoading ? (\n              <div className=\"p-4 text-center text-gray-400\">Loading categories...</div>\n            ) : categories.length === 0 ? (\n              <div className=\"p-4 text-center text-gray-400\">No categories found. Add your first category!</div>\n            ) : (\n              <div className=\"overflow-x-auto\">\n                <table className=\"w-full\">\n                  <thead>\n                    <tr className=\"border-b border-gray-800\">\n                      <th className=\"py-3 text-left text-gray-400\">ID</th>\n                      <th className=\"py-3 text-left text-gray-400\">Name</th>\n                      <th className=\"py-3 text-left text-gray-400\">Image</th>\n                      <th className=\"py-3 text-left text-gray-400\">Actions</th>\n                    </tr>\n                  </thead>\n                  <tbody>\n                    {categories.map(category => (\n                      <tr key={category.id} className=\"border-b border-gray-800/50\">\n                        <td className=\"py-4 text-gray-300\">{category.id}</td>\n                        <td className=\"py-4 font-medium text-white\">{category.name}</td>\n                        <td className=\"py-4\">\n                          {category.image_url && (\n                            <div className=\"w-16 h-16 rounded overflow-hidden\">\n                              <img\n                                src={category.image_url}\n                                alt={category.name}\n                                className=\"w-full h-full object-cover\"\n                                onError={(e) => (e.target as HTMLImageElement).src = 'https://placehold.co/64x64/gray/white?text=NA'}\n                              />\n                            </div>\n                          )}\n                        </td>\n                        <td className=\"py-4\">\n                          <div className=\"flex space-x-2\">\n                            <button\n                              onClick={() => handleEditCategory(category)}\n                              className=\"px-3 py-1 bg-amber-700 hover:bg-amber-600 text-white rounded-md text-sm transition-colors\"\n                            >\n                              Edit\n                            </button>\n                            <button\n                              onClick={() => handleDeleteCategory(category.id)}\n                              className=\"px-3 py-1 bg-red-800 hover:bg-red-700 text-white rounded-md text-sm transition-colors\"\n                            >\n                              Delete\n                            </button>\n                          </div>\n                        </td>\n                      </tr>\n                    ))}\n                  </tbody>\n                </table>\n              </div>\n            )}\n          </div>\n\n          {/* Menu Items List */}\n          <div className=\"mb-10 bg-gray-900/50 backdrop-blur-sm rounded-xl p-6 border border-gray-800\">\n            <h2 className=\"text-xl font-bold mb-4 text-white\">Menu Items</h2>\n\n            {isLoading ? (\n              <div className=\"p-4 text-center text-gray-400\">Loading menu items...</div>\n            ) : menuItems.length === 0 ? (\n              <div className=\"p-4 text-center text-gray-400\">No menu items found. Add your first menu item!</div>\n            ) : (\n              <div className=\"overflow-x-auto\">\n                <table className=\"w-full\">\n                  <thead>\n                    <tr className=\"border-b border-gray-800\">\n                      <th className=\"py-3 text-left text-gray-400\">ID</th>\n                      <th className=\"py-3 text-left text-gray-400\">Name</th>\n                      <th className=\"py-3 text-left text-gray-400\">Category</th>\n                      <th className=\"py-3 text-left text-gray-400\">Price</th>\n                      <th className=\"py-3 text-left text-gray-400\">Available</th>\n                      <th className=\"py-3 text-left text-gray-400\">Image</th>\n                      <th className=\"py-3 text-left text-gray-400\">Actions</th>\n                    </tr>\n                  </thead>\n                  <tbody>\n                    {menuItems.map(item => (\n                      <tr key={item.id} className=\"border-b border-gray-800/50\">\n                        <td className=\"py-4 text-gray-300\">{item.id}</td>\n                        <td className=\"py-4 font-medium text-white\">{item.name}</td>\n                        <td className=\"py-4 text-gray-300\">{item.category_name || 'Uncategorized'}</td>\n                        <td className=\"py-4 text-gray-300\">{item.price} NOK</td>\n                        <td className=\"py-4\">\n                          <span className={`px-2 py-1 rounded-full text-xs ${item.available ? 'bg-green-900/50 text-green-300' : 'bg-red-900/50 text-red-300'}`}>\n                            {item.available ? 'Yes' : 'No'}\n                          </span>\n                        </td>\n                        <td className=\"py-4\">\n                          {item.image_url && (\n                            <div className=\"w-16 h-16 rounded overflow-hidden\">\n                              <img\n                                src={item.image_url}\n                                alt={item.name}\n                                className=\"w-full h-full object-cover\"\n                                onError={(e) => (e.target as HTMLImageElement).src = 'https://placehold.co/64x64/gray/white?text=NA'}\n                              />\n                            </div>\n                          )}\n                        </td>\n                        <td className=\"py-4\">\n                          <div className=\"flex space-x-2\">\n                            <button\n                              onClick={() => handleEditMenuItem(item)}\n                              className=\"px-3 py-1 bg-amber-700 hover:bg-amber-600 text-white rounded-md text-sm transition-colors\"\n                            >\n                              Edit\n                            </button>\n                            <button\n                              onClick={() => handleDeleteMenuItem(item.id)}\n                              className=\"px-3 py-1 bg-red-800 hover:bg-red-700 text-white rounded-md text-sm transition-colors\"\n                            >\n                              Delete\n                            </button>\n                          </div>\n                        </td>\n                      </tr>\n                    ))}\n                  </tbody>\n                </table>\n              </div>\n            )}\n          </div>\n        </motion.div>\n      </div>\n    </AdminLayout>\n  );\n};\n\nexport default Menu;"}