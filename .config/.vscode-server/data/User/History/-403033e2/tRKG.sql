-- Restaurant Settings Table
CREATE TABLE IF NOT EXISTS restaurant_settings (
  id SERIAL PRIMARY KEY,
  restaurant_open BOOLEAN DEFAULT true,
  business_hours JSONB DEFAULT '{}',
  delivery_fee INTEGER DEFAULT 49,
  estimated_time VARCHAR(255) DEFAULT '25-35 min'
);

-- Categories Table
CREATE TABLE IF NOT EXISTS categories (
  id SERIAL PRIMARY KEY,
  name VARCHAR(255) NOT NULL,
  image_url TEXT
);

-- Menu Items Table
CREATE TABLE IF NOT EXISTS menu_items (
  id SERIAL PRIMARY KEY,
  name VARCHAR(255) NOT NULL,
  description TEXT,
  price INTEGER NOT NULL,
  image_url TEXT,
  category_id INTEGER REFERENCES categories(id) ON DELETE SET NULL,
  available BOOLEAN DEFAULT true
);

-- Orders Table
CREATE TABLE IF NOT EXISTS orders (
  id SERIAL PRIMARY KEY,
  customer JSONB NOT NULL,
  items JSONB NOT NULL,
  subtotal INTEGER NOT NULL,
  delivery_fee INTEGER NOT NULL,
  total INTEGER NOT NULL,
  status VARCHAR(50) DEFAULT 'pending',
  payment_method VARCHAR(50) NOT NULL,
  notes TEXT,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Insert sample data for restaurant settings
INSERT INTO restaurant_settings (restaurant_open, business_hours, delivery_fee, estimated_time)
VALUES (
  true,
  '{
    "monday": {"open": "10:00", "close": "22:00", "delivery": true},
    "tuesday": {"open": "10:00", "close": "22:00", "delivery": true},
    "wednesday": {"open": "10:00", "close": "22:00", "delivery": true},
    "thursday": {"open": "10:00", "close": "22:00", "delivery": true},
    "friday": {"open": "10:00", "close": "23:00", "delivery": true},
    "saturday": {"open": "10:00", "close": "23:00", "delivery": true},
    "sunday": {"open": "12:00", "close": "21:00", "delivery": true}
  }',
  49,
  '25-35 min'
) ON CONFLICT (id) DO NOTHING;

-- Insert sample categories
INSERT INTO categories (name, image_url)
VALUES
  ('Starters', 'https://images.unsplash.com/photo-1541745537411-b8046dc6d66c?q=80&w=2788&auto=format&fit=crop'),
  ('Main Courses', 'https://images.unsplash.com/photo-1544025162-d76694265947?q=80&w=2788&auto=format&fit=crop'),
  ('BBQ Specials', 'https://images.unsplash.com/photo-1529193591184-b1d58069ecdd?q=80&w=2790&auto=format&fit=crop'),
  ('Sides', 'https://images.unsplash.com/photo-1618040521538-e0cfee99a9aa?q=80&w=2940&auto=format&fit=crop'),
  ('Desserts', 'https://images.unsplash.com/photo-1551024601-bec78aea704b?q=80&w=2864&auto=format&fit=crop'),
  ('Drinks', 'https://images.unsplash.com/photo-1551538827-9c037cb4f32a?q=80&w=2832&auto=format&fit=crop')
ON CONFLICT DO NOTHING;

-- Insert sample menu items
INSERT INTO menu_items (name, description, price, image_url, category_id, available)
VALUES
  ('BBQ Chicken Wings', 'Juicy chicken wings marinated in our signature BBQ sauce', 129, 'https://images.unsplash.com/photo-1608039755401-742074f0548d?q=80&w=2960&auto=format&fit=crop', 1, true),
  ('Loaded Nachos', 'Crispy nachos with melted cheese, jalapeños, guacamole, and sour cream', 119, 'https://images.unsplash.com/photo-1582169296194-e4d644c48063?q=80&w=2800&auto=format&fit=crop', 1, true),
  ('Smoked Brisket', 'Slow-smoked beef brisket with our house rub, served with coleslaw and cornbread', 249, 'https://images.unsplash.com/photo-1529193591184-b1d58069ecdd?q=80&w=2790&auto=format&fit=crop', 3, true),
  ('BBQ Ribs Platter', 'Fall-off-the-bone pork ribs with our signature sauce, served with fries and coleslaw', 229, 'https://images.unsplash.com/photo-1544025162-d76694265947?q=80&w=2788&auto=format&fit=crop', 3, true),
  ('Grilled Salmon', 'Fresh Atlantic salmon with lemon herb butter, served with mashed potatoes and grilled asparagus', 239, 'https://images.unsplash.com/photo-1519708227418-c8fd9a32b7a2?q=80&w=2070&auto=format&fit=crop', 2, true),
  ('Barbecue Burger', 'Juicy beef patty with cheddar, bacon, onion rings, and our signature BBQ sauce', 189, 'https://images.unsplash.com/photo-1568901346375-23c9450c58cd?q=80&w=2899&auto=format&fit=crop', 2, true),
  ('Mac & Cheese', 'Creamy three-cheese blend with a crispy breadcrumb topping', 79, 'https://images.unsplash.com/photo-1543352634-a1c51d9f1fa7?q=80&w=2940&auto=format&fit=crop', 4, true),
  ('Sweet Potato Fries', 'Crispy sweet potato fries with chipotle mayo dip', 69, 'https://images.unsplash.com/photo-1604152135912-04a022e73fba?q=80&w=2987&auto=format&fit=crop', 4, true),
  ('New York Cheesecake', 'Creamy cheesecake with berry compote', 99, 'https://images.unsplash.com/photo-1611293388250-580b08c4a145?q=80&w=2940&auto=format&fit=crop', 5, true),
  ('Chocolate Lava Cake', 'Warm chocolate cake with a molten center, served with vanilla ice cream', 109, 'https://images.unsplash.com/photo-1606313564200-e75d8e3aabc3?q=80&w=2942&auto=format&fit=crop', 5, true),
  ('Craft Beer', 'Selection of local craft beers', 89, 'https://images.unsplash.com/photo-1600788886242-5c96aabe3757?q=80&w=2787&auto=format&fit=crop', 6, true),
  ('Signature Cocktails', 'Ask your server for our seasonal cocktail options', 119, 'https://images.unsplash.com/photo-1551024601-bec78aea704b?q=80&w=2864&auto=format&fit=crop', 6, true)
ON CONFLICT DO NOTHING;

-- Insert sample orders
INSERT INTO orders (customer, items, subtotal, delivery_fee, total, status, payment_method, notes)
VALUES
  (
    '{"firstName": "John", "lastName": "Doe", "email": "<EMAIL>", "phone": "12345678", "address": "123 Main St", "postalCode": "0001", "city": "Oslo"}',
    '[{"id": 3, "name": "Smoked Brisket", "price": 249, "quantity": 1}, {"id": 7, "name": "Mac & Cheese", "price": 79, "quantity": 1}]',
    328,
    49,
    377,
    'delivered',
    'card',
    'Please include extra sauce'
  ),
  (
    '{"firstName": "Jane", "lastName": "Smith", "email": "<EMAIL>", "phone": "87654321", "address": "456 Oak Ave", "postalCode": "0002", "city": "Oslo"}',
    '[{"id": 4, "name": "BBQ Ribs Platter", "price": 229, "quantity": 2}, {"id": 8, "name": "Sweet Potato Fries", "price": 69, "quantity": 1}]',
    527,
    49,
    576,
    'delivered',
    'card',
    NULL
  ),
  (
    '{"firstName": "Mike", "lastName": "Johnson", "email": "<EMAIL>", "phone": "55554444", "address": "789 Pine St", "postalCode": "0003", "city": "Oslo"}',
    '[{"id": 6, "name": "Barbecue Burger", "price": 189, "quantity": 1}, {"id": 11, "name": "Craft Beer", "price": 89, "quantity": 1}]',
    278,
    49,
    327,
    'in-progress',
    'cash',
    'Ring the bell when you arrive'
  ),
  (
    '{"firstName": "Sarah", "lastName": "Williams", "email": "<EMAIL>", "phone": "33332222", "address": "101 Maple Dr", "postalCode": "0004", "city": "Oslo"}',
    '[{"id": 1, "name": "BBQ Chicken Wings", "price": 129, "quantity": 1}, {"id": 2, "name": "Loaded Nachos", "price": 119, "quantity": 1}, {"id": 10, "name": "Chocolate Lava Cake", "price": 109, "quantity": 1}]',
    357,
    49,
    406,
    'pending',
    'card',
    NULL
  )
ON CONFLICT DO NOTHING;

-- Users Table for Authentication
CREATE TABLE IF NOT EXISTS users (
  id SERIAL PRIMARY KEY,
  username VARCHAR(50) NOT NULL UNIQUE,
  email VARCHAR(100) NOT NULL UNIQUE,
  password VARCHAR(255) NOT NULL,
  first_name VARCHAR(100),
  last_name VARCHAR(100),
  role VARCHAR(20) NOT NULL DEFAULT 'customer',
  is_active BOOLEAN NOT NULL DEFAULT true,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Insert staff users with secure passwords
-- Note: These passwords are hashed versions of secure passwords
INSERT INTO users (username, email, password, first_name, last_name, role, is_active)
VALUES
  ('admin', '<EMAIL>', '$2b$10$rMb4F.1Vd1UQF6QaJZJCm.dSWEJZJ1XGxwXv7jDsQ5Oa7H.6DMTVi', 'Admin', 'User', 'admin', true),
  ('manager', '<EMAIL>', '$2b$10$rMb4F.1Vd1UQF6QaJZJCm.dSWEJZJ1XGxwXv7jDsQ5Oa7H.6DMTVi', 'Kitchen', 'Manager', 'manager', true),
  ('driver', '<EMAIL>', '$2b$10$rMb4F.1Vd1UQF6QaJZJCm.dSWEJZJ1XGxwXv7jDsQ5Oa7H.6DMTVi', 'Delivery', 'Driver', 'driver', true)
ON CONFLICT (username) DO NOTHING;