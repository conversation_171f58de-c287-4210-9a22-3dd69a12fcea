import { useState, useEffect } from "react";
import { motion } from "framer-motion";
import AdminLayout from "./AdminLayout";
import { getCategories, getMenuItems, createMenuItem, updateMenuItem, deleteMenuItem, createCategory, updateCategory, deleteCategory, Category, MenuItem } from "@/api/adminApi";
import { useToast } from "@/hooks/use-toast";

const Menu = () => {
  const { toast } = useToast();

  // State for menu data
  const [categories, setCategories] = useState<Category[]>([]);
  const [menuItems, setMenuItems] = useState<MenuItem[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  // State for editing
  const [selectedCategory, setSelectedCategory] = useState<Category | null>(null);
  const [selectedMenuItem, setSelectedMenuItem] = useState<MenuItem | null>(null);
  const [showCategoryForm, setShowCategoryForm] = useState(false);
  const [showMenuItemForm, setShowMenuItemForm] = useState(false);

  // Form data
  const [categoryForm, setCategoryForm] = useState<Omit<Category, 'id'>>({
    name: '',
    image_url: ''
  });

  const [menuItemForm, setMenuItemForm] = useState<Omit<MenuItem, 'id' | 'category_name'>>({
    name: '',
    description: '',
    price: 0,
    imageUrl: '',
    categoryId: 0,
    available: true
  });

  // Load data on component mount
  useEffect(() => {
    const loadData = async () => {
      try {
        setIsLoading(true);

        // Fetch categories and menu items in parallel
        const [categoriesData, menuItemsData] = await Promise.all([
          fetch('/api/categories').then(res => res.json()),
          fetch('/api/items').then(res => res.json())
        ]);

        setCategories(categoriesData);
        setMenuItems(menuItemsData);
      } catch (error) {
        console.error('Error loading menu data:', error);
        toast({
          title: "Error",
          description: "Failed to load menu data",
          variant: "destructive"
        });
      } finally {
        setIsLoading(false);
      }
    };

    loadData();
  }, [toast]);

  // Reset forms
  const resetForms = () => {
    setCategoryForm({ name: '', image_url: '' });
    setMenuItemForm({
      name: '',
      description: '',
      price: 0,
      image_url: '',
      category_id: categories.length > 0 ? categories[0].id : 0,
      available: true
    });
    setSelectedCategory(null);
    setSelectedMenuItem(null);
  };

  // Open category form
  const handleAddCategory = () => {
    resetForms();
    setShowCategoryForm(true);
    setShowMenuItemForm(false);
  };

  // Open menu item form
  const handleAddMenuItem = () => {
    resetForms();
    setMenuItemForm({
      ...menuItemForm,
      category_id: categories.length > 0 ? categories[0].id : 0
    });
    setShowMenuItemForm(true);
    setShowCategoryForm(false);
  };

  // Edit category
  const handleEditCategory = (category: Category) => {
    setSelectedCategory(category);
    setCategoryForm({
      name: category.name,
      image_url: category.image_url || ''
    });
    setShowCategoryForm(true);
    setShowMenuItemForm(false);
  };

  // Edit menu item
  const handleEditMenuItem = (item: MenuItem) => {
    setSelectedMenuItem(item);
    setMenuItemForm({
      name: item.name,
      description: item.description || '',
      price: item.price,
      imageUrl: item.imageUrl || '',
      categoryId: item.categoryId,
      available: item.available
    });
    setShowMenuItemForm(true);
    setShowCategoryForm(false);
  };

  // Save category
  const handleSaveCategory = async (e: React.FormEvent) => {
    e.preventDefault();

    try {
      if (!categoryForm.name) {
        toast({
          title: "Validation Error",
          description: "Category name is required",
          variant: "destructive"
        });
        return;
      }

      if (selectedCategory) {
        // Update existing category
        const updatedCategory = await updateCategory(selectedCategory.id, categoryForm);

        // Update the categories list
        setCategories(prev =>
          prev.map(cat => cat.id === selectedCategory.id ? updatedCategory : cat)
        );

        toast({
          title: "Success",
          description: "Category updated successfully",
          variant: "default"
        });
      } else {
        // Create new category
        console.log('Creating new category:', categoryForm);
        const newCategory = await createCategory(categoryForm);
        console.log('New category created:', newCategory);

        // Add to categories list
        setCategories(prev => [...prev, newCategory]);

        toast({
          title: "Success",
          description: "Category created successfully",
          variant: "default"
        });
      }

      // Reset and close form
      resetForms();
      setShowCategoryForm(false);
    } catch (error) {
      console.error('Error saving category:', error);

      // More detailed error message
      let errorMessage = "Failed to save category";
      if (error instanceof Error) {
        errorMessage += `: ${error.message}`;
      }

      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive"
      });
    }
  };

  // Save menu item
  const handleSaveMenuItem = async (e: React.FormEvent) => {
    e.preventDefault();

    try {
      if (!menuItemForm.name || menuItemForm.price <= 0) {
        toast({
          title: "Validation Error",
          description: "Name and a valid price are required",
          variant: "destructive"
        });
        return;
      }

      if (selectedMenuItem) {
        // Update existing menu item
        const updatedItem = await updateMenuItem(selectedMenuItem.id, menuItemForm);

        // Update the menu items list
        setMenuItems(prev =>
          prev.map(item => item.id === selectedMenuItem.id ? updatedItem : item)
        );

        toast({
          title: "Success",
          description: "Menu item updated successfully",
          variant: "default"
        });
      } else {
        // Create new menu item
        console.log('Creating new menu item:', menuItemForm);
        const newItem = await createMenuItem(menuItemForm);
        console.log('New menu item created:', newItem);

        // Add to menu items list with category name
        const category = categories.find(c => c.id === menuItemForm.categoryId);
        const itemWithCategory = {
          ...newItem,
          category_name: category ? category.name : 'Uncategorized'
        };

        setMenuItems(prev => [...prev, itemWithCategory]);

        toast({
          title: "Success",
          description: "Menu item created successfully",
          variant: "default"
        });
      }

      // Reset and close form
      resetForms();
      setShowMenuItemForm(false);
    } catch (error) {
      console.error('Error saving menu item:', error);

      // More detailed error message
      let errorMessage = "Failed to save menu item";
      if (error instanceof Error) {
        errorMessage += `: ${error.message}`;
      }

      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive"
      });
    }
  };

  // Delete category
  const handleDeleteCategory = async (id: number) => {
    if (!confirm("Are you sure you want to delete this category? This will remove the category from all menu items.")) {
      return;
    }

    try {
      const result = await deleteCategory(id);

      // Remove from categories list
      setCategories(prev => prev.filter(cat => cat.id !== id));

      // Update menu items
      setMenuItems(prev =>
        prev.map(item =>
          item.categoryId === id
            ? { ...item, categoryId: 0, category_name: 'Uncategorized' }
            : item
        )
      );

      toast({
        title: "Success",
        description: "Category deleted successfully",
        variant: "default"
      });
    } catch (error) {
      console.error('Error deleting category:', error);

      // More detailed error message
      let errorMessage = "Failed to delete category";
      if (error instanceof Error) {
        errorMessage += `: ${error.message}`;
      }

      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive"
      });
    }
  };

  // Delete menu item
  const handleDeleteMenuItem = async (id: number) => {
    if (!confirm("Are you sure you want to delete this menu item?")) {
      return;
    }

    try {
      const result = await deleteMenuItem(id);

      // Remove from menu items list
      setMenuItems(prev => prev.filter(item => item.id !== id));

      toast({
        title: "Success",
        description: "Menu item deleted successfully",
        variant: "default"
      });
    } catch (error) {
      console.error('Error deleting menu item:', error);

      // More detailed error message
      let errorMessage = "Failed to delete menu item";
      if (error instanceof Error) {
        errorMessage += `: ${error.message}`;
      }

      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive"
      });
    }
  };

  return (
    <AdminLayout>
      <div className="max-w-6xl mx-auto">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
        >
          <div className="flex justify-between items-center mb-2">
            <h1 className="text-3xl font-bold bg-gradient-to-r from-purple-400 via-cyan-400 to-purple-400 text-transparent bg-clip-text">
              Menu Management
            </h1>

            {/* Action Buttons - Redesigned for better visibility */}
            <div className="flex items-center space-x-4">
              <button
                onClick={handleAddCategory}
                className="px-5 py-2.5 bg-gradient-to-r from-purple-600 to-purple-800 hover:from-purple-500 hover:to-purple-700
                        text-white rounded-lg transition-all duration-300 shadow-lg hover:shadow-purple-500/30 flex items-center"
              >
                <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                </svg>
                Add Category
              </button>
              <button
                onClick={handleAddMenuItem}
                className="px-5 py-2.5 bg-gradient-to-r from-cyan-600 to-cyan-800 hover:from-cyan-500 hover:to-cyan-700
                        text-white rounded-lg transition-all duration-300 shadow-lg hover:shadow-cyan-500/30 flex items-center"
              >
                <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                </svg>
                Add Menu Item
              </button>
            </div>
          </div>

          <p className="text-gray-400 mb-6">
            Easily manage your restaurant's menu categories and items below.
          </p>

          {/* Category Form */}
          {showCategoryForm && (
            <motion.div
              initial={{ opacity: 0, scale: 0.95 }}
              animate={{ opacity: 1, scale: 1 }}
              className="mb-8 bg-gray-900/50 backdrop-blur-sm rounded-xl p-6 border border-gray-800"
            >
              <h2 className="text-xl font-bold mb-4 text-white">
                {selectedCategory ? 'Edit Category' : 'Add New Category'}
              </h2>

              <form onSubmit={handleSaveCategory}>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label htmlFor="categoryName" className="block text-gray-300 mb-2">Category Name*</label>
                    <input
                      id="categoryName"
                      type="text"
                      value={categoryForm.name}
                      onChange={(e) => setCategoryForm({...categoryForm, name: e.target.value})}
                      className="bg-gray-800 border border-gray-700 text-white rounded px-3 py-2 w-full
                              focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                      required
                    />
                  </div>

                  <div>
                    <label htmlFor="categoryImage" className="block text-gray-300 mb-2">Image URL</label>
                    <input
                      id="categoryImage"
                      type="text"
                      value={categoryForm.image_url}
                      onChange={(e) => setCategoryForm({...categoryForm, image_url: e.target.value})}
                      className="bg-gray-800 border border-gray-700 text-white rounded px-3 py-2 w-full
                              focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                    />
                  </div>
                </div>

                <div className="mt-6 flex space-x-4">
                  <button
                    type="submit"
                    className="px-4 py-2 bg-gradient-to-r from-purple-600 to-purple-800 hover:from-purple-700 hover:to-purple-900
                            text-white rounded-md transition-all duration-300 shadow-lg hover:shadow-purple-500/20"
                  >
                    {selectedCategory ? 'Update Category' : 'Add Category'}
                  </button>
                  <button
                    type="button"
                    onClick={() => {
                      setShowCategoryForm(false);
                      resetForms();
                    }}
                    className="px-4 py-2 bg-gray-800 hover:bg-gray-700
                            text-white rounded-md transition-all duration-300"
                  >
                    Cancel
                  </button>
                </div>
              </form>
            </motion.div>
          )}

          {/* Menu Item Form */}
          {showMenuItemForm && (
            <motion.div
              initial={{ opacity: 0, scale: 0.95 }}
              animate={{ opacity: 1, scale: 1 }}
              className="mb-8 bg-gray-900/50 backdrop-blur-sm rounded-xl p-6 border border-gray-800"
            >
              <h2 className="text-xl font-bold mb-4 text-white">
                {selectedMenuItem ? 'Edit Menu Item' : 'Add New Menu Item'}
              </h2>

              <form onSubmit={handleSaveMenuItem}>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label htmlFor="itemName" className="block text-gray-300 mb-2">Item Name*</label>
                    <input
                      id="itemName"
                      type="text"
                      value={menuItemForm.name}
                      onChange={(e) => setMenuItemForm({...menuItemForm, name: e.target.value})}
                      className="bg-gray-800 border border-gray-700 text-white rounded px-3 py-2 w-full
                              focus:outline-none focus:ring-2 focus:ring-cyan-500 focus:border-transparent"
                      required
                    />
                  </div>

                  <div>
                    <label htmlFor="itemCategory" className="block text-gray-300 mb-2">Category*</label>
                    <select
                      id="itemCategory"
                      value={menuItemForm.category_id}
                      onChange={(e) => setMenuItemForm({...menuItemForm, category_id: Number(e.target.value)})}
                      className="bg-gray-800 border border-gray-700 text-white rounded px-3 py-2 w-full
                              focus:outline-none focus:ring-2 focus:ring-cyan-500 focus:border-transparent"
                      required
                    >
                      <option value="">Select a category</option>
                      {categories.map(category => (
                        <option key={category.id} value={category.id}>{category.name}</option>
                      ))}
                    </select>
                  </div>

                  <div>
                    <label htmlFor="itemPrice" className="block text-gray-300 mb-2">Price (NOK)*</label>
                    <input
                      id="itemPrice"
                      type="number"
                      value={menuItemForm.price}
                      onChange={(e) => setMenuItemForm({...menuItemForm, price: Number(e.target.value)})}
                      className="bg-gray-800 border border-gray-700 text-white rounded px-3 py-2 w-full
                              focus:outline-none focus:ring-2 focus:ring-cyan-500 focus:border-transparent"
                      required
                      min="0"
                    />
                  </div>

                  <div>
                    <label htmlFor="itemImage" className="block text-gray-300 mb-2">Image URL</label>
                    <input
                      id="itemImage"
                      type="text"
                      value={menuItemForm.image_url}
                      onChange={(e) => setMenuItemForm({...menuItemForm, image_url: e.target.value})}
                      className="bg-gray-800 border border-gray-700 text-white rounded px-3 py-2 w-full
                              focus:outline-none focus:ring-2 focus:ring-cyan-500 focus:border-transparent"
                    />
                  </div>

                  <div className="md:col-span-2">
                    <label htmlFor="itemDescription" className="block text-gray-300 mb-2">Description</label>
                    <textarea
                      id="itemDescription"
                      value={menuItemForm.description}
                      onChange={(e) => setMenuItemForm({...menuItemForm, description: e.target.value})}
                      className="bg-gray-800 border border-gray-700 text-white rounded px-3 py-2 w-full h-24
                              focus:outline-none focus:ring-2 focus:ring-cyan-500 focus:border-transparent"
                    />
                  </div>

                  <div>
                    <label className="flex items-center space-x-2 text-gray-300">
                      <input
                        type="checkbox"
                        checked={menuItemForm.available}
                        onChange={(e) => setMenuItemForm({...menuItemForm, available: e.target.checked})}
                        className="w-4 h-4 rounded border-gray-700 text-cyan-600 focus:ring-cyan-500"
                      />
                      <span>Available</span>
                    </label>
                  </div>
                </div>

                <div className="mt-6 flex space-x-4">
                  <button
                    type="submit"
                    className="px-4 py-2 bg-gradient-to-r from-cyan-600 to-cyan-800 hover:from-cyan-700 hover:to-cyan-900
                            text-white rounded-md transition-all duration-300 shadow-lg hover:shadow-cyan-500/20"
                  >
                    {selectedMenuItem ? 'Update Menu Item' : 'Add Menu Item'}
                  </button>
                  <button
                    type="button"
                    onClick={() => {
                      setShowMenuItemForm(false);
                      resetForms();
                    }}
                    className="px-4 py-2 bg-gray-800 hover:bg-gray-700
                            text-white rounded-md transition-all duration-300"
                  >
                    Cancel
                  </button>
                </div>
              </form>
            </motion.div>
          )}

          {/* Categories List */}
          <div className="mb-10 bg-gray-900/50 backdrop-blur-sm rounded-xl p-6 border border-gray-800">
            <h2 className="text-xl font-bold mb-4 text-white">Categories</h2>

            {isLoading ? (
              <div className="p-4 text-center text-gray-400">Loading categories...</div>
            ) : categories.length === 0 ? (
              <div className="p-4 text-center text-gray-400">No categories found. Add your first category!</div>
            ) : (
              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead>
                    <tr className="border-b border-gray-800">
                      <th className="py-3 text-left text-gray-400">ID</th>
                      <th className="py-3 text-left text-gray-400">Name</th>
                      <th className="py-3 text-left text-gray-400">Image</th>
                      <th className="py-3 text-left text-gray-400">Actions</th>
                    </tr>
                  </thead>
                  <tbody>
                    {categories.map(category => (
                      <tr key={category.id} className="border-b border-gray-800/50">
                        <td className="py-4 text-gray-300">{category.id}</td>
                        <td className="py-4 font-medium text-white">{category.name}</td>
                        <td className="py-4">
                          {category.image_url && (
                            <div className="w-16 h-16 rounded overflow-hidden">
                              <img
                                src={category.image_url}
                                alt={category.name}
                                className="w-full h-full object-cover"
                                onError={(e) => (e.target as HTMLImageElement).src = 'https://placehold.co/64x64/gray/white?text=NA'}
                              />
                            </div>
                          )}
                        </td>
                        <td className="py-4">
                          <div className="flex space-x-2">
                            <button
                              onClick={() => handleEditCategory(category)}
                              className="px-3 py-1 bg-amber-700 hover:bg-amber-600 text-white rounded-md text-sm transition-colors"
                            >
                              Edit
                            </button>
                            <button
                              onClick={() => handleDeleteCategory(category.id)}
                              className="px-3 py-1 bg-red-800 hover:bg-red-700 text-white rounded-md text-sm transition-colors"
                            >
                              Delete
                            </button>
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            )}
          </div>

          {/* Menu Items List */}
          <div className="mb-10 bg-gray-900/50 backdrop-blur-sm rounded-xl p-6 border border-gray-800">
            <h2 className="text-xl font-bold mb-4 text-white">Menu Items</h2>

            {isLoading ? (
              <div className="p-4 text-center text-gray-400">Loading menu items...</div>
            ) : menuItems.length === 0 ? (
              <div className="p-4 text-center text-gray-400">No menu items found. Add your first menu item!</div>
            ) : (
              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead>
                    <tr className="border-b border-gray-800">
                      <th className="py-3 text-left text-gray-400">ID</th>
                      <th className="py-3 text-left text-gray-400">Name</th>
                      <th className="py-3 text-left text-gray-400">Category</th>
                      <th className="py-3 text-left text-gray-400">Price</th>
                      <th className="py-3 text-left text-gray-400">Available</th>
                      <th className="py-3 text-left text-gray-400">Image</th>
                      <th className="py-3 text-left text-gray-400">Actions</th>
                    </tr>
                  </thead>
                  <tbody>
                    {menuItems.map(item => (
                      <tr key={item.id} className="border-b border-gray-800/50">
                        <td className="py-4 text-gray-300">{item.id}</td>
                        <td className="py-4 font-medium text-white">{item.name}</td>
                        <td className="py-4 text-gray-300">{item.category_name || 'Uncategorized'}</td>
                        <td className="py-4 text-gray-300">{item.price} NOK</td>
                        <td className="py-4">
                          <span className={`px-2 py-1 rounded-full text-xs ${item.available ? 'bg-green-900/50 text-green-300' : 'bg-red-900/50 text-red-300'}`}>
                            {item.available ? 'Yes' : 'No'}
                          </span>
                        </td>
                        <td className="py-4">
                          {item.image_url && (
                            <div className="w-16 h-16 rounded overflow-hidden">
                              <img
                                src={item.image_url}
                                alt={item.name}
                                className="w-full h-full object-cover"
                                onError={(e) => (e.target as HTMLImageElement).src = 'https://placehold.co/64x64/gray/white?text=NA'}
                              />
                            </div>
                          )}
                        </td>
                        <td className="py-4">
                          <div className="flex space-x-2">
                            <button
                              onClick={() => handleEditMenuItem(item)}
                              className="px-3 py-1 bg-amber-700 hover:bg-amber-600 text-white rounded-md text-sm transition-colors"
                            >
                              Edit
                            </button>
                            <button
                              onClick={() => handleDeleteMenuItem(item.id)}
                              className="px-3 py-1 bg-red-800 hover:bg-red-700 text-white rounded-md text-sm transition-colors"
                            >
                              Delete
                            </button>
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            )}
          </div>
        </motion.div>
      </div>
    </AdminLayout>
  );
};

export default Menu;