import {
  User, InsertUser,
  Dish, InsertDish,
  Order, InsertOrder,
  ContactMessage, InsertContactMessage,
  Category, InsertCategory,
  MenuItem, InsertMenuItem,
  CustomizationGroup, InsertCustomizationGroup,
  CustomizationOption, InsertCustomizationOption,
  ItemCustomizationMap, InsertItemCustomizationMap,
  users, categories, menuItems, orders, contactMessages,
  customizationGroups, customizationOptions, itemCustomizationMap
} from "@shared/schema";
import { db } from "./db";
import { eq, desc } from "drizzle-orm";
import * as crypto from "crypto";

// modify the interface with any CRUD methods
// you might need
export interface IStorage {
  // User operations
  getUser(id: number): Promise<User | undefined>;
  getUserByUsername(username: string): Promise<User | undefined>;
  createUser(user: InsertUser): Promise<User>;
  updateUser(id: number, user: Partial<InsertUser>): Promise<User | undefined>;
  deleteUser(id: number): Promise<boolean>;
  verifyUserCredentials(username: string, password: string): Promise<User | null>;

  // Dish/MenuItem operations
  getAllDishes(): Promise<Dish[]>;
  getDishById(id: number): Promise<Dish | undefined>;
  createDish(dish: InsertDish): Promise<Dish>;
  updateDish(id: number, dish: Partial<InsertDish>): Promise<Dish | undefined>;
  deleteDish(id: number): Promise<boolean>;

  // Enhanced Menu Item operations
  getAllMenuItems(): Promise<MenuItem[]>;
  getMenuItemsByCategory(categoryId: number): Promise<MenuItem[]>;
  getMenuItemById(id: number): Promise<MenuItem | undefined>;
  createMenuItem(item: InsertMenuItem): Promise<MenuItem>;
  updateMenuItem(id: number, item: Partial<InsertMenuItem>): Promise<MenuItem | undefined>;
  deleteMenuItem(id: number): Promise<boolean>;

  // Category operations
  getCategories(): Promise<string[]>; // Original method for backwards compatibility
  getAllMenuCategories(): Promise<Category[]>;
  getMenuCategoryById(id: number): Promise<Category | undefined>;
  createMenuCategory(category: InsertCategory): Promise<Category>;
  updateMenuCategory(id: number, category: Partial<InsertCategory>): Promise<Category | undefined>;
  deleteMenuCategory(id: number): Promise<boolean>;

  // Customization operations
  getAllCustomizationGroups(): Promise<CustomizationGroup[]>;
  getCustomizationGroupById(id: number): Promise<CustomizationGroup | undefined>;
  createCustomizationGroup(group: InsertCustomizationGroup): Promise<CustomizationGroup>;
  updateCustomizationGroup(id: number, group: Partial<InsertCustomizationGroup>): Promise<CustomizationGroup | undefined>;
  deleteCustomizationGroup(id: number): Promise<boolean>;

  getAllCustomizationOptions(): Promise<CustomizationOption[]>;
  getCustomizationOptionsByGroup(groupId: number): Promise<CustomizationOption[]>;
  getCustomizationOptionById(id: number): Promise<CustomizationOption | undefined>;
  createCustomizationOption(option: InsertCustomizationOption): Promise<CustomizationOption>;
  updateCustomizationOption(id: number, option: Partial<InsertCustomizationOption>): Promise<CustomizationOption | undefined>;
  deleteCustomizationOption(id: number): Promise<boolean>;

  // Item Customization Map operations
  getCustomizationOptionsForMenuItem(itemId: number): Promise<{
    group: CustomizationGroup;
    options: CustomizationOption[];
  }[]>;
  mapCustomizationOptionToMenuItem(itemId: number, optionId: number): Promise<ItemCustomizationMap>;
  unmapCustomizationOptionFromMenuItem(itemId: number, optionId: number): Promise<boolean>;

  // Order operations
  getOrderById(id: number): Promise<Order | undefined>;
  getAllOrders(): Promise<Order[]>;
  createOrder(order: InsertOrder): Promise<Order>;
  updateOrder(id: number, order: Partial<InsertOrder>): Promise<Order | undefined>;
  deleteOrder(id: number): Promise<boolean>;

  // Cart operations
  createCart(cart: { itemId: number, quantity: number, customizations: number[] }): Promise<{ success: boolean, id: number }>;

  // Contact operations
  createContactMessage(message: InsertContactMessage): Promise<ContactMessage>;
  getAllContactMessages(): Promise<ContactMessage[]>;
  getContactMessageById(id: number): Promise<ContactMessage | undefined>;
  deleteContactMessage(id: number): Promise<boolean>;
}

export class MemStorage implements IStorage {
  private users: Map<number, User>;
  private dishes: Map<number, Dish>;
  private orders: Map<number, Order>;
  private contactMessages: Map<number, ContactMessage>;

  // New storage for enhanced menu system
  private menuCategories: Map<number, Category>;
  private menuItems: Map<number, MenuItem>;
  private customizationGroups: Map<number, CustomizationGroup>;
  private customizationOptions: Map<number, CustomizationOption>;
  private itemCustomizationMaps: Map<number, ItemCustomizationMap>;

  // ID counters
  currentUserId: number;
  currentDishId: number;
  currentOrderId: number;
  currentContactId: number;
  currentCategoryId: number;
  currentMenuItemId: number;
  currentCustomizationGroupId: number;
  currentCustomizationOptionId: number;
  currentItemCustomizationMapId: number;

  constructor() {
    // Initialize maps
    this.users = new Map();
    this.dishes = new Map();
    this.orders = new Map();
    this.contactMessages = new Map();
    this.menuCategories = new Map();
    this.menuItems = new Map();
    this.customizationGroups = new Map();
    this.customizationOptions = new Map();
    this.itemCustomizationMaps = new Map();

    // Initialize counters
    this.currentUserId = 1;
    this.currentDishId = 1;
    this.currentOrderId = 1;
    this.currentContactId = 1;
    this.currentCategoryId = 1;
    this.currentMenuItemId = 1;
    this.currentCustomizationGroupId = 1;
    this.currentCustomizationOptionId = 1;
    this.currentItemCustomizationMapId = 1;

    // Initialize sample data
    this.initializeSampleData();
  }

  // User methods
  async getUser(id: number): Promise<User | undefined> {
    return this.users.get(id);
  }

  async getUserByUsername(username: string): Promise<User | undefined> {
    return Array.from(this.users.values()).find(
      (user) => user.username === username,
    );
  }

  async createUser(insertUser: InsertUser): Promise<User> {
    const id = this.currentUserId++;
    const user: User = { ...insertUser, id };
    this.users.set(id, user);
    return user;
  }

  async verifyUserCredentials(username: string, password: string): Promise<User | null> {
    const user = await this.getUserByUsername(username);

    if (!user) {
      return null;
    }

    // In a real implementation, you would use bcrypt.compare
    // For this demo, we'll just do a direct comparison
    if (user.password === password) {
      return user;
    }

    return null;
  }

  // Original Dish methods (for backwards compatibility)
  async getAllDishes(): Promise<Dish[]> {
    return Array.from(this.dishes.values());
  }

  async getDishById(id: number): Promise<Dish | undefined> {
    return this.dishes.get(id);
  }

  async createDish(insertDish: InsertDish): Promise<Dish> {
    const id = this.currentDishId++;
    const dish: Dish = { ...insertDish, id };
    this.dishes.set(id, dish);
    return dish;
  }

  // Enhanced Menu Item methods
  async getAllMenuItems(): Promise<MenuItem[]> {
    return Array.from(this.menuItems.values());
  }

  async getMenuItemsByCategory(categoryId: number): Promise<MenuItem[]> {
    return Array.from(this.menuItems.values()).filter(
      (item) => item.categoryId === categoryId
    );
  }

  async getMenuItemById(id: number): Promise<MenuItem | undefined> {
    return this.menuItems.get(id);
  }

  async createMenuItem(item: InsertMenuItem): Promise<MenuItem> {
    const id = this.currentMenuItemId++;
    const menuItem: MenuItem = { ...item, id };
    this.menuItems.set(id, menuItem);

    // Also add to dishes for backward compatibility
    const category = this.menuCategories.get(item.categoryId)?.name || "Uncategorized";
    this.dishes.set(id, {
      ...menuItem,
      category
    } as Dish);

    return menuItem;
  }

  // Enhanced Category methods
  async getAllMenuCategories(): Promise<Category[]> {
    try {
      // Make sure we actually return the full category objects
      return Array.from(this.menuCategories.values());
    } catch (error) {
      console.error("Error getting menu categories:", error);
      return [];
    }
  }

  async getMenuCategoryById(id: number): Promise<Category | undefined> {
    return this.menuCategories.get(id);
  }

  async createMenuCategory(category: InsertCategory): Promise<Category> {
    const id = this.currentCategoryId++;
    const newCategory: Category = { ...category, id };
    this.menuCategories.set(id, newCategory);
    return newCategory;
  }

  async updateMenuCategory(id: number, category: Partial<Category>): Promise<Category> {
    const existingCategory = this.menuCategories.get(id);
    if (!existingCategory) {
      throw new Error(`Category with id ${id} not found`);
    }

    const updatedCategory = { ...existingCategory, ...category };
    this.menuCategories.set(id, updatedCategory);
    return updatedCategory;
  }

  async deleteMenuCategory(id: number): Promise<Category> {
    const category = this.menuCategories.get(id);
    if (!category) {
      throw new Error(`Category with id ${id} not found`);
    }

    this.menuCategories.delete(id);

    // Update any menu items that were in this category
    for (const [itemId, item] of this.menuItems.entries()) {
      if (item.category_id === id) {
        const updatedItem = { ...item, category_id: 0 };
        this.menuItems.set(itemId, updatedItem);
      }
    }

    return category;
  }

  // Customization Group methods
  async getAllCustomizationGroups(): Promise<CustomizationGroup[]> {
    return Array.from(this.customizationGroups.values());
  }

  async getCustomizationGroupById(id: number): Promise<CustomizationGroup | undefined> {
    return this.customizationGroups.get(id);
  }

  async createCustomizationGroup(group: InsertCustomizationGroup): Promise<CustomizationGroup> {
    const id = this.currentCustomizationGroupId++;
    const newGroup: CustomizationGroup = { ...group, id };
    this.customizationGroups.set(id, newGroup);
    return newGroup;
  }

  // Customization Option methods
  async getAllCustomizationOptions(): Promise<CustomizationOption[]> {
    return Array.from(this.customizationOptions.values());
  }

  async getCustomizationOptionsByGroup(groupId: number): Promise<CustomizationOption[]> {
    return Array.from(this.customizationOptions.values()).filter(
      (option) => option.groupId === groupId
    );
  }

  async getCustomizationOptionById(id: number): Promise<CustomizationOption | undefined> {
    return this.customizationOptions.get(id);
  }

  async createCustomizationOption(option: InsertCustomizationOption): Promise<CustomizationOption> {
    const id = this.currentCustomizationOptionId++;
    const newOption: CustomizationOption = { ...option, id };
    this.customizationOptions.set(id, newOption);
    return newOption;
  }

  // Item Customization Map methods
  async getCustomizationOptionsForMenuItem(itemId: number): Promise<{
    group: CustomizationGroup;
    options: CustomizationOption[];
  }[]> {
    // Get all mapping entries for this item
    const mappings = Array.from(this.itemCustomizationMaps.values()).filter(
      (map) => map.itemId === itemId
    );

    // Get all option IDs for this item
    const optionIds = mappings.map((map) => map.optionId);

    // Get all options for these IDs
    const options = optionIds.map((id) => this.customizationOptions.get(id)!).filter(Boolean);

    // Group options by group ID
    const groupedOptions = new Map<number, CustomizationOption[]>();
    options.forEach((option) => {
      if (!groupedOptions.has(option.groupId)) {
        groupedOptions.set(option.groupId, []);
      }
      groupedOptions.get(option.groupId)!.push(option);
    });

    // Format the result with group objects
    const result: { group: CustomizationGroup; options: CustomizationOption[] }[] = [];
    groupedOptions.forEach((options, groupId) => {
      const group = this.customizationGroups.get(groupId);
      if (group) {
        result.push({ group, options });
      }
    });

    return result;
  }

  async mapCustomizationOptionToMenuItem(itemId: number, optionId: number): Promise<ItemCustomizationMap> {
    // Check if the menu item and option exist
    const menuItem = await this.getMenuItemById(itemId);
    const option = await this.getCustomizationOptionById(optionId);

    if (!menuItem || !option) {
      throw new Error("Menu item or customization option not found");
    }

    // Create the mapping
    const id = this.currentItemCustomizationMapId++;
    const mapping: ItemCustomizationMap = { id, itemId, optionId };
    this.itemCustomizationMaps.set(id, mapping);

    return mapping;
  }

  // Cart operations
  async createCart(cart: { itemId: number, quantity: number, customizations: number[] }): Promise<{ success: boolean, id: number }> {
    // Validate that the item exists
    const menuItem = await this.getMenuItemById(cart.itemId);
    if (!menuItem) {
      throw new Error("Menu item not found");
    }

    // Validate that all customization options exist
    for (const optionId of cart.customizations) {
      const option = await this.getCustomizationOptionById(optionId);
      if (!option) {
        throw new Error(`Customization option ${optionId} not found`);
      }
    }

    // Since we don't have a cart table yet, we'll just return success
    return { success: true, id: Date.now() };
  }

  // Order methods
  async getOrderById(id: number): Promise<Order | undefined> {
    return this.orders.get(id);
  }

  async getAllOrders(): Promise<Order[]> {
    return Array.from(this.orders.values());
  }

  async createOrder(insertOrder: InsertOrder): Promise<Order> {
    const id = this.currentOrderId++;
    const now = new Date();
    const order: Order = { ...insertOrder, id, createdAt: now };
    this.orders.set(id, order);
    return order;
  }

  // Contact methods
  async createContactMessage(insertMessage: InsertContactMessage): Promise<ContactMessage> {
    const id = this.currentContactId++;
    const now = new Date();
    const message: ContactMessage = { ...insertMessage, id, createdAt: now };
    this.contactMessages.set(id, message);
    return message;
  }

  // Category methods (for backwards compatibility)
  async getCategories(): Promise<string[]> {
    // Get all category names from the categories table
    return Array.from(this.menuCategories.values()).map(category => category.name);
  }

  // Initialize sample data for the menu system
  private initializeSampleData() {
    // 1. Initialize categories
    const sampleCategories: InsertCategory[] = [
      {
        name: "Signature BBQ",
        imageUrl: "https://images.unsplash.com/photo-1529193591184-b1d58069ecdd?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&h=400",
      },
      {
        name: "Starters",
        imageUrl: "https://images.unsplash.com/photo-1527477396000-e27163b481c2?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&h=400",
      },
      {
        name: "Main Course",
        imageUrl: "https://images.unsplash.com/photo-1519708227418-c8fd9a32b7a2?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&h=400",
      },
      {
        name: "Desserts",
        imageUrl: "https://images.unsplash.com/photo-1624353365286-3f8d62daad51?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&h=400",
      },
      {
        name: "BurgerZ",
        imageUrl: "https://images.unsplash.com/photo-1568901346375-23c9450c58cd?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&h=400",
      },
      {
        name: "SandwichZ",
        imageUrl: "https://images.unsplash.com/photo-1621996346565-e3dbc646d9a9?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&h=400",
      }
    ];

    // Insert categories and create a map for reference when creating menu items
    const categoryMap: Record<string, number> = {};
    sampleCategories.forEach(category => {
      const id = this.currentCategoryId++;
      this.menuCategories.set(id, { ...category, id });
      categoryMap[category.name] = id;
    });

    // 2. Initialize menu items
    const sampleMenuItems: Array<InsertMenuItem & { categoryName: string }> = [
      {
        name: "Smoked Beef Brisket",
        description: "24-hour smoked premium beef brisket with our signature spice rub, served with homemade BBQ sauce and pickled vegetables.",
        price: 329,
        imageUrl: "https://images.unsplash.com/photo-1529193591184-b1d58069ecdd?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&h=400",
        categoryId: 0, // Will be replaced with actual ID
        categoryName: "Signature BBQ",
        available: true,
        rating: 49,
        reviews: 120
      },
      {
        name: "BBQ Pork Ribs",
        description: "Slow-cooked St. Louis style ribs with our house dry rub, glazed with maple bourbon sauce and finished over open flame.",
        price: 289,
        imageUrl: "https://images.unsplash.com/photo-1544025162-d76694265947?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&h=400",
        categoryId: 0,
        categoryName: "Signature BBQ",
        available: true,
        rating: 48,
        reviews: 98
      },
      {
        name: "Pulled Pork Sandwich",
        description: "12-hour smoked pork shoulder, hand-pulled and tossed in Carolina vinegar sauce, served on a brioche bun with coleslaw.",
        price: 219,
        imageUrl: "https://images.unsplash.com/photo-1621996346565-e3dbc646d9a9?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&h=400",
        categoryId: 0,
        categoryName: "SandwichZ",
        available: true,
        rating: 47,
        reviews: 86
      },
      {
        name: "Smoked Chicken Wings",
        description: "Applewood smoked wings finished on the grill, tossed in your choice of sauce: Classic Buffalo, Honey Chipotle, or Garlic Parmesan.",
        price: 189,
        imageUrl: "https://images.unsplash.com/photo-1527477396000-e27163b481c2?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&h=400",
        categoryId: 0,
        categoryName: "Starters",
        available: true,
        rating: 46,
        reviews: 112
      },
      {
        name: "Cedar Plank Salmon",
        description: "Norwegian salmon fillet grilled on a cedar plank with maple glaze, served with grilled lemon and seasonal vegetables.",
        price: 299,
        imageUrl: "https://images.unsplash.com/photo-1519708227418-c8fd9a32b7a2?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&h=400",
        categoryId: 0,
        categoryName: "Main Course",
        available: true,
        rating: 49,
        reviews: 74
      },
      {
        name: "Smokehouse Burger",
        description: "House-ground prime beef patty with smoked cheddar, bacon jam, caramelized onions, and bourbon BBQ sauce on a toasted brioche bun.",
        price: 249,
        imageUrl: "https://images.unsplash.com/photo-1568901346375-23c9450c58cd?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&h=400",
        categoryId: 0,
        categoryName: "BurgerZ",
        available: true,
        rating: 48,
        reviews: 103
      },
      {
        name: "BBQ Sampler Platter",
        description: "A selection of our signature meats including brisket, ribs, pulled pork, and smoked sausage, served with two sides of your choice.",
        price: 399,
        imageUrl: "https://images.unsplash.com/photo-1508615263227-c5d58c1e5821?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&h=400",
        categoryId: 0,
        categoryName: "Signature BBQ",
        available: true,
        rating: 50,
        reviews: 135
      },
      {
        name: "Grilled Vegetable Skewers",
        description: "Seasonal vegetables marinated in herbs and olive oil, grilled to perfection and served with chimichurri sauce.",
        price: 179,
        imageUrl: "https://images.unsplash.com/photo-1625944525533-473f1a3d54a7?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&h=400",
        categoryId: 0,
        categoryName: "Starters",
        available: true,
        rating: 45,
        reviews: 62
      },
      {
        name: "Chocolate Lava Cake",
        description: "Warm chocolate cake with a molten center, served with vanilla bean ice cream and fresh berries.",
        price: 149,
        imageUrl: "https://images.unsplash.com/photo-1624353365286-3f8d62daad51?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&h=400",
        categoryId: 0,
        categoryName: "Desserts",
        available: true,
        rating: 48,
        reviews: 89
      }
    ];

    // Insert menu items with the correct category IDs
    sampleMenuItems.forEach(menuItem => {
      const { categoryName, ...item } = menuItem;
      const categoryId = categoryMap[categoryName];
      const id = this.currentMenuItemId++;
      const dishToInsert = { ...item, categoryId, id };

      // Add to both menu items and dishes (for backward compatibility)
      this.menuItems.set(id, dishToInsert);
      this.dishes.set(id, {
        ...dishToInsert,
        category: categoryName // For backward compatibility
      } as Dish);
    });

    // 3. Initialize customization groups
    const customizationGroups: InsertCustomizationGroup[] = [
      { title: "Saus & Topping" },
      { title: "Ost" },
      { title: "Ekstra Produkter" },
      { title: "Grønnsaker" }
    ];

    // Add customization groups and track IDs
    const groupMap: Record<string, number> = {};
    customizationGroups.forEach(group => {
      const id = this.currentCustomizationGroupId++;
      this.customizationGroups.set(id, { ...group, id });
      groupMap[group.title] = id;
    });

    // 4. Initialize customization options
    const customizationOptions: Array<InsertCustomizationOption & { groupTitle: string }> = [
      { name: "BBQ saus", extraPrice: 10, imageUrl: "/icons/bbq.png", groupId: 0, groupTitle: "Saus & Topping" },
      { name: "Hot Sauce", extraPrice: 10, imageUrl: "/icons/hot.png", groupId: 0, groupTitle: "Saus & Topping" },
      { name: "Mayo", extraPrice: 10, imageUrl: "/icons/mayo.png", groupId: 0, groupTitle: "Saus & Topping" },
      { name: "Cheddar", extraPrice: 15, imageUrl: "/icons/cheddar.png", groupId: 0, groupTitle: "Ost" },
      { name: "Blue Cheese", extraPrice: 20, imageUrl: "/icons/blue.png", groupId: 0, groupTitle: "Ost" },
      { name: "Bacon", extraPrice: 25, imageUrl: "/icons/bacon.png", groupId: 0, groupTitle: "Ekstra Produkter" },
      { name: "Double Meat", extraPrice: 40, imageUrl: "/icons/meat.png", groupId: 0, groupTitle: "Ekstra Produkter" },
      { name: "Lettuce", extraPrice: 0, imageUrl: "/icons/lettuce.png", groupId: 0, groupTitle: "Grønnsaker" },
      { name: "Tomato", extraPrice: 0, imageUrl: "/icons/tomato.png", groupId: 0, groupTitle: "Grønnsaker" },
      { name: "Onion", extraPrice: 0, imageUrl: "/icons/onion.png", groupId: 0, groupTitle: "Grønnsaker" },
      { name: "Avocado", extraPrice: 20, imageUrl: "/icons/avocado.png", groupId: 0, groupTitle: "Grønnsaker" }
    ];

    // Add customization options with correct group IDs
    const optionMap: Record<string, number> = {};
    customizationOptions.forEach(option => {
      const { groupTitle, ...optionData } = option;
      const groupId = groupMap[groupTitle];
      const id = this.currentCustomizationOptionId++;
      this.customizationOptions.set(id, { ...optionData, groupId, id });
      optionMap[option.name] = id;
    });

    // 5. Map customization options to menu items
    // For each burger, add some customization options
    const burgerItems = Array.from(this.menuItems.values())
      .filter(item => {
        const category = Array.from(this.menuCategories.values())
          .find(cat => cat.id === item.categoryId);
        return category && (category.name === "BurgerZ" || category.name === "SandwichZ");
      });

    burgerItems.forEach(burger => {
      // Add all sauce options
      const sauceOptions = Array.from(this.customizationOptions.values())
        .filter(option => {
          const group = this.customizationGroups.get(option.groupId);
          return group && group.title === "Saus & Topping";
        });

      sauceOptions.forEach(sauce => {
        const id = this.currentItemCustomizationMapId++;
        this.itemCustomizationMaps.set(id, {
          id,
          itemId: burger.id,
          optionId: sauce.id
        });
      });

      // Add all cheese options
      const cheeseOptions = Array.from(this.customizationOptions.values())
        .filter(option => {
          const group = this.customizationGroups.get(option.groupId);
          return group && group.title === "Ost";
        });

      cheeseOptions.forEach(cheese => {
        const id = this.currentItemCustomizationMapId++;
        this.itemCustomizationMaps.set(id, {
          id,
          itemId: burger.id,
          optionId: cheese.id
        });
      });

      // Add all vegetable options
      const vegOptions = Array.from(this.customizationOptions.values())
        .filter(option => {
          const group = this.customizationGroups.get(option.groupId);
          return group && group.title === "Grønnsaker";
        });

      vegOptions.forEach(veg => {
        const id = this.currentItemCustomizationMapId++;
        this.itemCustomizationMaps.set(id, {
          id,
          itemId: burger.id,
          optionId: veg.id
        });
      });

      // Add extra options
      const extraOptions = Array.from(this.customizationOptions.values())
        .filter(option => {
          const group = this.customizationGroups.get(option.groupId);
          return group && group.title === "Ekstra Produkter";
        });

      extraOptions.forEach(extra => {
        const id = this.currentItemCustomizationMapId++;
        this.itemCustomizationMaps.set(id, {
          id,
          itemId: burger.id,
          optionId: extra.id
        });
      });
    });
  }
}

// Password hashing utility functions
async function hashPassword(password: string): Promise<string> {
  const salt = crypto.randomBytes(16).toString("hex");
  return new Promise((resolve, reject) => {
    crypto.scrypt(password, salt, 64, (err, derivedKey) => {
      if (err) reject(err);
      resolve(`${derivedKey.toString("hex")}.${salt}`);
    });
  });
}

async function verifyPassword(password: string, hashedPassword: string): Promise<boolean> {
  const [hash, salt] = hashedPassword.split(".");
  return new Promise((resolve, reject) => {
    crypto.scrypt(password, salt, 64, (err, derivedKey) => {
      if (err) reject(err);
      resolve(hash === derivedKey.toString("hex"));
    });
  });
}

export class DatabaseStorage implements IStorage {
  constructor() {
    // Initialize database with sample data if needed
    this.initializeDatabase();
  }

  private async initializeDatabase() {
    try {
      // Check if we have any categories, if not, initialize with sample data
      const existingCategories = await db.select().from(categories).limit(1);

      if (existingCategories.length === 0) {
        console.log("Initializing database with sample data...");
        await this.seedDatabase();
      }
    } catch (error) {
      console.error("Error initializing database:", error);
    }
  }

  private async seedDatabase() {
    try {
      // Insert sample categories
      const sampleCategories = [
        {
          name: "Signature BBQ",
          imageUrl: "https://images.unsplash.com/photo-1529193591184-b1d58069ecdd?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&h=400",
        },
        {
          name: "Starters",
          imageUrl: "https://images.unsplash.com/photo-1527477396000-e27163b481c2?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&h=400",
        },
        {
          name: "Main Course",
          imageUrl: "https://images.unsplash.com/photo-1519708227418-c8fd9a32b7a2?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&h=400",
        },
        {
          name: "Desserts",
          imageUrl: "https://images.unsplash.com/photo-1624353365286-3f8d62daad51?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&h=400",
        },
        {
          name: "BurgerZ",
          imageUrl: "https://images.unsplash.com/photo-1568901346375-23c9450c58cd?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&h=400",
        },
        {
          name: "SandwichZ",
          imageUrl: "https://images.unsplash.com/photo-1621996346565-e3dbc646d9a9?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&h=400",
        }
      ];

      const insertedCategories = await db.insert(categories).values(sampleCategories).returning();
      console.log(`Inserted ${insertedCategories.length} categories`);

      // Insert sample menu items
      const sampleMenuItems = [
        {
          name: "Smoked Beef Brisket",
          description: "24-hour smoked premium beef brisket with our signature spice rub, served with homemade BBQ sauce and pickled vegetables.",
          price: 329,
          imageUrl: "https://images.unsplash.com/photo-1529193591184-b1d58069ecdd?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&h=400",
          categoryId: insertedCategories[0].id, // Signature BBQ
          available: true,
          rating: 49,
          reviews: 120
        },
        {
          name: "BBQ Pulled Pork",
          description: "Slow-cooked pulled pork shoulder with tangy BBQ sauce, served on a brioche bun with coleslaw.",
          price: 269,
          imageUrl: "https://images.unsplash.com/photo-1544025162-d76694265947?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&h=400",
          categoryId: insertedCategories[0].id, // Signature BBQ
          available: true,
          rating: 46,
          reviews: 89
        },
        {
          name: "Loaded Nachos",
          description: "Crispy tortilla chips topped with melted cheese, jalapeños, sour cream, and guacamole.",
          price: 189,
          imageUrl: "https://images.unsplash.com/photo-1513456852971-30c0b8199d4d?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&h=400",
          categoryId: insertedCategories[1].id, // Starters
          available: true,
          rating: 42,
          reviews: 67
        },
        {
          name: "BBQ Wings",
          description: "Crispy chicken wings tossed in our signature BBQ sauce, served with ranch dipping sauce.",
          price: 219,
          imageUrl: "https://images.unsplash.com/photo-1527477396000-e27163b481c2?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&h=400",
          categoryId: insertedCategories[1].id, // Starters
          available: true,
          rating: 44,
          reviews: 78
        }
      ];

      const insertedMenuItems = await db.insert(menuItems).values(sampleMenuItems).returning();
      console.log(`Inserted ${insertedMenuItems.length} menu items`);

      // Insert customization groups
      const sampleGroups = [
        { title: "Saus & Topping" },
        { title: "Ost" },
        { title: "Ekstra Produkter" },
        { title: "Grønnsaker" }
      ];

      const insertedGroups = await db.insert(customizationGroups).values(sampleGroups).returning();
      console.log(`Inserted ${insertedGroups.length} customization groups`);

      // Insert customization options
      const sampleOptions = [
        { name: "BBQ Sauce", extraPrice: 10, imageUrl: "", groupId: insertedGroups[0].id },
        { name: "Hot Sauce", extraPrice: 10, imageUrl: "", groupId: insertedGroups[0].id },
        { name: "Mayo", extraPrice: 10, imageUrl: "", groupId: insertedGroups[0].id },
        { name: "Honey Mustard", extraPrice: 15, imageUrl: "", groupId: insertedGroups[0].id },

        { name: "Cheddar", extraPrice: 15, imageUrl: "", groupId: insertedGroups[1].id },
        { name: "Blue Cheese", extraPrice: 20, imageUrl: "", groupId: insertedGroups[1].id },
        { name: "Mozzarella", extraPrice: 15, imageUrl: "", groupId: insertedGroups[1].id },

        { name: "Bacon", extraPrice: 25, imageUrl: "", groupId: insertedGroups[2].id },
        { name: "Double Meat", extraPrice: 40, imageUrl: "", groupId: insertedGroups[2].id },
        { name: "Fried Egg", extraPrice: 15, imageUrl: "", groupId: insertedGroups[2].id },

        { name: "Lettuce", extraPrice: 0, imageUrl: "", groupId: insertedGroups[3].id },
        { name: "Tomato", extraPrice: 0, imageUrl: "", groupId: insertedGroups[3].id },
        { name: "Onion", extraPrice: 0, imageUrl: "", groupId: insertedGroups[3].id },
        { name: "Cucumber", extraPrice: 0, imageUrl: "", groupId: insertedGroups[3].id },
        { name: "Avocado", extraPrice: 20, imageUrl: "", groupId: insertedGroups[3].id }
      ];

      const insertedOptions = await db.insert(customizationOptions).values(sampleOptions).returning();
      console.log(`Inserted ${insertedOptions.length} customization options`);

      console.log("Database seeded successfully!");
    } catch (error) {
      console.error("Error seeding database:", error);
    }
  }

  // User operations
  async getUser(id: number): Promise<User | undefined> {
    try {
      const result = await db.select().from(users).where(eq(users.id, id)).limit(1);
      return result[0];
    } catch (error) {
      console.error("Error getting user:", error);
      return undefined;
    }
  }

  async getUserByUsername(username: string): Promise<User | undefined> {
    try {
      const result = await db.select().from(users).where(eq(users.username, username)).limit(1);
      return result[0];
    } catch (error) {
      console.error("Error getting user by username:", error);
      return undefined;
    }
  }

  async createUser(insertUser: InsertUser): Promise<User> {
    try {
      // Hash the password before storing
      const hashedPassword = await hashPassword(insertUser.password);
      const userToInsert = { ...insertUser, password: hashedPassword };

      const result = await db.insert(users).values(userToInsert).returning();
      return result[0];
    } catch (error) {
      console.error("Error creating user:", error);
      throw error;
    }
  }

  async updateUser(id: number, user: Partial<InsertUser>): Promise<User | undefined> {
    try {
      // Hash password if it's being updated
      const updateData = { ...user };
      if (updateData.password) {
        updateData.password = await hashPassword(updateData.password);
      }

      const result = await db.update(users).set(updateData).where(eq(users.id, id)).returning();
      return result[0];
    } catch (error) {
      console.error("Error updating user:", error);
      return undefined;
    }
  }

  async deleteUser(id: number): Promise<boolean> {
    try {
      const result = await db.delete(users).where(eq(users.id, id));
      return result.rowCount > 0;
    } catch (error) {
      console.error("Error deleting user:", error);
      return false;
    }
  }

  async verifyUserCredentials(username: string, password: string): Promise<User | null> {
    try {
      const user = await this.getUserByUsername(username);
      if (!user) {
        return null;
      }

      const isValid = await verifyPassword(password, user.password);
      return isValid ? user : null;
    } catch (error) {
      console.error("Error verifying user credentials:", error);
      return null;
    }
  }
