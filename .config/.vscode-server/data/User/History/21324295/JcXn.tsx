import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import {
  Clock,
  CheckCircle2,
  X,
  Truck,
  Bar<PERSON>hart,
  ChefHat,
  ShoppingBag,
  Package,
  AlertTriangle
} from 'lucide-react';
import AdminLayout from './AdminLayout';
import SingleUpdateButton from '@/components/admin/SingleUpdateButton';
// We'll use native fetch instead of apiRequest for this component
import { format, parseISO } from 'date-fns';

// Status options for orders
const ORDER_STATUSES = [
  { value: 'confirmed', label: 'Confirmed', icon: <CheckCircle2 className="w-4 h-4" /> },
  { value: 'processing', label: 'Processing', icon: <Clock className="w-4 h-4" /> },
  { value: 'preparing', label: 'Preparing', icon: <ChefHat className="w-4 h-4" /> },
  { value: 'ready_for_pickup', label: 'Ready for Pickup', icon: <Package className="w-4 h-4" /> },
  { value: 'ready_for_delivery', label: 'Ready for Delivery', icon: <ShoppingBag className="w-4 h-4" /> },
  { value: 'out_for_delivery', label: 'Out for Delivery', icon: <Truck className="w-4 h-4" /> },
  { value: 'delivered', label: 'Delivered', icon: <CheckCircle2 className="w-4 h-4" /> },
  { value: 'completed', label: 'Completed', icon: <BarChart className="w-4 h-4" /> },
  { value: 'cancelled', label: 'Cancelled', icon: <X className="w-4 h-4" /> }
];

// Format currency for display
const formatCurrency = (amount: number) => {
  return `$${(amount / 100).toFixed(2)}`;
};

// Format date for display
const formatDate = (dateString: string) => {
  try {
    const date = parseISO(dateString);
    return format(date, 'MMM d, h:mm a');
  } catch (e) {
    return dateString;
  }
};

interface OrderItem {
  id: number;
  name: string;
  price: number;
  quantity: number;
}

interface OrderCustomer {
  firstName: string;
  lastName: string;
  phone: string;
  email: string;
}

interface OrderDetails {
  type?: 'delivery' | 'takeaway';
  time?: 'asap' | 'scheduled';
  scheduledTime?: string | null;
}

interface Order {
  id: number;
  createdAt: string;
  status: string;
  items: OrderItem[];
  customer: OrderCustomer;
  orderDetails?: OrderDetails;
  subtotal: number;
  deliveryFee: number;
  total: number;
  paymentMethod: string;
  notes: string | null;
}

// Component for status badge
const StatusBadge = ({ status }: { status: string }) => {
  const statusObj = ORDER_STATUSES.find(s => s.value === status) || {
    value: status,
    label: status.charAt(0).toUpperCase() + status.slice(1).replace('_', ' '),
    icon: <AlertTriangle className="w-4 h-4" />
  };

  const getBadgeColor = () => {
    switch (status) {
      case 'confirmed':
        return 'bg-blue-900/30 text-blue-400 border-blue-700/30';
      case 'processing':
        return 'bg-purple-900/30 text-purple-400 border-purple-700/30';
      case 'preparing':
        return 'bg-yellow-900/30 text-yellow-400 border-yellow-700/30';
      case 'ready_for_pickup':
      case 'ready_for_delivery':
        return 'bg-green-900/30 text-green-400 border-green-700/30';
      case 'out_for_delivery':
        return 'bg-cyan-900/30 text-cyan-400 border-cyan-700/30';
      case 'delivered':
      case 'completed':
        return 'bg-emerald-900/30 text-emerald-400 border-emerald-700/30';
      case 'cancelled':
        return 'bg-red-900/30 text-red-400 border-red-700/30';
      default:
        return 'bg-gray-900/30 text-gray-400 border-gray-700/30';
    }
  };

  return (
    <span className={`inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium border ${getBadgeColor()}`}>
      <span className="mr-1">{statusObj.icon}</span>
      {statusObj.label}
    </span>
  );
};

const OrderManager = () => {
  const [orders, setOrders] = useState<Order[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [statusFilter, setStatusFilter] = useState('active');
  const [selectedOrder, setSelectedOrder] = useState<Order | null>(null);
  const [isUpdating, setIsUpdating] = useState(false);

  // Fetch orders on component mount and when status filter changes
  useEffect(() => {
    const fetchOrders = async () => {
      setLoading(true);
      try {
        const response = await fetch(`/api/admin/orders?status=${statusFilter}`);
        if (!response.ok) {
          throw new Error(`Failed to fetch orders: ${response.status}`);
        }
        const data = await response.json();
        setOrders(data);
        setError(null);
      } catch (err) {
        console.error('Error fetching orders:', err);
        setError('Failed to load orders. Please try again.');
      } finally {
        setLoading(false);
      }
    };

    fetchOrders();
  }, [statusFilter]);

  // Determine the next status based on current status and order type
  const getNextStatus = (order: Order): string | null => {
    const { status, orderDetails } = order;
    const orderType = orderDetails?.type;

    switch (status) {
      case 'confirmed':
        return 'preparing';
      case 'preparing':
        return orderType === 'delivery' ? 'ready_for_delivery' : 'ready_for_pickup';
      case 'ready_for_pickup':
        return 'completed';
      case 'ready_for_delivery':
        return 'out_for_delivery';
      case 'out_for_delivery':
        return 'delivered';
      case 'delivered':
        return 'completed';
      default:
        return null;
    }
  };

  // Get human-readable label for the next status
  const getNextStatusLabel = (order: Order): string => {
    const nextStatus = getNextStatus(order);
    if (!nextStatus) return '';

    // Find the status object that matches the next status
    const statusObj = ORDER_STATUSES.find(s => s.value === nextStatus);
    return statusObj ? statusObj.label : nextStatus.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase());
  };

  // Update order status
  const updateOrderStatus = async (orderId: number, newStatus: string) => {
    if (!newStatus) return;

    setIsUpdating(true);
    try {
      const response = await fetch(`/api/admin/orders/${orderId}/status`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ newStatus })
      });

      if (!response.ok) {
        throw new Error(`Failed to update order status: ${response.status}`);
      }

      // Update the orders list with the new status
      setOrders(orders.map(order =>
        order.id === orderId
          ? { ...order, status: newStatus }
          : order
      ));

      // If the selected order is the one being updated, update it as well
      if (selectedOrder && selectedOrder.id === orderId) {
        setSelectedOrder({ ...selectedOrder, status: newStatus });
      }

      // Show success notification
      console.log(`Order #${orderId} status updated to ${newStatus}`);

    } catch (err) {
      console.error('Error updating order status:', err);
      setError('Failed to update order status. Please try again.');
    } finally {
      setIsUpdating(false);
    }
  };

  // Handle order selection
  const handleOrderClick = (order: Order) => {
    setSelectedOrder(order);
  };

  return (
    <AdminLayout>
      <motion.div
        className="space-y-6"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3 }}
      >
        <div className="flex justify-between items-center">
          <h1 className="text-2xl font-bold bg-gradient-to-r from-orange-400 to-red-500 text-transparent bg-clip-text">
            Kitchen Order Manager
          </h1>
          <div className="flex space-x-2">
            <select
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value)}
              className="bg-gray-900 border border-gray-700 rounded-lg px-3 py-2 text-sm text-white focus:ring-cyan-500 focus:border-cyan-500"
            >
              <option value="active">Active Orders</option>
              {ORDER_STATUSES.map(status => (
                <option key={status.value} value={status.value}>
                  {status.label} Only
                </option>
              ))}
              <option value="all">All Orders</option>
            </select>
          </div>
        </div>

        {error && (
          <div className="bg-red-900/40 border border-red-700 text-red-400 p-4 rounded-lg">
            {error}
          </div>
        )}

        {loading ? (
          <div className="flex justify-center items-center py-12">
            <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-cyan-500"></div>
          </div>
        ) : (
          <div className="grid grid-cols-1 xl:grid-cols-[1fr_1.5fr] gap-6">
            {/* Orders List */}
            <div className="bg-gray-900/50 rounded-xl border border-gray-800 overflow-hidden backdrop-blur-sm h-[calc(100vh-220px)] flex flex-col">
              <div className="p-4 border-b border-gray-800 bg-gray-900/80">
                <h2 className="text-lg font-medium text-white flex items-center">
                  <ShoppingBag className="w-5 h-5 mr-2 text-orange-400" />
                  Orders ({orders.length})
                </h2>
              </div>

              <div className="flex-1 overflow-y-auto">
                {orders.length === 0 ? (
                  <div className="flex flex-col items-center justify-center h-full p-6 text-center">
                    <ShoppingBag className="w-16 h-16 text-gray-600 mb-4" />
                    <p className="text-gray-400">No orders found matching your filter.</p>
                    {statusFilter !== 'all' && (
                      <button
                        onClick={() => setStatusFilter('all')}
                        className="mt-4 text-cyan-400 hover:text-cyan-300"
                      >
                        View all orders
                      </button>
                    )}
                  </div>
                ) : (
                  <ul className="divide-y divide-gray-800">
                    {orders.map(order => (
                      <li
                        key={order.id}
                        className={`p-4 hover:bg-gray-800/50 cursor-pointer transition-colors ${selectedOrder?.id === order.id ? 'bg-gray-800/70' : ''}`}
                        onClick={() => handleOrderClick(order)}
                      >
                        <div className="flex justify-between items-center mb-2">
                          <span className="font-medium text-white">Order #{order.id}</span>
                          <StatusBadge status={order.status} />
                        </div>
                        <div className="flex justify-between text-sm text-gray-400 mb-2">
                          <span>{formatDate(order.createdAt)}</span>
                          <span className="font-medium text-white">{formatCurrency(order.total)}</span>
                        </div>
                        <div className="flex justify-between text-sm">
                          <span className="text-gray-400">
                            {order.customer.firstName} {order.customer.lastName}
                          </span>
                          <span className="text-cyan-400 font-medium">
                            {order.orderDetails?.type === 'delivery' ? 'Delivery' : 'Takeaway'}
                          </span>
                        </div>
                      </li>
                    ))}
                  </ul>
                )}
              </div>
            </div>

            {/* Order Details */}
            <div className="bg-gray-900/50 rounded-xl border border-gray-800 overflow-hidden backdrop-blur-sm h-[calc(100vh-220px)] flex flex-col">
              {selectedOrder ? (
                <>
                  <div className="p-4 border-b border-gray-800 bg-gray-900/80 flex justify-between items-center">
                    <h2 className="text-lg font-medium text-white">Order #{selectedOrder.id} Details</h2>
                    <StatusBadge status={selectedOrder.status} />
                  </div>

                  <div className="flex-1 overflow-y-auto p-4 space-y-6">
                    {/* Order Info */}
                    <div className="grid grid-cols-2 gap-4 text-sm">
                      <div className="space-y-1">
                        <p className="text-gray-500">Order Date</p>
                        <p className="text-white">{formatDate(selectedOrder.createdAt)}</p>
                      </div>
                      <div className="space-y-1">
                        <p className="text-gray-500">Order Type</p>
                        <p className="text-white capitalize">{selectedOrder.orderDetails?.type || 'N/A'}</p>
                      </div>
                      <div className="space-y-1">
                        <p className="text-gray-500">Payment Method</p>
                        <p className="text-white capitalize">{selectedOrder.paymentMethod}</p>
                      </div>
                      <div className="space-y-1">
                        <p className="text-gray-500">Timing</p>
                        <p className="text-white capitalize">
                          {selectedOrder.orderDetails?.time === 'asap'
                            ? 'ASAP'
                            : selectedOrder.orderDetails?.time === 'scheduled'
                            ? `Scheduled: ${selectedOrder.orderDetails?.scheduledTime
                                ? formatDate(selectedOrder.orderDetails.scheduledTime)
                                : 'N/A'}`
                            : 'N/A'
                          }
                        </p>
                      </div>
                    </div>

                    {/* Customer Info */}
                    <div>
                      <h3 className="text-md font-medium text-white mb-3">Customer Information</h3>
                      <div className="bg-gray-800/50 rounded-lg p-4 text-sm space-y-2">
                        <p className="text-white">
                          {selectedOrder.customer.firstName} {selectedOrder.customer.lastName}
                        </p>
                        <p className="text-gray-400">{selectedOrder.customer.email}</p>
                        <p className="text-gray-400">{selectedOrder.customer.phone}</p>
                      </div>
                    </div>

                    {/* Order Items */}
                    <div>
                      <h3 className="text-md font-medium text-white mb-3">Order Items</h3>
                      <div className="bg-gray-800/50 rounded-lg overflow-hidden">
                        <ul className="divide-y divide-gray-700">
                          {selectedOrder.items.map(item => (
                            <li key={item.id} className="p-3 flex justify-between">
                              <div className="flex items-start">
                                <span className="text-orange-400 font-medium mr-2">{item.quantity}x</span>
                                <span className="text-white">{item.name}</span>
                              </div>
                              <span className="text-gray-300">{formatCurrency(item.price * item.quantity)}</span>
                            </li>
                          ))}
                        </ul>

                        {/* Order Totals */}
                        <div className="border-t border-gray-700 p-3 space-y-1 text-sm">
                          <div className="flex justify-between">
                            <span className="text-gray-400">Subtotal</span>
                            <span className="text-white">{formatCurrency(selectedOrder.subtotal)}</span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-gray-400">Delivery Fee</span>
                            <span className="text-white">{formatCurrency(selectedOrder.deliveryFee)}</span>
                          </div>
                          <div className="flex justify-between font-medium pt-1">
                            <span className="text-gray-300">Total</span>
                            <span className="text-white">{formatCurrency(selectedOrder.total)}</span>
                          </div>
                        </div>
                      </div>
                    </div>

                    {/* Order Notes */}
                    {selectedOrder.notes && (
                      <div>
                        <h3 className="text-md font-medium text-white mb-3">Special Instructions</h3>
                        <div className="bg-gray-800/50 rounded-lg p-4 text-sm text-gray-300">
                          {selectedOrder.notes}
                        </div>
                      </div>
                    )}

                    {/* Status Management - Simplified with single update button */}
                    <div>
                      <h3 className="text-md font-medium text-white mb-3">Update Order Status</h3>
                      <div className="bg-gray-800/50 rounded-lg p-4">
                        <SingleUpdateButton
                          currentStatus={selectedOrder.status}
                          nextStatus={getNextStatus(selectedOrder)}
                          nextStatusLabel={getNextStatusLabel(selectedOrder)}
                          isUpdating={isUpdating}
                          onUpdate={() => {
                            const nextStatus = getNextStatus(selectedOrder);
                            if (nextStatus) {
                              updateOrderStatus(selectedOrder.id, nextStatus);
                            }
                          }}
                        />
                      </div>
                    </div>
                  </div>
                </>
              ) : (
                <div className="flex flex-col items-center justify-center h-full p-6 text-center">
                  <Package className="w-16 h-16 text-gray-600 mb-4" />
                  <p className="text-gray-400">Select an order to see details.</p>
                </div>
              )}
            </div>
          </div>
        )}
      </motion.div>
    </AdminLayout>
  );
};

export default OrderManager;