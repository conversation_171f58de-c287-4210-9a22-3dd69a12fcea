import { apiRequest } from "@/lib/queryClient";
import { Dish, ContactMessage, Order } from "@shared/schema";

// Fetch all dishes
export const getDishes = async (): Promise<Dish[]> => {
  const response = await apiRequest('GET', '/api/items', undefined);
  return response.json();
};

// Fetch a single dish by ID
export const getDishById = async (id: number): Promise<Dish> => {
  const response = await apiRequest('GET', `/api/dishes/${id}`, undefined);
  return response.json();
};

// Create a new order
export const createOrder = async (orderData: Order): Promise<Order> => {
  const response = await apiRequest('POST', '/api/orders', orderData);
  return response.json();
};

// Send a contact message
export const sendContactMessage = async (messageData: ContactMessage): Promise<{ success: boolean }> => {
  const response = await apiRequest('POST', '/api/contact', messageData);
  return response.json();
};

// Fetch menu categories
export const getCategories = async (): Promise<string[]> => {
  const response = await apiRequest('GET', '/api/categories', undefined);
  return response.json();
};

// Add more API functions as needed
