// Base URL for API
const API_URL = '/api';
// Base URL for admin-specific API
const ADMIN_API_URL = '/api/admin';

const fetchWrapper = async (url: string, options: RequestInit = {}) => {
  const response = await fetch(url, options);

  if (!response.ok) {
    throw new Error(`API request failed: ${response.statusText}`);
  }

  return await response.json();
};

// Types
export interface AdminSettings {
  id: number;
  restaurant_open: boolean;
  business_hours: Record<string, { open: string; close: string; delivery: boolean }>;
  delivery_fee: number;
  estimated_time: string;
}

export interface Category {
  id: number;
  name: string;
  image_url: string;
}

export interface MenuItem {
  id: number;
  name: string;
  description: string;
  price: number;
  image_url: string;
  category_id: number;
  available: boolean;
  category_name?: string;
}

export interface AnalyticsSummary {
  today: number;
  week: number;
  month: number;
  orderCount: number;
}

export interface DailyRevenue {
  date: string;
  total: number;
}

export interface CategoryRevenue {
  category: string;
  total: number;
}

// Admin Settings API
export const getAdminSettings = async (): Promise<AdminSettings> => {
  return await fetchWrapper(`${ADMIN_API_URL}/admin/settings`);
};

export const updateAdminSettings = async (settings: Partial<AdminSettings>): Promise<AdminSettings> => {
  return await fetchWrapper(`${ADMIN_API_URL}/admin/settings`, {
    method: 'PUT',
    body: JSON.stringify(settings),
    headers: {
      'Content-Type': 'application/json',
    },
  });
};

// Categories API
export const getCategories = async (): Promise<Category[]> => {
  return await fetchWrapper(`${API_URL}/categories`);
};

export const createCategory = async (category: Omit<Category, 'id'>): Promise<Category> => {
  return await fetchWrapper(`${API_URL}/categories`, {
    method: 'POST',
    body: JSON.stringify(category),
    headers: {
      'Content-Type': 'application/json',
    },
  });
};

export const updateCategory = async (id: number, category: Partial<Category>): Promise<Category> => {
  return await fetchWrapper(`${API_URL}/categories/${id}`, {
    method: 'PUT',
    body: JSON.stringify(category),
    headers: {
      'Content-Type': 'application/json',
    },
  });
};

export const deleteCategory = async (id: number): Promise<{ message: string; category: Category }> => {
  return await fetchWrapper(`${API_URL}/categories/${id}`, {
    method: 'DELETE',
  });
};

// Menu Items API
export const getMenuItems = async (): Promise<MenuItem[]> => {
  return await fetchWrapper(`${API_URL}/items`);
};

export const createMenuItem = async (item: Omit<MenuItem, 'id'>): Promise<MenuItem> => {
  return await fetchWrapper(`${API_URL}/items`, {
    method: 'POST',
    body: JSON.stringify(item),
    headers: {
      'Content-Type': 'application/json',
    },
  });
};

export const updateMenuItem = async (id: number, item: Partial<MenuItem>): Promise<MenuItem> => {
  return await fetchWrapper(`${API_URL}/items/${id}`, {
    method: 'PUT',
    body: JSON.stringify(item),
    headers: {
      'Content-Type': 'application/json',
    },
  });
};

export const deleteMenuItem = async (id: number): Promise<{ message: string; item: MenuItem }> => {
  return await fetchWrapper(`${ADMIN_API_URL}/items/${id}`, {
    method: 'DELETE',
  });
};

// Analytics API
export const getAnalyticsSummary = async (): Promise<AnalyticsSummary> => {
  return await fetchWrapper(`${ADMIN_API_URL}/analytics/summary`);
};

export const getDailyRevenue = async (): Promise<DailyRevenue[]> => {
  return await fetchWrapper(`${ADMIN_API_URL}/analytics/daily`);
};

export const getCategoryRevenue = async (): Promise<CategoryRevenue[]> => {
  return await fetchWrapper(`${ADMIN_API_URL}/analytics/by-category`);
};