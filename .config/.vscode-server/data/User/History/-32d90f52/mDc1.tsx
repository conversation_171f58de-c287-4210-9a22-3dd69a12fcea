import { useState, useEffect } from 'react';
import { Dish, CustomizationGroup, CustomizationOption } from '@shared/schema';
import { formatCurrency } from '@/lib/utils';
import Button from './Button';
import { useCart } from '@/context/CartContext';

interface CustomizeModalProps {
  dish: Dish;
  isOpen: boolean;
  onClose: () => void;
}

interface CustomizationGroupWithOptions {
  group: CustomizationGroup;
  options: CustomizationOption[];
}

const CustomizeModal = ({ dish, isOpen, onClose }: CustomizeModalProps) => {
  const { addToCart } = useCart();
  const [selectedOptions, setSelectedOptions] = useState<number[]>([]);
  const [totalPrice, setTotalPrice] = useState(dish.price);
  const [customizationGroups, setCustomizationGroups] = useState<CustomizationGroupWithOptions[]>([]);
  const [loading, setLoading] = useState(true);

  // Fetch customizations for this dish when modal opens
  useEffect(() => {
    if (isOpen && dish.id) {
      fetchCustomizations();
    }
  }, [isOpen, dish.id]);

  const fetchCustomizations = async () => {
    try {
      setLoading(true);
      const response = await fetch(`/api/items/${dish.id}/customizations`);
      if (response.ok) {
        const data = await response.json();
        setCustomizationGroups(data);
      }
    } catch (error) {
      console.error('Error fetching customizations:', error);
    } finally {
      setLoading(false);
    }
  };

  // Reset selected options when modal opens with a new dish
  useEffect(() => {
    if (isOpen) {
      setSelectedOptions([]);
      setTotalPrice(dish.price);
    }
  }, [isOpen, dish]);

  // Handle option selection
  const toggleOption = (optionId: string, price: number) => {
    setSelectedOptions(prev => {
      if (prev.includes(optionId)) {
        // Remove option
        setTotalPrice(current => current - price);
        return prev.filter(id => id !== optionId);
      } else {
        // Add option
        setTotalPrice(current => current + price);
        return [...prev, optionId];
      }
    });
  };

  // Handle adding customized item to cart
  const handleAddToCart = () => {
    // Create a customized dish with selected options information
    const customizedDish: Dish = {
      ...dish,
      price: totalPrice,
      description: `${dish.description} (Customized with ${selectedOptions.length} extras)`
    };

    addToCart(customizedDish);
    onClose();
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-80 z-50 flex items-center justify-center p-4">
      <div className="bg-gray-900 rounded-lg max-w-4xl w-full max-h-[90vh] overflow-y-auto border border-gray-800">
        {/* Header with close button */}
        <div className="flex justify-between items-center p-5 border-b border-gray-800">
          <h2 className="font-playfair text-2xl font-bold text-white">{dish.name}</h2>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-secondary transition-colors"
            aria-label="Close modal"
          >
            <i className="fas fa-times text-xl"></i>
          </button>
        </div>

        {/* Content */}
        <div className="p-5">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-8">
            {/* Dish image */}
            <div className="rounded-lg overflow-hidden">
              <img
                src={(dish as any).imageUrl || (dish as any).image_url || 'https://images.unsplash.com/photo-1555939594-58d7cb561ad1?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&h=400'}
                alt={dish.name}
                className="w-full h-64 object-cover"
              />
            </div>

            {/* Dish description */}
            <div>
              <p className="font-poppins text-gray-300 mb-4">{dish.description}</p>
              <div className="flex items-center mb-2">
                <span className="text-yellow-400 mr-1"><i className="fas fa-star"></i></span>
                <span className="font-poppins text-sm text-gray-300">
                  {(dish.rating || 0) / 10} ({dish.reviews || 0} reviews)
                </span>
              </div>
              <div className="font-poppins text-xl font-medium text-accent mb-4">
                Base price: {formatCurrency(dish.price)}
              </div>

              <p className="font-poppins text-gray-400 text-sm mb-4">
                Customize your dish with the options below. Select or deselect options to build your perfect meal.
              </p>
            </div>
          </div>

          {/* Customization Options */}
          <div className="space-y-8">
            {customizationGroups.map(group => (
              <div key={group.id} className="border-t border-gray-800 pt-4">
                <h3 className="font-playfair text-xl font-medium text-white mb-4">{group.title}</h3>

                <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-3">
                  {group.options.map(option => {
                    const isSelected = selectedOptions.includes(option.id);

                    return (
                      <button
                        key={option.id}
                        onClick={() => toggleOption(option.id, option.price)}
                        className={`flex items-center p-3 rounded-md border transition-all duration-300 ${
                          isSelected
                            ? 'border-secondary box-glow-pink bg-gray-800'
                            : 'border-gray-700 hover:border-gray-600'
                        }`}
                      >
                        <div className="text-xl mr-3">{option.icon}</div>
                        <div className="flex-1 text-left">
                          <div className="font-poppins text-white">{option.name}</div>
                          <div className="font-poppins text-xs text-gray-400">
                            {option.price > 0 ? `+${formatCurrency(option.price)}` : 'Included'}
                          </div>
                        </div>
                        <div className={`w-5 h-5 rounded-full flex items-center justify-center ml-2 ${
                          isSelected ? 'bg-secondary text-black' : 'bg-gray-700'
                        }`}>
                          {isSelected && <i className="fas fa-check text-xs"></i>}
                        </div>
                      </button>
                    );
                  })}
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Footer with total price and actions */}
        <div className="bg-gray-800 p-5 flex flex-col md:flex-row items-center justify-between sticky bottom-0">
          <div className="mb-4 md:mb-0">
            <div className="font-poppins text-gray-300 text-sm">Total Price</div>
            <div className="font-playfair text-2xl font-bold text-accent text-glow-green animate-glow">
              {formatCurrency(totalPrice)}
            </div>
          </div>

          <div className="flex space-x-4">
            <Button
              variant="outline-accent"
              size="md"
              onClick={onClose}
            >
              Cancel
            </Button>

            <Button
              variant="secondary"
              size="md"
              onClick={handleAddToCart}
            >
              Add to Cart
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CustomizeModal;