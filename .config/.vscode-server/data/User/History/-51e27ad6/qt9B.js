// Simple script to fix the orders table by adding the missing order_details column
import { Pool, neonConfig } from '@neondatabase/serverless';
import ws from 'ws';
import dotenv from 'dotenv';

// Configure WebSocket for Neon
neonConfig.webSocketConstructor = ws;

dotenv.config();

const connectionString = process.env.DATABASE_URL;

if (!connectionString) {
  console.error("DATABASE_URL must be set");
  process.exit(1);
}

async function fixOrdersTable() {
  const client = new Pool({ connectionString });

  try {
    console.log("Connecting to database...");

    // Add the orderDetails column
    console.log("Adding order_details column...");
    await client.query(`ALTER TABLE orders ADD COLUMN IF NOT EXISTS order_details JSONB;`);

    // Update existing orders to have default orderDetails
    console.log("Setting default values for existing orders...");
    await client.query(`
      UPDATE orders
      SET order_details = '{"type": "delivery", "time": "asap", "scheduledTime": null}'::jsonb
      WHERE order_details IS NULL;
    `);

    // Make the column NOT NULL after setting default values
    console.log("Making order_details column NOT NULL...");
    await client.query(`ALTER TABLE orders ALTER COLUMN order_details SET NOT NULL;`);

    console.log("✅ Orders table fixed successfully!");

    // Verify the fix
    const result = await client.query(`
      SELECT column_name, data_type, is_nullable
      FROM information_schema.columns
      WHERE table_name = 'orders' AND column_name = 'order_details';
    `);

    if (result.rows.length > 0) {
      console.log("✅ Verification: order_details column exists:", result.rows[0]);
    } else {
      console.log("❌ Verification failed: order_details column not found");
    }

  } catch (error) {
    console.error("❌ Error fixing orders table:", error);
  } finally {
    await client.end();
  }
}

fixOrdersTable();
