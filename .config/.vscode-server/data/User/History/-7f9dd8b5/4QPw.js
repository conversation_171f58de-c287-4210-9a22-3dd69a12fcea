// Simple test script to test customization API endpoints
// Using built-in fetch (Node.js 18+)

const BASE_URL = 'http://localhost:5000';

async function testCustomizationAPI() {
  try {
    console.log('Testing customization API endpoints...');

    // Test getting customization groups
    console.log('\n1. Testing GET /api/admin/customization-groups');
    const groupsResponse = await fetch(`${BASE_URL}/api/admin/customization-groups`);
    console.log('Status:', groupsResponse.status);

    if (groupsResponse.ok) {
      const groups = await groupsResponse.json();
      console.log('Groups:', groups);
    } else {
      const error = await groupsResponse.text();
      console.log('Error:', error);
    }

    // Test getting customization options
    console.log('\n2. Testing GET /api/admin/customization-options');
    const optionsResponse = await fetch(`${BASE_URL}/api/admin/customization-options`);
    console.log('Status:', optionsResponse.status);

    if (optionsResponse.ok) {
      const options = await optionsResponse.json();
      console.log('Options:', options);
    } else {
      const error = await optionsResponse.text();
      console.log('Error:', error);
    }

    // Test public customizations endpoint for a menu item
    console.log('\n3. Testing GET /api/items/1/customizations');
    const itemCustomizationsResponse = await fetch(`${BASE_URL}/api/items/1/customizations`);
    console.log('Status:', itemCustomizationsResponse.status);

    if (itemCustomizationsResponse.ok) {
      const customizations = await itemCustomizationsResponse.json();
      console.log('Item customizations:', customizations);
    } else {
      const error = await itemCustomizationsResponse.text();
      console.log('Error:', error);
    }

  } catch (error) {
    console.error('Test failed:', error);
  }
}

testCustomizationAPI();
