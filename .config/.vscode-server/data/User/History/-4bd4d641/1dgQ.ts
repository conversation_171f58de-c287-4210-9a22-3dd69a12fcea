import { drizzle } from "drizzle-orm/neon-serverless";
import { migrate } from "drizzle-orm/neon-serverless/migrator";
import { Pool } from "@neondatabase/serverless";

// Database connection for migrations
const connectionString = process.env.DATABASE_URL || "postgresql://localhost:5432/restaurant_db";

// Create a connection for migrations
const migrationClient = postgres(connectionString, { max: 1 });
const db = drizzle(migrationClient);

async function runMigrations() {
  try {
    console.log("Running database migrations...");

    // Run migrations from the drizzle folder
    await migrate(db, { migrationsFolder: "./drizzle" });

    console.log("Migrations completed successfully!");
  } catch (error) {
    console.error("Migration failed:", error);
    process.exit(1);
  } finally {
    await migrationClient.end();
  }
}

// Run migrations if this file is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  runMigrations();
}

export { runMigrations };
