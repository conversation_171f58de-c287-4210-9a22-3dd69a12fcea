import { drizzle } from "drizzle-orm/postgres-js";
import { migrate } from "drizzle-orm/postgres-js/migrator";
import postgres from "postgres";

// Database connection for migrations
const connectionString = process.env.DATABASE_URL || "postgresql://localhost:5432/restaurant_db";

// Create a connection for migrations
const migrationClient = postgres(connectionString, { max: 1 });
const db = drizzle(migrationClient);

async function runMigrations() {
  try {
    console.log("Running database migrations...");

    // Run migrations from the drizzle folder
    await migrate(db, { migrationsFolder: "./drizzle" });

    console.log("Migrations completed successfully!");
  } catch (error) {
    console.error("Migration failed:", error);
    process.exit(1);
  } finally {
    await migrationClient.end();
  }
}

// Run migrations if this file is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  runMigrations();
}

export { runMigrations };
