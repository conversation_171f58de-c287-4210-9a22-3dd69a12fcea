# Security
- Remove 'Staff Login' from public header navigation for security reasons while maintaining the functionality through a more secure method.

# Website Control
- Ensure admin settings properly control the restaurant website with real-time updates to the public-facing site.

# Data Persistence
- User prefers persistent database storage (SQLite, PostgreSQL, or MySQL) over in-memory storage for restaurant management systems, with proper schema, migrations, and full CRUD persistence for all entities including menu items, orders, and admin settings.