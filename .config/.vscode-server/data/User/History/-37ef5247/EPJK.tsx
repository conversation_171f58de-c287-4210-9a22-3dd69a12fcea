import { useState, useEffect } from "react";
import { motion } from "framer-motion";
import AdminLayout from "./AdminLayout";
import { getAdminSettings, updateAdminSettings, AdminSettings as SettingsType } from "@/api/adminApi";
import { useToast } from "@/hooks/use-toast";
import { useRestaurantStatus } from "@/context/RestaurantStatusContext";

// Days of the week for business hours
const DAYS_OF_WEEK = [
  "Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday", "Sunday"
];

// Map the business hours from API format to our component format
const formatBusinessHours = (data: SettingsType | null) => {
  if (!data || !data.business_hours) return {};

  const hours: Record<string, { open: boolean; deliveryAvailable: boolean; openTime: string; closeTime: string }> = {};

  // Convert from API format to our component format
  for (const day of DAYS_OF_WEEK) {
    const dayLower = day.toLowerCase();
    if (data.business_hours[dayLower]) {
      hours[dayLower] = {
        open: true, // If it exists in the data, it's open
        deliveryAvailable: data.business_hours[dayLower].delivery,
        openTime: data.business_hours[dayLower].open,
        closeTime: data.business_hours[dayLower].close
      };
    } else {
      // Default values if not present
      hours[dayLower] = {
        open: false,
        deliveryAvailable: false,
        openTime: "10:00",
        closeTime: "22:00"
      };
    }
  }

  return hours;
};

const Settings = () => {
  const { toast } = useToast();

  // Restaurant status state
  const [isOpen, setIsOpen] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const [saveSuccess, setSaveSuccess] = useState(false);
  const [isLoading, setIsLoading] = useState(true);

  // Initialize business hours with default values for all days
  const defaultBusinessHours = DAYS_OF_WEEK.reduce((acc, day) => {
    acc[day.toLowerCase()] = {
      open: true,
      deliveryAvailable: true,
      openTime: "10:00",
      closeTime: "22:00"
    };
    return acc;
  }, {} as Record<string, { open: boolean; deliveryAvailable: boolean; openTime: string; closeTime: string }>);

  // Business hours state - initialize with default hours, will update from API
  const [businessHours, setBusinessHours] = useState<Record<string, { open: boolean; deliveryAvailable: boolean; openTime: string; closeTime: string }>>(defaultBusinessHours);

  // Delivery settings
  const [deliveryFee, setDeliveryFee] = useState(49);
  const [deliveryTimeMin, setDeliveryTimeMin] = useState(25);
  const [deliveryTimeMax, setDeliveryTimeMax] = useState(35);
  const [estimatedTime, setEstimatedTime] = useState("25-35 min");

  // Load settings from API
  useEffect(() => {
    const loadSettings = async () => {
      setIsLoading(true);

      let data;
      // Try to load settings from API
      try {
        const response = await fetch('/api/admin/settings');
        data = await response.json();
        console.log("Settings loaded from API:", data);
      } catch (error) {
        console.log("Failed to load settings from API, using defaults");
        // Use default settings if the API call fails
        data = {
          id: 1,
          restaurant_open: true,
          business_hours: {
            monday: { open: "10:00", close: "22:00", delivery: true },
            tuesday: { open: "10:00", close: "22:00", delivery: true },
            wednesday: { open: "10:00", close: "22:00", delivery: true },
            thursday: { open: "10:00", close: "22:00", delivery: true },
            friday: { open: "10:00", close: "23:00", delivery: true },
            saturday: { open: "10:00", close: "23:00", delivery: true },
            sunday: { open: "12:00", close: "21:00", delivery: true }
          },
          delivery_fee: 49,
          estimated_time: "25-35 min"
        };

        toast({
          title: "Using Default Settings",
          description: "Could not connect to settings API, using defaults",
          variant: "default"
        });
      }

      try {
        // Update state with the data (either from API or defaults)
        setIsOpen(data.restaurant_open);
        setBusinessHours(formatBusinessHours(data));
        setDeliveryFee(data.delivery_fee);
        setEstimatedTime(data.estimated_time);

        // Parse time range from string like "25-35 min"
        const timeMatch = data.estimated_time.match(/(\d+)-(\d+)/);
        if (timeMatch) {
          setDeliveryTimeMin(parseInt(timeMatch[1]));
          setDeliveryTimeMax(parseInt(timeMatch[2]));
        }
      } catch (error) {
        console.error('Error processing settings data:', error);
        toast({
          title: "Error",
          description: "Failed to process restaurant settings",
          variant: "destructive"
        });
      } finally {
        setIsLoading(false);
      }
    };

    loadSettings();
  }, [toast]);

  const handleDayToggle = (day: string) => {
    setBusinessHours(prev => ({
      ...prev,
      [day.toLowerCase()]: {
        ...prev[day.toLowerCase()],
        open: !prev[day.toLowerCase()].open
      }
    }));
  };

  const handleDeliveryToggle = (day: string) => {
    setBusinessHours(prev => ({
      ...prev,
      [day.toLowerCase()]: {
        ...prev[day.toLowerCase()],
        deliveryAvailable: !prev[day.toLowerCase()].deliveryAvailable
      }
    }));
  };

  const handleTimeChange = (day: string, field: 'openTime' | 'closeTime', value: string) => {
    setBusinessHours(prev => ({
      ...prev,
      [day.toLowerCase()]: {
        ...prev[day.toLowerCase()],
        [field]: value
      }
    }));
  };

  const handleSave = async () => {
    setIsSaving(true);

    try {
      // Convert the business hours format to API format
      const businessHoursForApi: Record<string, { open: string; close: string; delivery: boolean }> = {};

      for (const day of DAYS_OF_WEEK) {
        const dayLower = day.toLowerCase();
        const dayData = businessHours[dayLower];

        // Only include open days in the API data
        if (dayData && dayData.open) {
          businessHoursForApi[dayLower] = {
            open: dayData.openTime,
            close: dayData.closeTime,
            delivery: dayData.deliveryAvailable
          };
        }
      }

      // Prepare data for API
      const settingsData = {
        restaurant_open: isOpen,
        business_hours: businessHoursForApi,
        delivery_fee: deliveryFee,
        estimated_time: `${deliveryTimeMin}-${deliveryTimeMax} min`
      };

      // Save to API
      await fetch('/api/admin/settings', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(settingsData),
      });

      // Show success message
      toast({
        title: "Settings saved",
        description: "Your restaurant settings have been updated",
        variant: "default"
      });

      setSaveSuccess(true);
      setTimeout(() => setSaveSuccess(false), 3000);
    } catch (error) {
      console.error('Error saving settings:', error);
      toast({
        title: "Error",
        description: "Failed to save restaurant settings",
        variant: "destructive"
      });
    } finally {
      setIsSaving(false);
    }
  };

  return (
    <AdminLayout>
      <div className="max-w-5xl mx-auto">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
        >
          <h1 className="text-3xl font-bold mb-2 bg-gradient-to-r from-purple-400 via-cyan-400 to-purple-400 text-transparent bg-clip-text">
            Restaurant Settings
          </h1>
          <p className="text-gray-400 mb-8">
            Manage your restaurant's operational settings and business hours.
          </p>

          {/* Restaurant Status */}
          <div className="mb-10 bg-gray-900/50 backdrop-blur-sm rounded-xl p-6 border border-gray-800">
            <h2 className="text-xl font-bold mb-4 text-white">Restaurant Status</h2>

            <div className="flex items-center">
              <div className="mr-4">
                <label className="relative inline-flex items-center cursor-pointer">
                  <input
                    type="checkbox"
                    checked={isOpen}
                    onChange={() => setIsOpen(!isOpen)}
                    className="sr-only peer"
                  />
                  <div className={`w-14 h-7 rounded-full peer transition-all
                                  ${isOpen
                                    ? 'bg-gradient-to-r from-green-600 to-green-400'
                                    : 'bg-gradient-to-r from-red-600 to-red-400'}
                                  peer-focus:outline-none peer-focus:ring-2 peer-focus:ring-cyan-300
                                  peer-checked:after:translate-x-7 after:content-[''] after:absolute
                                  after:top-0.5 after:left-[2px] after:bg-white after:border-gray-300
                                  after:border after:rounded-full after:h-6 after:w-6 after:transition-all
                                  peer-checked:bg-green-600`}>
                  </div>
                  <span className="ml-3 text-lg text-white">
                    Restaurant is {isOpen ? 'OPEN' : 'CLOSED'}
                  </span>
                </label>
              </div>

              <div className={`ml-3 p-2 rounded text-sm
                              ${isOpen ? 'bg-green-900/50 text-green-300' : 'bg-red-900/50 text-red-300'}`}>
                {isOpen
                  ? '✓ Customers can place orders'
                  : '✕ Customers cannot place orders'}
              </div>
            </div>
          </div>

          {/* Business Hours */}
          <div className="mb-10 bg-gray-900/50 backdrop-blur-sm rounded-xl p-6 border border-gray-800">
            <h2 className="text-xl font-bold mb-4 text-white">Business Hours</h2>

            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="border-b border-gray-800">
                    <th className="py-3 text-left text-gray-400">Day</th>
                    <th className="py-3 text-left text-gray-400">Status</th>
                    <th className="py-3 text-left text-gray-400">Hours</th>
                    <th className="py-3 text-left text-gray-400">Delivery Available</th>
                  </tr>
                </thead>
                <tbody>
                  {DAYS_OF_WEEK.map((day) => {
                    const dayData = businessHours[day.toLowerCase()];
                    return (
                      <tr key={day} className="border-b border-gray-800/50">
                        <td className="py-4">
                          <span className="font-medium text-white">{day}</span>
                        </td>
                        <td className="py-4">
                          <label className="relative inline-flex items-center cursor-pointer">
                            <input
                              type="checkbox"
                              checked={dayData.open}
                              onChange={() => handleDayToggle(day.toLowerCase())}
                              className="sr-only peer"
                            />
                            <div className="w-11 h-6 rounded-full peer
                                        bg-gray-700 peer-checked:bg-cyan-900
                                        peer-focus:outline-none peer-focus:ring-2 peer-focus:ring-cyan-300
                                        peer-checked:after:translate-x-5 after:content-[''] after:absolute
                                        after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300
                                        after:border after:rounded-full after:h-5 after:w-5 after:transition-all">
                            </div>
                            <span className="ml-3 text-sm text-gray-300">
                              {dayData.open ? 'Open' : 'Closed'}
                            </span>
                          </label>
                        </td>
                        <td className="py-4">
                          <div className="flex items-center space-x-2">
                            <input
                              type="time"
                              value={dayData.openTime}
                              onChange={(e) => handleTimeChange(day.toLowerCase(), 'openTime', e.target.value)}
                              disabled={!dayData.open}
                              className="bg-gray-800 border border-gray-700 text-white rounded px-3 py-2
                                      focus:outline-none focus:ring-2 focus:ring-cyan-500 focus:border-transparent
                                      disabled:opacity-50 disabled:cursor-not-allowed"
                            />
                            <span className="text-gray-400">to</span>
                            <input
                              type="time"
                              value={dayData.closeTime}
                              onChange={(e) => handleTimeChange(day.toLowerCase(), 'closeTime', e.target.value)}
                              disabled={!dayData.open}
                              className="bg-gray-800 border border-gray-700 text-white rounded px-3 py-2
                                      focus:outline-none focus:ring-2 focus:ring-cyan-500 focus:border-transparent
                                      disabled:opacity-50 disabled:cursor-not-allowed"
                            />
                          </div>
                        </td>
                        <td className="py-4">
                          <label className="relative inline-flex items-center cursor-pointer">
                            <input
                              type="checkbox"
                              checked={dayData.deliveryAvailable}
                              onChange={() => handleDeliveryToggle(day.toLowerCase())}
                              disabled={!dayData.open}
                              className="sr-only peer"
                            />
                            <div className="w-11 h-6 rounded-full peer
                                        bg-gray-700 peer-checked:bg-purple-800
                                        peer-focus:outline-none peer-focus:ring-2 peer-focus:ring-purple-300
                                        peer-checked:after:translate-x-5 after:content-[''] after:absolute
                                        after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300
                                        after:border after:rounded-full after:h-5 after:w-5 after:transition-all
                                        disabled:opacity-50 disabled:cursor-not-allowed">
                            </div>
                            <span className="ml-3 text-sm text-gray-300">
                              {dayData.deliveryAvailable ? 'Yes' : 'No'}
                            </span>
                          </label>
                        </td>
                      </tr>
                    );
                  })}
                </tbody>
              </table>
            </div>
          </div>

          {/* Delivery Settings */}
          <div className="mb-10 bg-gray-900/50 backdrop-blur-sm rounded-xl p-6 border border-gray-800">
            <h2 className="text-xl font-bold mb-4 text-white">Delivery Settings</h2>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-gray-300 mb-2">Delivery Fee (NOK)</label>
                <input
                  type="number"
                  value={deliveryFee}
                  onChange={(e) => setDeliveryFee(parseInt(e.target.value))}
                  className="bg-gray-800 border border-gray-700 text-white rounded px-3 py-2 w-full
                           focus:outline-none focus:ring-2 focus:ring-cyan-500 focus:border-transparent"
                />
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-gray-300 mb-2">Est. Time Min (min)</label>
                  <input
                    type="number"
                    value={deliveryTimeMin}
                    onChange={(e) => setDeliveryTimeMin(parseInt(e.target.value))}
                    className="bg-gray-800 border border-gray-700 text-white rounded px-3 py-2 w-full
                             focus:outline-none focus:ring-2 focus:ring-cyan-500 focus:border-transparent"
                  />
                </div>

                <div>
                  <label className="block text-gray-300 mb-2">Est. Time Max (min)</label>
                  <input
                    type="number"
                    value={deliveryTimeMax}
                    onChange={(e) => setDeliveryTimeMax(parseInt(e.target.value))}
                    className="bg-gray-800 border border-gray-700 text-white rounded px-3 py-2 w-full
                             focus:outline-none focus:ring-2 focus:ring-cyan-500 focus:border-transparent"
                  />
                </div>
              </div>

              <div className="md:col-span-2">
                <p className="text-gray-400 text-sm">
                  This will be displayed to customers as:
                  <span className="ml-2 text-cyan-400 font-medium">
                    Delivery Time: {deliveryTimeMin}–{deliveryTimeMax} min, Fee: {deliveryFee} NOK
                  </span>
                </p>
              </div>
            </div>
          </div>

          {/* Save Button */}
          <div className="flex justify-end">
            <button
              onClick={handleSave}
              disabled={isSaving}
              className="relative overflow-hidden rounded-lg px-6 py-3 group"
            >
              {/* Button Background */}
              <span className="absolute inset-0 w-full h-full bg-gradient-to-r from-cyan-700 to-cyan-600"></span>

              {/* Button Glow Effect */}
              <span className={`absolute inset-0 w-full h-full transition-all duration-300
                               bg-gradient-to-r from-cyan-600 via-cyan-400 to-cyan-600
                               opacity-0 group-hover:opacity-50 group-hover:blur-lg
                               ${saveSuccess ? 'opacity-50 blur-lg' : ''}`}>
              </span>

              {/* Button Border */}
              <span className="absolute inset-0 w-full h-full border border-cyan-500 rounded-lg"></span>

              {/* Button Text */}
              <span className="relative z-10 flex items-center justify-center text-white font-medium">
                {isSaving ? (
                  <>
                    <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    Saving...
                  </>
                ) : saveSuccess ? (
                  <>
                    <svg className="w-4 h-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                    </svg>
                    Saved!
                  </>
                ) : (
                  'Save Settings'
                )}
              </span>
            </button>
          </div>
        </motion.div>
      </div>
    </AdminLayout>
  );
};

export default Settings;