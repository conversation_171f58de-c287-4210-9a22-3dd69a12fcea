import { Pool } from 'pg';

async function testDatabase() {
  console.log('Starting database test...');
  console.log('DATABASE_URL:', process.env.DATABASE_URL ? 'Set' : 'Not set');

  const pool = new Pool({
    connectionString: process.env.DATABASE_URL,
  });

  try {
    // Check if restaurant_settings table exists
    const tableCheck = await pool.query(`
      SELECT EXISTS (
        SELECT FROM information_schema.tables
        WHERE table_schema = 'public'
        AND table_name = 'restaurant_settings'
      );
    `);

    console.log('Table exists:', tableCheck.rows[0].exists);

    if (tableCheck.rows[0].exists) {
      // Get current settings
      const settings = await pool.query('SELECT * FROM restaurant_settings ORDER BY id');
      console.log('Current settings:', JSON.stringify(settings.rows, null, 2));

      // Try to update delivery fee
      const updateResult = await pool.query(
        'UPDATE restaurant_settings SET delivery_fee = $1 WHERE id = 1 RETURNING *',
        [100]
      );
      console.log('Update result:', JSON.stringify(updateResult.rows, null, 2));

      // Check settings again
      const newSettings = await pool.query('SELECT * FROM restaurant_settings ORDER BY id');
      console.log('Settings after update:', JSON.stringify(newSettings.rows, null, 2));
    }
  } catch (error) {
    console.error('Database error:', error.message);
  } finally {
    await pool.end();
  }
}

testDatabase();
