import { db } from "./db";
import {
  users, categories, menuItems, orders, contactMessages,
  customizationGroups, customizationOptions, itemCustomizationMap
} from "@shared/schema";
import * as crypto from "crypto";

// Password hashing utility functions
async function hashPassword(password: string): Promise<string> {
  const salt = crypto.randomBytes(16).toString("hex");
  return new Promise((resolve, reject) => {
    crypto.scrypt(password, salt, 64, (err, derivedKey) => {
      if (err) reject(err);
      resolve(`${derivedKey.toString("hex")}.${salt}`);
    });
  });
}

async function initializeDatabase() {
  try {
    console.log("Initializing database...");

    // Check if we have any categories, if not, initialize with sample data
    const existingCategories = await db.select().from(categories).limit(1);

    if (existingCategories.length === 0) {
      console.log("Database is empty. Seeding with initial data...");
      await seedDatabase();
    } else {
      console.log("Database already contains data. Skipping seeding.");
    }

    console.log("Database initialization completed!");
  } catch (error) {
    console.error("Error initializing database:", error);
    throw error;
  }
}

async function seedDatabase() {
  try {
    // Insert sample categories
    const sampleCategories = [
      {
        name: "Signature BBQ",
        imageUrl: "https://images.unsplash.com/photo-1529193591184-b1d58069ecdd?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&h=400",
      },
      {
        name: "Starters",
        imageUrl: "https://images.unsplash.com/photo-1527477396000-e27163b481c2?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&h=400",
      },
      {
        name: "Main Course",
        imageUrl: "https://images.unsplash.com/photo-1519708227418-c8fd9a32b7a2?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&h=400",
      },
      {
        name: "Desserts",
        imageUrl: "https://images.unsplash.com/photo-1624353365286-3f8d62daad51?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&h=400",
      },
      {
        name: "BurgerZ",
        imageUrl: "https://images.unsplash.com/photo-1568901346375-23c9450c58cd?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&h=400",
      },
      {
        name: "SandwichZ",
        imageUrl: "https://images.unsplash.com/photo-1621996346565-e3dbc646d9a9?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&h=400",
      }
    ];

    const insertedCategories = await db.insert(categories).values(sampleCategories).returning();
    console.log(`Inserted ${insertedCategories.length} categories`);

    // Insert sample menu items
    const sampleMenuItems = [
      {
        name: "Smoked Beef Brisket",
        description: "24-hour smoked premium beef brisket with our signature spice rub, served with homemade BBQ sauce and pickled vegetables.",
        price: 329,
        imageUrl: "https://images.unsplash.com/photo-1529193591184-b1d58069ecdd?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&h=400",
        categoryId: insertedCategories[0].id, // Signature BBQ
        available: true,
        rating: 49,
        reviews: 120
      },
      {
        name: "BBQ Pulled Pork",
        description: "Slow-cooked pulled pork shoulder with tangy BBQ sauce, served on a brioche bun with coleslaw.",
        price: 269,
        imageUrl: "https://images.unsplash.com/photo-1544025162-d76694265947?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&h=400",
        categoryId: insertedCategories[0].id, // Signature BBQ
        available: true,
        rating: 46,
        reviews: 89
      },
      {
        name: "Loaded Nachos",
        description: "Crispy tortilla chips topped with melted cheese, jalapeños, sour cream, and guacamole.",
        price: 189,
        imageUrl: "https://images.unsplash.com/photo-1513456852971-30c0b8199d4d?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&h=400",
        categoryId: insertedCategories[1].id, // Starters
        available: true,
        rating: 42,
        reviews: 67
      },
      {
        name: "BBQ Wings",
        description: "Crispy chicken wings tossed in our signature BBQ sauce, served with ranch dipping sauce.",
        price: 219,
        imageUrl: "https://images.unsplash.com/photo-1527477396000-e27163b481c2?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&h=400",
        categoryId: insertedCategories[1].id, // Starters
        available: true,
        rating: 44,
        reviews: 78
      },
      {
        name: "Classic Cheeseburger",
        description: "Juicy beef patty with melted cheese, lettuce, tomato, and our special sauce on a toasted bun.",
        price: 249,
        imageUrl: "https://images.unsplash.com/photo-1568901346375-23c9450c58cd?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&h=400",
        categoryId: insertedCategories[4].id, // BurgerZ
        available: true,
        rating: 47,
        reviews: 156
      },
      {
        name: "BBQ Sandwich",
        description: "Tender pulled pork with BBQ sauce, pickles, and coleslaw on a fresh sandwich roll.",
        price: 229,
        imageUrl: "https://images.unsplash.com/photo-1621996346565-e3dbc646d9a9?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&h=400",
        categoryId: insertedCategories[5].id, // SandwichZ
        available: true,
        rating: 45,
        reviews: 92
      }
    ];

    const insertedMenuItems = await db.insert(menuItems).values(sampleMenuItems).returning();
    console.log(`Inserted ${insertedMenuItems.length} menu items`);

    // Insert customization groups
    const sampleGroups = [
      { title: "Saus & Topping" },
      { title: "Ost" },
      { title: "Ekstra Produkter" },
      { title: "Grønnsaker" }
    ];

    const insertedGroups = await db.insert(customizationGroups).values(sampleGroups).returning();
    console.log(`Inserted ${insertedGroups.length} customization groups`);

    // Insert customization options
    const sampleOptions = [
      { name: "BBQ Sauce", extraPrice: 10, imageUrl: "", groupId: insertedGroups[0].id },
      { name: "Hot Sauce", extraPrice: 10, imageUrl: "", groupId: insertedGroups[0].id },
      { name: "Mayo", extraPrice: 10, imageUrl: "", groupId: insertedGroups[0].id },
      { name: "Honey Mustard", extraPrice: 15, imageUrl: "", groupId: insertedGroups[0].id },

      { name: "Cheddar", extraPrice: 15, imageUrl: "", groupId: insertedGroups[1].id },
      { name: "Blue Cheese", extraPrice: 20, imageUrl: "", groupId: insertedGroups[1].id },
      { name: "Mozzarella", extraPrice: 15, imageUrl: "", groupId: insertedGroups[1].id },

      { name: "Bacon", extraPrice: 25, imageUrl: "", groupId: insertedGroups[2].id },
      { name: "Double Meat", extraPrice: 40, imageUrl: "", groupId: insertedGroups[2].id },
      { name: "Fried Egg", extraPrice: 15, imageUrl: "", groupId: insertedGroups[2].id },

      { name: "Lettuce", extraPrice: 0, imageUrl: "", groupId: insertedGroups[3].id },
      { name: "Tomato", extraPrice: 0, imageUrl: "", groupId: insertedGroups[3].id },
      { name: "Onion", extraPrice: 0, imageUrl: "", groupId: insertedGroups[3].id },
      { name: "Cucumber", extraPrice: 0, imageUrl: "", groupId: insertedGroups[3].id },
      { name: "Avocado", extraPrice: 20, imageUrl: "", groupId: insertedGroups[3].id }
    ];

    const insertedOptions = await db.insert(customizationOptions).values(sampleOptions).returning();
    console.log(`Inserted ${insertedOptions.length} customization options`);

    // Create a default admin user
    const hashedPassword = await hashPassword("admin123");
    const adminUser = {
      username: "admin",
      password: hashedPassword,
      role: "admin" as const
    };

    const insertedUsers = await db.insert(users).values([adminUser]).returning();
    console.log(`Created admin user: ${insertedUsers[0].username}`);

    console.log("Database seeded successfully!");
  } catch (error) {
    console.error("Error seeding database:", error);
    throw error;
  }
}

// Run initialization if this file is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  initializeDatabase()
    .then(() => {
      console.log("Database initialization completed!");
      process.exit(0);
    })
    .catch((error) => {
      console.error("Database initialization failed:", error);
      process.exit(1);
    });
}

export { initializeDatabase, seedDatabase };
