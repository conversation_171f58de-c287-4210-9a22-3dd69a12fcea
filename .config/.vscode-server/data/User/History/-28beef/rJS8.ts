import { storage } from "./storage";

async function testDatabaseOperations() {
  console.log("Testing database operations...");

  try {
    // Test category operations
    console.log("\n=== Testing Category Operations ===");

    // Get all categories
    const categories = await storage.getAllMenuCategories();
    console.log(`Found ${categories.length} categories`);

    if (categories.length > 0) {
      console.log("First category:", categories[0]);

      // Test updating a category
      const updatedCategory = await storage.updateMenuCategory(categories[0].id, {
        name: categories[0].name + " (Updated)"
      });
      console.log("Updated category:", updatedCategory);

      // Revert the update
      await storage.updateMenuCategory(categories[0].id, {
        name: categories[0].name.replace(" (Updated)", "")
      });
    }

    // Test menu item operations
    console.log("\n=== Testing Menu Item Operations ===");

    // Get all menu items
    const menuItems = await storage.getAllMenuItems();
    console.log(`Found ${menuItems.length} menu items`);

    if (menuItems.length > 0) {
      console.log("First menu item:", menuItems[0]);

      // Test getting items by category
      if (categories.length > 0) {
        const itemsByCategory = await storage.getMenuItemsByCategory(categories[0].id);
        console.log(`Found ${itemsByCategory.length} items in category "${categories[0].name}"`);
      }
    }

    // Test creating and deleting a test category
    console.log("\n=== Testing Create/Delete Operations ===");

    const testCategory = await storage.createMenuCategory({
      name: "Test Category",
      imageUrl: "https://example.com/test.jpg"
    });
    console.log("Created test category:", testCategory);

    const deleted = await storage.deleteMenuCategory(testCategory.id);
    console.log("Deleted test category:", deleted);

    // Test order operations
    console.log("\n=== Testing Order Operations ===");

    const orders = await storage.getAllOrders();
    console.log(`Found ${orders.length} orders`);

    // Test contact message operations
    console.log("\n=== Testing Contact Message Operations ===");

    const contactMessages = await storage.getAllContactMessages();
    console.log(`Found ${contactMessages.length} contact messages`);

    // Test customization operations
    console.log("\n=== Testing Customization Operations ===");

    const customizationGroups = await storage.getAllCustomizationGroups();
    console.log(`Found ${customizationGroups.length} customization groups`);

    const customizationOptions = await storage.getAllCustomizationOptions();
    console.log(`Found ${customizationOptions.length} customization options`);

    console.log("\n✅ All database operations completed successfully!");

  } catch (error) {
    console.error("❌ Database test failed:", error);
    throw error;
  }
}

// Run the test if this file is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  testDatabaseOperations()
    .then(() => {
      console.log("Database test completed!");
      process.exit(0);
    })
    .catch((error) => {
      console.error("Database test failed:", error);
      process.exit(1);
    });
}

export { testDatabaseOperations };
