import { useState } from "react";
import { useLocation } from "wouter";
import { motion } from "framer-motion";
import { z } from "zod";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { Eye, EyeOff, Lock, User } from "lucide-react";

// Define validation schema
const loginSchema = z.object({
  username: z.string().min(3, "Username must be at least 3 characters"),
  password: z.string().min(6, "Password must be at least 6 characters"),
});

type LoginFormData = z.infer<typeof loginSchema>;

const AuthPage = () => {
  const [, setLocation] = useLocation();
  const [showPassword, setShowPassword] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState("");

  // Form handling
  const { register, handleSubmit, formState: { errors } } = useForm<LoginFormData>({
    resolver: zodResolver(loginSchema),
    defaultValues: {
      username: "",
      password: ""
    }
  });

  const togglePasswordVisibility = () => {
    setShowPassword(!showPassword);
  };

  const onSubmit = async (data: LoginFormData) => {
    setIsLoading(true);
    setError("");

    try {
      // Make API call to authenticate
      const response = await fetch('/api/auth/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Authentication failed');
      }

      const userData = await response.json();

      // Redirect based on user role
      if (userData.role === 'admin') {
        alert("Login Successful! Welcome to the Admin Portal");
        setLocation("/admin/settings");
      } else if (userData.role === 'manager') {
        alert("Login Successful! Welcome to the Kitchen Manager");
        setLocation("/manager");
      } else if (userData.role === 'driver') {
        alert("Login Successful! Welcome to the Delivery Dashboard");
        setLocation("/driver");
      } else {
        // Fallback for unknown roles
        setError("User role not recognized");
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : "Invalid username or password");
      console.error("Login error:", err);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-black text-white flex flex-col md:flex-row">
      {/* Left side - Login Form */}
      <div className="w-full md:w-1/2 flex flex-col justify-center p-6 md:p-12">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          className="max-w-md mx-auto w-full"
        >
          <div className="text-center mb-8">
            <h1 className="text-3xl font-bold bg-gradient-to-r from-pink-500 via-purple-500 to-cyan-500 text-transparent bg-clip-text mb-2">
              Barbecuez Staff Portal
            </h1>
            <p className="text-gray-400">Sign in to access your dashboard</p>
          </div>

          {error && (
            <div className="bg-red-900/40 border border-red-700 text-red-400 p-4 rounded-lg mb-6">
              {error}
            </div>
          )}

          <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
            <div className="space-y-2">
              <label htmlFor="username" className="text-sm font-medium text-gray-300 block">
                Username
              </label>
              <div className="relative">
                <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                  <User className="h-5 w-5 text-gray-500" />
                </div>
                <input
                  id="username"
                  type="text"
                  {...register("username")}
                  className={`bg-gray-900 border ${
                    errors.username ? "border-red-600" : "border-gray-700"
                  } text-white rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full pl-10 p-3 placeholder-gray-500`}
                  placeholder="Enter your username"
                />
              </div>
              {errors.username && (
                <p className="text-red-500 text-sm">{errors.username.message}</p>
              )}
            </div>

            <div className="space-y-2">
              <label htmlFor="password" className="text-sm font-medium text-gray-300 block">
                Password
              </label>
              <div className="relative">
                <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                  <Lock className="h-5 w-5 text-gray-500" />
                </div>
                <input
                  id="password"
                  type={showPassword ? "text" : "password"}
                  {...register("password")}
                  className={`bg-gray-900 border ${
                    errors.password ? "border-red-600" : "border-gray-700"
                  } text-white rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full pl-10 p-3 pr-10 placeholder-gray-500`}
                  placeholder="Enter your password"
                />
                <div className="absolute inset-y-0 right-0 flex items-center pr-3">
                  <button
                    type="button"
                    onClick={togglePasswordVisibility}
                    className="text-gray-500 hover:text-gray-300 focus:outline-none"
                  >
                    {showPassword ? (
                      <EyeOff className="h-5 w-5" />
                    ) : (
                      <Eye className="h-5 w-5" />
                    )}
                  </button>
                </div>
              </div>
              {errors.password && (
                <p className="text-red-500 text-sm">{errors.password.message}</p>
              )}
            </div>

            <motion.button
              type="submit"
              disabled={isLoading}
              className="w-full bg-gradient-to-r from-pink-600 to-purple-600 hover:from-pink-700 hover:to-purple-700 text-white font-medium rounded-lg p-3 flex items-center justify-center disabled:opacity-70 disabled:cursor-not-allowed"
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
            >
              {isLoading ? (
                <>
                  <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  Signing in...
                </>
              ) : (
                "Sign In"
              )}
            </motion.button>

            <div className="text-sm text-center text-gray-500 mt-4">
              <p>Need help? Contact system administrator</p>
            </div>
          </form>

          {/* Quick login for testing */}
          {process.env.NODE_ENV === "development" && (
            <div className="mt-8 p-4 border border-gray-800 rounded-lg">
              <h3 className="text-sm font-medium text-gray-400 mb-2">Test Accounts:</h3>
              <div className="grid grid-cols-3 gap-2 text-xs">
                <div className="bg-gray-900 p-2 rounded border border-gray-800">
                  <p className="font-medium text-cyan-400">Admin</p>
                  <p className="text-gray-500">admin / admin123</p>
                </div>
                <div className="bg-gray-900 p-2 rounded border border-gray-800">
                  <p className="font-medium text-orange-400">Manager</p>
                  <p className="text-gray-500">manager / manager123</p>
                </div>
                <div className="bg-gray-900 p-2 rounded border border-gray-800">
                  <p className="font-medium text-pink-400">Driver</p>
                  <p className="text-gray-500">driver / driver123</p>
                </div>
              </div>
            </div>
          )}
        </motion.div>
      </div>

      {/* Right side - Hero Section */}
      <div className="hidden md:block md:w-1/2 bg-gradient-to-br from-gray-900 to-black relative overflow-hidden">
        <div className="absolute inset-0 bg-black opacity-60"></div>
        <div
          className="absolute inset-0 bg-[url('https://images.unsplash.com/photo-1544025162-d76694265947?q=80&w=2069')] bg-cover bg-center"
          style={{ backgroundPosition: 'center 40%' }}
        >
          <div className="absolute inset-0 bg-gradient-to-r from-black via-black/90 to-transparent"></div>
        </div>

        <div className="relative h-full flex flex-col justify-center px-12 z-10">
          <h2 className="text-4xl font-bold text-white mb-6">
            Welcome to the<br />
            <span className="bg-gradient-to-r from-pink-500 via-purple-500 to-cyan-500 text-transparent bg-clip-text">
              Barbecuez Staff Portal
            </span>
          </h2>

          <div className="space-y-6 max-w-md">
            <div className="flex items-start space-x-3">
              <div className="mt-1 bg-pink-600/20 p-1 rounded">
                <svg className="h-5 w-5 text-pink-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7" />
                </svg>
              </div>
              <div>
                <h3 className="text-lg font-medium text-white">Admin Portal</h3>
                <p className="text-gray-400">Manage restaurant settings, menu items, and view analytics</p>
              </div>
            </div>

            <div className="flex items-start space-x-3">
              <div className="mt-1 bg-purple-600/20 p-1 rounded">
                <svg className="h-5 w-5 text-purple-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7" />
                </svg>
              </div>
              <div>
                <h3 className="text-lg font-medium text-white">Kitchen Manager</h3>
                <p className="text-gray-400">Process and track orders in real-time with status updates</p>
              </div>
            </div>

            <div className="flex items-start space-x-3">
              <div className="mt-1 bg-cyan-600/20 p-1 rounded">
                <svg className="h-5 w-5 text-cyan-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7" />
                </svg>
              </div>
              <div>
                <h3 className="text-lg font-medium text-white">Delivery Dashboard</h3>
                <p className="text-gray-400">Track deliveries and update customer order status</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AuthPage;