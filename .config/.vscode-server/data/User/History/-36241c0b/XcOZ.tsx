import { useState, useEffect } from "react";
import { useQuery } from "@tanstack/react-query";
import { getDishes, getCategories } from "@/api/api";
import { Dish } from "@shared/schema";
import Button from "@/components/Button";
import CategoryCard from "@/components/CategoryCard";
import MenuItemCard from "@/components/MenuItemCard";
import CustomizeModal from "@/components/CustomizeModal";
import { motion, AnimatePresence } from "framer-motion";
import { useInView } from "react-intersection-observer";
import { Swiper, SwiperSlide } from "swiper/react";
import { Autoplay, EffectCards } from "swiper/modules";

// Import Swiper styles
import "swiper/css";
import "swiper/css/effect-cards";

// Define enhanced category interface with images
interface MenuCategory {
  id: string;
  name: string;
  image: string;
}

// API category interface
interface ApiCategory {
  id: number;
  name: string;
  imageUrl: string;
}

const Menu = () => {
  // Animation refs
  const [headerRef, headerInView] = useInView({
    triggerOnce: true,
    threshold: 0.1,
  });

  const [featuredRef, featuredInView] = useInView({
    triggerOnce: false,
    threshold: 0.2,
  });

  const [categoriesRef, categoriesInView] = useInView({
    triggerOnce: true,
    threshold: 0.1,
  });

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
      }
    }
  };

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: { type: "spring", stiffness: 100 }
    }
  };

  // State management
  const [viewMode, setViewMode] = useState<'categories' | 'items'>('categories');
  const [selectedCategory, setSelectedCategory] = useState<string>('');
  const [selectedDish, setSelectedDish] = useState<Dish | null>(null);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [visibleDishes, setVisibleDishes] = useState<number>(6);

  // Featured dishes for the slider
  const featuredDishes = [
    {
      id: 'f1',
      name: 'Signature BBQ Platter',
      description: 'Our chef\'s selection of premium meats',
      price: 399,
      image: 'https://images.unsplash.com/photo-1508615263227-c5d58c1e5821?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&h=400'
    },
    {
      id: 'f2',
      name: 'Smokehouse Burger Supreme',
      description: 'Our award-winning burger with all the fixings',
      price: 269,
      image: 'https://images.unsplash.com/photo-1568901346375-23c9450c58cd?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&h=400'
    },
    {
      id: 'f3',
      name: 'Cedar Plank Salmon',
      description: 'Fresh Norwegian salmon with maple glaze',
      price: 299,
      image: 'https://images.unsplash.com/photo-1519708227418-c8fd9a32b7a2?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&h=400'
    }
  ];

  // Fetch dishes from API
  const { data: dishes, isLoading, error } = useQuery({
    queryKey: ['/api/dishes'],
    staleTime: 300000, // 5 minutes
  });

  // Enhanced menu categories with images
  const menuCategories: MenuCategory[] = [
    {
      id: 'bbq',
      name: 'Signature BBQ',
      image: 'https://images.unsplash.com/photo-1529193591184-b1d58069ecdd?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&h=400'
    },
    {
      id: 'starters',
      name: 'Starters',
      image: 'https://images.unsplash.com/photo-1527477396000-e27163b481c2?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&h=400'
    },
    {
      id: 'main',
      name: 'Main Course',
      image: 'https://images.unsplash.com/photo-1519708227418-c8fd9a32b7a2?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&h=400'
    },
    {
      id: 'desserts',
      name: 'Desserts',
      image: 'https://images.unsplash.com/photo-1624353365286-3f8d62daad51?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&h=400'
    },
    {
      id: 'sandwiches',
      name: 'SandwichZ',
      image: 'https://images.unsplash.com/photo-1621996346565-e3dbc646d9a9?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&h=400'
    },
    {
      id: 'burgerz',
      name: 'BurgerZ',
      image: 'https://images.unsplash.com/photo-1568901346375-23c9450c58cd?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&h=400'
    }
  ];

  // Map category IDs to names in our API data
  const categoryMapping: Record<string, string> = {
    'bbq': 'Signature BBQ',
    'starters': 'Starters',
    'main': 'Main Course',
    'desserts': 'Desserts',
    'sandwiches': 'Main Course', // Map custom categories to our existing data
    'burgerz': 'Main Course'
  };

  // Handle category selection
  const handleCategorySelect = (categoryId: string) => {
    setSelectedCategory(categoryId);
    setViewMode('items');
    // Reset visible dishes when category changes
    setVisibleDishes(6);
    // Scroll to top of menu section
    document.getElementById('menu')?.scrollIntoView({ behavior: 'smooth' });
  };

  // Handle back to categories
  const handleBackToCategories = () => {
    setViewMode('categories');
  };

  // Handle dish customization
  const handleCustomizeDish = (dish: Dish) => {
    setSelectedDish(dish);
    setIsModalOpen(true);
  };

  // Handle load more dishes
  const loadMoreDishes = () => {
    setVisibleDishes(prev => prev + 3);
  };

  // Filter dishes based on selected category
  const filteredDishes = dishes ?
    dishes.filter((dish: Dish) => {
      if (!selectedCategory) return true;
      const apiCategory = dish.category;
      return apiCategory === categoryMapping[selectedCategory];
    }) : [];

  // Get displayed dishes based on visibility limit
  const displayedDishes = filteredDishes.slice(0, visibleDishes);
  const hasMoreDishes = filteredDishes.length > visibleDishes;

  // Get displayed category name
  const getCategoryDisplayName = () => {
    const category = menuCategories.find(cat => cat.id === selectedCategory);
    return category ? category.name : 'All Items';
  };

  return (
    <section id="menu" className="min-h-screen py-32 bg-black relative overflow-hidden">
      {/* Animated Gradient Background */}
      <div
        className="absolute inset-0 z-0 bg-black"
        style={{
          background: "radial-gradient(circle at 20% 80%, rgba(57, 255, 20, 0.03), transparent 33%), " +
                    "radial-gradient(circle at 80% 20%, rgba(0, 255, 255, 0.03), transparent 33%), " +
                    "radial-gradient(circle at 50% 50%, rgba(255, 0, 255, 0.03), rgba(0, 0, 0, 1) 100%)",
        }}
      ></div>

      {/* Neon Grid Overlay */}
      <div className="absolute inset-0 z-0 opacity-[0.03]"
           style={{
             backgroundImage: "linear-gradient(to right, #FF00FF 1px, transparent 1px), linear-gradient(to bottom, #FF00FF 1px, transparent 1px)",
             backgroundSize: "40px 40px"
           }}>
      </div>

      <div className="container mx-auto px-4 relative z-10">
        {/* Header Section */}
        <motion.div
          ref={headerRef}
          initial={{ opacity: 0, y: -20 }}
          animate={headerInView ? { opacity: 1, y: 0 } : {}}
          transition={{ duration: 0.8 }}
          className="text-center mb-16"
        >
          <motion.h2
            className="font-playfair text-4xl md:text-5xl lg:text-6xl font-bold mb-6 relative inline-block"
            initial={{ opacity: 0 }}
            animate={headerInView ? { opacity: 1 } : {}}
            transition={{ duration: 0.5, delay: 0.3 }}
          >
            {viewMode === 'categories' ? (
              <>
                <span className="text-white">Our </span>
                <motion.span
                  className="text-transparent bg-clip-text bg-gradient-to-r from-cyan-400 via-fuchsia-500 to-lime-300 relative z-10"
                  animate={{
                    textShadow: [
                      "0 0 5px rgba(0, 255, 255, 0.7), 0 0 10px rgba(0, 255, 255, 0.5), 0 0 15px rgba(0, 255, 255, 0.3)",
                      "0 0 5px rgba(255, 0, 255, 0.7), 0 0 10px rgba(255, 0, 255, 0.5), 0 0 15px rgba(255, 0, 255, 0.3)",
                      "0 0 5px rgba(57, 255, 20, 0.7), 0 0 10px rgba(57, 255, 20, 0.5), 0 0 15px rgba(57, 255, 20, 0.3)",
                      "0 0 5px rgba(0, 255, 255, 0.7), 0 0 10px rgba(0, 255, 255, 0.5), 0 0 15px rgba(0, 255, 255, 0.3)",
                    ]
                  }}
                  transition={{ duration: 5, repeat: Infinity }}
                >
                  Menu
                </motion.span>
              </>
            ) : (
              <>
                <span className="text-white">{getCategoryDisplayName()} </span>
                <motion.span
                  className="text-transparent bg-clip-text bg-gradient-to-r from-fuchsia-500 to-cyan-400 relative z-10"
                  animate={{
                    textShadow: [
                      "0 0 5px rgba(255, 0, 255, 0.7), 0 0 10px rgba(255, 0, 255, 0.5), 0 0 15px rgba(255, 0, 255, 0.3)",
                      "0 0 5px rgba(0, 255, 255, 0.7), 0 0 10px rgba(0, 255, 255, 0.5), 0 0 15px rgba(0, 255, 255, 0.3)",
                      "0 0 5px rgba(255, 0, 255, 0.7), 0 0 10px rgba(255, 0, 255, 0.5), 0 0 15px rgba(255, 0, 255, 0.3)",
                    ]
                  }}
                  transition={{ duration: 3, repeat: Infinity }}
                >
                  Selection
                </motion.span>
              </>
            )}
          </motion.h2>

          <motion.p
            className="font-poppins text-lg md:text-xl text-gray-300 max-w-2xl mx-auto"
            initial={{ opacity: 0, y: 20 }}
            animate={headerInView ? { opacity: 1, y: 0 } : {}}
            transition={{ duration: 0.6, delay: 0.5 }}
          >
            {viewMode === 'categories'
              ? 'Explore our handcrafted selection of premium barbecue dishes'
              : `Discover our ${getCategoryDisplayName()} offerings crafted with premium ingredients`
            }
          </motion.p>
        </motion.div>

        {/* Featured Menu Slider - Only show on category view */}
        {viewMode === 'categories' && (
          <motion.div
            ref={featuredRef}
            initial={{ opacity: 0 }}
            animate={featuredInView ? { opacity: 1 } : {}}
            transition={{ duration: 0.8 }}
            className="mb-20"
          >
            <motion.h3
              className="text-2xl md:text-3xl font-playfair font-bold mb-8 text-center"
              initial={{ opacity: 0, y: 20 }}
              animate={featuredInView ? { opacity: 1, y: 0 } : {}}
              transition={{ duration: 0.6 }}
            >
              <span className="text-white">Featured </span>
              <span className="text-transparent bg-clip-text bg-gradient-to-r from-lime-300 to-cyan-400">
                Selections
              </span>
            </motion.h3>

            <div className="px-4 sm:px-8 md:px-16 py-8">
              <Swiper
                effect="cards"
                grabCursor={true}
                modules={[EffectCards, Autoplay]}
                className="max-w-xs mx-auto"
                autoplay={{
                  delay: 3000,
                  disableOnInteraction: false,
                }}
              >
                {featuredDishes.map((dish) => (
                  <SwiperSlide key={dish.id} className="bg-transparent">
                    <div className="bg-black/20 backdrop-blur-sm rounded-xl overflow-hidden
                                    border border-gray-800 shadow-[0_0_15px_rgba(0,255,255,0.15)]
                                    hover:shadow-[0_0_30px_rgba(0,255,255,0.3)] transition-all duration-500">
                      <div className="relative h-72 overflow-hidden">
                        <img
                          src={dish.image}
                          alt={dish.name}
                          className="w-full h-full object-cover"
                        />
                        <div className="absolute inset-0 bg-gradient-to-t from-black via-black/50 to-transparent"></div>
                        <div className="absolute bottom-0 left-0 right-0 p-6">
                          <h3 className="text-xl font-playfair font-bold text-white mb-2">{dish.name}</h3>
                          <p className="text-gray-300 text-sm mb-3">{dish.description}</p>

                          <div className="flex justify-between items-center">
                            <span className="text-cyan-400 font-semibold">{dish.price} NOK</span>
                            <motion.button
                              className="bg-gradient-to-r from-fuchsia-600 to-cyan-600 text-white
                                       px-4 py-2 rounded-md text-sm font-medium
                                       hover:from-fuchsia-500 hover:to-cyan-500 transition-all"
                              whileHover={{ scale: 1.05 }}
                              whileTap={{ scale: 0.95 }}
                              onClick={() => handleCategorySelect('bbq')} // Redirect to appropriate category
                            >
                              Order Now
                            </motion.button>
                          </div>
                        </div>
                      </div>
                    </div>
                  </SwiperSlide>
                ))}
              </Swiper>
            </div>
          </motion.div>
        )}

        {/* Back Button (when viewing items) */}
        {viewMode === 'items' && (
          <motion.div
            className="mb-8"
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.5 }}
          >
            <motion.button
              className="group flex items-center px-5 py-2.5 relative overflow-hidden
                         text-cyan-400 border border-cyan-400 rounded-md
                         hover:text-white transition-colors duration-300"
              onClick={handleBackToCategories}
              whileHover={{ scale: 1.03 }}
              whileTap={{ scale: 0.97 }}
            >
              {/* Button glow effect */}
              <span className="absolute inset-0 w-full h-full transition-all duration-300
                               bg-gradient-to-r from-cyan-500 to-blue-500
                               opacity-0 group-hover:opacity-100"></span>

              <span className="relative flex items-center">
                <svg
                  className="w-4 h-4 mr-2 transition-transform duration-300 group-hover:-translate-x-1"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                </svg>
                Back to Categories
              </span>
            </motion.button>
          </motion.div>
        )}

        {/* Categories Grid View */}
        {viewMode === 'categories' && (
          <motion.div
            ref={categoriesRef}
            variants={containerVariants}
            initial="hidden"
            animate={categoriesInView ? "visible" : "hidden"}
            className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-8"
          >
            {menuCategories.map((category, index) => (
              <motion.div
                key={category.id}
                variants={itemVariants}
                transition={{ duration: 0.5, delay: index * 0.1 }}
                className="h-full"
              >
                <CategoryCard
                  category={category}
                  isActive={selectedCategory === category.id}
                  onClick={() => handleCategorySelect(category.id)}
                />
              </motion.div>
            ))}
          </motion.div>
        )}

        {/* Menu Items Grid View */}
        {viewMode === 'items' && (
          isLoading ? (
            <motion.div
              className="text-center py-12"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ duration: 0.3 }}
            >
              <div className="inline-block h-16 w-16 mb-6 relative">
                <div className="absolute inset-0 rounded-full border-t-2 border-b-2 border-cyan-400 animate-spin"></div>
                <div className="absolute inset-2 rounded-full border-r-2 border-l-2 border-fuchsia-500 animate-spin animation-delay-150"></div>
                <div className="absolute inset-4 rounded-full border-t-2 border-b-2 border-lime-300 animate-spin animation-delay-300"></div>
              </div>
              <p className="font-poppins text-cyan-400">Preparing your delicious options...</p>
            </motion.div>
          ) : error ? (
            <motion.div
              className="text-center py-12 backdrop-blur-sm bg-black/40 rounded-lg border border-red-900"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
            >
              <div className="text-red-500 text-5xl mb-4">
                <svg className="w-16 h-16 mx-auto" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                </svg>
              </div>
              <h3 className="font-playfair text-2xl font-bold text-white mb-3">Something went wrong</h3>
              <p className="font-poppins text-gray-400 mb-6 max-w-md mx-auto">We couldn't load the menu at this time. Our chefs are working to fix the issue.</p>
              <motion.button
                className="bg-gradient-to-r from-red-600 to-red-800 text-white
                          px-6 py-3 rounded-md text-sm font-medium inline-flex items-center
                          hover:from-red-500 hover:to-red-700 transition-all"
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                onClick={() => window.location.reload()}
              >
                <svg className="w-4 h-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                </svg>
                Try Again
              </motion.button>
            </motion.div>
          ) : displayedDishes.length === 0 ? (
            <motion.div
              className="text-center py-16 backdrop-blur-sm bg-black/40 rounded-lg border border-gray-800"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
            >
              <div className="text-gray-500 text-5xl mb-4">
                <svg className="w-16 h-16 mx-auto" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
                </svg>
              </div>
              <h3 className="font-playfair text-2xl font-bold text-white mb-3">No items found</h3>
              <p className="font-poppins text-gray-400 mb-6">No dishes available in this category at the moment.</p>
              <motion.button
                className="bg-gradient-to-r from-cyan-600 to-cyan-800 text-white
                           px-6 py-3 rounded-md text-sm font-medium inline-flex items-center
                           hover:from-cyan-500 hover:to-cyan-700 transition-all"
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                onClick={handleBackToCategories}
              >
                <svg className="w-4 h-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                </svg>
                Back to Categories
              </motion.button>
            </motion.div>
          ) : (
            <AnimatePresence>
              <motion.div
                variants={containerVariants}
                initial="hidden"
                animate="visible"
                className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8"
              >
                {displayedDishes.map((dish: Dish, index) => (
                  <motion.div
                    key={dish.id}
                    variants={itemVariants}
                    transition={{ duration: 0.5, delay: index * 0.08 }}
                  >
                    <MenuItemCard
                      item={dish}
                      onCustomize={() => handleCustomizeDish(dish)}
                    />
                  </motion.div>
                ))}
              </motion.div>

              {/* Load More Button */}
              {hasMoreDishes && (
                <motion.div
                  className="text-center mt-16"
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5, delay: 0.3 }}
                >
                  <motion.button
                    className="group relative px-8 py-4 overflow-hidden rounded-md bg-transparent"
                    whileHover={{ scale: 1.03 }}
                    whileTap={{ scale: 0.97 }}
                    onClick={loadMoreDishes}
                  >
                    {/* Button Background */}
                    <span className="absolute inset-0 w-full h-full bg-gradient-to-r from-fuchsia-800/70 to-fuchsia-600/70"></span>

                    {/* Button Glow Effect */}
                    <span className="absolute inset-0 w-full h-full transition-all duration-300
                                    bg-gradient-to-r from-fuchsia-600 via-purple-600 to-fuchsia-600
                                    opacity-0 group-hover:opacity-100 group-hover:blur-md"></span>

                    {/* Button Border */}
                    <span className="absolute inset-0 w-full h-full border border-fuchsia-500 rounded-md"></span>

                    {/* Button Text */}
                    <span className="relative font-medium text-lg text-white tracking-wider flex items-center">
                      Load More
                      <svg className="w-5 h-5 ml-2 transition-transform duration-300 group-hover:translate-x-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                      </svg>
                    </span>
                  </motion.button>
                </motion.div>
              )}
            </AnimatePresence>
          )
        )}
      </div>

      {/* Customize Modal */}
      <AnimatePresence>
        {selectedDish && isModalOpen && (
          <CustomizeModal
            dish={selectedDish}
            isOpen={isModalOpen}
            onClose={() => setIsModalOpen(false)}
          />
        )}
      </AnimatePresence>
    </section>
  );
};

export default Menu;
