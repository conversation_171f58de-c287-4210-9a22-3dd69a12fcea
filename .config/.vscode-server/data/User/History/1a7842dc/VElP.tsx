import { useState, useEffect } from "react";
import { useLocation, Link } from "wouter";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { useCart } from "@/context/CartContext";
import { useRestaurantStatus } from "@/context/RestaurantStatusContext";
import { formatCurrency } from "@/lib/utils";
import { createOrder } from "@/api/api";
import Button from "@/components/Button";
import { motion, AnimatePresence } from "framer-motion";
import { useInView } from "react-intersection-observer";

const checkoutSchema = z.object({
  firstName: z.string().min(2, "First name is required"),
  email: z.string().email("Please enter a valid email").optional().or(z.literal("")),
  phone: z.string()
    .min(1, "Phone number is required")
    .regex(/^\+47\s?\d{2}\s?\d{2}\s?\d{2}\s?\d{2}$/, "Please enter a valid Norwegian phone number (+47 XX XX XX XX)"),
  orderType: z.enum(["delivery", "takeaway"]),
  deliveryTime: z.enum(["asap", "scheduled"]),
  scheduledTime: z.string().optional(),
  address: z.string().optional(),
  postalCode: z.string().optional(),
  city: z.string().optional(),
  notes: z.string().optional(),
  paymentMethod: z.enum(["card", "vipps", "cash"])
}).refine(data => {
  // If delivery is selected, address is required
  if (data.orderType === "delivery") {
    return !!data.address && !!data.postalCode && !!data.city;
  }
  return true;
}, {
  message: "Address details are required for delivery",
  path: ["address"]
}).refine(data => {
  // If scheduled time is selected, it must be provided
  if (data.deliveryTime === "scheduled") {
    return !!data.scheduledTime;
  }
  return true;
}, {
  message: "Please select a time for scheduled delivery/pickup",
  path: ["scheduledTime"]
});

type CheckoutFormData = z.infer<typeof checkoutSchema>;

const Checkout = () => {
  const { cartItems, clearCart } = useCart();
  const { isOpen } = useRestaurantStatus();
  const [, setLocation] = useLocation();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitSuccess, setSubmitSuccess] = useState(false);
  const [animatedTotal, setAnimatedTotal] = useState(0);
  const [focusedField, setFocusedField] = useState<string | null>(null);

  // Animation refs for sections
  const [headerRef, headerInView] = useInView({
    triggerOnce: true,
    threshold: 0.1,
  });

  const [formRef, formInView] = useInView({
    triggerOnce: true,
    threshold: 0.1,
  });

  const [summaryRef, summaryInView] = useInView({
    triggerOnce: true,
    threshold: 0.1,
  });

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    },
    exit: {
      opacity: 0,
      transition: {
        duration: 0.3
      }
    }
  };

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        type: "spring",
        stiffness: 100,
        damping: 12
      }
    }
  };

  const successVariants = {
    hidden: { scale: 0.8, opacity: 0 },
    visible: {
      scale: 1,
      opacity: 1,
      transition: {
        type: "spring",
        stiffness: 200,
        damping: 20,
        duration: 0.5
      }
    }
  };

  const formInputVariants = {
    initial: { scale: 1, boxShadow: "0 0 0px rgba(0, 0, 0, 0)" },
    focused: {
      scale: 1.02,
      boxShadow: "0 0 15px rgba(0, 255, 255, 0.3)",
      transition: {
        type: "spring",
        stiffness: 500,
        damping: 20,
        duration: 0.2
      }
    },
    paymentFocused: {
      scale: 1.02,
      boxShadow: "0 0 15px rgba(57, 255, 20, 0.3)",
      transition: {
        type: "spring",
        stiffness: 500,
        damping: 20,
        duration: 0.2
      }
    },
    error: {
      scale: [1, 1.02, 1],
      boxShadow: "0 0 15px rgba(255, 0, 0, 0.3)",
      transition: {
        scale: {
          duration: 0.3,
          repeat: 2
        }
      }
    }
  };

  const {
    register,
    handleSubmit,
    watch,
    setValue,
    clearErrors,
    formState: { errors, touchedFields, isValid }
  } = useForm<CheckoutFormData>({
    resolver: zodResolver(checkoutSchema),
    defaultValues: {
      paymentMethod: "card",
      orderType: "delivery",
      deliveryTime: "asap",
      phone: "+47 "
    },
    mode: "onChange"
  });

  // Watch form values for conditional rendering
  const orderType = watch("orderType");
  const deliveryTime = watch("deliveryTime");

  // State for delivery fee and settings
  const [deliveryFee, setDeliveryFee] = useState(89); // Default fallback
  const [estimatedTime, setEstimatedTime] = useState("30-45 minutes");
  const [isLoadingSettings, setIsLoadingSettings] = useState(true);

  // Fetch delivery fee from API
  useEffect(() => {
    const fetchSettings = async () => {
      try {
        const response = await fetch('/api/settings');
        if (response.ok) {
          const settings = await response.json();
          setDeliveryFee(settings.delivery_fee);
          setEstimatedTime(settings.estimated_time);
        }
      } catch (error) {
        console.error('Failed to fetch settings:', error);
        // Keep default values on error
      } finally {
        setIsLoadingSettings(false);
      }
    };

    fetchSettings();
  }, []);

  const subtotal = cartItems.reduce((total, item) => total + (item.price * item.quantity), 0);
  const actualDeliveryFee = cartItems.length > 0 ? deliveryFee : 0;
  const VAT = subtotal * 0.25; // 25% VAT
  const total = subtotal + actualDeliveryFee + VAT;

  // Use effect for animated total - much faster animation
  useEffect(() => {
    const duration = 200; // Much faster animation (was 800ms)
    const steps = 5;      // Fewer steps (was 20)
    const stepDuration = duration / steps;
    const increment = (total - animatedTotal) / steps;

    // If the difference is very small, just set it directly
    if (Math.abs(total - animatedTotal) < 2) {
      setAnimatedTotal(total);
      return;
    }

    const timer = setTimeout(() => {
      setAnimatedTotal(prev => {
        const next = prev + increment;
        // If we're close enough, just set the exact value
        if (Math.abs(next - total) < 2) return total;
        return next;
      });
    }, stepDuration);

    return () => clearTimeout(timer);
  }, [total, animatedTotal]);

  // Add a missing state
  const [isUpdating, setIsUpdating] = useState<number | null>(null);

  // Geolocation states
  const [isGettingLocation, setIsGettingLocation] = useState(false);
  const [locationError, setLocationError] = useState<string | null>(null);
  const [locationSuccess, setLocationSuccess] = useState(false);

  // Clear location error and success when order type changes or address field is modified
  useEffect(() => {
    setLocationError(null);
    setLocationSuccess(false);
  }, [orderType]);

  // Clear location error and success when user starts typing in address field
  const addressValue = watch('address');
  useEffect(() => {
    if (addressValue && (locationError || locationSuccess)) {
      setLocationError(null);
      setLocationSuccess(false);
    }
  }, [addressValue, locationError, locationSuccess]);

  // Custom register to track focus state
  const registerWithFocus = (name: keyof CheckoutFormData) => {
    return {
      ...register(name),
      onFocus: () => setFocusedField(name),
      onBlur: () => setFocusedField(null)
    };
  };

  const onSubmit = async (data: CheckoutFormData) => {
    // Check if cart is empty
    if (cartItems.length === 0) {
      setLocation("/menu");
      return;
    }

    // Check if restaurant is open
    if (!isOpen) {
      alert("Sorry, the restaurant is currently closed. Please try again during business hours.");
      return;
    }

    setIsSubmitting(true);

    try {
      const orderData = {
        customer: {
          firstName: data.firstName,
          lastName: data.lastName,
          email: data.email,
          phone: data.phone,
          address: data.address,
          postalCode: data.postalCode,
          city: data.city
        },
        orderDetails: {
          type: data.orderType,
          time: data.deliveryTime,
          scheduledTime: data.scheduledTime || null
        },
        items: cartItems,
        subtotal,
        deliveryFee: data.orderType === "delivery" ? actualDeliveryFee : 0,
        total: data.orderType === "delivery" ? total : subtotal + VAT,
        paymentMethod: data.paymentMethod,
        notes: data.notes || "",
        status: "pending"
      };

      const createdOrder = await createOrder(orderData);

      // Clear cart after successful order
      clearCart();

      // Navigate to order confirmation with the order ID
      const orderId = createdOrder.id || `BBC${Math.floor(Math.random() * 100000)}`;
      setLocation(`/order-confirmation/${orderId}`);

    } catch (error) {
      console.error("Order submission failed:", error);
      alert("There was an error processing your order. Please try again.");
      setIsSubmitting(false);
    }
  };

  // Check geolocation permission status
  const checkGeolocationPermission = async () => {
    if ('permissions' in navigator) {
      try {
        const permission = await navigator.permissions.query({ name: 'geolocation' });
        console.log('🔐 Geolocation permission status:', permission.state);
        return permission.state;
      } catch (error) {
        console.log('⚠️ Could not check permission status:', error);
        return 'unknown';
      }
    }
    return 'unknown';
  };

  // Geolocation function
  const getCurrentLocation = async () => {
    console.log('🔍 getCurrentLocation function called');
    setIsGettingLocation(true);
    setLocationError(null);

    try {
      // Check if geolocation is supported
      if (!navigator.geolocation) {
        console.error('❌ Geolocation not supported');
        throw new Error('Geolocation is not supported by this browser');
      }

      console.log('✅ Geolocation supported');

      // Check current permission status
      const permissionStatus = await checkGeolocationPermission();
      console.log('🔐 Permission status:', permissionStatus);

      if (permissionStatus === 'denied') {
        throw new Error('Location access has been denied. Please enable location services in your browser settings and refresh the page.');
      }

      console.log('📍 Requesting location...');

      // Get current position
      const position = await new Promise<GeolocationPosition>((resolve, reject) => {
        console.log('📍 Calling navigator.geolocation.getCurrentPosition...');
        navigator.geolocation.getCurrentPosition(
          (pos) => {
            console.log('✅ Geolocation success:', pos);
            resolve(pos);
          },
          (err) => {
            console.error('❌ Geolocation error:', err);
            reject(err);
          },
          {
            enableHighAccuracy: true,
            timeout: 15000, // Increased timeout
            maximumAge: 60000 // Reduced cache time to 1 minute
          }
        );
      });

      const { latitude, longitude } = position.coords;

      // Use Nominatim (OpenStreetMap) for reverse geocoding (free alternative to Google Maps)
      const response = await fetch(
        `https://nominatim.openstreetmap.org/reverse?format=json&lat=${latitude}&lon=${longitude}&addressdetails=1&accept-language=no,en`
      );

      if (!response.ok) {
        throw new Error('Failed to get address from coordinates');
      }

      const data = await response.json();

      if (!data || !data.address) {
        throw new Error('No address found for this location');
      }

      const address = data.address;

      // Extract Norwegian address components
      const streetNumber = address.house_number || '';
      const streetName = address.road || address.pedestrian || address.footway || '';
      const fullStreetAddress = streetNumber && streetName
        ? `${streetName} ${streetNumber}`
        : streetName || address.display_name?.split(',')[0] || '';

      const postalCode = address.postcode || '';
      const city = address.city || address.town || address.village || address.municipality || '';

      // Validate that we have essential Norwegian address components
      if (!fullStreetAddress || !postalCode || !city) {
        throw new Error('Could not determine complete address. Please enter manually.');
      }

      // Check if the location is in Norway (basic validation)
      if (address.country_code !== 'no') {
        throw new Error('Location appears to be outside Norway. Please enter a Norwegian address.');
      }

      // Clear any existing errors for these fields first
      clearErrors(['address', 'postalCode', 'city']);

      // Auto-fill the form fields without triggering validation initially
      setValue('address', fullStreetAddress, { shouldValidate: false, shouldDirty: true, shouldTouch: true });
      setValue('postalCode', postalCode, { shouldValidate: false, shouldDirty: true, shouldTouch: true });
      setValue('city', city, { shouldValidate: false, shouldDirty: true, shouldTouch: true });

      // Use a longer delay and trigger validation manually
      setTimeout(() => {
        // Trigger validation for the entire form to ensure all fields are validated together
        setValue('address', fullStreetAddress, { shouldValidate: true, shouldDirty: true, shouldTouch: true });
        setValue('postalCode', postalCode, { shouldValidate: true, shouldDirty: true, shouldTouch: true });
        setValue('city', city, { shouldValidate: true, shouldDirty: true, shouldTouch: true });
      }, 200);

      // Show success message briefly
      setLocationError(null);
      setLocationSuccess(true);
      setTimeout(() => setLocationSuccess(false), 3000); // Hide success message after 3 seconds

    } catch (error: any) {
      console.error('Geolocation error:', error);

      let errorMessage = 'Unable to get your location. ';

      if (error.code === 1) {
        errorMessage += 'Location access denied. Please enable location services and try again.';
      } else if (error.code === 2) {
        errorMessage += 'Location unavailable. Please check your connection and try again.';
      } else if (error.code === 3) {
        errorMessage += 'Location request timed out. Please try again.';
      } else {
        errorMessage += error.message || 'Please enter your address manually.';
      }

      setLocationError(errorMessage);
    } finally {
      setIsGettingLocation(false);
    }
  };

  // Success Confirmation Component
  if (submitSuccess) {
    return (
      <section className="min-h-screen py-32 bg-black relative overflow-hidden">
        {/* Animated Gradient Background */}
        <div
          className="absolute inset-0 z-0"
          style={{
            background: "radial-gradient(circle at 20% 80%, rgba(57, 255, 20, 0.03), transparent 33%), " +
                      "radial-gradient(circle at 80% 20%, rgba(0, 255, 255, 0.03), transparent 33%), " +
                      "radial-gradient(circle at 50% 50%, rgba(255, 0, 255, 0.03), rgba(0, 0, 0, 1) 100%)",
          }}
        ></div>

        {/* Animated success message */}
        <div className="container mx-auto px-4 z-10 relative">
          <motion.div
            className="max-w-2xl mx-auto"
            variants={successVariants}
            initial="hidden"
            animate="visible"
          >
            <div className="bg-black/30 backdrop-blur-sm rounded-2xl p-8 md:p-12 border border-lime-800/30
                          shadow-[0_0_30px_rgba(57,255,20,0.2)]">
              <div className="text-center">
                <motion.div
                  className="w-24 h-24 mx-auto mb-6 rounded-full bg-gradient-to-br from-lime-500 to-green-700
                           flex items-center justify-center text-white"
                  animate={{
                    boxShadow: [
                      "0 0 0px rgba(57, 255, 20, 0)",
                      "0 0 30px rgba(57, 255, 20, 0.8)",
                      "0 0 10px rgba(57, 255, 20, 0.4)"
                    ],
                    scale: [1, 1.1, 1]
                  }}
                  transition={{ duration: 2, repeat: Infinity, repeatType: "reverse" }}
                >
                  <svg className="w-12 h-12" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={3} d="M5 13l4 4L19 7" />
                  </svg>
                </motion.div>

                <motion.h2
                  className="font-playfair text-3xl md:text-4xl font-bold mb-4 text-white"
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.3, duration: 0.5 }}
                >
                  Order Confirmed!
                </motion.h2>

                <motion.div
                  className="text-gray-300 mb-8 space-y-2"
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  transition={{ delay: 0.5, duration: 0.5 }}
                >
                  <p className="text-lg">Your delicious BBQ is on the way!</p>
                  <p>You will receive a confirmation email shortly.</p>
                </motion.div>

                {/* Confetti animation */}
                <div className="absolute inset-0 pointer-events-none">
                  {[...Array(20)].map((_, i) => (
                    <motion.div
                      key={i}
                      className="absolute w-4 h-4 rounded-full"
                      style={{
                        backgroundColor:
                          i % 3 === 0 ? 'rgba(57, 255, 20, 0.7)' :
                          i % 3 === 1 ? 'rgba(0, 255, 255, 0.7)' :
                          'rgba(255, 0, 255, 0.7)',
                        top: `${Math.random() * 100}%`,
                        left: `${Math.random() * 100}%`,
                      }}
                      initial={{
                        y: -20,
                        opacity: 0,
                        scale: 0
                      }}
                      animate={{
                        y: [0, 100 + Math.random() * 300],
                        opacity: [1, 0],
                        scale: [1, 0.5],
                        rotate: [0, Math.random() * 360]
                      }}
                      transition={{
                        duration: 2 + Math.random() * 3,
                        delay: Math.random() * 1,
                        ease: "easeOut"
                      }}
                    />
                  ))}
                </div>

                <motion.p
                  className="text-sm text-gray-500 mb-8"
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  transition={{ delay: 0.8, duration: 0.5 }}
                >
                  Redirecting to home page...
                </motion.p>

                <div className="flex justify-center">
                  <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 1, duration: 0.5 }}
                  >
                    <Link href="/">
                      <Button variant="outline-primary">
                        Return to Home
                      </Button>
                    </Link>
                  </motion.div>
                </div>
              </div>
            </div>
          </motion.div>
        </div>
      </section>
    );
  }

  // Empty Cart State
  if (cartItems.length === 0) {
    return (
      <section className="min-h-screen py-32 bg-black relative overflow-hidden">
        {/* Animated Gradient Background */}
        <div
          className="absolute inset-0 z-0"
          style={{
            background: "radial-gradient(circle at 20% 80%, rgba(0, 255, 255, 0.03), transparent 33%), " +
                      "radial-gradient(circle at 80% 20%, rgba(255, 0, 255, 0.03), transparent 33%), " +
                      "radial-gradient(circle at 50% 50%, rgba(57, 255, 20, 0.01), rgba(0, 0, 0, 1) 100%)",
          }}
        ></div>

        {/* Neon Grid Overlay */}
        <div className="absolute inset-0 z-0 opacity-[0.02]"
             style={{
               backgroundImage: "linear-gradient(to right, #FF00FF 1px, transparent 1px), linear-gradient(to bottom, #FF00FF 1px, transparent 1px)",
               backgroundSize: "40px 40px"
             }}>
        </div>

        <div className="container mx-auto px-4 relative z-10">
          <motion.div
            className="text-center mb-16"
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
          >
            <motion.h2
              className="font-playfair text-4xl md:text-5xl font-bold mb-4 text-white"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ duration: 0.5, delay: 0.2 }}
            >
              <span className="text-white">Your </span>
              <motion.span
                className="text-transparent bg-clip-text bg-gradient-to-r from-cyan-400 to-fuchsia-500"
                animate={{
                  textShadow: [
                    "0 0 5px rgba(0, 255, 255, 0.7), 0 0 10px rgba(0, 255, 255, 0.5), 0 0 15px rgba(0, 255, 255, 0.3)",
                    "0 0 5px rgba(255, 0, 255, 0.7), 0 0 10px rgba(255, 0, 255, 0.5), 0 0 15px rgba(255, 0, 255, 0.3)",
                    "0 0 5px rgba(0, 255, 255, 0.7), 0 0 10px rgba(0, 255, 255, 0.5), 0 0 15px rgba(0, 255, 255, 0.3)",
                  ]
                }}
                transition={{ duration: 3, repeat: Infinity }}
              >
                Checkout
              </motion.span>
            </motion.h2>
          </motion.div>

          <motion.div
            className="max-w-2xl mx-auto"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.3 }}
          >
            <div className="backdrop-blur-sm bg-black/20 rounded-xl p-10 border border-gray-800">
              <div className="text-center">
                {/* Animated Empty Cart Icon */}
                <motion.div
                  className="w-32 h-32 mx-auto mb-6 relative"
                  animate={{
                    rotateY: [0, 10, 0, -10, 0],
                  }}
                  transition={{ duration: 4, repeat: Infinity }}
                >
                  <svg
                    className="w-full h-full text-gray-600"
                    viewBox="0 0 24 24"
                    fill="none"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <motion.path
                      d="M3 3h2l.5 3M7 13h10l4-8H5.5M7 13L5.5 6M7 13l-2.3 2.3c-.4.4-.1 1.7 1.1 1.7H17"
                      stroke="currentColor"
                      strokeWidth="1.5"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      initial={{ pathLength: 0 }}
                      animate={{ pathLength: 1 }}
                      transition={{ duration: 2 }}
                    />
                    <motion.path
                      d="M17 18a2 2 0 100 4 2 2 0 000-4zM7 18a2 2 0 100 4 2 2 0 000-4z"
                      stroke="currentColor"
                      strokeWidth="1.5"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      initial={{ pathLength: 0 }}
                      animate={{ pathLength: 1 }}
                      transition={{ duration: 1.5, delay: 1.5 }}
                    />
                  </svg>

                  {/* Animated empty indicator */}
                  <motion.div
                    className="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 w-16 h-0.5 bg-gray-600"
                    initial={{ rotate: 45, scale: 0 }}
                    animate={{ rotate: 45, scale: 1 }}
                    transition={{ delay: 2.5, duration: 0.5 }}
                  />
                </motion.div>

                <motion.h3
                  className="font-playfair text-2xl font-bold text-white mb-3"
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.5, duration: 0.5 }}
                >
                  Your cart is empty
                </motion.h3>

                <motion.p
                  className="font-poppins text-gray-400 mb-8 max-w-md mx-auto"
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  transition={{ delay: 0.7, duration: 0.5 }}
                >
                  Your culinary journey awaits! Visit our menu to discover premium BBQ dishes crafted to perfection.
                </motion.p>

                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.9, duration: 0.5 }}
                >
                  <Link href="/menu">
                    <motion.button
                      className="relative overflow-hidden rounded-md px-8 py-3 bg-transparent"
                      whileHover={{ scale: 1.03 }}
                      whileTap={{ scale: 0.97 }}
                    >
                      {/* Button Background */}
                      <span className="absolute inset-0 w-full h-full bg-gradient-to-r from-cyan-800/70 to-cyan-600/70"></span>

                      {/* Button Glow Effect */}
                      <span className="absolute inset-0 w-full h-full transition-all duration-300
                                      bg-gradient-to-r from-cyan-600 via-blue-600 to-cyan-600
                                      opacity-0 hover:opacity-100 hover:blur-md"></span>

                      {/* Button Border */}
                      <span className="absolute inset-0 w-full h-full border border-cyan-500 rounded-md"></span>

                      {/* Button Text */}
                      <span className="relative z-10 flex items-center font-medium text-white">
                        <svg className="w-5 h-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h8m-8 6h16" />
                        </svg>
                        Browse Menu
                      </span>
                    </motion.button>
                  </Link>
                </motion.div>
              </div>
            </div>
          </motion.div>
        </div>
      </section>
    );
  }

  return (
    <section className="min-h-screen py-32 bg-black relative overflow-hidden">
      {/* Animated Gradient Background */}
      <div
        className="absolute inset-0 z-0"
        style={{
          background: "radial-gradient(circle at 20% 80%, rgba(0, 255, 255, 0.03), transparent 33%), " +
                    "radial-gradient(circle at 80% 20%, rgba(255, 0, 255, 0.03), transparent 33%), " +
                    "radial-gradient(circle at 50% 50%, rgba(57, 255, 20, 0.01), rgba(0, 0, 0, 1) 100%)",
        }}
      ></div>

      {/* Neon Grid Overlay */}
      <div className="absolute inset-0 z-0 opacity-[0.02]"
           style={{
             backgroundImage: "linear-gradient(to right, #FF00FF 1px, transparent 1px), linear-gradient(to bottom, #FF00FF 1px, transparent 1px)",
             backgroundSize: "40px 40px"
           }}>
      </div>

      <div className="container mx-auto px-4 relative z-10">
        {/* Header Section */}
        <motion.div
          ref={headerRef}
          initial={{ opacity: 0, y: -20 }}
          animate={headerInView ? { opacity: 1, y: 0 } : {}}
          transition={{ duration: 0.8 }}
          className="text-center mb-16"
        >
          <motion.h2
            className="font-playfair text-4xl md:text-5xl font-bold mb-4 text-white inline-block"
            initial={{ opacity: 0 }}
            animate={headerInView ? { opacity: 1 } : {}}
            transition={{ duration: 0.5, delay: 0.2 }}
          >
            <span className="text-white">Complete </span>
            <motion.span
              className="text-transparent bg-clip-text bg-gradient-to-r from-lime-400 to-green-500"
              animate={{
                textShadow: [
                  "0 0 5px rgba(57, 255, 20, 0.7), 0 0 10px rgba(57, 255, 20, 0.5), 0 0 15px rgba(57, 255, 20, 0.3)",
                  "0 0 5px rgba(0, 255, 255, 0.7), 0 0 10px rgba(0, 255, 255, 0.5), 0 0 15px rgba(0, 255, 255, 0.3)",
                  "0 0 5px rgba(57, 255, 20, 0.7), 0 0 10px rgba(57, 255, 20, 0.5), 0 0 15px rgba(57, 255, 20, 0.3)"
                ]
              }}
              transition={{ duration: 3, repeat: Infinity }}
            >
              Your Order
            </motion.span>
          </motion.h2>

          <motion.p
            className="font-poppins text-lg text-gray-400 max-w-2xl mx-auto"
            initial={{ opacity: 0, y: 20 }}
            animate={headerInView ? { opacity: 1, y: 0 } : {}}
            transition={{ duration: 0.6, delay: 0.4 }}
          >
            We're just a few details away from your premium BBQ experience
          </motion.p>
        </motion.div>

        <motion.div
          ref={formRef}
          variants={containerVariants}
          initial="hidden"
          animate={formInView ? "visible" : "hidden"}
          className="max-w-6xl mx-auto"
        >
          <div className="lg:grid lg:grid-cols-3 lg:gap-10">
            {/* Checkout Form (2/3 width on desktop) */}
            <motion.div
              className="lg:col-span-2 mb-10 lg:mb-0"
              variants={itemVariants}
            >
              <div className="backdrop-blur-sm bg-black/20 rounded-xl border border-gray-800
                             overflow-hidden shadow-[0_10px_30px_rgba(0,0,0,0.3)]">
                {/* Form Header */}
                <div className="relative">
                  <div className="h-3 w-full bg-gradient-to-r from-cyan-500 via-fuchsia-500 to-lime-500"></div>
                  <div className="px-8 py-6 border-b border-gray-800">
                    <h3 className="font-playfair text-2xl font-bold text-white flex items-center">
                      <svg className="w-6 h-6 mr-2 text-cyan-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                      </svg>
                      Delivery Information
                    </h3>
                  </div>
                </div>

                {/* Form Content */}
                <div className="p-8">
                  <form onSubmit={handleSubmit(onSubmit)}>
                    {/* Order Type Selection */}
                    <div className="mb-8">
                      <label className="block font-medium text-gray-300 mb-4">
                        <span>Order Type</span>
                      </label>

                      <div className="grid grid-cols-2 gap-4">
                        <motion.label
                          className={`flex flex-col items-center justify-center bg-black/40 p-6 rounded-lg border
                            ${orderType === "delivery"
                              ? 'border-cyan-500 shadow-[0_0_15px_rgba(0,255,255,0.2)]'
                              : 'border-gray-700'}
                            cursor-pointer transition-all duration-300 hover:border-cyan-700`}
                          whileHover={{ scale: 1.02 }}
                          whileTap={{ scale: 0.98 }}
                        >
                          <input
                            type="radio"
                            value="delivery"
                            className="sr-only"
                            {...register("orderType")}
                          />
                          <div className="w-14 h-14 mb-3 rounded-full bg-gradient-to-br from-cyan-500/40 to-cyan-700/40
                                        flex items-center justify-center text-cyan-400">
                            <svg className="w-8 h-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5}
                                d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5}
                                d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                            </svg>
                          </div>
                          <span className="font-medium text-white text-lg">Delivery</span>
                          <span className="text-gray-400 text-sm mt-1">
                            To your address
                          </span>

                          {orderType === "delivery" && (
                            <motion.div
                              className="absolute top-2 right-2 w-4 h-4 rounded-full bg-cyan-500"
                              initial={{ scale: 0 }}
                              animate={{ scale: 1 }}
                              transition={{ type: "spring", stiffness: 500, damping: 15 }}
                            />
                          )}
                        </motion.label>

                        <motion.label
                          className={`flex flex-col items-center justify-center bg-black/40 p-6 rounded-lg border
                            ${orderType === "takeaway"
                              ? 'border-cyan-500 shadow-[0_0_15px_rgba(0,255,255,0.2)]'
                              : 'border-gray-700'}
                            cursor-pointer transition-all duration-300 hover:border-cyan-700`}
                          whileHover={{ scale: 1.02 }}
                          whileTap={{ scale: 0.98 }}
                        >
                          <input
                            type="radio"
                            value="takeaway"
                            className="sr-only"
                            {...register("orderType")}
                          />
                          <div className="w-14 h-14 mb-3 rounded-full bg-gradient-to-br from-fuchsia-500/40 to-fuchsia-700/40
                                        flex items-center justify-center text-fuchsia-400">
                            <svg className="w-8 h-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5}
                                d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 002 2z" />
                            </svg>
                          </div>
                          <span className="font-medium text-white text-lg">Takeaway</span>
                          <span className="text-gray-400 text-sm mt-1">
                            Pick up at restaurant
                          </span>

                          {orderType === "takeaway" && (
                            <motion.div
                              className="absolute top-2 right-2 w-4 h-4 rounded-full bg-fuchsia-500"
                              initial={{ scale: 0 }}
                              animate={{ scale: 1 }}
                              transition={{ type: "spring", stiffness: 500, damping: 15 }}
                            />
                          )}
                        </motion.label>
                      </div>
                    </div>

                    {/* Delivery/Pickup Time */}
                    <div className="mb-8">
                      <label className="block font-medium text-gray-300 mb-4">
                        <span>{orderType === "delivery" ? "Delivery" : "Pickup"} Time</span>
                      </label>

                      <div className="grid grid-cols-2 gap-4 mb-4">
                        <motion.label
                          className={`flex flex-col items-center justify-center bg-black/40 p-5 rounded-lg border
                            ${deliveryTime === "asap"
                              ? 'border-lime-500 shadow-[0_0_15px_rgba(57,255,20,0.2)]'
                              : 'border-gray-700'}
                            cursor-pointer transition-all duration-300 hover:border-lime-700`}
                          whileHover={{ scale: 1.02 }}
                          whileTap={{ scale: 0.98 }}
                        >
                          <input
                            type="radio"
                            value="asap"
                            className="sr-only"
                            {...register("deliveryTime")}
                          />
                          <div className="w-12 h-12 mb-2 rounded-full bg-gradient-to-br from-lime-500/40 to-lime-700/40
                                        flex items-center justify-center text-lime-400">
                            <svg className="w-7 h-7" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5}
                                d="M13 10V3L4 14h7v7l9-11h-7z" />
                            </svg>
                          </div>
                          <span className="font-medium text-white">As Soon As Possible</span>
                          <span className="text-gray-400 text-xs mt-1">
                            30-45 min estimated
                          </span>

                          {deliveryTime === "asap" && (
                            <motion.div
                              className="absolute top-2 right-2 w-4 h-4 rounded-full bg-lime-500"
                              initial={{ scale: 0 }}
                              animate={{ scale: 1 }}
                              transition={{ type: "spring", stiffness: 500, damping: 15 }}
                            />
                          )}
                        </motion.label>

                        <motion.label
                          className={`flex flex-col items-center justify-center bg-black/40 p-5 rounded-lg border
                            ${deliveryTime === "scheduled"
                              ? 'border-lime-500 shadow-[0_0_15px_rgba(57,255,20,0.2)]'
                              : 'border-gray-700'}
                            cursor-pointer transition-all duration-300 hover:border-lime-700`}
                          whileHover={{ scale: 1.02 }}
                          whileTap={{ scale: 0.98 }}
                        >
                          <input
                            type="radio"
                            value="scheduled"
                            className="sr-only"
                            {...register("deliveryTime")}
                          />
                          <div className="w-12 h-12 mb-2 rounded-full bg-gradient-to-br from-lime-500/40 to-lime-700/40
                                        flex items-center justify-center text-lime-400">
                            <svg className="w-7 h-7" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5}
                                d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                          </div>
                          <span className="font-medium text-white">Schedule for Later</span>
                          <span className="text-gray-400 text-xs mt-1">
                            Select a time
                          </span>

                          {deliveryTime === "scheduled" && (
                            <motion.div
                              className="absolute top-2 right-2 w-4 h-4 rounded-full bg-lime-500"
                              initial={{ scale: 0 }}
                              animate={{ scale: 1 }}
                              transition={{ type: "spring", stiffness: 500, damping: 15 }}
                            />
                          )}
                        </motion.label>
                      </div>

                      {/* Scheduled Time Selector */}
                      <AnimatePresence>
                        {deliveryTime === "scheduled" && (
                          <motion.div
                            initial={{ opacity: 0, height: 0 }}
                            animate={{ opacity: 1, height: "auto" }}
                            exit={{ opacity: 0, height: 0 }}
                            transition={{ duration: 0.3 }}
                            className="overflow-hidden"
                          >
                            <div className="pt-3">
                              <motion.div
                                animate={errors.scheduledTime
                                  ? "error"
                                  : focusedField === "scheduledTime"
                                    ? "focused"
                                    : "initial"
                                }
                                variants={formInputVariants}
                                custom="scheduledTime"
                                className="relative"
                              >
                                <div className="relative">
                                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                    <svg className="h-5 w-5 text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5}
                                        d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                                    </svg>
                                  </div>
                                  <select
                                    id="scheduledTime"
                                    className={`w-full bg-black/40 border ${
                                      errors.scheduledTime
                                        ? 'border-red-500 focus:border-red-400'
                                        : focusedField === "scheduledTime"
                                          ? 'border-lime-500'
                                          : 'border-gray-700 focus:border-lime-700'
                                    } rounded-lg pl-10 px-4 py-3 font-poppins text-white focus:outline-none transition-all duration-300 appearance-none`}
                                    {...registerWithFocus("scheduledTime")}
                                  >
                                    <option value="">Select a time</option>
                                    {/* Dynamic time options based on current time */}
                                    <option value="12:00">Today, 12:00 PM</option>
                                    <option value="12:30">Today, 12:30 PM</option>
                                    <option value="13:00">Today, 1:00 PM</option>
                                    <option value="13:30">Today, 1:30 PM</option>
                                    <option value="14:00">Today, 2:00 PM</option>
                                    <option value="18:00">Today, 6:00 PM</option>
                                    <option value="18:30">Today, 6:30 PM</option>
                                    <option value="19:00">Today, 7:00 PM</option>
                                    <option value="19:30">Today, 7:30 PM</option>
                                    <option value="20:00">Today, 8:00 PM</option>
                                  </select>
                                  <div className="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                                    <svg className="h-5 w-5 text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M19 9l-7 7-7-7" />
                                    </svg>
                                  </div>
                                </div>
                                {focusedField === "scheduledTime" && (
                                  <motion.span
                                    className="absolute inset-0 rounded-lg ring-2 ring-lime-500 pointer-events-none"
                                    initial={{ opacity: 0 }}
                                    animate={{ opacity: 1 }}
                                    exit={{ opacity: 0 }}
                                  />
                                )}
                              </motion.div>
                              {errors.scheduledTime && (
                                <motion.p
                                  initial={{ opacity: 0, y: -10 }}
                                  animate={{ opacity: 1, y: 0 }}
                                  className="mt-1 font-poppins text-red-500 text-sm"
                                >
                                  {errors.scheduledTime.message}
                                </motion.p>
                              )}
                            </div>
                          </motion.div>
                        )}
                      </AnimatePresence>
                    </div>

                    {/* Personal Information Section Divider */}
                    <div className="relative flex py-5 items-center mb-6">
                      <div className="flex-grow border-t border-gray-800"></div>
                      <span className="flex-shrink mx-3 text-gray-400 font-medium">
                        Personal Information
                      </span>
                      <div className="flex-grow border-t border-gray-800"></div>
                    </div>

                    {/* First Name */}
                    <div className="mb-8">
                      <label htmlFor="firstName" className="block font-medium text-gray-300 mb-2 flex items-center">
                        <span>First Name</span>
                        {errors.firstName && (
                          <motion.span
                            initial={{ opacity: 0, scale: 0.5 }}
                            animate={{ opacity: 1, scale: 1 }}
                            className="ml-2 text-red-500 text-xs"
                          >
                            Required
                          </motion.span>
                        )}
                      </label>
                      <motion.div
                        animate={errors.firstName
                          ? "error"
                          : focusedField === "firstName"
                            ? "focused"
                            : "initial"
                        }
                        variants={formInputVariants}
                        custom="firstName"
                        className="relative"
                      >
                        <input
                          id="firstName"
                          className={`w-full bg-black/40 border ${
                            errors.firstName
                              ? 'border-red-500 focus:border-red-400'
                              : focusedField === "firstName"
                                ? 'border-cyan-500'
                                : 'border-gray-700 focus:border-cyan-700'
                          } rounded-lg px-4 py-3 font-poppins text-white focus:outline-none transition-all duration-300`}
                          placeholder="John"
                          {...registerWithFocus("firstName")}
                        />
                        {focusedField === "firstName" && (
                          <motion.span
                            className="absolute inset-0 rounded-lg ring-2 ring-cyan-500 pointer-events-none"
                            initial={{ opacity: 0 }}
                            animate={{ opacity: 1 }}
                            exit={{ opacity: 0 }}
                          />
                        )}
                      </motion.div>
                      {errors.firstName && (
                        <motion.p
                          initial={{ opacity: 0, y: -10 }}
                          animate={{ opacity: 1, y: 0 }}
                          className="mt-1 font-poppins text-red-500 text-sm"
                        >
                          {errors.firstName.message}
                        </motion.p>
                      )}
                    </div>

                    {/* Email Address */}
                    <div className="mb-8">
                      <label htmlFor="email" className="block font-medium text-gray-300 mb-2 flex items-center">
                        <span>Email Address (Optional)</span>
                      </label>
                      <motion.div
                        animate={errors.email
                          ? "error"
                          : focusedField === "email"
                            ? "focused"
                            : "initial"
                        }
                        variants={formInputVariants}
                        custom="email"
                        className="relative"
                      >
                        <div className="relative">
                          <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                            <svg className="h-5 w-5 text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                            </svg>
                          </div>
                          <input
                            id="email"
                            type="email"
                            className={`w-full bg-black/40 border ${
                              errors.email
                                ? 'border-red-500 focus:border-red-400'
                                : focusedField === "email"
                                  ? 'border-cyan-500'
                                  : 'border-gray-700 focus:border-cyan-700'
                            } rounded-lg pl-10 px-4 py-3 font-poppins text-white focus:outline-none transition-all duration-300`}
                            placeholder="<EMAIL> (optional)"
                            {...registerWithFocus("email")}
                          />
                        </div>
                        {focusedField === "email" && (
                          <motion.span
                            className="absolute inset-0 rounded-lg ring-2 ring-cyan-500 pointer-events-none"
                            initial={{ opacity: 0 }}
                            animate={{ opacity: 1 }}
                            exit={{ opacity: 0 }}
                          />
                        )}
                      </motion.div>
                      {errors.email && (
                        <motion.p
                          initial={{ opacity: 0, y: -10 }}
                          animate={{ opacity: 1, y: 0 }}
                          className="mt-1 font-poppins text-red-500 text-sm"
                        >
                          {errors.email.message}
                        </motion.p>
                      )}
                    </div>

                    {/* Phone Number */}
                    <div className="mb-8">
                      <label htmlFor="phone" className="block font-medium text-gray-300 mb-2 flex items-center">
                        <span>Phone Number</span>
                        {errors.phone && (
                          <motion.span
                            initial={{ opacity: 0, scale: 0.5 }}
                            animate={{ opacity: 1, scale: 1 }}
                            className="ml-2 text-red-500 text-xs"
                          >
                            Required
                          </motion.span>
                        )}
                      </label>
                      <motion.div
                        animate={errors.phone
                          ? "error"
                          : focusedField === "phone"
                            ? "focused"
                            : "initial"
                        }
                        variants={formInputVariants}
                        custom="phone"
                        className="relative"
                      >
                        <div className="relative">
                          <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                            <svg className="h-5 w-5 text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
                            </svg>
                          </div>
                          <input
                            id="phone"
                            type="tel"
                            className={`w-full bg-black/40 border ${
                              errors.phone
                                ? 'border-red-500 focus:border-red-400'
                                : focusedField === "phone"
                                  ? 'border-cyan-500'
                                  : 'border-gray-700 focus:border-cyan-700'
                            } rounded-lg pl-10 px-4 py-3 font-poppins text-white focus:outline-none transition-all duration-300`}
                            placeholder="+47 12 34 56 78"
                            {...registerWithFocus("phone")}
                          />
                        </div>
                        {focusedField === "phone" && (
                          <motion.span
                            className="absolute inset-0 rounded-lg ring-2 ring-cyan-500 pointer-events-none"
                            initial={{ opacity: 0 }}
                            animate={{ opacity: 1 }}
                            exit={{ opacity: 0 }}
                          />
                        )}
                      </motion.div>
                      {errors.phone && (
                        <motion.p
                          initial={{ opacity: 0, y: -10 }}
                          animate={{ opacity: 1, y: 0 }}
                          className="mt-1 font-poppins text-red-500 text-sm"
                        >
                          {errors.phone.message}
                        </motion.p>
                      )}
                    </div>

                    {/* Delivery Section Divider */}
                    <div className="relative flex py-5 items-center mb-6">
                      <div className="flex-grow border-t border-gray-800"></div>
                      <span className="flex-shrink mx-3 text-gray-400 font-medium">
                        {orderType === "delivery" ? "Delivery Address" : "Additional Details"}
                      </span>
                      <div className="flex-grow border-t border-gray-800"></div>
                    </div>

                    {/* Address Fields - Only shown for delivery */}
                    <AnimatePresence>
                      {orderType === "delivery" && (
                        <motion.div
                          initial={{ opacity: 0, height: 0 }}
                          animate={{ opacity: 1, height: "auto" }}
                          exit={{ opacity: 0, height: 0 }}
                          transition={{ duration: 0.3 }}
                          className="space-y-8 overflow-hidden"
                        >
                          {/* Address */}
                          <div className="mb-8">
                            <label htmlFor="address" className="block font-medium text-gray-300 mb-2 flex items-center">
                              <span>Delivery Address</span>
                              {errors.address && (
                                <motion.span
                                  initial={{ opacity: 0, scale: 0.5 }}
                                  animate={{ opacity: 1, scale: 1 }}
                                  className="ml-2 text-red-500 text-xs"
                                >
                                  Required
                                </motion.span>
                              )}
                            </label>
                            <motion.div
                              animate={errors.address
                                ? "error"
                                : focusedField === "address"
                                  ? "focused"
                                  : "initial"
                              }
                              variants={formInputVariants}
                              custom="address"
                              className="relative"
                            >
                              <div className="relative">
                                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                  <svg className="h-5 w-5 text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                                  </svg>
                                </div>
                                <input
                                  id="address"
                                  className={`w-full bg-black/40 border ${
                                    errors.address
                                      ? 'border-red-500 focus:border-red-400'
                                      : focusedField === "address"
                                        ? 'border-cyan-500'
                                        : 'border-gray-700 focus:border-cyan-700'
                                  } rounded-lg pl-10 px-4 py-3 font-poppins text-white focus:outline-none transition-all duration-300`}
                                  placeholder="Street address"
                                  {...registerWithFocus("address")}
                                />
                              </div>
                              {focusedField === "address" && (
                                <motion.span
                                  className="absolute inset-0 rounded-lg ring-2 ring-cyan-500 pointer-events-none"
                                  initial={{ opacity: 0 }}
                                  animate={{ opacity: 1 }}
                                  exit={{ opacity: 0 }}
                                />
                              )}
                            </motion.div>
                            {errors.address && (
                              <motion.p
                                initial={{ opacity: 0, y: -10 }}
                                animate={{ opacity: 1, y: 0 }}
                                className="mt-1 font-poppins text-red-500 text-sm"
                              >
                                {errors.address.message}
                              </motion.p>
                            )}

                            {/* Geolocation Button - Only show for delivery */}
                            {orderType === "delivery" && (
                              <motion.div
                                className="mt-3 mb-6"
                                initial={{ opacity: 0, y: -10 }}
                                animate={{ opacity: 1, y: 0 }}
                                exit={{ opacity: 0, y: -10 }}
                                transition={{ duration: 0.3 }}
                              >
                                <motion.button
                                  type="button"
                                  onClick={(e) => {
                                    console.log('🖱️ Geolocation button clicked');
                                    e.preventDefault();
                                    getCurrentLocation();
                                  }}
                                  disabled={isGettingLocation}
                                  className={`flex items-center justify-center px-4 py-2 rounded-lg border transition-all duration-300
                                    ${isGettingLocation
                                      ? 'bg-gray-800/50 border-gray-600 cursor-not-allowed'
                                      : 'bg-black/40 border-gray-700 hover:border-cyan-600 hover:bg-black/60'
                                    } text-white font-poppins text-sm`}
                                  whileHover={!isGettingLocation ? { scale: 1.02 } : {}}
                                  whileTap={!isGettingLocation ? { scale: 0.98 } : {}}
                                >
                                  {isGettingLocation ? (
                                    <>
                                      <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-cyan-400" fill="none" viewBox="0 0 24 24">
                                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                      </svg>
                                      Getting location...
                                    </>
                                  ) : (
                                    <>
                                      <svg className="w-4 h-4 mr-2 text-cyan-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5}
                                          d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5}
                                          d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                                      </svg>
                                      Use Current Location
                                    </>
                                  )}
                                </motion.button>

                                {/* Location Error Message */}
                                <AnimatePresence>
                                  {locationError && (
                                    <motion.div
                                      initial={{ opacity: 0, y: -10 }}
                                      animate={{ opacity: 1, y: 0 }}
                                      exit={{ opacity: 0, y: -10 }}
                                      className="mt-2 p-3 bg-red-900/20 border border-red-800/30 rounded-lg"
                                    >
                                      <div className="flex items-start">
                                        <svg className="w-4 h-4 text-red-400 mt-0.5 mr-2 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5}
                                            d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                                        </svg>
                                        <p className="text-red-300 text-xs font-poppins">{locationError}</p>
                                      </div>
                                    </motion.div>
                                  )}
                                </AnimatePresence>

                                {/* Location Success Message */}
                                <AnimatePresence>
                                  {locationSuccess && (
                                    <motion.div
                                      initial={{ opacity: 0, y: -10 }}
                                      animate={{ opacity: 1, y: 0 }}
                                      exit={{ opacity: 0, y: -10 }}
                                      className="mt-2 p-3 bg-green-900/20 border border-green-800/30 rounded-lg"
                                    >
                                      <div className="flex items-start">
                                        <svg className="w-4 h-4 text-green-400 mt-0.5 mr-2 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5}
                                            d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                                        </svg>
                                        <p className="text-green-300 text-xs font-poppins">Address successfully filled from your current location!</p>
                                      </div>
                                    </motion.div>
                                  )}
                                </AnimatePresence>

                                {/* Helper Text */}
                                <div className="mt-2 text-xs text-gray-500 font-poppins space-y-1">
                                  <p>Click to automatically fill your address using your current location</p>
                                  <p className="text-gray-600">
                                    💡 Your browser will ask for location permission when you click the button
                                  </p>
                                </div>
                              </motion.div>
                            )}
                          </div>

                          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                            {/* Postal Code */}
                            <div>
                              <label htmlFor="postalCode" className="block font-medium text-gray-300 mb-2 flex items-center">
                                <span>Postal Code</span>
                                {errors.postalCode && (
                                  <motion.span
                                    initial={{ opacity: 0, scale: 0.5 }}
                                    animate={{ opacity: 1, scale: 1 }}
                                    className="ml-2 text-red-500 text-xs"
                                  >
                                    Required
                                  </motion.span>
                                )}
                              </label>
                              <motion.div
                                animate={errors.postalCode
                                  ? "error"
                                  : focusedField === "postalCode"
                                    ? "focused"
                                    : "initial"
                                }
                                variants={formInputVariants}
                                custom="postalCode"
                                className="relative"
                              >
                                <input
                                  id="postalCode"
                                  className={`w-full bg-black/40 border ${
                                    errors.postalCode
                                      ? 'border-red-500 focus:border-red-400'
                                      : focusedField === "postalCode"
                                        ? 'border-cyan-500'
                                        : 'border-gray-700 focus:border-cyan-700'
                                  } rounded-lg px-4 py-3 font-poppins text-white focus:outline-none transition-all duration-300`}
                                  placeholder="0000"
                                  {...registerWithFocus("postalCode")}
                                />
                                {focusedField === "postalCode" && (
                                  <motion.span
                                    className="absolute inset-0 rounded-lg ring-2 ring-cyan-500 pointer-events-none"
                                    initial={{ opacity: 0 }}
                                    animate={{ opacity: 1 }}
                                    exit={{ opacity: 0 }}
                                  />
                                )}
                              </motion.div>
                              {errors.postalCode && (
                                <motion.p
                                  initial={{ opacity: 0, y: -10 }}
                                  animate={{ opacity: 1, y: 0 }}
                                  className="mt-1 font-poppins text-red-500 text-sm"
                                >
                                  {errors.postalCode.message}
                                </motion.p>
                              )}
                            </div>

                            {/* City */}
                            <div className="md:col-span-2">
                              <label htmlFor="city" className="block font-medium text-gray-300 mb-2 flex items-center">
                                <span>City</span>
                                {errors.city && (
                                  <motion.span
                                    initial={{ opacity: 0, scale: 0.5 }}
                                    animate={{ opacity: 1, scale: 1 }}
                                    className="ml-2 text-red-500 text-xs"
                                  >
                                    Required
                                  </motion.span>
                                )}
                              </label>
                              <motion.div
                                animate={errors.city
                                  ? "error"
                                  : focusedField === "city"
                                    ? "focused"
                                    : "initial"
                                }
                                variants={formInputVariants}
                                custom="city"
                                className="relative"
                              >
                                <input
                                  id="city"
                                  className={`w-full bg-black/40 border ${
                                    errors.city
                                      ? 'border-red-500 focus:border-red-400'
                                      : focusedField === "city"
                                        ? 'border-cyan-500'
                                        : 'border-gray-700 focus:border-cyan-700'
                                  } rounded-lg px-4 py-3 font-poppins text-white focus:outline-none transition-all duration-300`}
                                  placeholder="Oslo"
                                  {...registerWithFocus("city")}
                                />
                                {focusedField === "city" && (
                                  <motion.span
                                    className="absolute inset-0 rounded-lg ring-2 ring-cyan-500 pointer-events-none"
                                    initial={{ opacity: 0 }}
                                    animate={{ opacity: 1 }}
                                    exit={{ opacity: 0 }}
                                  />
                                )}
                              </motion.div>
                              {errors.city && (
                                <motion.p
                                  initial={{ opacity: 0, y: -10 }}
                                  animate={{ opacity: 1, y: 0 }}
                                  className="mt-1 font-poppins text-red-500 text-sm"
                                >
                                  {errors.city.message}
                                </motion.p>
                              )}
                            </div>
                          </div>
                        </motion.div>
                      )}
                    </AnimatePresence>

                    {/* Special Instructions */}
                    <div className="mb-8">
                      <label htmlFor="notes" className="block font-medium text-gray-300 mb-2 flex items-center">
                        <span>Special Instructions (Optional)</span>
                      </label>
                      <motion.div
                        animate={focusedField === "notes" ? "focused" : "initial"}
                        variants={formInputVariants}
                        custom="notes"
                        className="relative"
                      >
                        <div className="relative">
                          <div className="absolute top-3 left-3 flex items-start pointer-events-none">
                            <svg className="h-5 w-5 text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                            </svg>
                          </div>
                          <textarea
                            id="notes"
                            rows={3}
                            className={`w-full bg-black/40 border border-gray-700 rounded-lg pl-10 px-4 py-3 font-poppins text-white focus:outline-none focus:border-cyan-700 transition-all duration-300`}
                            placeholder="Any special requests or delivery instructions"
                            {...registerWithFocus("notes")}
                          ></textarea>
                        </div>
                        {focusedField === "notes" && (
                          <motion.span
                            className="absolute inset-0 rounded-lg ring-2 ring-cyan-500 pointer-events-none"
                            initial={{ opacity: 0 }}
                            animate={{ opacity: 1 }}
                            exit={{ opacity: 0 }}
                          />
                        )}
                      </motion.div>
                    </div>

                    {/* Order Button */}
                    <motion.div
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: 0.4, duration: 0.5 }}
                      className="mt-10"
                    >
                      <motion.button
                        type="submit"
                        disabled={isSubmitting || !isValid || !isOpen}
                        className={`relative w-full overflow-hidden rounded-lg p-4 flex items-center justify-center
                                 font-bold text-white tracking-wide text-lg
                                 ${isValid && isOpen ? 'opacity-100' : 'opacity-70'}`}
                        whileHover={isValid && isOpen ? { scale: 1.02 } : {}}
                        whileTap={isValid && isOpen ? { scale: 0.98 } : {}}
                        title={!isOpen ? "Restaurant is currently closed" : ""}
                      >
                        {/* Button Background */}
                        <span className="absolute inset-0 w-full h-full bg-gradient-to-r from-lime-600 to-green-700"></span>

                        {/* Animated Glow Effect */}
                        <motion.span
                          className="absolute inset-0 w-full h-full bg-gradient-to-r from-lime-500 to-green-600 opacity-0"
                          animate={{
                            opacity: [0, 0.5, 0],
                            scale: [1, 1.05, 1],
                            boxShadow: [
                              "0 0 0 rgba(57, 255, 20, 0)",
                              "0 0 20px rgba(57, 255, 20, 0.5)",
                              "0 0 0 rgba(57, 255, 20, 0)"
                            ]
                          }}
                          transition={{ duration: 2.5, repeat: Infinity }}
                        />

                        {/* Button Text */}
                        <span className="relative z-10 flex items-center">
                          {isSubmitting ? (
                            <>
                              <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" fill="none" viewBox="0 0 24 24">
                                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                              </svg>
                              Processing...
                            </>
                          ) : !isOpen ? (
                            <>
                              <svg className="w-6 h-6 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                              </svg>
                              Restaurant Closed
                            </>
                          ) : (
                            <>
                              Place Order
                              <svg className="w-6 h-6 ml-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14 5l7 7m0 0l-7 7m7-7H3" />
                              </svg>
                            </>
                          )}
                        </span>
                      </motion.button>
                      <p className="text-gray-500 text-xs text-center mt-3">
                        By placing an order you agree to our Terms and Conditions
                      </p>
                    </motion.div>
                  </form>
                </div>
              </div>
            </motion.div>

            {/* Order Summary (1/3 width on desktop) */}
            <motion.div
              variants={itemVariants}
              ref={summaryRef}
              className="lg:block"
            >
              <div className="backdrop-blur-sm bg-black/30 rounded-xl border border-gray-800
                            shadow-[0_0_25px_rgba(0,0,0,0.2)] sticky top-24">

                {/* Glowing top border */}
                <div className="h-1 w-full bg-gradient-to-r from-fuchsia-500 via-cyan-500 to-fuchsia-500 rounded-t-xl"></div>

                <div className="p-6">
                  <h3 className="font-playfair text-xl font-bold text-white mb-6 flex items-center">
                    <svg className="w-5 h-5 mr-2 text-fuchsia-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 3h2l.4 2M7 13h10l4-8H5.4M7 13L5.4 5M7 13l-2.293 2.293c-.63.63-.184 1.707.707 1.707H17m0 0a2 2 0 100 4 2 2 0 000-4zm-8 2a2 2 0 11-4 0 2 2 0 014 0z" />
                    </svg>
                    Order Summary
                  </h3>

                  <div className="space-y-5 mb-6">
                    {/* Order Items Summary */}
                    <div className="border-b border-gray-800 pb-4">
                      <motion.div className="space-y-3">
                        <AnimatePresence>
                          {cartItems.map((item, index) => (
                            <motion.div
                              key={item.id}
                              initial={{ opacity: 0, y: 10 }}
                              animate={{ opacity: 1, y: 0 }}
                              exit={{ opacity: 0, y: -10 }}
                              transition={{ delay: index * 0.1 }}
                              className="flex items-start gap-3"
                            >
                              <div className="relative w-10 h-10 rounded overflow-hidden flex-shrink-0">
                                <img
                                  src={item.imageUrl}
                                  alt={item.name}
                                  className="w-full h-full object-cover"
                                />
                              </div>
                              <div className="flex-1 min-w-0">
                                <div className="flex justify-between">
                                  <p className="font-poppins font-medium text-sm text-white truncate pr-2">
                                    {item.name}
                                  </p>
                                  <div className="text-xs font-medium text-gray-400 whitespace-nowrap">
                                    × {item.quantity}
                                  </div>
                                </div>
                                <motion.div
                                  className="font-poppins text-sm text-transparent bg-clip-text bg-gradient-to-r from-fuchsia-400 to-cyan-400"
                                  animate={{
                                    textShadow: ['0 0 3px rgba(255, 0, 255, 0.2)', '0 0 5px rgba(255, 0, 255, 0.3)', '0 0 3px rgba(255, 0, 255, 0.2)']
                                  }}
                                  transition={{ duration: 1.5, repeat: Infinity }}
                                >
                                  {formatCurrency(item.price * item.quantity)}
                                </motion.div>
                              </div>
                            </motion.div>
                          ))}
                        </AnimatePresence>
                      </motion.div>
                    </div>

                    {/* Costs Breakdown */}
                    <div className="border-b border-gray-800 pb-4 space-y-3">
                      <motion.div
                        className="flex justify-between"
                        initial={{ opacity: 0 }}
                        animate={{ opacity: 1 }}
                        transition={{ delay: 0.2 }}
                      >
                        <span className="font-poppins text-gray-400">Subtotal</span>
                        <motion.span
                          className="font-poppins font-medium text-white"
                          animate={
                            isUpdating !== null
                              ? { scale: [1, 1.05, 1] }
                              : {}
                          }
                          transition={{ duration: 0.3 }}
                        >
                          {formatCurrency(subtotal)}
                        </motion.span>
                      </motion.div>

                      <motion.div
                        className="flex justify-between"
                        initial={{ opacity: 0 }}
                        animate={{ opacity: 1 }}
                        transition={{ delay: 0.3 }}
                      >
                        <span className="font-poppins text-gray-400">VAT (25%)</span>
                        <span className="font-poppins font-medium text-white">
                          {formatCurrency(VAT)}
                        </span>
                      </motion.div>

                      <motion.div
                        className="flex justify-between"
                        initial={{ opacity: 0 }}
                        animate={{ opacity: 1 }}
                        transition={{ delay: 0.4 }}
                      >
                        <span className="font-poppins text-gray-400">Delivery Fee</span>
                        <span className="font-poppins font-medium text-white">{formatCurrency(actualDeliveryFee)}</span>
                      </motion.div>
                    </div>

                    {/* Total */}
                    <motion.div
                      className="flex justify-between items-center"
                      initial={{ opacity: 0 }}
                      animate={{ opacity: 1 }}
                      transition={{ delay: 0.5 }}
                    >
                      <span className="font-poppins font-medium text-lg text-white">Total</span>
                      <motion.span
                        className="font-poppins font-bold text-transparent bg-clip-text bg-gradient-to-r from-lime-400 to-green-500 text-xl"
                        animate={{
                          textShadow: ['0 0 5px rgba(57, 255, 20, 0.4)', '0 0 10px rgba(57, 255, 20, 0.6)', '0 0 5px rgba(57, 255, 20, 0.4)']
                        }}
                        transition={{ duration: 0.5, repeat: Infinity }}
                      >
                        {formatCurrency(animatedTotal)}
                      </motion.span>
                    </motion.div>
                  </div>

                  {/* Payment Method Selection */}
                  <motion.div
                    className="mb-6"
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.6, duration: 0.5 }}
                  >
                    <h4 className="font-playfair text-lg font-medium text-white mb-3 flex items-center">
                      <svg className="w-5 h-5 mr-2 text-lime-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z" />
                      </svg>
                      Payment Method
                    </h4>

                    <div className="space-y-3">
                      <motion.label
                        className={`flex items-center bg-black/40 p-3 rounded-lg border border-gray-700 cursor-pointer
                                  ${focusedField === "paymentMethod" ? 'border-lime-600 shadow-[0_0_10px_rgba(57,255,20,0.2)]' : 'hover:border-gray-600'}`}
                        whileHover={{ scale: 1.01 }}
                        whileTap={{ scale: 0.99 }}
                      >
                        <input
                          type="radio"
                          value="card"
                          className="mr-3 accent-lime-500"
                          {...registerWithFocus("paymentMethod")}
                        />
                        <span className="font-poppins text-white">Credit Card</span>
                        <div className="ml-auto flex space-x-2">
                          <span className="text-blue-400 opacity-80">
                            <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
                              <path d="M21.5,9.5h-19C1.7,9.5,1,8.8,1,8s0.7-1.5,1.5-1.5h19c0.8,0,1.5,0.7,1.5,1.5S22.3,9.5,21.5,9.5z M21.5,5h-19 C1.7,5,1,4.3,1,3.5S1.7,2,2.5,2h19C22.3,2,23,2.7,23,3.5S22.3,5,21.5,5z M21.5,14h-19c-0.8,0-1.5-0.7-1.5-1.5s0.7-1.5,1.5-1.5h19 c0.8,0,1.5,0.7,1.5,1.5S22.3,14,21.5,14z M21.5,22h-19c-0.8,0-1.5-0.7-1.5-1.5S1.7,19,2.5,19h19c0.8,0,1.5,0.7,1.5,1.5 S22.3,22,21.5,22z"/>
                            </svg>
                          </span>
                          <span className="text-red-400 opacity-80">
                            <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
                              <path d="M16,9C13.2,9,11,6.8,11,4s2.2-4,5-4s5,2.2,5,4S18.8,9,16,9z M16,2c-1.1,0-2,0.9-2,2s0.9,2,2,2s2-0.9,2-2S17.1,2,16,2z  M8,9C5.2,9,3,6.8,3,4s2.2-4,5-4s5,2.2,5,4S10.8,9,8,9z M8,2C6.9,2,6,2.9,6,4s0.9,2,2,2s2-0.9,2-2S9.1,2,8,2z"/>
                            </svg>
                          </span>
                        </div>
                      </motion.label>

                      <motion.label
                        className={`flex items-center bg-black/40 p-3 rounded-lg border border-gray-700 cursor-pointer
                                 ${focusedField === "paymentMethod" ? 'border-lime-600 shadow-[0_0_10px_rgba(57,255,20,0.2)]' : 'hover:border-gray-600'}`}
                        whileHover={{ scale: 1.01 }}
                        whileTap={{ scale: 0.99 }}
                      >
                        <input
                          type="radio"
                          value="vipps"
                          className="mr-3 accent-lime-500"
                          {...registerWithFocus("paymentMethod")}
                        />
                        <span className="font-poppins text-white">Vipps</span>
                      </motion.label>

                      <motion.label
                        className={`flex items-center bg-black/40 p-3 rounded-lg border border-gray-700 cursor-pointer
                                 ${focusedField === "paymentMethod" ? 'border-lime-600 shadow-[0_0_10px_rgba(57,255,20,0.2)]' : 'hover:border-gray-600'}`}
                        whileHover={{ scale: 1.01 }}
                        whileTap={{ scale: 0.99 }}
                      >
                        <input
                          type="radio"
                          value="cash"
                          className="mr-3 accent-lime-500"
                          {...registerWithFocus("paymentMethod")}
                        />
                        <span className="font-poppins text-white">Cash on Delivery</span>
                      </motion.label>
                    </div>
                  </motion.div>

                  {/* Estimated Delivery */}
                  <motion.div
                    className="bg-black/40 p-4 rounded-lg border border-lime-800/30
                             shadow-[0_0_15px_rgba(57,255,20,0.1)]"
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.7, duration: 0.5 }}
                  >
                    <div className="flex items-center mb-2">
                      <svg className="w-5 h-5 mr-2 text-lime-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                      <span className="font-medium text-white">Estimated Delivery</span>
                    </div>
                    <p className="text-gray-400 text-sm">{estimatedTime} after order confirmation</p>
                  </motion.div>
                </div>
              </div>
            </motion.div>
          </div>
        </motion.div>
      </div>
    </section>
  );
};

export default Checkout;
