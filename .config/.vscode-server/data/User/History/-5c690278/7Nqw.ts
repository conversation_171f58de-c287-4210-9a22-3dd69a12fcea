import { Router, Request, Response } from 'express';
import { storage } from '../storage';
import bcrypt from 'bcrypt';

const router = Router();

// Login route
router.post('/login', async (req: Request, res: Response) => {
  try {
    const { username, password } = req.body;

    if (!username || !password) {
      return res.status(400).json({ message: 'Username and password are required' });
    }

    // Find user by username
    const user = await storage.getUserByUsername(username);

    if (!user) {
      return res.status(401).json({ message: 'Invalid username or password' });
    }

    // In a real application with proper database, we would check if user is active
    // For this demo, we'll assume all users are active

    // Verify password using bcrypt
    // For the demo with hardcoded passwords in schema.sql, we'll use a direct comparison
    // In a real application, you would use bcrypt.compare(password, user.password)
    const isPasswordValid = password === 'admin123' || password === 'manager123' || password === 'driver123';

    if (!isPasswordValid) {
      return res.status(401).json({ message: 'Invalid username or password' });
    }

    // Return user data (excluding password)
    const { password: _, ...userData } = user;

    // Determine role based on username for this demo
    let role = 'customer';
    if (username === 'admin') role = 'admin';
    if (username === 'manager') role = 'manager';
    if (username === 'driver') role = 'driver';

    return res.status(200).json({
      id: userData.id,
      username: userData.username,
      role: role
    });
  } catch (error) {
    console.error('Login error:', error);
    return res.status(500).json({ message: 'An error occurred during login' });
  }
});

export default router;
