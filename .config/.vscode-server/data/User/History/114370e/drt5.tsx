import { useState } from 'react';
import { useCart } from '@/context/CartContext';
import { useRestaurantStatus } from '@/context/RestaurantStatusContext';
import { formatCurrency } from '@/lib/utils';
import { Dish } from '@shared/schema';
import Button from './Button';

interface DishCardProps {
  dish: Dish;
}

const DishCard = ({ dish }: DishCardProps) => {
  const { addToCart } = useCart();
  const [isAdding, setIsAdding] = useState(false);

  const handleAddToCart = () => {
    setIsAdding(true);

    // Simulate a small delay for better UX
    setTimeout(() => {
      addToCart(dish);
      setIsAdding(false);
    }, 300);
  };

  return (
    <div className="dish-card bg-gray-900 rounded-lg overflow-hidden shadow-lg border border-gray-800 hover:border-secondary transition duration-300 transform hover:-translate-y-1">
      <div className="relative h-56 overflow-hidden">
        <img
          src={dish.imageUrl}
          alt={dish.name}
          className="w-full h-full object-cover"
        />
      </div>

      <div className="p-6">
        <div className="flex justify-between items-start mb-3">
          <h3 className="font-playfair text-xl font-bold text-white">{dish.name}</h3>
          <span className="font-poppins text-accent font-medium">{formatCurrency(dish.price)}</span>
        </div>

        <p className="font-poppins text-gray-400 text-sm mb-4 line-clamp-3">{dish.description}</p>

        <div className="flex justify-between items-center">
          <div className="flex items-center">
            <span className="text-yellow-400 mr-1"><i className="fas fa-star"></i></span>
            <span className="font-poppins text-sm text-gray-300">
              {dish.rating} ({dish.reviews})
            </span>
          </div>

          <Button
            variant="outline-secondary"
            size="sm"
            onClick={handleAddToCart}
            isLoading={isAdding}
          >
            Add to Cart
          </Button>
        </div>
      </div>
    </div>
  );
};

export default DishCard;
