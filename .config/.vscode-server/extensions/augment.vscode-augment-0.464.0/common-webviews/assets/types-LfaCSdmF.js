var a=(n=>(n[n.agentUnspecified=0]="agentUnspecified",n[n.agentPending=5]="agentPending",n[n.agentStarting=1]="agentStarting",n[n.agentRunning=2]="agentRunning",n[n.agentIdle=3]="agentIdle",n[n.agentFailed=4]="agentFailed",n))(a||{}),s=(n=>(n[n.workspaceUnspecified=0]="workspaceUnspecified",n[n.workspaceRunning=1]="workspaceRunning",n[n.workspacePausing=2]="workspacePausing",n[n.workspacePaused=3]="workspacePaused",n[n.workspaceResuming=4]="workspaceResuming",n))(s||{}),i=(n=>(n[n.AGENT_HISTORY_UPDATE_TYPE_UNSPECIFIED=0]="AGENT_HISTORY_UPDATE_TYPE_UNSPECIFIED",n[n.AGENT_HISTORY_EXCHANGE=1]="AGENT_HISTORY_EXCHANGE",n[n.AGENT_HISTORY_EXCHANGE_UPDATE=2]="AGENT_HISTORY_EXCHANGE_UPDATE",n[n.AGENT_HISTORY_AGENT_STATUS=3]="AGENT_HISTORY_AGENT_STATUS",n))(i||{}),d=(n=>(n[n.added=0]="added",n[n.deleted=1]="deleted",n[n.modified=2]="modified",n[n.renamed=3]="renamed",n))(d||{}),E=(n=>(n[n.unknown=0]="unknown",n[n.running=1]="running",n[n.success=2]="success",n[n.failure=3]="failure",n[n.skipped=4]="skipped",n))(E||{});export{i as A,d as F,a as R,s as a,E as b};
