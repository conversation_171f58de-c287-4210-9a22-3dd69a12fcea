{"name": "coderabbit-vscode", "version": "0.7.8", "description": "Free AI code reviews that run directly in VS Code. Review each commit immediately without waiting for PR to be raised. Catch more bugs and ship code faster.", "keywords": ["ai assistant", "bug detection", "code quality", "code review", "productivity", "pull request", "static analysis"], "homepage": "https://coderabbit.ai", "license": "See LICENSE", "main": "./dist/extension.js", "scripts": {"build": "npm-run-all build:extension build:webview", "build:extension": "npm-run-all compile:extension lint", "build:webview": "cd webview && pnpm run build", "check-types": "tsc --noEmit", "clean": "rm -rf .turbo dist docs node_modules tsconfig.tsbuildinfo", "compile": "npm-run-all check-types compile:extension compile:webview", "compile-tests": "tsc -p . --outDir out", "compile:extension": "pnpm check-types && node esbuild.js", "compile:webview": "cd webview && pnpm run build", "format": "prettier --write .", "lint": "eslint src && prettier --check .", "lint:fix": "eslint --fix --quiet src; prettier --list-different --write .", "package": "vsce package --no-dependencies --allow-missing-repository", "pretest": "npm-run-all compile-tests compile lint", "vscode:prepublish": "pnpm run build", "vscode:uninstall": "node ./dist/lifecycle.js", "watch": "npm-run-all -p watch:*", "watch-tests": "tsc -p . -w --outDir out", "watch:esbuild": "node esbuild.js --watch", "watch:tsc": "tsc --noEmit --watch --project tsconfig.json", "watch:webview": "cd webview && pnpm run watch:build"}, "dependencies": {"@coderabbitai/coderabbit-handler": "workspace:*", "@coderabbitai/common": "workspace:*", "@coderabbitai/mono-config": "workspace:*", "@coderabbitai/pr-reviewer-saas": "workspace:*", "@coderabbitai/telemetry": "workspace:*", "@trpc/client": "^10.45.2", "execa": "^9.5.1", "minimatch": "^9.0.4", "p-limit": "^4.0.0", "semver": "^7.7.2", "string-similarity": "^4.0.4", "ws": "^8.18.1", "zod": "^3.23.8"}, "devDependencies": {"@types/mocha": "^10.0.10", "@types/node": "^22.13.10", "@types/p-limit": "^2.2.0", "@types/semver": "^7.7.0", "@types/string-similarity": "^4.0.2", "@types/vscode": "^1.93.1", "@types/ws": "^8.18.0", "@typescript-eslint/eslint-plugin": "^8.22.0", "@typescript-eslint/parser": "^8.22.0", "@vscode/test-cli": "^0.0.10", "@vscode/test-electron": "^2.4.1", "@vscode/vsce": "^3.3.2", "chargebee-typescript": "^2.33.0", "esbuild": "^0.21.2", "eslint": "^9.27.0", "npm-run-all": "^4.1.5", "prettier": "^3.5.3", "typescript": "5.5.4", "typescript-eslint": "^8.32.1"}, "bundleDependencies": ["@coderabbitai/common", "@coderabbitai/mono-config", "@coderabbitai/coderabbit-handler", "@coderabbitai/pr-reviewer-saas"], "engines": {"vscode": "^1.93.1"}, "activationEvents": ["onStartupFinished"], "categories": ["AI", "Programming Languages", "Machine Learning", "Other"], "contributes": {"configuration": {"title": "CodeRabbit", "properties": {"coderabbit.reviewTimeout": {"type": "number", "default": 20, "minimum": 0, "description": "Timeout for code reviews in minutes. Set to 0 for no timeout."}, "coderabbit.autoReviewMode": {"type": "string", "enum": ["disabled", "prompt", "auto"], "enumDescriptions": ["Don't prompt for review after commits", "Prompt to start a review after commits (default)", "Automatically start review after commits with 10 second cancel window"], "default": "prompt", "description": "How to handle review after commits"}, "coderabbit.agentType": {"type": "string", "enum": ["Native", "<PERSON>", "Codex CLI", "Clipboard"], "enumDescriptions": ["Use built-in AI agent (default)", "Use Claude Code in terminal", "Use Codex CLI in terminal", "Copy instructions to clipboard"], "default": "Native", "description": "The AI agent to use for 'Fix with AI' functionality."}}}, "viewsContainers": {"activitybar": [{"id": "coderabbit-vscode-sidebar-view", "title": "CodeRabbit", "icon": "resources/coderabbit.svg"}]}, "views": {"coderabbit-vscode-sidebar-view": [{"type": "webview", "id": "coderabbit-vscode-sidebar", "name": "CodeRabbit", "icon": "resources/coderabbit.svg"}]}, "commands": [{"command": "coderabbit-vscode.initiateReview", "title": "Start Review", "category": "CodeRabbit"}, {"command": "coderabbit-vscode.logout", "title": "Logout", "category": "CodeRabbit", "icon": "$(sign-out)"}, {"command": "coderabbit-vscode.applyDiffChanges", "title": "Apply suggested change", "category": "CodeRabbit", "icon": "$(check)"}, {"command": "coderabbit-vscode.changeOrganization", "title": "Change Organization", "category": "CodeRabbit"}, {"command": "coderabbit-vscode.applyChangesInComment", "title": "Apply all", "category": "CodeRabbit", "icon": "$(check-all)"}, {"command": "coderabbit-vscode.handoffToAgent", "title": "Fix with AI", "category": "CodeRabbit", "icon": "$(sparkle)"}, {"command": "coderabbit-vscode.expandAllComments", "title": "Expand all", "category": "CodeRabbit"}, {"command": "coderabbit-vscode.collapseAllComments", "title": "Collapse all", "category": "CodeRabbit", "icon": "$(collapse-all)"}, {"command": "coderabbit-vscode.resolveComment", "title": "Ignore", "category": "CodeRabbit", "icon": "$(circle-slash)"}], "menus": {"view/title": [{"command": "coderabbit-vscode.logout", "when": "view == coderabbit-vscode-sidebar && coderabbit-vscode.isLoggedIn", "group": "navigation"}], "commandPalette": [{"command": "coderabbit-vscode.applyDiffChanges", "when": "false"}, {"command": "coderabbit-vscode.applyChangesInComment", "when": "false"}, {"command": "coderabbit-vscode.resolveComment", "when": "false"}, {"command": "coderabbit-vscode.handoffToAgent", "when": "false"}, {"command": "coderabbit-vscode.logout", "when": "coderabbit-vscode.isLoggedIn"}, {"command": "coderabbit-vscode.initiateReview", "when": "coderabbit-vscode.isLoggedIn"}, {"command": "coderabbit-vscode.changeOrganization", "when": "coderabbit-vscode.isLoggedIn"}, {"command": "coderabbit-vscode.expandAllComments", "when": "coderabbit-vscode.isLoggedIn"}, {"command": "coderabbit-vscode.collapseAllComments", "when": "coderabbit-vscode.isLoggedIn"}], "editor/context": [{"submenu": "coderabbit.contextMenu", "group": "navigation"}], "coderabbit.contextMenu": [{"command": "coderabbit-vscode.initiateReview", "group": "navigation@1"}], "comments/commentThread/title": [{"command": "coderabbit-vscode.applyChangesInComment", "group": "inline@0", "when": "commentController =~ /^coderabbit/ && commentThread =~ /multipleDiff/"}, {"command": "coderabbit-vscode.applyDiffChanges", "group": "inline@1", "when": "commentController =~ /^coderabbit/ && commentThread =~ /singleDiff/"}, {"command": "coderabbit-vscode.resolveComment", "group": "inline@2", "when": "commentController =~ /^coderabbit/"}, {"command": "coderabbit-vscode.handoffToAgent", "group": "inline@3", "when": "commentController =~ /^coderabbit/ && commentThread =~ /hasCodegenInstructions/"}, {"command": "coderabbit-vscode.collapseAllComments", "group": "inline@4", "when": "commentController =~ /^coderabbit/"}]}, "submenus": [{"id": "coderabbit.contextMenu", "label": "CodeRabbit"}]}, "displayName": "CodeRabbit", "extensionDependencies": ["vscode.git"], "icon": "resources/coderabbit.png", "publisher": "coderabbit", "__metadata": {"installedTimestamp": 1748050885843, "targetPlatform": "undefined", "size": 2049922}}