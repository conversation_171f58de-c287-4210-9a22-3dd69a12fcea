var wy=Object.defineProperty;var _y=(e,t,n)=>t in e?wy(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n;var hd=(e,t,n)=>_y(e,typeof t!="symbol"?t+"":t,n);function Cy(e,t){for(var n=0;n<t.length;n++){const r=t[n];if(typeof r!="string"&&!Array.isArray(r)){for(const o in r)if(o!=="default"&&!(o in e)){const s=Object.getOwnPropertyDescriptor(r,o);s&&Object.defineProperty(e,o,s.get?s:{enumerable:!0,get:()=>r[o]})}}}return Object.freeze(Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}))}(function(){const t=document.createElement("link").relList;if(t&&t.supports&&t.supports("modulepreload"))return;for(const o of document.querySelectorAll('link[rel="modulepreload"]'))r(o);new MutationObserver(o=>{for(const s of o)if(s.type==="childList")for(const i of s.addedNodes)i.tagName==="LINK"&&i.rel==="modulepreload"&&r(i)}).observe(document,{childList:!0,subtree:!0});function n(o){const s={};return o.integrity&&(s.integrity=o.integrity),o.referrerPolicy&&(s.referrerPolicy=o.referrerPolicy),o.crossOrigin==="use-credentials"?s.credentials="include":o.crossOrigin==="anonymous"?s.credentials="omit":s.credentials="same-origin",s}function r(o){if(o.ep)return;o.ep=!0;const s=n(o);fetch(o.href,s)}})();var ua=typeof globalThis<"u"?globalThis:typeof window<"u"?window:typeof global<"u"?global:typeof self<"u"?self:{};function mr(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}var sp={exports:{}},ca={},ip={exports:{}},te={};/**
 * @license React
 * react.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var bs=Symbol.for("react.element"),ky=Symbol.for("react.portal"),Sy=Symbol.for("react.fragment"),Ey=Symbol.for("react.strict_mode"),by=Symbol.for("react.profiler"),Ny=Symbol.for("react.provider"),Ty=Symbol.for("react.context"),Ry=Symbol.for("react.forward_ref"),jy=Symbol.for("react.suspense"),My=Symbol.for("react.memo"),Py=Symbol.for("react.lazy"),md=Symbol.iterator;function Ay(e){return e===null||typeof e!="object"?null:(e=md&&e[md]||e["@@iterator"],typeof e=="function"?e:null)}var ap={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},lp=Object.assign,up={};function co(e,t,n){this.props=e,this.context=t,this.refs=up,this.updater=n||ap}co.prototype.isReactComponent={};co.prototype.setState=function(e,t){if(typeof e!="object"&&typeof e!="function"&&e!=null)throw Error("setState(...): takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,e,t,"setState")};co.prototype.forceUpdate=function(e){this.updater.enqueueForceUpdate(this,e,"forceUpdate")};function cp(){}cp.prototype=co.prototype;function zu(e,t,n){this.props=e,this.context=t,this.refs=up,this.updater=n||ap}var Fu=zu.prototype=new cp;Fu.constructor=zu;lp(Fu,co.prototype);Fu.isPureReactComponent=!0;var vd=Array.isArray,dp=Object.prototype.hasOwnProperty,Bu={current:null},fp={key:!0,ref:!0,__self:!0,__source:!0};function pp(e,t,n){var r,o={},s=null,i=null;if(t!=null)for(r in t.ref!==void 0&&(i=t.ref),t.key!==void 0&&(s=""+t.key),t)dp.call(t,r)&&!fp.hasOwnProperty(r)&&(o[r]=t[r]);var a=arguments.length-2;if(a===1)o.children=n;else if(1<a){for(var l=Array(a),u=0;u<a;u++)l[u]=arguments[u+2];o.children=l}if(e&&e.defaultProps)for(r in a=e.defaultProps,a)o[r]===void 0&&(o[r]=a[r]);return{$$typeof:bs,type:e,key:s,ref:i,props:o,_owner:Bu.current}}function Oy(e,t){return{$$typeof:bs,type:e.type,key:t,ref:e.ref,props:e.props,_owner:e._owner}}function Vu(e){return typeof e=="object"&&e!==null&&e.$$typeof===bs}function Iy(e){var t={"=":"=0",":":"=2"};return"$"+e.replace(/[=:]/g,function(n){return t[n]})}var gd=/\/+/g;function Ha(e,t){return typeof e=="object"&&e!==null&&e.key!=null?Iy(""+e.key):t.toString(36)}function li(e,t,n,r,o){var s=typeof e;(s==="undefined"||s==="boolean")&&(e=null);var i=!1;if(e===null)i=!0;else switch(s){case"string":case"number":i=!0;break;case"object":switch(e.$$typeof){case bs:case ky:i=!0}}if(i)return i=e,o=o(i),e=r===""?"."+Ha(i,0):r,vd(o)?(n="",e!=null&&(n=e.replace(gd,"$&/")+"/"),li(o,t,n,"",function(u){return u})):o!=null&&(Vu(o)&&(o=Oy(o,n+(!o.key||i&&i.key===o.key?"":(""+o.key).replace(gd,"$&/")+"/")+e)),t.push(o)),1;if(i=0,r=r===""?".":r+":",vd(e))for(var a=0;a<e.length;a++){s=e[a];var l=r+Ha(s,a);i+=li(s,t,n,l,o)}else if(l=Ay(e),typeof l=="function")for(e=l.call(e),a=0;!(s=e.next()).done;)s=s.value,l=r+Ha(s,a++),i+=li(s,t,n,l,o);else if(s==="object")throw t=String(e),Error("Objects are not valid as a React child (found: "+(t==="[object Object]"?"object with keys {"+Object.keys(e).join(", ")+"}":t)+"). If you meant to render a collection of children, use an array instead.");return i}function Ls(e,t,n){if(e==null)return e;var r=[],o=0;return li(e,r,"","",function(s){return t.call(n,s,o++)}),r}function Dy(e){if(e._status===-1){var t=e._result;t=t(),t.then(function(n){(e._status===0||e._status===-1)&&(e._status=1,e._result=n)},function(n){(e._status===0||e._status===-1)&&(e._status=2,e._result=n)}),e._status===-1&&(e._status=0,e._result=t)}if(e._status===1)return e._result.default;throw e._result}var We={current:null},ui={transition:null},Ly={ReactCurrentDispatcher:We,ReactCurrentBatchConfig:ui,ReactCurrentOwner:Bu};function hp(){throw Error("act(...) is not supported in production builds of React.")}te.Children={map:Ls,forEach:function(e,t,n){Ls(e,function(){t.apply(this,arguments)},n)},count:function(e){var t=0;return Ls(e,function(){t++}),t},toArray:function(e){return Ls(e,function(t){return t})||[]},only:function(e){if(!Vu(e))throw Error("React.Children.only expected to receive a single React element child.");return e}};te.Component=co;te.Fragment=Sy;te.Profiler=by;te.PureComponent=zu;te.StrictMode=Ey;te.Suspense=jy;te.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=Ly;te.act=hp;te.cloneElement=function(e,t,n){if(e==null)throw Error("React.cloneElement(...): The argument must be a React element, but you passed "+e+".");var r=lp({},e.props),o=e.key,s=e.ref,i=e._owner;if(t!=null){if(t.ref!==void 0&&(s=t.ref,i=Bu.current),t.key!==void 0&&(o=""+t.key),e.type&&e.type.defaultProps)var a=e.type.defaultProps;for(l in t)dp.call(t,l)&&!fp.hasOwnProperty(l)&&(r[l]=t[l]===void 0&&a!==void 0?a[l]:t[l])}var l=arguments.length-2;if(l===1)r.children=n;else if(1<l){a=Array(l);for(var u=0;u<l;u++)a[u]=arguments[u+2];r.children=a}return{$$typeof:bs,type:e.type,key:o,ref:s,props:r,_owner:i}};te.createContext=function(e){return e={$$typeof:Ty,_currentValue:e,_currentValue2:e,_threadCount:0,Provider:null,Consumer:null,_defaultValue:null,_globalName:null},e.Provider={$$typeof:Ny,_context:e},e.Consumer=e};te.createElement=pp;te.createFactory=function(e){var t=pp.bind(null,e);return t.type=e,t};te.createRef=function(){return{current:null}};te.forwardRef=function(e){return{$$typeof:Ry,render:e}};te.isValidElement=Vu;te.lazy=function(e){return{$$typeof:Py,_payload:{_status:-1,_result:e},_init:Dy}};te.memo=function(e,t){return{$$typeof:My,type:e,compare:t===void 0?null:t}};te.startTransition=function(e){var t=ui.transition;ui.transition={};try{e()}finally{ui.transition=t}};te.unstable_act=hp;te.useCallback=function(e,t){return We.current.useCallback(e,t)};te.useContext=function(e){return We.current.useContext(e)};te.useDebugValue=function(){};te.useDeferredValue=function(e){return We.current.useDeferredValue(e)};te.useEffect=function(e,t){return We.current.useEffect(e,t)};te.useId=function(){return We.current.useId()};te.useImperativeHandle=function(e,t,n){return We.current.useImperativeHandle(e,t,n)};te.useInsertionEffect=function(e,t){return We.current.useInsertionEffect(e,t)};te.useLayoutEffect=function(e,t){return We.current.useLayoutEffect(e,t)};te.useMemo=function(e,t){return We.current.useMemo(e,t)};te.useReducer=function(e,t,n){return We.current.useReducer(e,t,n)};te.useRef=function(e){return We.current.useRef(e)};te.useState=function(e){return We.current.useState(e)};te.useSyncExternalStore=function(e,t,n){return We.current.useSyncExternalStore(e,t,n)};te.useTransition=function(){return We.current.useTransition()};te.version="18.3.1";ip.exports=te;var v=ip.exports;const Ce=mr(v),$y=Cy({__proto__:null,default:Ce},[v]);/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var zy=v,Fy=Symbol.for("react.element"),By=Symbol.for("react.fragment"),Vy=Object.prototype.hasOwnProperty,Uy=zy.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,Wy={key:!0,ref:!0,__self:!0,__source:!0};function mp(e,t,n){var r,o={},s=null,i=null;n!==void 0&&(s=""+n),t.key!==void 0&&(s=""+t.key),t.ref!==void 0&&(i=t.ref);for(r in t)Vy.call(t,r)&&!Wy.hasOwnProperty(r)&&(o[r]=t[r]);if(e&&e.defaultProps)for(r in t=e.defaultProps,t)o[r]===void 0&&(o[r]=t[r]);return{$$typeof:Fy,type:e,key:s,ref:i,props:o,_owner:Uy.current}}ca.Fragment=By;ca.jsx=mp;ca.jsxs=mp;sp.exports=ca;var c=sp.exports,jl={},vp={exports:{}},it={},gp={exports:{}},yp={};/**
 * @license React
 * scheduler.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */(function(e){function t(E,T){var P=E.length;E.push(T);e:for(;0<P;){var B=P-1>>>1,Z=E[B];if(0<o(Z,T))E[B]=T,E[P]=Z,P=B;else break e}}function n(E){return E.length===0?null:E[0]}function r(E){if(E.length===0)return null;var T=E[0],P=E.pop();if(P!==T){E[0]=P;e:for(var B=0,Z=E.length,se=Z>>>1;B<se;){var oe=2*(B+1)-1,_e=E[oe],ge=oe+1,ye=E[ge];if(0>o(_e,P))ge<Z&&0>o(ye,_e)?(E[B]=ye,E[ge]=P,B=ge):(E[B]=_e,E[oe]=P,B=oe);else if(ge<Z&&0>o(ye,P))E[B]=ye,E[ge]=P,B=ge;else break e}}return T}function o(E,T){var P=E.sortIndex-T.sortIndex;return P!==0?P:E.id-T.id}if(typeof performance=="object"&&typeof performance.now=="function"){var s=performance;e.unstable_now=function(){return s.now()}}else{var i=Date,a=i.now();e.unstable_now=function(){return i.now()-a}}var l=[],u=[],d=1,f=null,p=3,y=!1,w=!1,g=!1,k=typeof setTimeout=="function"?setTimeout:null,m=typeof clearTimeout=="function"?clearTimeout:null,h=typeof setImmediate<"u"?setImmediate:null;typeof navigator<"u"&&navigator.scheduling!==void 0&&navigator.scheduling.isInputPending!==void 0&&navigator.scheduling.isInputPending.bind(navigator.scheduling);function x(E){for(var T=n(u);T!==null;){if(T.callback===null)r(u);else if(T.startTime<=E)r(u),T.sortIndex=T.expirationTime,t(l,T);else break;T=n(u)}}function _(E){if(g=!1,x(E),!w)if(n(l)!==null)w=!0,j(b);else{var T=n(u);T!==null&&z(_,T.startTime-E)}}function b(E,T){w=!1,g&&(g=!1,m(C),C=-1),y=!0;var P=p;try{for(x(T),f=n(l);f!==null&&(!(f.expirationTime>T)||E&&!$());){var B=f.callback;if(typeof B=="function"){f.callback=null,p=f.priorityLevel;var Z=B(f.expirationTime<=T);T=e.unstable_now(),typeof Z=="function"?f.callback=Z:f===n(l)&&r(l),x(T)}else r(l);f=n(l)}if(f!==null)var se=!0;else{var oe=n(u);oe!==null&&z(_,oe.startTime-T),se=!1}return se}finally{f=null,p=P,y=!1}}var N=!1,S=null,C=-1,L=5,M=-1;function $(){return!(e.unstable_now()-M<L)}function V(){if(S!==null){var E=e.unstable_now();M=E;var T=!0;try{T=S(!0,E)}finally{T?G():(N=!1,S=null)}}else N=!1}var G;if(typeof h=="function")G=function(){h(V)};else if(typeof MessageChannel<"u"){var I=new MessageChannel,D=I.port2;I.port1.onmessage=V,G=function(){D.postMessage(null)}}else G=function(){k(V,0)};function j(E){S=E,N||(N=!0,G())}function z(E,T){C=k(function(){E(e.unstable_now())},T)}e.unstable_IdlePriority=5,e.unstable_ImmediatePriority=1,e.unstable_LowPriority=4,e.unstable_NormalPriority=3,e.unstable_Profiling=null,e.unstable_UserBlockingPriority=2,e.unstable_cancelCallback=function(E){E.callback=null},e.unstable_continueExecution=function(){w||y||(w=!0,j(b))},e.unstable_forceFrameRate=function(E){0>E||125<E?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):L=0<E?Math.floor(1e3/E):5},e.unstable_getCurrentPriorityLevel=function(){return p},e.unstable_getFirstCallbackNode=function(){return n(l)},e.unstable_next=function(E){switch(p){case 1:case 2:case 3:var T=3;break;default:T=p}var P=p;p=T;try{return E()}finally{p=P}},e.unstable_pauseExecution=function(){},e.unstable_requestPaint=function(){},e.unstable_runWithPriority=function(E,T){switch(E){case 1:case 2:case 3:case 4:case 5:break;default:E=3}var P=p;p=E;try{return T()}finally{p=P}},e.unstable_scheduleCallback=function(E,T,P){var B=e.unstable_now();switch(typeof P=="object"&&P!==null?(P=P.delay,P=typeof P=="number"&&0<P?B+P:B):P=B,E){case 1:var Z=-1;break;case 2:Z=250;break;case 5:Z=**********;break;case 4:Z=1e4;break;default:Z=5e3}return Z=P+Z,E={id:d++,callback:T,priorityLevel:E,startTime:P,expirationTime:Z,sortIndex:-1},P>B?(E.sortIndex=P,t(u,E),n(l)===null&&E===n(u)&&(g?(m(C),C=-1):g=!0,z(_,P-B))):(E.sortIndex=Z,t(l,E),w||y||(w=!0,j(b))),E},e.unstable_shouldYield=$,e.unstable_wrapCallback=function(E){var T=p;return function(){var P=p;p=T;try{return E.apply(this,arguments)}finally{p=P}}}})(yp);gp.exports=yp;var Hy=gp.exports;/**
 * @license React
 * react-dom.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Zy=v,st=Hy;function O(e){for(var t="https://reactjs.org/docs/error-decoder.html?invariant="+e,n=1;n<arguments.length;n++)t+="&args[]="+encodeURIComponent(arguments[n]);return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}var xp=new Set,Zo={};function vr(e,t){Qr(e,t),Qr(e+"Capture",t)}function Qr(e,t){for(Zo[e]=t,e=0;e<t.length;e++)xp.add(t[e])}var on=!(typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"),Ml=Object.prototype.hasOwnProperty,Gy=/^[:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD][:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD\-.0-9\u00B7\u0300-\u036F\u203F-\u2040]*$/,yd={},xd={};function Yy(e){return Ml.call(xd,e)?!0:Ml.call(yd,e)?!1:Gy.test(e)?xd[e]=!0:(yd[e]=!0,!1)}function Ky(e,t,n,r){if(n!==null&&n.type===0)return!1;switch(typeof t){case"function":case"symbol":return!0;case"boolean":return r?!1:n!==null?!n.acceptsBooleans:(e=e.toLowerCase().slice(0,5),e!=="data-"&&e!=="aria-");default:return!1}}function Qy(e,t,n,r){if(t===null||typeof t>"u"||Ky(e,t,n,r))return!0;if(r)return!1;if(n!==null)switch(n.type){case 3:return!t;case 4:return t===!1;case 5:return isNaN(t);case 6:return isNaN(t)||1>t}return!1}function He(e,t,n,r,o,s,i){this.acceptsBooleans=t===2||t===3||t===4,this.attributeName=r,this.attributeNamespace=o,this.mustUseProperty=n,this.propertyName=e,this.type=t,this.sanitizeURL=s,this.removeEmptyString=i}var Ie={};"children dangerouslySetInnerHTML defaultValue defaultChecked innerHTML suppressContentEditableWarning suppressHydrationWarning style".split(" ").forEach(function(e){Ie[e]=new He(e,0,!1,e,null,!1,!1)});[["acceptCharset","accept-charset"],["className","class"],["htmlFor","for"],["httpEquiv","http-equiv"]].forEach(function(e){var t=e[0];Ie[t]=new He(t,1,!1,e[1],null,!1,!1)});["contentEditable","draggable","spellCheck","value"].forEach(function(e){Ie[e]=new He(e,2,!1,e.toLowerCase(),null,!1,!1)});["autoReverse","externalResourcesRequired","focusable","preserveAlpha"].forEach(function(e){Ie[e]=new He(e,2,!1,e,null,!1,!1)});"allowFullScreen async autoFocus autoPlay controls default defer disabled disablePictureInPicture disableRemotePlayback formNoValidate hidden loop noModule noValidate open playsInline readOnly required reversed scoped seamless itemScope".split(" ").forEach(function(e){Ie[e]=new He(e,3,!1,e.toLowerCase(),null,!1,!1)});["checked","multiple","muted","selected"].forEach(function(e){Ie[e]=new He(e,3,!0,e,null,!1,!1)});["capture","download"].forEach(function(e){Ie[e]=new He(e,4,!1,e,null,!1,!1)});["cols","rows","size","span"].forEach(function(e){Ie[e]=new He(e,6,!1,e,null,!1,!1)});["rowSpan","start"].forEach(function(e){Ie[e]=new He(e,5,!1,e.toLowerCase(),null,!1,!1)});var Uu=/[\-:]([a-z])/g;function Wu(e){return e[1].toUpperCase()}"accent-height alignment-baseline arabic-form baseline-shift cap-height clip-path clip-rule color-interpolation color-interpolation-filters color-profile color-rendering dominant-baseline enable-background fill-opacity fill-rule flood-color flood-opacity font-family font-size font-size-adjust font-stretch font-style font-variant font-weight glyph-name glyph-orientation-horizontal glyph-orientation-vertical horiz-adv-x horiz-origin-x image-rendering letter-spacing lighting-color marker-end marker-mid marker-start overline-position overline-thickness paint-order panose-1 pointer-events rendering-intent shape-rendering stop-color stop-opacity strikethrough-position strikethrough-thickness stroke-dasharray stroke-dashoffset stroke-linecap stroke-linejoin stroke-miterlimit stroke-opacity stroke-width text-anchor text-decoration text-rendering underline-position underline-thickness unicode-bidi unicode-range units-per-em v-alphabetic v-hanging v-ideographic v-mathematical vector-effect vert-adv-y vert-origin-x vert-origin-y word-spacing writing-mode xmlns:xlink x-height".split(" ").forEach(function(e){var t=e.replace(Uu,Wu);Ie[t]=new He(t,1,!1,e,null,!1,!1)});"xlink:actuate xlink:arcrole xlink:role xlink:show xlink:title xlink:type".split(" ").forEach(function(e){var t=e.replace(Uu,Wu);Ie[t]=new He(t,1,!1,e,"http://www.w3.org/1999/xlink",!1,!1)});["xml:base","xml:lang","xml:space"].forEach(function(e){var t=e.replace(Uu,Wu);Ie[t]=new He(t,1,!1,e,"http://www.w3.org/XML/1998/namespace",!1,!1)});["tabIndex","crossOrigin"].forEach(function(e){Ie[e]=new He(e,1,!1,e.toLowerCase(),null,!1,!1)});Ie.xlinkHref=new He("xlinkHref",1,!1,"xlink:href","http://www.w3.org/1999/xlink",!0,!1);["src","href","action","formAction"].forEach(function(e){Ie[e]=new He(e,1,!1,e.toLowerCase(),null,!0,!0)});function Hu(e,t,n,r){var o=Ie.hasOwnProperty(t)?Ie[t]:null;(o!==null?o.type!==0:r||!(2<t.length)||t[0]!=="o"&&t[0]!=="O"||t[1]!=="n"&&t[1]!=="N")&&(Qy(t,n,o,r)&&(n=null),r||o===null?Yy(t)&&(n===null?e.removeAttribute(t):e.setAttribute(t,""+n)):o.mustUseProperty?e[o.propertyName]=n===null?o.type===3?!1:"":n:(t=o.attributeName,r=o.attributeNamespace,n===null?e.removeAttribute(t):(o=o.type,n=o===3||o===4&&n===!0?"":""+n,r?e.setAttributeNS(r,t,n):e.setAttribute(t,n))))}var fn=Zy.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED,$s=Symbol.for("react.element"),Er=Symbol.for("react.portal"),br=Symbol.for("react.fragment"),Zu=Symbol.for("react.strict_mode"),Pl=Symbol.for("react.profiler"),wp=Symbol.for("react.provider"),_p=Symbol.for("react.context"),Gu=Symbol.for("react.forward_ref"),Al=Symbol.for("react.suspense"),Ol=Symbol.for("react.suspense_list"),Yu=Symbol.for("react.memo"),_n=Symbol.for("react.lazy"),Cp=Symbol.for("react.offscreen"),wd=Symbol.iterator;function yo(e){return e===null||typeof e!="object"?null:(e=wd&&e[wd]||e["@@iterator"],typeof e=="function"?e:null)}var ve=Object.assign,Za;function To(e){if(Za===void 0)try{throw Error()}catch(n){var t=n.stack.trim().match(/\n( *(at )?)/);Za=t&&t[1]||""}return`
`+Za+e}var Ga=!1;function Ya(e,t){if(!e||Ga)return"";Ga=!0;var n=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{if(t)if(t=function(){throw Error()},Object.defineProperty(t.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(t,[])}catch(u){var r=u}Reflect.construct(e,[],t)}else{try{t.call()}catch(u){r=u}e.call(t.prototype)}else{try{throw Error()}catch(u){r=u}e()}}catch(u){if(u&&r&&typeof u.stack=="string"){for(var o=u.stack.split(`
`),s=r.stack.split(`
`),i=o.length-1,a=s.length-1;1<=i&&0<=a&&o[i]!==s[a];)a--;for(;1<=i&&0<=a;i--,a--)if(o[i]!==s[a]){if(i!==1||a!==1)do if(i--,a--,0>a||o[i]!==s[a]){var l=`
`+o[i].replace(" at new "," at ");return e.displayName&&l.includes("<anonymous>")&&(l=l.replace("<anonymous>",e.displayName)),l}while(1<=i&&0<=a);break}}}finally{Ga=!1,Error.prepareStackTrace=n}return(e=e?e.displayName||e.name:"")?To(e):""}function Xy(e){switch(e.tag){case 5:return To(e.type);case 16:return To("Lazy");case 13:return To("Suspense");case 19:return To("SuspenseList");case 0:case 2:case 15:return e=Ya(e.type,!1),e;case 11:return e=Ya(e.type.render,!1),e;case 1:return e=Ya(e.type,!0),e;default:return""}}function Il(e){if(e==null)return null;if(typeof e=="function")return e.displayName||e.name||null;if(typeof e=="string")return e;switch(e){case br:return"Fragment";case Er:return"Portal";case Pl:return"Profiler";case Zu:return"StrictMode";case Al:return"Suspense";case Ol:return"SuspenseList"}if(typeof e=="object")switch(e.$$typeof){case _p:return(e.displayName||"Context")+".Consumer";case wp:return(e._context.displayName||"Context")+".Provider";case Gu:var t=e.render;return e=e.displayName,e||(e=t.displayName||t.name||"",e=e!==""?"ForwardRef("+e+")":"ForwardRef"),e;case Yu:return t=e.displayName||null,t!==null?t:Il(e.type)||"Memo";case _n:t=e._payload,e=e._init;try{return Il(e(t))}catch{}}return null}function Jy(e){var t=e.type;switch(e.tag){case 24:return"Cache";case 9:return(t.displayName||"Context")+".Consumer";case 10:return(t._context.displayName||"Context")+".Provider";case 18:return"DehydratedFragment";case 11:return e=t.render,e=e.displayName||e.name||"",t.displayName||(e!==""?"ForwardRef("+e+")":"ForwardRef");case 7:return"Fragment";case 5:return t;case 4:return"Portal";case 3:return"Root";case 6:return"Text";case 16:return Il(t);case 8:return t===Zu?"StrictMode":"Mode";case 22:return"Offscreen";case 12:return"Profiler";case 21:return"Scope";case 13:return"Suspense";case 19:return"SuspenseList";case 25:return"TracingMarker";case 1:case 0:case 17:case 2:case 14:case 15:if(typeof t=="function")return t.displayName||t.name||null;if(typeof t=="string")return t}return null}function In(e){switch(typeof e){case"boolean":case"number":case"string":case"undefined":return e;case"object":return e;default:return""}}function kp(e){var t=e.type;return(e=e.nodeName)&&e.toLowerCase()==="input"&&(t==="checkbox"||t==="radio")}function qy(e){var t=kp(e)?"checked":"value",n=Object.getOwnPropertyDescriptor(e.constructor.prototype,t),r=""+e[t];if(!e.hasOwnProperty(t)&&typeof n<"u"&&typeof n.get=="function"&&typeof n.set=="function"){var o=n.get,s=n.set;return Object.defineProperty(e,t,{configurable:!0,get:function(){return o.call(this)},set:function(i){r=""+i,s.call(this,i)}}),Object.defineProperty(e,t,{enumerable:n.enumerable}),{getValue:function(){return r},setValue:function(i){r=""+i},stopTracking:function(){e._valueTracker=null,delete e[t]}}}}function zs(e){e._valueTracker||(e._valueTracker=qy(e))}function Sp(e){if(!e)return!1;var t=e._valueTracker;if(!t)return!0;var n=t.getValue(),r="";return e&&(r=kp(e)?e.checked?"true":"false":e.value),e=r,e!==n?(t.setValue(e),!0):!1}function Ei(e){if(e=e||(typeof document<"u"?document:void 0),typeof e>"u")return null;try{return e.activeElement||e.body}catch{return e.body}}function Dl(e,t){var n=t.checked;return ve({},t,{defaultChecked:void 0,defaultValue:void 0,value:void 0,checked:n??e._wrapperState.initialChecked})}function _d(e,t){var n=t.defaultValue==null?"":t.defaultValue,r=t.checked!=null?t.checked:t.defaultChecked;n=In(t.value!=null?t.value:n),e._wrapperState={initialChecked:r,initialValue:n,controlled:t.type==="checkbox"||t.type==="radio"?t.checked!=null:t.value!=null}}function Ep(e,t){t=t.checked,t!=null&&Hu(e,"checked",t,!1)}function Ll(e,t){Ep(e,t);var n=In(t.value),r=t.type;if(n!=null)r==="number"?(n===0&&e.value===""||e.value!=n)&&(e.value=""+n):e.value!==""+n&&(e.value=""+n);else if(r==="submit"||r==="reset"){e.removeAttribute("value");return}t.hasOwnProperty("value")?$l(e,t.type,n):t.hasOwnProperty("defaultValue")&&$l(e,t.type,In(t.defaultValue)),t.checked==null&&t.defaultChecked!=null&&(e.defaultChecked=!!t.defaultChecked)}function Cd(e,t,n){if(t.hasOwnProperty("value")||t.hasOwnProperty("defaultValue")){var r=t.type;if(!(r!=="submit"&&r!=="reset"||t.value!==void 0&&t.value!==null))return;t=""+e._wrapperState.initialValue,n||t===e.value||(e.value=t),e.defaultValue=t}n=e.name,n!==""&&(e.name=""),e.defaultChecked=!!e._wrapperState.initialChecked,n!==""&&(e.name=n)}function $l(e,t,n){(t!=="number"||Ei(e.ownerDocument)!==e)&&(n==null?e.defaultValue=""+e._wrapperState.initialValue:e.defaultValue!==""+n&&(e.defaultValue=""+n))}var Ro=Array.isArray;function zr(e,t,n,r){if(e=e.options,t){t={};for(var o=0;o<n.length;o++)t["$"+n[o]]=!0;for(n=0;n<e.length;n++)o=t.hasOwnProperty("$"+e[n].value),e[n].selected!==o&&(e[n].selected=o),o&&r&&(e[n].defaultSelected=!0)}else{for(n=""+In(n),t=null,o=0;o<e.length;o++){if(e[o].value===n){e[o].selected=!0,r&&(e[o].defaultSelected=!0);return}t!==null||e[o].disabled||(t=e[o])}t!==null&&(t.selected=!0)}}function zl(e,t){if(t.dangerouslySetInnerHTML!=null)throw Error(O(91));return ve({},t,{value:void 0,defaultValue:void 0,children:""+e._wrapperState.initialValue})}function kd(e,t){var n=t.value;if(n==null){if(n=t.children,t=t.defaultValue,n!=null){if(t!=null)throw Error(O(92));if(Ro(n)){if(1<n.length)throw Error(O(93));n=n[0]}t=n}t==null&&(t=""),n=t}e._wrapperState={initialValue:In(n)}}function bp(e,t){var n=In(t.value),r=In(t.defaultValue);n!=null&&(n=""+n,n!==e.value&&(e.value=n),t.defaultValue==null&&e.defaultValue!==n&&(e.defaultValue=n)),r!=null&&(e.defaultValue=""+r)}function Sd(e){var t=e.textContent;t===e._wrapperState.initialValue&&t!==""&&t!==null&&(e.value=t)}function Np(e){switch(e){case"svg":return"http://www.w3.org/2000/svg";case"math":return"http://www.w3.org/1998/Math/MathML";default:return"http://www.w3.org/1999/xhtml"}}function Fl(e,t){return e==null||e==="http://www.w3.org/1999/xhtml"?Np(t):e==="http://www.w3.org/2000/svg"&&t==="foreignObject"?"http://www.w3.org/1999/xhtml":e}var Fs,Tp=function(e){return typeof MSApp<"u"&&MSApp.execUnsafeLocalFunction?function(t,n,r,o){MSApp.execUnsafeLocalFunction(function(){return e(t,n,r,o)})}:e}(function(e,t){if(e.namespaceURI!=="http://www.w3.org/2000/svg"||"innerHTML"in e)e.innerHTML=t;else{for(Fs=Fs||document.createElement("div"),Fs.innerHTML="<svg>"+t.valueOf().toString()+"</svg>",t=Fs.firstChild;e.firstChild;)e.removeChild(e.firstChild);for(;t.firstChild;)e.appendChild(t.firstChild)}});function Go(e,t){if(t){var n=e.firstChild;if(n&&n===e.lastChild&&n.nodeType===3){n.nodeValue=t;return}}e.textContent=t}var Io={animationIterationCount:!0,aspectRatio:!0,borderImageOutset:!0,borderImageSlice:!0,borderImageWidth:!0,boxFlex:!0,boxFlexGroup:!0,boxOrdinalGroup:!0,columnCount:!0,columns:!0,flex:!0,flexGrow:!0,flexPositive:!0,flexShrink:!0,flexNegative:!0,flexOrder:!0,gridArea:!0,gridRow:!0,gridRowEnd:!0,gridRowSpan:!0,gridRowStart:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnSpan:!0,gridColumnStart:!0,fontWeight:!0,lineClamp:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,tabSize:!0,widows:!0,zIndex:!0,zoom:!0,fillOpacity:!0,floodOpacity:!0,stopOpacity:!0,strokeDasharray:!0,strokeDashoffset:!0,strokeMiterlimit:!0,strokeOpacity:!0,strokeWidth:!0},ex=["Webkit","ms","Moz","O"];Object.keys(Io).forEach(function(e){ex.forEach(function(t){t=t+e.charAt(0).toUpperCase()+e.substring(1),Io[t]=Io[e]})});function Rp(e,t,n){return t==null||typeof t=="boolean"||t===""?"":n||typeof t!="number"||t===0||Io.hasOwnProperty(e)&&Io[e]?(""+t).trim():t+"px"}function jp(e,t){e=e.style;for(var n in t)if(t.hasOwnProperty(n)){var r=n.indexOf("--")===0,o=Rp(n,t[n],r);n==="float"&&(n="cssFloat"),r?e.setProperty(n,o):e[n]=o}}var tx=ve({menuitem:!0},{area:!0,base:!0,br:!0,col:!0,embed:!0,hr:!0,img:!0,input:!0,keygen:!0,link:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0});function Bl(e,t){if(t){if(tx[e]&&(t.children!=null||t.dangerouslySetInnerHTML!=null))throw Error(O(137,e));if(t.dangerouslySetInnerHTML!=null){if(t.children!=null)throw Error(O(60));if(typeof t.dangerouslySetInnerHTML!="object"||!("__html"in t.dangerouslySetInnerHTML))throw Error(O(61))}if(t.style!=null&&typeof t.style!="object")throw Error(O(62))}}function Vl(e,t){if(e.indexOf("-")===-1)return typeof t.is=="string";switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var Ul=null;function Ku(e){return e=e.target||e.srcElement||window,e.correspondingUseElement&&(e=e.correspondingUseElement),e.nodeType===3?e.parentNode:e}var Wl=null,Fr=null,Br=null;function Ed(e){if(e=Rs(e)){if(typeof Wl!="function")throw Error(O(280));var t=e.stateNode;t&&(t=ma(t),Wl(e.stateNode,e.type,t))}}function Mp(e){Fr?Br?Br.push(e):Br=[e]:Fr=e}function Pp(){if(Fr){var e=Fr,t=Br;if(Br=Fr=null,Ed(e),t)for(e=0;e<t.length;e++)Ed(t[e])}}function Ap(e,t){return e(t)}function Op(){}var Ka=!1;function Ip(e,t,n){if(Ka)return e(t,n);Ka=!0;try{return Ap(e,t,n)}finally{Ka=!1,(Fr!==null||Br!==null)&&(Op(),Pp())}}function Yo(e,t){var n=e.stateNode;if(n===null)return null;var r=ma(n);if(r===null)return null;n=r[t];e:switch(t){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(r=!r.disabled)||(e=e.type,r=!(e==="button"||e==="input"||e==="select"||e==="textarea")),e=!r;break e;default:e=!1}if(e)return null;if(n&&typeof n!="function")throw Error(O(231,t,typeof n));return n}var Hl=!1;if(on)try{var xo={};Object.defineProperty(xo,"passive",{get:function(){Hl=!0}}),window.addEventListener("test",xo,xo),window.removeEventListener("test",xo,xo)}catch{Hl=!1}function nx(e,t,n,r,o,s,i,a,l){var u=Array.prototype.slice.call(arguments,3);try{t.apply(n,u)}catch(d){this.onError(d)}}var Do=!1,bi=null,Ni=!1,Zl=null,rx={onError:function(e){Do=!0,bi=e}};function ox(e,t,n,r,o,s,i,a,l){Do=!1,bi=null,nx.apply(rx,arguments)}function sx(e,t,n,r,o,s,i,a,l){if(ox.apply(this,arguments),Do){if(Do){var u=bi;Do=!1,bi=null}else throw Error(O(198));Ni||(Ni=!0,Zl=u)}}function gr(e){var t=e,n=e;if(e.alternate)for(;t.return;)t=t.return;else{e=t;do t=e,t.flags&4098&&(n=t.return),e=t.return;while(e)}return t.tag===3?n:null}function Dp(e){if(e.tag===13){var t=e.memoizedState;if(t===null&&(e=e.alternate,e!==null&&(t=e.memoizedState)),t!==null)return t.dehydrated}return null}function bd(e){if(gr(e)!==e)throw Error(O(188))}function ix(e){var t=e.alternate;if(!t){if(t=gr(e),t===null)throw Error(O(188));return t!==e?null:e}for(var n=e,r=t;;){var o=n.return;if(o===null)break;var s=o.alternate;if(s===null){if(r=o.return,r!==null){n=r;continue}break}if(o.child===s.child){for(s=o.child;s;){if(s===n)return bd(o),e;if(s===r)return bd(o),t;s=s.sibling}throw Error(O(188))}if(n.return!==r.return)n=o,r=s;else{for(var i=!1,a=o.child;a;){if(a===n){i=!0,n=o,r=s;break}if(a===r){i=!0,r=o,n=s;break}a=a.sibling}if(!i){for(a=s.child;a;){if(a===n){i=!0,n=s,r=o;break}if(a===r){i=!0,r=s,n=o;break}a=a.sibling}if(!i)throw Error(O(189))}}if(n.alternate!==r)throw Error(O(190))}if(n.tag!==3)throw Error(O(188));return n.stateNode.current===n?e:t}function Lp(e){return e=ix(e),e!==null?$p(e):null}function $p(e){if(e.tag===5||e.tag===6)return e;for(e=e.child;e!==null;){var t=$p(e);if(t!==null)return t;e=e.sibling}return null}var zp=st.unstable_scheduleCallback,Nd=st.unstable_cancelCallback,ax=st.unstable_shouldYield,lx=st.unstable_requestPaint,ke=st.unstable_now,ux=st.unstable_getCurrentPriorityLevel,Qu=st.unstable_ImmediatePriority,Fp=st.unstable_UserBlockingPriority,Ti=st.unstable_NormalPriority,cx=st.unstable_LowPriority,Bp=st.unstable_IdlePriority,da=null,Dt=null;function dx(e){if(Dt&&typeof Dt.onCommitFiberRoot=="function")try{Dt.onCommitFiberRoot(da,e,void 0,(e.current.flags&128)===128)}catch{}}var kt=Math.clz32?Math.clz32:hx,fx=Math.log,px=Math.LN2;function hx(e){return e>>>=0,e===0?32:31-(fx(e)/px|0)|0}var Bs=64,Vs=4194304;function jo(e){switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return e&4194240;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return e&130023424;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 1073741824;default:return e}}function Ri(e,t){var n=e.pendingLanes;if(n===0)return 0;var r=0,o=e.suspendedLanes,s=e.pingedLanes,i=n&268435455;if(i!==0){var a=i&~o;a!==0?r=jo(a):(s&=i,s!==0&&(r=jo(s)))}else i=n&~o,i!==0?r=jo(i):s!==0&&(r=jo(s));if(r===0)return 0;if(t!==0&&t!==r&&!(t&o)&&(o=r&-r,s=t&-t,o>=s||o===16&&(s&4194240)!==0))return t;if(r&4&&(r|=n&16),t=e.entangledLanes,t!==0)for(e=e.entanglements,t&=r;0<t;)n=31-kt(t),o=1<<n,r|=e[n],t&=~o;return r}function mx(e,t){switch(e){case 1:case 2:case 4:return t+250;case 8:case 16:case 32:case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t+5e3;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return-1;case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return-1}}function vx(e,t){for(var n=e.suspendedLanes,r=e.pingedLanes,o=e.expirationTimes,s=e.pendingLanes;0<s;){var i=31-kt(s),a=1<<i,l=o[i];l===-1?(!(a&n)||a&r)&&(o[i]=mx(a,t)):l<=t&&(e.expiredLanes|=a),s&=~a}}function Gl(e){return e=e.pendingLanes&-1073741825,e!==0?e:e&1073741824?1073741824:0}function Vp(){var e=Bs;return Bs<<=1,!(Bs&4194240)&&(Bs=64),e}function Qa(e){for(var t=[],n=0;31>n;n++)t.push(e);return t}function Ns(e,t,n){e.pendingLanes|=t,t!==536870912&&(e.suspendedLanes=0,e.pingedLanes=0),e=e.eventTimes,t=31-kt(t),e[t]=n}function gx(e,t){var n=e.pendingLanes&~t;e.pendingLanes=t,e.suspendedLanes=0,e.pingedLanes=0,e.expiredLanes&=t,e.mutableReadLanes&=t,e.entangledLanes&=t,t=e.entanglements;var r=e.eventTimes;for(e=e.expirationTimes;0<n;){var o=31-kt(n),s=1<<o;t[o]=0,r[o]=-1,e[o]=-1,n&=~s}}function Xu(e,t){var n=e.entangledLanes|=t;for(e=e.entanglements;n;){var r=31-kt(n),o=1<<r;o&t|e[r]&t&&(e[r]|=t),n&=~o}}var ie=0;function Up(e){return e&=-e,1<e?4<e?e&268435455?16:536870912:4:1}var Wp,Ju,Hp,Zp,Gp,Yl=!1,Us=[],Nn=null,Tn=null,Rn=null,Ko=new Map,Qo=new Map,kn=[],yx="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset submit".split(" ");function Td(e,t){switch(e){case"focusin":case"focusout":Nn=null;break;case"dragenter":case"dragleave":Tn=null;break;case"mouseover":case"mouseout":Rn=null;break;case"pointerover":case"pointerout":Ko.delete(t.pointerId);break;case"gotpointercapture":case"lostpointercapture":Qo.delete(t.pointerId)}}function wo(e,t,n,r,o,s){return e===null||e.nativeEvent!==s?(e={blockedOn:t,domEventName:n,eventSystemFlags:r,nativeEvent:s,targetContainers:[o]},t!==null&&(t=Rs(t),t!==null&&Ju(t)),e):(e.eventSystemFlags|=r,t=e.targetContainers,o!==null&&t.indexOf(o)===-1&&t.push(o),e)}function xx(e,t,n,r,o){switch(t){case"focusin":return Nn=wo(Nn,e,t,n,r,o),!0;case"dragenter":return Tn=wo(Tn,e,t,n,r,o),!0;case"mouseover":return Rn=wo(Rn,e,t,n,r,o),!0;case"pointerover":var s=o.pointerId;return Ko.set(s,wo(Ko.get(s)||null,e,t,n,r,o)),!0;case"gotpointercapture":return s=o.pointerId,Qo.set(s,wo(Qo.get(s)||null,e,t,n,r,o)),!0}return!1}function Yp(e){var t=er(e.target);if(t!==null){var n=gr(t);if(n!==null){if(t=n.tag,t===13){if(t=Dp(n),t!==null){e.blockedOn=t,Gp(e.priority,function(){Hp(n)});return}}else if(t===3&&n.stateNode.current.memoizedState.isDehydrated){e.blockedOn=n.tag===3?n.stateNode.containerInfo:null;return}}}e.blockedOn=null}function ci(e){if(e.blockedOn!==null)return!1;for(var t=e.targetContainers;0<t.length;){var n=Kl(e.domEventName,e.eventSystemFlags,t[0],e.nativeEvent);if(n===null){n=e.nativeEvent;var r=new n.constructor(n.type,n);Ul=r,n.target.dispatchEvent(r),Ul=null}else return t=Rs(n),t!==null&&Ju(t),e.blockedOn=n,!1;t.shift()}return!0}function Rd(e,t,n){ci(e)&&n.delete(t)}function wx(){Yl=!1,Nn!==null&&ci(Nn)&&(Nn=null),Tn!==null&&ci(Tn)&&(Tn=null),Rn!==null&&ci(Rn)&&(Rn=null),Ko.forEach(Rd),Qo.forEach(Rd)}function _o(e,t){e.blockedOn===t&&(e.blockedOn=null,Yl||(Yl=!0,st.unstable_scheduleCallback(st.unstable_NormalPriority,wx)))}function Xo(e){function t(o){return _o(o,e)}if(0<Us.length){_o(Us[0],e);for(var n=1;n<Us.length;n++){var r=Us[n];r.blockedOn===e&&(r.blockedOn=null)}}for(Nn!==null&&_o(Nn,e),Tn!==null&&_o(Tn,e),Rn!==null&&_o(Rn,e),Ko.forEach(t),Qo.forEach(t),n=0;n<kn.length;n++)r=kn[n],r.blockedOn===e&&(r.blockedOn=null);for(;0<kn.length&&(n=kn[0],n.blockedOn===null);)Yp(n),n.blockedOn===null&&kn.shift()}var Vr=fn.ReactCurrentBatchConfig,ji=!0;function _x(e,t,n,r){var o=ie,s=Vr.transition;Vr.transition=null;try{ie=1,qu(e,t,n,r)}finally{ie=o,Vr.transition=s}}function Cx(e,t,n,r){var o=ie,s=Vr.transition;Vr.transition=null;try{ie=4,qu(e,t,n,r)}finally{ie=o,Vr.transition=s}}function qu(e,t,n,r){if(ji){var o=Kl(e,t,n,r);if(o===null)il(e,t,r,Mi,n),Td(e,r);else if(xx(o,e,t,n,r))r.stopPropagation();else if(Td(e,r),t&4&&-1<yx.indexOf(e)){for(;o!==null;){var s=Rs(o);if(s!==null&&Wp(s),s=Kl(e,t,n,r),s===null&&il(e,t,r,Mi,n),s===o)break;o=s}o!==null&&r.stopPropagation()}else il(e,t,r,null,n)}}var Mi=null;function Kl(e,t,n,r){if(Mi=null,e=Ku(r),e=er(e),e!==null)if(t=gr(e),t===null)e=null;else if(n=t.tag,n===13){if(e=Dp(t),e!==null)return e;e=null}else if(n===3){if(t.stateNode.current.memoizedState.isDehydrated)return t.tag===3?t.stateNode.containerInfo:null;e=null}else t!==e&&(e=null);return Mi=e,null}function Kp(e){switch(e){case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 1;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"toggle":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 4;case"message":switch(ux()){case Qu:return 1;case Fp:return 4;case Ti:case cx:return 16;case Bp:return 536870912;default:return 16}default:return 16}}var En=null,ec=null,di=null;function Qp(){if(di)return di;var e,t=ec,n=t.length,r,o="value"in En?En.value:En.textContent,s=o.length;for(e=0;e<n&&t[e]===o[e];e++);var i=n-e;for(r=1;r<=i&&t[n-r]===o[s-r];r++);return di=o.slice(e,1<r?1-r:void 0)}function fi(e){var t=e.keyCode;return"charCode"in e?(e=e.charCode,e===0&&t===13&&(e=13)):e=t,e===10&&(e=13),32<=e||e===13?e:0}function Ws(){return!0}function jd(){return!1}function at(e){function t(n,r,o,s,i){this._reactName=n,this._targetInst=o,this.type=r,this.nativeEvent=s,this.target=i,this.currentTarget=null;for(var a in e)e.hasOwnProperty(a)&&(n=e[a],this[a]=n?n(s):s[a]);return this.isDefaultPrevented=(s.defaultPrevented!=null?s.defaultPrevented:s.returnValue===!1)?Ws:jd,this.isPropagationStopped=jd,this}return ve(t.prototype,{preventDefault:function(){this.defaultPrevented=!0;var n=this.nativeEvent;n&&(n.preventDefault?n.preventDefault():typeof n.returnValue!="unknown"&&(n.returnValue=!1),this.isDefaultPrevented=Ws)},stopPropagation:function(){var n=this.nativeEvent;n&&(n.stopPropagation?n.stopPropagation():typeof n.cancelBubble!="unknown"&&(n.cancelBubble=!0),this.isPropagationStopped=Ws)},persist:function(){},isPersistent:Ws}),t}var fo={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},tc=at(fo),Ts=ve({},fo,{view:0,detail:0}),kx=at(Ts),Xa,Ja,Co,fa=ve({},Ts,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:nc,button:0,buttons:0,relatedTarget:function(e){return e.relatedTarget===void 0?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(e!==Co&&(Co&&e.type==="mousemove"?(Xa=e.screenX-Co.screenX,Ja=e.screenY-Co.screenY):Ja=Xa=0,Co=e),Xa)},movementY:function(e){return"movementY"in e?e.movementY:Ja}}),Md=at(fa),Sx=ve({},fa,{dataTransfer:0}),Ex=at(Sx),bx=ve({},Ts,{relatedTarget:0}),qa=at(bx),Nx=ve({},fo,{animationName:0,elapsedTime:0,pseudoElement:0}),Tx=at(Nx),Rx=ve({},fo,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}}),jx=at(Rx),Mx=ve({},fo,{data:0}),Pd=at(Mx),Px={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},Ax={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},Ox={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function Ix(e){var t=this.nativeEvent;return t.getModifierState?t.getModifierState(e):(e=Ox[e])?!!t[e]:!1}function nc(){return Ix}var Dx=ve({},Ts,{key:function(e){if(e.key){var t=Px[e.key]||e.key;if(t!=="Unidentified")return t}return e.type==="keypress"?(e=fi(e),e===13?"Enter":String.fromCharCode(e)):e.type==="keydown"||e.type==="keyup"?Ax[e.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:nc,charCode:function(e){return e.type==="keypress"?fi(e):0},keyCode:function(e){return e.type==="keydown"||e.type==="keyup"?e.keyCode:0},which:function(e){return e.type==="keypress"?fi(e):e.type==="keydown"||e.type==="keyup"?e.keyCode:0}}),Lx=at(Dx),$x=ve({},fa,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0}),Ad=at($x),zx=ve({},Ts,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:nc}),Fx=at(zx),Bx=ve({},fo,{propertyName:0,elapsedTime:0,pseudoElement:0}),Vx=at(Bx),Ux=ve({},fa,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0}),Wx=at(Ux),Hx=[9,13,27,32],rc=on&&"CompositionEvent"in window,Lo=null;on&&"documentMode"in document&&(Lo=document.documentMode);var Zx=on&&"TextEvent"in window&&!Lo,Xp=on&&(!rc||Lo&&8<Lo&&11>=Lo),Od=" ",Id=!1;function Jp(e,t){switch(e){case"keyup":return Hx.indexOf(t.keyCode)!==-1;case"keydown":return t.keyCode!==229;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function qp(e){return e=e.detail,typeof e=="object"&&"data"in e?e.data:null}var Nr=!1;function Gx(e,t){switch(e){case"compositionend":return qp(t);case"keypress":return t.which!==32?null:(Id=!0,Od);case"textInput":return e=t.data,e===Od&&Id?null:e;default:return null}}function Yx(e,t){if(Nr)return e==="compositionend"||!rc&&Jp(e,t)?(e=Qp(),di=ec=En=null,Nr=!1,e):null;switch(e){case"paste":return null;case"keypress":if(!(t.ctrlKey||t.altKey||t.metaKey)||t.ctrlKey&&t.altKey){if(t.char&&1<t.char.length)return t.char;if(t.which)return String.fromCharCode(t.which)}return null;case"compositionend":return Xp&&t.locale!=="ko"?null:t.data;default:return null}}var Kx={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function Dd(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t==="input"?!!Kx[e.type]:t==="textarea"}function eh(e,t,n,r){Mp(r),t=Pi(t,"onChange"),0<t.length&&(n=new tc("onChange","change",null,n,r),e.push({event:n,listeners:t}))}var $o=null,Jo=null;function Qx(e){dh(e,0)}function pa(e){var t=jr(e);if(Sp(t))return e}function Xx(e,t){if(e==="change")return t}var th=!1;if(on){var el;if(on){var tl="oninput"in document;if(!tl){var Ld=document.createElement("div");Ld.setAttribute("oninput","return;"),tl=typeof Ld.oninput=="function"}el=tl}else el=!1;th=el&&(!document.documentMode||9<document.documentMode)}function $d(){$o&&($o.detachEvent("onpropertychange",nh),Jo=$o=null)}function nh(e){if(e.propertyName==="value"&&pa(Jo)){var t=[];eh(t,Jo,e,Ku(e)),Ip(Qx,t)}}function Jx(e,t,n){e==="focusin"?($d(),$o=t,Jo=n,$o.attachEvent("onpropertychange",nh)):e==="focusout"&&$d()}function qx(e){if(e==="selectionchange"||e==="keyup"||e==="keydown")return pa(Jo)}function ew(e,t){if(e==="click")return pa(t)}function tw(e,t){if(e==="input"||e==="change")return pa(t)}function nw(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}var bt=typeof Object.is=="function"?Object.is:nw;function qo(e,t){if(bt(e,t))return!0;if(typeof e!="object"||e===null||typeof t!="object"||t===null)return!1;var n=Object.keys(e),r=Object.keys(t);if(n.length!==r.length)return!1;for(r=0;r<n.length;r++){var o=n[r];if(!Ml.call(t,o)||!bt(e[o],t[o]))return!1}return!0}function zd(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function Fd(e,t){var n=zd(e);e=0;for(var r;n;){if(n.nodeType===3){if(r=e+n.textContent.length,e<=t&&r>=t)return{node:n,offset:t-e};e=r}e:{for(;n;){if(n.nextSibling){n=n.nextSibling;break e}n=n.parentNode}n=void 0}n=zd(n)}}function rh(e,t){return e&&t?e===t?!0:e&&e.nodeType===3?!1:t&&t.nodeType===3?rh(e,t.parentNode):"contains"in e?e.contains(t):e.compareDocumentPosition?!!(e.compareDocumentPosition(t)&16):!1:!1}function oh(){for(var e=window,t=Ei();t instanceof e.HTMLIFrameElement;){try{var n=typeof t.contentWindow.location.href=="string"}catch{n=!1}if(n)e=t.contentWindow;else break;t=Ei(e.document)}return t}function oc(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t&&(t==="input"&&(e.type==="text"||e.type==="search"||e.type==="tel"||e.type==="url"||e.type==="password")||t==="textarea"||e.contentEditable==="true")}function rw(e){var t=oh(),n=e.focusedElem,r=e.selectionRange;if(t!==n&&n&&n.ownerDocument&&rh(n.ownerDocument.documentElement,n)){if(r!==null&&oc(n)){if(t=r.start,e=r.end,e===void 0&&(e=t),"selectionStart"in n)n.selectionStart=t,n.selectionEnd=Math.min(e,n.value.length);else if(e=(t=n.ownerDocument||document)&&t.defaultView||window,e.getSelection){e=e.getSelection();var o=n.textContent.length,s=Math.min(r.start,o);r=r.end===void 0?s:Math.min(r.end,o),!e.extend&&s>r&&(o=r,r=s,s=o),o=Fd(n,s);var i=Fd(n,r);o&&i&&(e.rangeCount!==1||e.anchorNode!==o.node||e.anchorOffset!==o.offset||e.focusNode!==i.node||e.focusOffset!==i.offset)&&(t=t.createRange(),t.setStart(o.node,o.offset),e.removeAllRanges(),s>r?(e.addRange(t),e.extend(i.node,i.offset)):(t.setEnd(i.node,i.offset),e.addRange(t)))}}for(t=[],e=n;e=e.parentNode;)e.nodeType===1&&t.push({element:e,left:e.scrollLeft,top:e.scrollTop});for(typeof n.focus=="function"&&n.focus(),n=0;n<t.length;n++)e=t[n],e.element.scrollLeft=e.left,e.element.scrollTop=e.top}}var ow=on&&"documentMode"in document&&11>=document.documentMode,Tr=null,Ql=null,zo=null,Xl=!1;function Bd(e,t,n){var r=n.window===n?n.document:n.nodeType===9?n:n.ownerDocument;Xl||Tr==null||Tr!==Ei(r)||(r=Tr,"selectionStart"in r&&oc(r)?r={start:r.selectionStart,end:r.selectionEnd}:(r=(r.ownerDocument&&r.ownerDocument.defaultView||window).getSelection(),r={anchorNode:r.anchorNode,anchorOffset:r.anchorOffset,focusNode:r.focusNode,focusOffset:r.focusOffset}),zo&&qo(zo,r)||(zo=r,r=Pi(Ql,"onSelect"),0<r.length&&(t=new tc("onSelect","select",null,t,n),e.push({event:t,listeners:r}),t.target=Tr)))}function Hs(e,t){var n={};return n[e.toLowerCase()]=t.toLowerCase(),n["Webkit"+e]="webkit"+t,n["Moz"+e]="moz"+t,n}var Rr={animationend:Hs("Animation","AnimationEnd"),animationiteration:Hs("Animation","AnimationIteration"),animationstart:Hs("Animation","AnimationStart"),transitionend:Hs("Transition","TransitionEnd")},nl={},sh={};on&&(sh=document.createElement("div").style,"AnimationEvent"in window||(delete Rr.animationend.animation,delete Rr.animationiteration.animation,delete Rr.animationstart.animation),"TransitionEvent"in window||delete Rr.transitionend.transition);function ha(e){if(nl[e])return nl[e];if(!Rr[e])return e;var t=Rr[e],n;for(n in t)if(t.hasOwnProperty(n)&&n in sh)return nl[e]=t[n];return e}var ih=ha("animationend"),ah=ha("animationiteration"),lh=ha("animationstart"),uh=ha("transitionend"),ch=new Map,Vd="abort auxClick cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");function Yn(e,t){ch.set(e,t),vr(t,[e])}for(var rl=0;rl<Vd.length;rl++){var ol=Vd[rl],sw=ol.toLowerCase(),iw=ol[0].toUpperCase()+ol.slice(1);Yn(sw,"on"+iw)}Yn(ih,"onAnimationEnd");Yn(ah,"onAnimationIteration");Yn(lh,"onAnimationStart");Yn("dblclick","onDoubleClick");Yn("focusin","onFocus");Yn("focusout","onBlur");Yn(uh,"onTransitionEnd");Qr("onMouseEnter",["mouseout","mouseover"]);Qr("onMouseLeave",["mouseout","mouseover"]);Qr("onPointerEnter",["pointerout","pointerover"]);Qr("onPointerLeave",["pointerout","pointerover"]);vr("onChange","change click focusin focusout input keydown keyup selectionchange".split(" "));vr("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" "));vr("onBeforeInput",["compositionend","keypress","textInput","paste"]);vr("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" "));vr("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" "));vr("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var Mo="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),aw=new Set("cancel close invalid load scroll toggle".split(" ").concat(Mo));function Ud(e,t,n){var r=e.type||"unknown-event";e.currentTarget=n,sx(r,t,void 0,e),e.currentTarget=null}function dh(e,t){t=(t&4)!==0;for(var n=0;n<e.length;n++){var r=e[n],o=r.event;r=r.listeners;e:{var s=void 0;if(t)for(var i=r.length-1;0<=i;i--){var a=r[i],l=a.instance,u=a.currentTarget;if(a=a.listener,l!==s&&o.isPropagationStopped())break e;Ud(o,a,u),s=l}else for(i=0;i<r.length;i++){if(a=r[i],l=a.instance,u=a.currentTarget,a=a.listener,l!==s&&o.isPropagationStopped())break e;Ud(o,a,u),s=l}}}if(Ni)throw e=Zl,Ni=!1,Zl=null,e}function ue(e,t){var n=t[nu];n===void 0&&(n=t[nu]=new Set);var r=e+"__bubble";n.has(r)||(fh(t,e,2,!1),n.add(r))}function sl(e,t,n){var r=0;t&&(r|=4),fh(n,e,r,t)}var Zs="_reactListening"+Math.random().toString(36).slice(2);function es(e){if(!e[Zs]){e[Zs]=!0,xp.forEach(function(n){n!=="selectionchange"&&(aw.has(n)||sl(n,!1,e),sl(n,!0,e))});var t=e.nodeType===9?e:e.ownerDocument;t===null||t[Zs]||(t[Zs]=!0,sl("selectionchange",!1,t))}}function fh(e,t,n,r){switch(Kp(t)){case 1:var o=_x;break;case 4:o=Cx;break;default:o=qu}n=o.bind(null,t,n,e),o=void 0,!Hl||t!=="touchstart"&&t!=="touchmove"&&t!=="wheel"||(o=!0),r?o!==void 0?e.addEventListener(t,n,{capture:!0,passive:o}):e.addEventListener(t,n,!0):o!==void 0?e.addEventListener(t,n,{passive:o}):e.addEventListener(t,n,!1)}function il(e,t,n,r,o){var s=r;if(!(t&1)&&!(t&2)&&r!==null)e:for(;;){if(r===null)return;var i=r.tag;if(i===3||i===4){var a=r.stateNode.containerInfo;if(a===o||a.nodeType===8&&a.parentNode===o)break;if(i===4)for(i=r.return;i!==null;){var l=i.tag;if((l===3||l===4)&&(l=i.stateNode.containerInfo,l===o||l.nodeType===8&&l.parentNode===o))return;i=i.return}for(;a!==null;){if(i=er(a),i===null)return;if(l=i.tag,l===5||l===6){r=s=i;continue e}a=a.parentNode}}r=r.return}Ip(function(){var u=s,d=Ku(n),f=[];e:{var p=ch.get(e);if(p!==void 0){var y=tc,w=e;switch(e){case"keypress":if(fi(n)===0)break e;case"keydown":case"keyup":y=Lx;break;case"focusin":w="focus",y=qa;break;case"focusout":w="blur",y=qa;break;case"beforeblur":case"afterblur":y=qa;break;case"click":if(n.button===2)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":y=Md;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":y=Ex;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":y=Fx;break;case ih:case ah:case lh:y=Tx;break;case uh:y=Vx;break;case"scroll":y=kx;break;case"wheel":y=Wx;break;case"copy":case"cut":case"paste":y=jx;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":y=Ad}var g=(t&4)!==0,k=!g&&e==="scroll",m=g?p!==null?p+"Capture":null:p;g=[];for(var h=u,x;h!==null;){x=h;var _=x.stateNode;if(x.tag===5&&_!==null&&(x=_,m!==null&&(_=Yo(h,m),_!=null&&g.push(ts(h,_,x)))),k)break;h=h.return}0<g.length&&(p=new y(p,w,null,n,d),f.push({event:p,listeners:g}))}}if(!(t&7)){e:{if(p=e==="mouseover"||e==="pointerover",y=e==="mouseout"||e==="pointerout",p&&n!==Ul&&(w=n.relatedTarget||n.fromElement)&&(er(w)||w[sn]))break e;if((y||p)&&(p=d.window===d?d:(p=d.ownerDocument)?p.defaultView||p.parentWindow:window,y?(w=n.relatedTarget||n.toElement,y=u,w=w?er(w):null,w!==null&&(k=gr(w),w!==k||w.tag!==5&&w.tag!==6)&&(w=null)):(y=null,w=u),y!==w)){if(g=Md,_="onMouseLeave",m="onMouseEnter",h="mouse",(e==="pointerout"||e==="pointerover")&&(g=Ad,_="onPointerLeave",m="onPointerEnter",h="pointer"),k=y==null?p:jr(y),x=w==null?p:jr(w),p=new g(_,h+"leave",y,n,d),p.target=k,p.relatedTarget=x,_=null,er(d)===u&&(g=new g(m,h+"enter",w,n,d),g.target=x,g.relatedTarget=k,_=g),k=_,y&&w)t:{for(g=y,m=w,h=0,x=g;x;x=wr(x))h++;for(x=0,_=m;_;_=wr(_))x++;for(;0<h-x;)g=wr(g),h--;for(;0<x-h;)m=wr(m),x--;for(;h--;){if(g===m||m!==null&&g===m.alternate)break t;g=wr(g),m=wr(m)}g=null}else g=null;y!==null&&Wd(f,p,y,g,!1),w!==null&&k!==null&&Wd(f,k,w,g,!0)}}e:{if(p=u?jr(u):window,y=p.nodeName&&p.nodeName.toLowerCase(),y==="select"||y==="input"&&p.type==="file")var b=Xx;else if(Dd(p))if(th)b=tw;else{b=qx;var N=Jx}else(y=p.nodeName)&&y.toLowerCase()==="input"&&(p.type==="checkbox"||p.type==="radio")&&(b=ew);if(b&&(b=b(e,u))){eh(f,b,n,d);break e}N&&N(e,p,u),e==="focusout"&&(N=p._wrapperState)&&N.controlled&&p.type==="number"&&$l(p,"number",p.value)}switch(N=u?jr(u):window,e){case"focusin":(Dd(N)||N.contentEditable==="true")&&(Tr=N,Ql=u,zo=null);break;case"focusout":zo=Ql=Tr=null;break;case"mousedown":Xl=!0;break;case"contextmenu":case"mouseup":case"dragend":Xl=!1,Bd(f,n,d);break;case"selectionchange":if(ow)break;case"keydown":case"keyup":Bd(f,n,d)}var S;if(rc)e:{switch(e){case"compositionstart":var C="onCompositionStart";break e;case"compositionend":C="onCompositionEnd";break e;case"compositionupdate":C="onCompositionUpdate";break e}C=void 0}else Nr?Jp(e,n)&&(C="onCompositionEnd"):e==="keydown"&&n.keyCode===229&&(C="onCompositionStart");C&&(Xp&&n.locale!=="ko"&&(Nr||C!=="onCompositionStart"?C==="onCompositionEnd"&&Nr&&(S=Qp()):(En=d,ec="value"in En?En.value:En.textContent,Nr=!0)),N=Pi(u,C),0<N.length&&(C=new Pd(C,e,null,n,d),f.push({event:C,listeners:N}),S?C.data=S:(S=qp(n),S!==null&&(C.data=S)))),(S=Zx?Gx(e,n):Yx(e,n))&&(u=Pi(u,"onBeforeInput"),0<u.length&&(d=new Pd("onBeforeInput","beforeinput",null,n,d),f.push({event:d,listeners:u}),d.data=S))}dh(f,t)})}function ts(e,t,n){return{instance:e,listener:t,currentTarget:n}}function Pi(e,t){for(var n=t+"Capture",r=[];e!==null;){var o=e,s=o.stateNode;o.tag===5&&s!==null&&(o=s,s=Yo(e,n),s!=null&&r.unshift(ts(e,s,o)),s=Yo(e,t),s!=null&&r.push(ts(e,s,o))),e=e.return}return r}function wr(e){if(e===null)return null;do e=e.return;while(e&&e.tag!==5);return e||null}function Wd(e,t,n,r,o){for(var s=t._reactName,i=[];n!==null&&n!==r;){var a=n,l=a.alternate,u=a.stateNode;if(l!==null&&l===r)break;a.tag===5&&u!==null&&(a=u,o?(l=Yo(n,s),l!=null&&i.unshift(ts(n,l,a))):o||(l=Yo(n,s),l!=null&&i.push(ts(n,l,a)))),n=n.return}i.length!==0&&e.push({event:t,listeners:i})}var lw=/\r\n?/g,uw=/\u0000|\uFFFD/g;function Hd(e){return(typeof e=="string"?e:""+e).replace(lw,`
`).replace(uw,"")}function Gs(e,t,n){if(t=Hd(t),Hd(e)!==t&&n)throw Error(O(425))}function Ai(){}var Jl=null,ql=null;function eu(e,t){return e==="textarea"||e==="noscript"||typeof t.children=="string"||typeof t.children=="number"||typeof t.dangerouslySetInnerHTML=="object"&&t.dangerouslySetInnerHTML!==null&&t.dangerouslySetInnerHTML.__html!=null}var tu=typeof setTimeout=="function"?setTimeout:void 0,cw=typeof clearTimeout=="function"?clearTimeout:void 0,Zd=typeof Promise=="function"?Promise:void 0,dw=typeof queueMicrotask=="function"?queueMicrotask:typeof Zd<"u"?function(e){return Zd.resolve(null).then(e).catch(fw)}:tu;function fw(e){setTimeout(function(){throw e})}function al(e,t){var n=t,r=0;do{var o=n.nextSibling;if(e.removeChild(n),o&&o.nodeType===8)if(n=o.data,n==="/$"){if(r===0){e.removeChild(o),Xo(t);return}r--}else n!=="$"&&n!=="$?"&&n!=="$!"||r++;n=o}while(n);Xo(t)}function jn(e){for(;e!=null;e=e.nextSibling){var t=e.nodeType;if(t===1||t===3)break;if(t===8){if(t=e.data,t==="$"||t==="$!"||t==="$?")break;if(t==="/$")return null}}return e}function Gd(e){e=e.previousSibling;for(var t=0;e;){if(e.nodeType===8){var n=e.data;if(n==="$"||n==="$!"||n==="$?"){if(t===0)return e;t--}else n==="/$"&&t++}e=e.previousSibling}return null}var po=Math.random().toString(36).slice(2),Ot="__reactFiber$"+po,ns="__reactProps$"+po,sn="__reactContainer$"+po,nu="__reactEvents$"+po,pw="__reactListeners$"+po,hw="__reactHandles$"+po;function er(e){var t=e[Ot];if(t)return t;for(var n=e.parentNode;n;){if(t=n[sn]||n[Ot]){if(n=t.alternate,t.child!==null||n!==null&&n.child!==null)for(e=Gd(e);e!==null;){if(n=e[Ot])return n;e=Gd(e)}return t}e=n,n=e.parentNode}return null}function Rs(e){return e=e[Ot]||e[sn],!e||e.tag!==5&&e.tag!==6&&e.tag!==13&&e.tag!==3?null:e}function jr(e){if(e.tag===5||e.tag===6)return e.stateNode;throw Error(O(33))}function ma(e){return e[ns]||null}var ru=[],Mr=-1;function Kn(e){return{current:e}}function ce(e){0>Mr||(e.current=ru[Mr],ru[Mr]=null,Mr--)}function ae(e,t){Mr++,ru[Mr]=e.current,e.current=t}var Dn={},ze=Kn(Dn),Qe=Kn(!1),ir=Dn;function Xr(e,t){var n=e.type.contextTypes;if(!n)return Dn;var r=e.stateNode;if(r&&r.__reactInternalMemoizedUnmaskedChildContext===t)return r.__reactInternalMemoizedMaskedChildContext;var o={},s;for(s in n)o[s]=t[s];return r&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=t,e.__reactInternalMemoizedMaskedChildContext=o),o}function Xe(e){return e=e.childContextTypes,e!=null}function Oi(){ce(Qe),ce(ze)}function Yd(e,t,n){if(ze.current!==Dn)throw Error(O(168));ae(ze,t),ae(Qe,n)}function ph(e,t,n){var r=e.stateNode;if(t=t.childContextTypes,typeof r.getChildContext!="function")return n;r=r.getChildContext();for(var o in r)if(!(o in t))throw Error(O(108,Jy(e)||"Unknown",o));return ve({},n,r)}function Ii(e){return e=(e=e.stateNode)&&e.__reactInternalMemoizedMergedChildContext||Dn,ir=ze.current,ae(ze,e),ae(Qe,Qe.current),!0}function Kd(e,t,n){var r=e.stateNode;if(!r)throw Error(O(169));n?(e=ph(e,t,ir),r.__reactInternalMemoizedMergedChildContext=e,ce(Qe),ce(ze),ae(ze,e)):ce(Qe),ae(Qe,n)}var Qt=null,va=!1,ll=!1;function hh(e){Qt===null?Qt=[e]:Qt.push(e)}function mw(e){va=!0,hh(e)}function Qn(){if(!ll&&Qt!==null){ll=!0;var e=0,t=ie;try{var n=Qt;for(ie=1;e<n.length;e++){var r=n[e];do r=r(!0);while(r!==null)}Qt=null,va=!1}catch(o){throw Qt!==null&&(Qt=Qt.slice(e+1)),zp(Qu,Qn),o}finally{ie=t,ll=!1}}return null}var Pr=[],Ar=0,Di=null,Li=0,lt=[],ut=0,ar=null,Jt=1,qt="";function Jn(e,t){Pr[Ar++]=Li,Pr[Ar++]=Di,Di=e,Li=t}function mh(e,t,n){lt[ut++]=Jt,lt[ut++]=qt,lt[ut++]=ar,ar=e;var r=Jt;e=qt;var o=32-kt(r)-1;r&=~(1<<o),n+=1;var s=32-kt(t)+o;if(30<s){var i=o-o%5;s=(r&(1<<i)-1).toString(32),r>>=i,o-=i,Jt=1<<32-kt(t)+o|n<<o|r,qt=s+e}else Jt=1<<s|n<<o|r,qt=e}function sc(e){e.return!==null&&(Jn(e,1),mh(e,1,0))}function ic(e){for(;e===Di;)Di=Pr[--Ar],Pr[Ar]=null,Li=Pr[--Ar],Pr[Ar]=null;for(;e===ar;)ar=lt[--ut],lt[ut]=null,qt=lt[--ut],lt[ut]=null,Jt=lt[--ut],lt[ut]=null}var nt=null,tt=null,fe=!1,_t=null;function vh(e,t){var n=ct(5,null,null,0);n.elementType="DELETED",n.stateNode=t,n.return=e,t=e.deletions,t===null?(e.deletions=[n],e.flags|=16):t.push(n)}function Qd(e,t){switch(e.tag){case 5:var n=e.type;return t=t.nodeType!==1||n.toLowerCase()!==t.nodeName.toLowerCase()?null:t,t!==null?(e.stateNode=t,nt=e,tt=jn(t.firstChild),!0):!1;case 6:return t=e.pendingProps===""||t.nodeType!==3?null:t,t!==null?(e.stateNode=t,nt=e,tt=null,!0):!1;case 13:return t=t.nodeType!==8?null:t,t!==null?(n=ar!==null?{id:Jt,overflow:qt}:null,e.memoizedState={dehydrated:t,treeContext:n,retryLane:1073741824},n=ct(18,null,null,0),n.stateNode=t,n.return=e,e.child=n,nt=e,tt=null,!0):!1;default:return!1}}function ou(e){return(e.mode&1)!==0&&(e.flags&128)===0}function su(e){if(fe){var t=tt;if(t){var n=t;if(!Qd(e,t)){if(ou(e))throw Error(O(418));t=jn(n.nextSibling);var r=nt;t&&Qd(e,t)?vh(r,n):(e.flags=e.flags&-4097|2,fe=!1,nt=e)}}else{if(ou(e))throw Error(O(418));e.flags=e.flags&-4097|2,fe=!1,nt=e}}}function Xd(e){for(e=e.return;e!==null&&e.tag!==5&&e.tag!==3&&e.tag!==13;)e=e.return;nt=e}function Ys(e){if(e!==nt)return!1;if(!fe)return Xd(e),fe=!0,!1;var t;if((t=e.tag!==3)&&!(t=e.tag!==5)&&(t=e.type,t=t!=="head"&&t!=="body"&&!eu(e.type,e.memoizedProps)),t&&(t=tt)){if(ou(e))throw gh(),Error(O(418));for(;t;)vh(e,t),t=jn(t.nextSibling)}if(Xd(e),e.tag===13){if(e=e.memoizedState,e=e!==null?e.dehydrated:null,!e)throw Error(O(317));e:{for(e=e.nextSibling,t=0;e;){if(e.nodeType===8){var n=e.data;if(n==="/$"){if(t===0){tt=jn(e.nextSibling);break e}t--}else n!=="$"&&n!=="$!"&&n!=="$?"||t++}e=e.nextSibling}tt=null}}else tt=nt?jn(e.stateNode.nextSibling):null;return!0}function gh(){for(var e=tt;e;)e=jn(e.nextSibling)}function Jr(){tt=nt=null,fe=!1}function ac(e){_t===null?_t=[e]:_t.push(e)}var vw=fn.ReactCurrentBatchConfig;function ko(e,t,n){if(e=n.ref,e!==null&&typeof e!="function"&&typeof e!="object"){if(n._owner){if(n=n._owner,n){if(n.tag!==1)throw Error(O(309));var r=n.stateNode}if(!r)throw Error(O(147,e));var o=r,s=""+e;return t!==null&&t.ref!==null&&typeof t.ref=="function"&&t.ref._stringRef===s?t.ref:(t=function(i){var a=o.refs;i===null?delete a[s]:a[s]=i},t._stringRef=s,t)}if(typeof e!="string")throw Error(O(284));if(!n._owner)throw Error(O(290,e))}return e}function Ks(e,t){throw e=Object.prototype.toString.call(t),Error(O(31,e==="[object Object]"?"object with keys {"+Object.keys(t).join(", ")+"}":e))}function Jd(e){var t=e._init;return t(e._payload)}function yh(e){function t(m,h){if(e){var x=m.deletions;x===null?(m.deletions=[h],m.flags|=16):x.push(h)}}function n(m,h){if(!e)return null;for(;h!==null;)t(m,h),h=h.sibling;return null}function r(m,h){for(m=new Map;h!==null;)h.key!==null?m.set(h.key,h):m.set(h.index,h),h=h.sibling;return m}function o(m,h){return m=On(m,h),m.index=0,m.sibling=null,m}function s(m,h,x){return m.index=x,e?(x=m.alternate,x!==null?(x=x.index,x<h?(m.flags|=2,h):x):(m.flags|=2,h)):(m.flags|=1048576,h)}function i(m){return e&&m.alternate===null&&(m.flags|=2),m}function a(m,h,x,_){return h===null||h.tag!==6?(h=ml(x,m.mode,_),h.return=m,h):(h=o(h,x),h.return=m,h)}function l(m,h,x,_){var b=x.type;return b===br?d(m,h,x.props.children,_,x.key):h!==null&&(h.elementType===b||typeof b=="object"&&b!==null&&b.$$typeof===_n&&Jd(b)===h.type)?(_=o(h,x.props),_.ref=ko(m,h,x),_.return=m,_):(_=xi(x.type,x.key,x.props,null,m.mode,_),_.ref=ko(m,h,x),_.return=m,_)}function u(m,h,x,_){return h===null||h.tag!==4||h.stateNode.containerInfo!==x.containerInfo||h.stateNode.implementation!==x.implementation?(h=vl(x,m.mode,_),h.return=m,h):(h=o(h,x.children||[]),h.return=m,h)}function d(m,h,x,_,b){return h===null||h.tag!==7?(h=or(x,m.mode,_,b),h.return=m,h):(h=o(h,x),h.return=m,h)}function f(m,h,x){if(typeof h=="string"&&h!==""||typeof h=="number")return h=ml(""+h,m.mode,x),h.return=m,h;if(typeof h=="object"&&h!==null){switch(h.$$typeof){case $s:return x=xi(h.type,h.key,h.props,null,m.mode,x),x.ref=ko(m,null,h),x.return=m,x;case Er:return h=vl(h,m.mode,x),h.return=m,h;case _n:var _=h._init;return f(m,_(h._payload),x)}if(Ro(h)||yo(h))return h=or(h,m.mode,x,null),h.return=m,h;Ks(m,h)}return null}function p(m,h,x,_){var b=h!==null?h.key:null;if(typeof x=="string"&&x!==""||typeof x=="number")return b!==null?null:a(m,h,""+x,_);if(typeof x=="object"&&x!==null){switch(x.$$typeof){case $s:return x.key===b?l(m,h,x,_):null;case Er:return x.key===b?u(m,h,x,_):null;case _n:return b=x._init,p(m,h,b(x._payload),_)}if(Ro(x)||yo(x))return b!==null?null:d(m,h,x,_,null);Ks(m,x)}return null}function y(m,h,x,_,b){if(typeof _=="string"&&_!==""||typeof _=="number")return m=m.get(x)||null,a(h,m,""+_,b);if(typeof _=="object"&&_!==null){switch(_.$$typeof){case $s:return m=m.get(_.key===null?x:_.key)||null,l(h,m,_,b);case Er:return m=m.get(_.key===null?x:_.key)||null,u(h,m,_,b);case _n:var N=_._init;return y(m,h,x,N(_._payload),b)}if(Ro(_)||yo(_))return m=m.get(x)||null,d(h,m,_,b,null);Ks(h,_)}return null}function w(m,h,x,_){for(var b=null,N=null,S=h,C=h=0,L=null;S!==null&&C<x.length;C++){S.index>C?(L=S,S=null):L=S.sibling;var M=p(m,S,x[C],_);if(M===null){S===null&&(S=L);break}e&&S&&M.alternate===null&&t(m,S),h=s(M,h,C),N===null?b=M:N.sibling=M,N=M,S=L}if(C===x.length)return n(m,S),fe&&Jn(m,C),b;if(S===null){for(;C<x.length;C++)S=f(m,x[C],_),S!==null&&(h=s(S,h,C),N===null?b=S:N.sibling=S,N=S);return fe&&Jn(m,C),b}for(S=r(m,S);C<x.length;C++)L=y(S,m,C,x[C],_),L!==null&&(e&&L.alternate!==null&&S.delete(L.key===null?C:L.key),h=s(L,h,C),N===null?b=L:N.sibling=L,N=L);return e&&S.forEach(function($){return t(m,$)}),fe&&Jn(m,C),b}function g(m,h,x,_){var b=yo(x);if(typeof b!="function")throw Error(O(150));if(x=b.call(x),x==null)throw Error(O(151));for(var N=b=null,S=h,C=h=0,L=null,M=x.next();S!==null&&!M.done;C++,M=x.next()){S.index>C?(L=S,S=null):L=S.sibling;var $=p(m,S,M.value,_);if($===null){S===null&&(S=L);break}e&&S&&$.alternate===null&&t(m,S),h=s($,h,C),N===null?b=$:N.sibling=$,N=$,S=L}if(M.done)return n(m,S),fe&&Jn(m,C),b;if(S===null){for(;!M.done;C++,M=x.next())M=f(m,M.value,_),M!==null&&(h=s(M,h,C),N===null?b=M:N.sibling=M,N=M);return fe&&Jn(m,C),b}for(S=r(m,S);!M.done;C++,M=x.next())M=y(S,m,C,M.value,_),M!==null&&(e&&M.alternate!==null&&S.delete(M.key===null?C:M.key),h=s(M,h,C),N===null?b=M:N.sibling=M,N=M);return e&&S.forEach(function(V){return t(m,V)}),fe&&Jn(m,C),b}function k(m,h,x,_){if(typeof x=="object"&&x!==null&&x.type===br&&x.key===null&&(x=x.props.children),typeof x=="object"&&x!==null){switch(x.$$typeof){case $s:e:{for(var b=x.key,N=h;N!==null;){if(N.key===b){if(b=x.type,b===br){if(N.tag===7){n(m,N.sibling),h=o(N,x.props.children),h.return=m,m=h;break e}}else if(N.elementType===b||typeof b=="object"&&b!==null&&b.$$typeof===_n&&Jd(b)===N.type){n(m,N.sibling),h=o(N,x.props),h.ref=ko(m,N,x),h.return=m,m=h;break e}n(m,N);break}else t(m,N);N=N.sibling}x.type===br?(h=or(x.props.children,m.mode,_,x.key),h.return=m,m=h):(_=xi(x.type,x.key,x.props,null,m.mode,_),_.ref=ko(m,h,x),_.return=m,m=_)}return i(m);case Er:e:{for(N=x.key;h!==null;){if(h.key===N)if(h.tag===4&&h.stateNode.containerInfo===x.containerInfo&&h.stateNode.implementation===x.implementation){n(m,h.sibling),h=o(h,x.children||[]),h.return=m,m=h;break e}else{n(m,h);break}else t(m,h);h=h.sibling}h=vl(x,m.mode,_),h.return=m,m=h}return i(m);case _n:return N=x._init,k(m,h,N(x._payload),_)}if(Ro(x))return w(m,h,x,_);if(yo(x))return g(m,h,x,_);Ks(m,x)}return typeof x=="string"&&x!==""||typeof x=="number"?(x=""+x,h!==null&&h.tag===6?(n(m,h.sibling),h=o(h,x),h.return=m,m=h):(n(m,h),h=ml(x,m.mode,_),h.return=m,m=h),i(m)):n(m,h)}return k}var qr=yh(!0),xh=yh(!1),$i=Kn(null),zi=null,Or=null,lc=null;function uc(){lc=Or=zi=null}function cc(e){var t=$i.current;ce($i),e._currentValue=t}function iu(e,t,n){for(;e!==null;){var r=e.alternate;if((e.childLanes&t)!==t?(e.childLanes|=t,r!==null&&(r.childLanes|=t)):r!==null&&(r.childLanes&t)!==t&&(r.childLanes|=t),e===n)break;e=e.return}}function Ur(e,t){zi=e,lc=Or=null,e=e.dependencies,e!==null&&e.firstContext!==null&&(e.lanes&t&&(Ke=!0),e.firstContext=null)}function pt(e){var t=e._currentValue;if(lc!==e)if(e={context:e,memoizedValue:t,next:null},Or===null){if(zi===null)throw Error(O(308));Or=e,zi.dependencies={lanes:0,firstContext:e}}else Or=Or.next=e;return t}var tr=null;function dc(e){tr===null?tr=[e]:tr.push(e)}function wh(e,t,n,r){var o=t.interleaved;return o===null?(n.next=n,dc(t)):(n.next=o.next,o.next=n),t.interleaved=n,an(e,r)}function an(e,t){e.lanes|=t;var n=e.alternate;for(n!==null&&(n.lanes|=t),n=e,e=e.return;e!==null;)e.childLanes|=t,n=e.alternate,n!==null&&(n.childLanes|=t),n=e,e=e.return;return n.tag===3?n.stateNode:null}var Cn=!1;function fc(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,interleaved:null,lanes:0},effects:null}}function _h(e,t){e=e.updateQueue,t.updateQueue===e&&(t.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,effects:e.effects})}function en(e,t){return{eventTime:e,lane:t,tag:0,payload:null,callback:null,next:null}}function Mn(e,t,n){var r=e.updateQueue;if(r===null)return null;if(r=r.shared,re&2){var o=r.pending;return o===null?t.next=t:(t.next=o.next,o.next=t),r.pending=t,an(e,n)}return o=r.interleaved,o===null?(t.next=t,dc(r)):(t.next=o.next,o.next=t),r.interleaved=t,an(e,n)}function pi(e,t,n){if(t=t.updateQueue,t!==null&&(t=t.shared,(n&4194240)!==0)){var r=t.lanes;r&=e.pendingLanes,n|=r,t.lanes=n,Xu(e,n)}}function qd(e,t){var n=e.updateQueue,r=e.alternate;if(r!==null&&(r=r.updateQueue,n===r)){var o=null,s=null;if(n=n.firstBaseUpdate,n!==null){do{var i={eventTime:n.eventTime,lane:n.lane,tag:n.tag,payload:n.payload,callback:n.callback,next:null};s===null?o=s=i:s=s.next=i,n=n.next}while(n!==null);s===null?o=s=t:s=s.next=t}else o=s=t;n={baseState:r.baseState,firstBaseUpdate:o,lastBaseUpdate:s,shared:r.shared,effects:r.effects},e.updateQueue=n;return}e=n.lastBaseUpdate,e===null?n.firstBaseUpdate=t:e.next=t,n.lastBaseUpdate=t}function Fi(e,t,n,r){var o=e.updateQueue;Cn=!1;var s=o.firstBaseUpdate,i=o.lastBaseUpdate,a=o.shared.pending;if(a!==null){o.shared.pending=null;var l=a,u=l.next;l.next=null,i===null?s=u:i.next=u,i=l;var d=e.alternate;d!==null&&(d=d.updateQueue,a=d.lastBaseUpdate,a!==i&&(a===null?d.firstBaseUpdate=u:a.next=u,d.lastBaseUpdate=l))}if(s!==null){var f=o.baseState;i=0,d=u=l=null,a=s;do{var p=a.lane,y=a.eventTime;if((r&p)===p){d!==null&&(d=d.next={eventTime:y,lane:0,tag:a.tag,payload:a.payload,callback:a.callback,next:null});e:{var w=e,g=a;switch(p=t,y=n,g.tag){case 1:if(w=g.payload,typeof w=="function"){f=w.call(y,f,p);break e}f=w;break e;case 3:w.flags=w.flags&-65537|128;case 0:if(w=g.payload,p=typeof w=="function"?w.call(y,f,p):w,p==null)break e;f=ve({},f,p);break e;case 2:Cn=!0}}a.callback!==null&&a.lane!==0&&(e.flags|=64,p=o.effects,p===null?o.effects=[a]:p.push(a))}else y={eventTime:y,lane:p,tag:a.tag,payload:a.payload,callback:a.callback,next:null},d===null?(u=d=y,l=f):d=d.next=y,i|=p;if(a=a.next,a===null){if(a=o.shared.pending,a===null)break;p=a,a=p.next,p.next=null,o.lastBaseUpdate=p,o.shared.pending=null}}while(!0);if(d===null&&(l=f),o.baseState=l,o.firstBaseUpdate=u,o.lastBaseUpdate=d,t=o.shared.interleaved,t!==null){o=t;do i|=o.lane,o=o.next;while(o!==t)}else s===null&&(o.shared.lanes=0);ur|=i,e.lanes=i,e.memoizedState=f}}function ef(e,t,n){if(e=t.effects,t.effects=null,e!==null)for(t=0;t<e.length;t++){var r=e[t],o=r.callback;if(o!==null){if(r.callback=null,r=n,typeof o!="function")throw Error(O(191,o));o.call(r)}}}var js={},Lt=Kn(js),rs=Kn(js),os=Kn(js);function nr(e){if(e===js)throw Error(O(174));return e}function pc(e,t){switch(ae(os,t),ae(rs,e),ae(Lt,js),e=t.nodeType,e){case 9:case 11:t=(t=t.documentElement)?t.namespaceURI:Fl(null,"");break;default:e=e===8?t.parentNode:t,t=e.namespaceURI||null,e=e.tagName,t=Fl(t,e)}ce(Lt),ae(Lt,t)}function eo(){ce(Lt),ce(rs),ce(os)}function Ch(e){nr(os.current);var t=nr(Lt.current),n=Fl(t,e.type);t!==n&&(ae(rs,e),ae(Lt,n))}function hc(e){rs.current===e&&(ce(Lt),ce(rs))}var he=Kn(0);function Bi(e){for(var t=e;t!==null;){if(t.tag===13){var n=t.memoizedState;if(n!==null&&(n=n.dehydrated,n===null||n.data==="$?"||n.data==="$!"))return t}else if(t.tag===19&&t.memoizedProps.revealOrder!==void 0){if(t.flags&128)return t}else if(t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}return null}var ul=[];function mc(){for(var e=0;e<ul.length;e++)ul[e]._workInProgressVersionPrimary=null;ul.length=0}var hi=fn.ReactCurrentDispatcher,cl=fn.ReactCurrentBatchConfig,lr=0,me=null,Te=null,je=null,Vi=!1,Fo=!1,ss=0,gw=0;function De(){throw Error(O(321))}function vc(e,t){if(t===null)return!1;for(var n=0;n<t.length&&n<e.length;n++)if(!bt(e[n],t[n]))return!1;return!0}function gc(e,t,n,r,o,s){if(lr=s,me=t,t.memoizedState=null,t.updateQueue=null,t.lanes=0,hi.current=e===null||e.memoizedState===null?_w:Cw,e=n(r,o),Fo){s=0;do{if(Fo=!1,ss=0,25<=s)throw Error(O(301));s+=1,je=Te=null,t.updateQueue=null,hi.current=kw,e=n(r,o)}while(Fo)}if(hi.current=Ui,t=Te!==null&&Te.next!==null,lr=0,je=Te=me=null,Vi=!1,t)throw Error(O(300));return e}function yc(){var e=ss!==0;return ss=0,e}function At(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return je===null?me.memoizedState=je=e:je=je.next=e,je}function ht(){if(Te===null){var e=me.alternate;e=e!==null?e.memoizedState:null}else e=Te.next;var t=je===null?me.memoizedState:je.next;if(t!==null)je=t,Te=e;else{if(e===null)throw Error(O(310));Te=e,e={memoizedState:Te.memoizedState,baseState:Te.baseState,baseQueue:Te.baseQueue,queue:Te.queue,next:null},je===null?me.memoizedState=je=e:je=je.next=e}return je}function is(e,t){return typeof t=="function"?t(e):t}function dl(e){var t=ht(),n=t.queue;if(n===null)throw Error(O(311));n.lastRenderedReducer=e;var r=Te,o=r.baseQueue,s=n.pending;if(s!==null){if(o!==null){var i=o.next;o.next=s.next,s.next=i}r.baseQueue=o=s,n.pending=null}if(o!==null){s=o.next,r=r.baseState;var a=i=null,l=null,u=s;do{var d=u.lane;if((lr&d)===d)l!==null&&(l=l.next={lane:0,action:u.action,hasEagerState:u.hasEagerState,eagerState:u.eagerState,next:null}),r=u.hasEagerState?u.eagerState:e(r,u.action);else{var f={lane:d,action:u.action,hasEagerState:u.hasEagerState,eagerState:u.eagerState,next:null};l===null?(a=l=f,i=r):l=l.next=f,me.lanes|=d,ur|=d}u=u.next}while(u!==null&&u!==s);l===null?i=r:l.next=a,bt(r,t.memoizedState)||(Ke=!0),t.memoizedState=r,t.baseState=i,t.baseQueue=l,n.lastRenderedState=r}if(e=n.interleaved,e!==null){o=e;do s=o.lane,me.lanes|=s,ur|=s,o=o.next;while(o!==e)}else o===null&&(n.lanes=0);return[t.memoizedState,n.dispatch]}function fl(e){var t=ht(),n=t.queue;if(n===null)throw Error(O(311));n.lastRenderedReducer=e;var r=n.dispatch,o=n.pending,s=t.memoizedState;if(o!==null){n.pending=null;var i=o=o.next;do s=e(s,i.action),i=i.next;while(i!==o);bt(s,t.memoizedState)||(Ke=!0),t.memoizedState=s,t.baseQueue===null&&(t.baseState=s),n.lastRenderedState=s}return[s,r]}function kh(){}function Sh(e,t){var n=me,r=ht(),o=t(),s=!bt(r.memoizedState,o);if(s&&(r.memoizedState=o,Ke=!0),r=r.queue,xc(Nh.bind(null,n,r,e),[e]),r.getSnapshot!==t||s||je!==null&&je.memoizedState.tag&1){if(n.flags|=2048,as(9,bh.bind(null,n,r,o,t),void 0,null),Me===null)throw Error(O(349));lr&30||Eh(n,t,o)}return o}function Eh(e,t,n){e.flags|=16384,e={getSnapshot:t,value:n},t=me.updateQueue,t===null?(t={lastEffect:null,stores:null},me.updateQueue=t,t.stores=[e]):(n=t.stores,n===null?t.stores=[e]:n.push(e))}function bh(e,t,n,r){t.value=n,t.getSnapshot=r,Th(t)&&Rh(e)}function Nh(e,t,n){return n(function(){Th(t)&&Rh(e)})}function Th(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!bt(e,n)}catch{return!0}}function Rh(e){var t=an(e,1);t!==null&&St(t,e,1,-1)}function tf(e){var t=At();return typeof e=="function"&&(e=e()),t.memoizedState=t.baseState=e,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:is,lastRenderedState:e},t.queue=e,e=e.dispatch=ww.bind(null,me,e),[t.memoizedState,e]}function as(e,t,n,r){return e={tag:e,create:t,destroy:n,deps:r,next:null},t=me.updateQueue,t===null?(t={lastEffect:null,stores:null},me.updateQueue=t,t.lastEffect=e.next=e):(n=t.lastEffect,n===null?t.lastEffect=e.next=e:(r=n.next,n.next=e,e.next=r,t.lastEffect=e)),e}function jh(){return ht().memoizedState}function mi(e,t,n,r){var o=At();me.flags|=e,o.memoizedState=as(1|t,n,void 0,r===void 0?null:r)}function ga(e,t,n,r){var o=ht();r=r===void 0?null:r;var s=void 0;if(Te!==null){var i=Te.memoizedState;if(s=i.destroy,r!==null&&vc(r,i.deps)){o.memoizedState=as(t,n,s,r);return}}me.flags|=e,o.memoizedState=as(1|t,n,s,r)}function nf(e,t){return mi(8390656,8,e,t)}function xc(e,t){return ga(2048,8,e,t)}function Mh(e,t){return ga(4,2,e,t)}function Ph(e,t){return ga(4,4,e,t)}function Ah(e,t){if(typeof t=="function")return e=e(),t(e),function(){t(null)};if(t!=null)return e=e(),t.current=e,function(){t.current=null}}function Oh(e,t,n){return n=n!=null?n.concat([e]):null,ga(4,4,Ah.bind(null,t,e),n)}function wc(){}function Ih(e,t){var n=ht();t=t===void 0?null:t;var r=n.memoizedState;return r!==null&&t!==null&&vc(t,r[1])?r[0]:(n.memoizedState=[e,t],e)}function Dh(e,t){var n=ht();t=t===void 0?null:t;var r=n.memoizedState;return r!==null&&t!==null&&vc(t,r[1])?r[0]:(e=e(),n.memoizedState=[e,t],e)}function Lh(e,t,n){return lr&21?(bt(n,t)||(n=Vp(),me.lanes|=n,ur|=n,e.baseState=!0),t):(e.baseState&&(e.baseState=!1,Ke=!0),e.memoizedState=n)}function yw(e,t){var n=ie;ie=n!==0&&4>n?n:4,e(!0);var r=cl.transition;cl.transition={};try{e(!1),t()}finally{ie=n,cl.transition=r}}function $h(){return ht().memoizedState}function xw(e,t,n){var r=An(e);if(n={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null},zh(e))Fh(t,n);else if(n=wh(e,t,n,r),n!==null){var o=Ve();St(n,e,r,o),Bh(n,t,r)}}function ww(e,t,n){var r=An(e),o={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null};if(zh(e))Fh(t,o);else{var s=e.alternate;if(e.lanes===0&&(s===null||s.lanes===0)&&(s=t.lastRenderedReducer,s!==null))try{var i=t.lastRenderedState,a=s(i,n);if(o.hasEagerState=!0,o.eagerState=a,bt(a,i)){var l=t.interleaved;l===null?(o.next=o,dc(t)):(o.next=l.next,l.next=o),t.interleaved=o;return}}catch{}finally{}n=wh(e,t,o,r),n!==null&&(o=Ve(),St(n,e,r,o),Bh(n,t,r))}}function zh(e){var t=e.alternate;return e===me||t!==null&&t===me}function Fh(e,t){Fo=Vi=!0;var n=e.pending;n===null?t.next=t:(t.next=n.next,n.next=t),e.pending=t}function Bh(e,t,n){if(n&4194240){var r=t.lanes;r&=e.pendingLanes,n|=r,t.lanes=n,Xu(e,n)}}var Ui={readContext:pt,useCallback:De,useContext:De,useEffect:De,useImperativeHandle:De,useInsertionEffect:De,useLayoutEffect:De,useMemo:De,useReducer:De,useRef:De,useState:De,useDebugValue:De,useDeferredValue:De,useTransition:De,useMutableSource:De,useSyncExternalStore:De,useId:De,unstable_isNewReconciler:!1},_w={readContext:pt,useCallback:function(e,t){return At().memoizedState=[e,t===void 0?null:t],e},useContext:pt,useEffect:nf,useImperativeHandle:function(e,t,n){return n=n!=null?n.concat([e]):null,mi(4194308,4,Ah.bind(null,t,e),n)},useLayoutEffect:function(e,t){return mi(4194308,4,e,t)},useInsertionEffect:function(e,t){return mi(4,2,e,t)},useMemo:function(e,t){var n=At();return t=t===void 0?null:t,e=e(),n.memoizedState=[e,t],e},useReducer:function(e,t,n){var r=At();return t=n!==void 0?n(t):t,r.memoizedState=r.baseState=t,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:t},r.queue=e,e=e.dispatch=xw.bind(null,me,e),[r.memoizedState,e]},useRef:function(e){var t=At();return e={current:e},t.memoizedState=e},useState:tf,useDebugValue:wc,useDeferredValue:function(e){return At().memoizedState=e},useTransition:function(){var e=tf(!1),t=e[0];return e=yw.bind(null,e[1]),At().memoizedState=e,[t,e]},useMutableSource:function(){},useSyncExternalStore:function(e,t,n){var r=me,o=At();if(fe){if(n===void 0)throw Error(O(407));n=n()}else{if(n=t(),Me===null)throw Error(O(349));lr&30||Eh(r,t,n)}o.memoizedState=n;var s={value:n,getSnapshot:t};return o.queue=s,nf(Nh.bind(null,r,s,e),[e]),r.flags|=2048,as(9,bh.bind(null,r,s,n,t),void 0,null),n},useId:function(){var e=At(),t=Me.identifierPrefix;if(fe){var n=qt,r=Jt;n=(r&~(1<<32-kt(r)-1)).toString(32)+n,t=":"+t+"R"+n,n=ss++,0<n&&(t+="H"+n.toString(32)),t+=":"}else n=gw++,t=":"+t+"r"+n.toString(32)+":";return e.memoizedState=t},unstable_isNewReconciler:!1},Cw={readContext:pt,useCallback:Ih,useContext:pt,useEffect:xc,useImperativeHandle:Oh,useInsertionEffect:Mh,useLayoutEffect:Ph,useMemo:Dh,useReducer:dl,useRef:jh,useState:function(){return dl(is)},useDebugValue:wc,useDeferredValue:function(e){var t=ht();return Lh(t,Te.memoizedState,e)},useTransition:function(){var e=dl(is)[0],t=ht().memoizedState;return[e,t]},useMutableSource:kh,useSyncExternalStore:Sh,useId:$h,unstable_isNewReconciler:!1},kw={readContext:pt,useCallback:Ih,useContext:pt,useEffect:xc,useImperativeHandle:Oh,useInsertionEffect:Mh,useLayoutEffect:Ph,useMemo:Dh,useReducer:fl,useRef:jh,useState:function(){return fl(is)},useDebugValue:wc,useDeferredValue:function(e){var t=ht();return Te===null?t.memoizedState=e:Lh(t,Te.memoizedState,e)},useTransition:function(){var e=fl(is)[0],t=ht().memoizedState;return[e,t]},useMutableSource:kh,useSyncExternalStore:Sh,useId:$h,unstable_isNewReconciler:!1};function xt(e,t){if(e&&e.defaultProps){t=ve({},t),e=e.defaultProps;for(var n in e)t[n]===void 0&&(t[n]=e[n]);return t}return t}function au(e,t,n,r){t=e.memoizedState,n=n(r,t),n=n==null?t:ve({},t,n),e.memoizedState=n,e.lanes===0&&(e.updateQueue.baseState=n)}var ya={isMounted:function(e){return(e=e._reactInternals)?gr(e)===e:!1},enqueueSetState:function(e,t,n){e=e._reactInternals;var r=Ve(),o=An(e),s=en(r,o);s.payload=t,n!=null&&(s.callback=n),t=Mn(e,s,o),t!==null&&(St(t,e,o,r),pi(t,e,o))},enqueueReplaceState:function(e,t,n){e=e._reactInternals;var r=Ve(),o=An(e),s=en(r,o);s.tag=1,s.payload=t,n!=null&&(s.callback=n),t=Mn(e,s,o),t!==null&&(St(t,e,o,r),pi(t,e,o))},enqueueForceUpdate:function(e,t){e=e._reactInternals;var n=Ve(),r=An(e),o=en(n,r);o.tag=2,t!=null&&(o.callback=t),t=Mn(e,o,r),t!==null&&(St(t,e,r,n),pi(t,e,r))}};function rf(e,t,n,r,o,s,i){return e=e.stateNode,typeof e.shouldComponentUpdate=="function"?e.shouldComponentUpdate(r,s,i):t.prototype&&t.prototype.isPureReactComponent?!qo(n,r)||!qo(o,s):!0}function Vh(e,t,n){var r=!1,o=Dn,s=t.contextType;return typeof s=="object"&&s!==null?s=pt(s):(o=Xe(t)?ir:ze.current,r=t.contextTypes,s=(r=r!=null)?Xr(e,o):Dn),t=new t(n,s),e.memoizedState=t.state!==null&&t.state!==void 0?t.state:null,t.updater=ya,e.stateNode=t,t._reactInternals=e,r&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=o,e.__reactInternalMemoizedMaskedChildContext=s),t}function of(e,t,n,r){e=t.state,typeof t.componentWillReceiveProps=="function"&&t.componentWillReceiveProps(n,r),typeof t.UNSAFE_componentWillReceiveProps=="function"&&t.UNSAFE_componentWillReceiveProps(n,r),t.state!==e&&ya.enqueueReplaceState(t,t.state,null)}function lu(e,t,n,r){var o=e.stateNode;o.props=n,o.state=e.memoizedState,o.refs={},fc(e);var s=t.contextType;typeof s=="object"&&s!==null?o.context=pt(s):(s=Xe(t)?ir:ze.current,o.context=Xr(e,s)),o.state=e.memoizedState,s=t.getDerivedStateFromProps,typeof s=="function"&&(au(e,t,s,n),o.state=e.memoizedState),typeof t.getDerivedStateFromProps=="function"||typeof o.getSnapshotBeforeUpdate=="function"||typeof o.UNSAFE_componentWillMount!="function"&&typeof o.componentWillMount!="function"||(t=o.state,typeof o.componentWillMount=="function"&&o.componentWillMount(),typeof o.UNSAFE_componentWillMount=="function"&&o.UNSAFE_componentWillMount(),t!==o.state&&ya.enqueueReplaceState(o,o.state,null),Fi(e,n,o,r),o.state=e.memoizedState),typeof o.componentDidMount=="function"&&(e.flags|=4194308)}function to(e,t){try{var n="",r=t;do n+=Xy(r),r=r.return;while(r);var o=n}catch(s){o=`
Error generating stack: `+s.message+`
`+s.stack}return{value:e,source:t,stack:o,digest:null}}function pl(e,t,n){return{value:e,source:null,stack:n??null,digest:t??null}}function uu(e,t){try{console.error(t.value)}catch(n){setTimeout(function(){throw n})}}var Sw=typeof WeakMap=="function"?WeakMap:Map;function Uh(e,t,n){n=en(-1,n),n.tag=3,n.payload={element:null};var r=t.value;return n.callback=function(){Hi||(Hi=!0,xu=r),uu(e,t)},n}function Wh(e,t,n){n=en(-1,n),n.tag=3;var r=e.type.getDerivedStateFromError;if(typeof r=="function"){var o=t.value;n.payload=function(){return r(o)},n.callback=function(){uu(e,t)}}var s=e.stateNode;return s!==null&&typeof s.componentDidCatch=="function"&&(n.callback=function(){uu(e,t),typeof r!="function"&&(Pn===null?Pn=new Set([this]):Pn.add(this));var i=t.stack;this.componentDidCatch(t.value,{componentStack:i!==null?i:""})}),n}function sf(e,t,n){var r=e.pingCache;if(r===null){r=e.pingCache=new Sw;var o=new Set;r.set(t,o)}else o=r.get(t),o===void 0&&(o=new Set,r.set(t,o));o.has(n)||(o.add(n),e=$w.bind(null,e,t,n),t.then(e,e))}function af(e){do{var t;if((t=e.tag===13)&&(t=e.memoizedState,t=t!==null?t.dehydrated!==null:!0),t)return e;e=e.return}while(e!==null);return null}function lf(e,t,n,r,o){return e.mode&1?(e.flags|=65536,e.lanes=o,e):(e===t?e.flags|=65536:(e.flags|=128,n.flags|=131072,n.flags&=-52805,n.tag===1&&(n.alternate===null?n.tag=17:(t=en(-1,1),t.tag=2,Mn(n,t,1))),n.lanes|=1),e)}var Ew=fn.ReactCurrentOwner,Ke=!1;function Be(e,t,n,r){t.child=e===null?xh(t,null,n,r):qr(t,e.child,n,r)}function uf(e,t,n,r,o){n=n.render;var s=t.ref;return Ur(t,o),r=gc(e,t,n,r,s,o),n=yc(),e!==null&&!Ke?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~o,ln(e,t,o)):(fe&&n&&sc(t),t.flags|=1,Be(e,t,r,o),t.child)}function cf(e,t,n,r,o){if(e===null){var s=n.type;return typeof s=="function"&&!Tc(s)&&s.defaultProps===void 0&&n.compare===null&&n.defaultProps===void 0?(t.tag=15,t.type=s,Hh(e,t,s,r,o)):(e=xi(n.type,null,r,t,t.mode,o),e.ref=t.ref,e.return=t,t.child=e)}if(s=e.child,!(e.lanes&o)){var i=s.memoizedProps;if(n=n.compare,n=n!==null?n:qo,n(i,r)&&e.ref===t.ref)return ln(e,t,o)}return t.flags|=1,e=On(s,r),e.ref=t.ref,e.return=t,t.child=e}function Hh(e,t,n,r,o){if(e!==null){var s=e.memoizedProps;if(qo(s,r)&&e.ref===t.ref)if(Ke=!1,t.pendingProps=r=s,(e.lanes&o)!==0)e.flags&131072&&(Ke=!0);else return t.lanes=e.lanes,ln(e,t,o)}return cu(e,t,n,r,o)}function Zh(e,t,n){var r=t.pendingProps,o=r.children,s=e!==null?e.memoizedState:null;if(r.mode==="hidden")if(!(t.mode&1))t.memoizedState={baseLanes:0,cachePool:null,transitions:null},ae(Dr,qe),qe|=n;else{if(!(n&1073741824))return e=s!==null?s.baseLanes|n:n,t.lanes=t.childLanes=1073741824,t.memoizedState={baseLanes:e,cachePool:null,transitions:null},t.updateQueue=null,ae(Dr,qe),qe|=e,null;t.memoizedState={baseLanes:0,cachePool:null,transitions:null},r=s!==null?s.baseLanes:n,ae(Dr,qe),qe|=r}else s!==null?(r=s.baseLanes|n,t.memoizedState=null):r=n,ae(Dr,qe),qe|=r;return Be(e,t,o,n),t.child}function Gh(e,t){var n=t.ref;(e===null&&n!==null||e!==null&&e.ref!==n)&&(t.flags|=512,t.flags|=2097152)}function cu(e,t,n,r,o){var s=Xe(n)?ir:ze.current;return s=Xr(t,s),Ur(t,o),n=gc(e,t,n,r,s,o),r=yc(),e!==null&&!Ke?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~o,ln(e,t,o)):(fe&&r&&sc(t),t.flags|=1,Be(e,t,n,o),t.child)}function df(e,t,n,r,o){if(Xe(n)){var s=!0;Ii(t)}else s=!1;if(Ur(t,o),t.stateNode===null)vi(e,t),Vh(t,n,r),lu(t,n,r,o),r=!0;else if(e===null){var i=t.stateNode,a=t.memoizedProps;i.props=a;var l=i.context,u=n.contextType;typeof u=="object"&&u!==null?u=pt(u):(u=Xe(n)?ir:ze.current,u=Xr(t,u));var d=n.getDerivedStateFromProps,f=typeof d=="function"||typeof i.getSnapshotBeforeUpdate=="function";f||typeof i.UNSAFE_componentWillReceiveProps!="function"&&typeof i.componentWillReceiveProps!="function"||(a!==r||l!==u)&&of(t,i,r,u),Cn=!1;var p=t.memoizedState;i.state=p,Fi(t,r,i,o),l=t.memoizedState,a!==r||p!==l||Qe.current||Cn?(typeof d=="function"&&(au(t,n,d,r),l=t.memoizedState),(a=Cn||rf(t,n,a,r,p,l,u))?(f||typeof i.UNSAFE_componentWillMount!="function"&&typeof i.componentWillMount!="function"||(typeof i.componentWillMount=="function"&&i.componentWillMount(),typeof i.UNSAFE_componentWillMount=="function"&&i.UNSAFE_componentWillMount()),typeof i.componentDidMount=="function"&&(t.flags|=4194308)):(typeof i.componentDidMount=="function"&&(t.flags|=4194308),t.memoizedProps=r,t.memoizedState=l),i.props=r,i.state=l,i.context=u,r=a):(typeof i.componentDidMount=="function"&&(t.flags|=4194308),r=!1)}else{i=t.stateNode,_h(e,t),a=t.memoizedProps,u=t.type===t.elementType?a:xt(t.type,a),i.props=u,f=t.pendingProps,p=i.context,l=n.contextType,typeof l=="object"&&l!==null?l=pt(l):(l=Xe(n)?ir:ze.current,l=Xr(t,l));var y=n.getDerivedStateFromProps;(d=typeof y=="function"||typeof i.getSnapshotBeforeUpdate=="function")||typeof i.UNSAFE_componentWillReceiveProps!="function"&&typeof i.componentWillReceiveProps!="function"||(a!==f||p!==l)&&of(t,i,r,l),Cn=!1,p=t.memoizedState,i.state=p,Fi(t,r,i,o);var w=t.memoizedState;a!==f||p!==w||Qe.current||Cn?(typeof y=="function"&&(au(t,n,y,r),w=t.memoizedState),(u=Cn||rf(t,n,u,r,p,w,l)||!1)?(d||typeof i.UNSAFE_componentWillUpdate!="function"&&typeof i.componentWillUpdate!="function"||(typeof i.componentWillUpdate=="function"&&i.componentWillUpdate(r,w,l),typeof i.UNSAFE_componentWillUpdate=="function"&&i.UNSAFE_componentWillUpdate(r,w,l)),typeof i.componentDidUpdate=="function"&&(t.flags|=4),typeof i.getSnapshotBeforeUpdate=="function"&&(t.flags|=1024)):(typeof i.componentDidUpdate!="function"||a===e.memoizedProps&&p===e.memoizedState||(t.flags|=4),typeof i.getSnapshotBeforeUpdate!="function"||a===e.memoizedProps&&p===e.memoizedState||(t.flags|=1024),t.memoizedProps=r,t.memoizedState=w),i.props=r,i.state=w,i.context=l,r=u):(typeof i.componentDidUpdate!="function"||a===e.memoizedProps&&p===e.memoizedState||(t.flags|=4),typeof i.getSnapshotBeforeUpdate!="function"||a===e.memoizedProps&&p===e.memoizedState||(t.flags|=1024),r=!1)}return du(e,t,n,r,s,o)}function du(e,t,n,r,o,s){Gh(e,t);var i=(t.flags&128)!==0;if(!r&&!i)return o&&Kd(t,n,!1),ln(e,t,s);r=t.stateNode,Ew.current=t;var a=i&&typeof n.getDerivedStateFromError!="function"?null:r.render();return t.flags|=1,e!==null&&i?(t.child=qr(t,e.child,null,s),t.child=qr(t,null,a,s)):Be(e,t,a,s),t.memoizedState=r.state,o&&Kd(t,n,!0),t.child}function Yh(e){var t=e.stateNode;t.pendingContext?Yd(e,t.pendingContext,t.pendingContext!==t.context):t.context&&Yd(e,t.context,!1),pc(e,t.containerInfo)}function ff(e,t,n,r,o){return Jr(),ac(o),t.flags|=256,Be(e,t,n,r),t.child}var fu={dehydrated:null,treeContext:null,retryLane:0};function pu(e){return{baseLanes:e,cachePool:null,transitions:null}}function Kh(e,t,n){var r=t.pendingProps,o=he.current,s=!1,i=(t.flags&128)!==0,a;if((a=i)||(a=e!==null&&e.memoizedState===null?!1:(o&2)!==0),a?(s=!0,t.flags&=-129):(e===null||e.memoizedState!==null)&&(o|=1),ae(he,o&1),e===null)return su(t),e=t.memoizedState,e!==null&&(e=e.dehydrated,e!==null)?(t.mode&1?e.data==="$!"?t.lanes=8:t.lanes=1073741824:t.lanes=1,null):(i=r.children,e=r.fallback,s?(r=t.mode,s=t.child,i={mode:"hidden",children:i},!(r&1)&&s!==null?(s.childLanes=0,s.pendingProps=i):s=_a(i,r,0,null),e=or(e,r,n,null),s.return=t,e.return=t,s.sibling=e,t.child=s,t.child.memoizedState=pu(n),t.memoizedState=fu,e):_c(t,i));if(o=e.memoizedState,o!==null&&(a=o.dehydrated,a!==null))return bw(e,t,i,r,a,o,n);if(s){s=r.fallback,i=t.mode,o=e.child,a=o.sibling;var l={mode:"hidden",children:r.children};return!(i&1)&&t.child!==o?(r=t.child,r.childLanes=0,r.pendingProps=l,t.deletions=null):(r=On(o,l),r.subtreeFlags=o.subtreeFlags&14680064),a!==null?s=On(a,s):(s=or(s,i,n,null),s.flags|=2),s.return=t,r.return=t,r.sibling=s,t.child=r,r=s,s=t.child,i=e.child.memoizedState,i=i===null?pu(n):{baseLanes:i.baseLanes|n,cachePool:null,transitions:i.transitions},s.memoizedState=i,s.childLanes=e.childLanes&~n,t.memoizedState=fu,r}return s=e.child,e=s.sibling,r=On(s,{mode:"visible",children:r.children}),!(t.mode&1)&&(r.lanes=n),r.return=t,r.sibling=null,e!==null&&(n=t.deletions,n===null?(t.deletions=[e],t.flags|=16):n.push(e)),t.child=r,t.memoizedState=null,r}function _c(e,t){return t=_a({mode:"visible",children:t},e.mode,0,null),t.return=e,e.child=t}function Qs(e,t,n,r){return r!==null&&ac(r),qr(t,e.child,null,n),e=_c(t,t.pendingProps.children),e.flags|=2,t.memoizedState=null,e}function bw(e,t,n,r,o,s,i){if(n)return t.flags&256?(t.flags&=-257,r=pl(Error(O(422))),Qs(e,t,i,r)):t.memoizedState!==null?(t.child=e.child,t.flags|=128,null):(s=r.fallback,o=t.mode,r=_a({mode:"visible",children:r.children},o,0,null),s=or(s,o,i,null),s.flags|=2,r.return=t,s.return=t,r.sibling=s,t.child=r,t.mode&1&&qr(t,e.child,null,i),t.child.memoizedState=pu(i),t.memoizedState=fu,s);if(!(t.mode&1))return Qs(e,t,i,null);if(o.data==="$!"){if(r=o.nextSibling&&o.nextSibling.dataset,r)var a=r.dgst;return r=a,s=Error(O(419)),r=pl(s,r,void 0),Qs(e,t,i,r)}if(a=(i&e.childLanes)!==0,Ke||a){if(r=Me,r!==null){switch(i&-i){case 4:o=2;break;case 16:o=8;break;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:o=32;break;case 536870912:o=268435456;break;default:o=0}o=o&(r.suspendedLanes|i)?0:o,o!==0&&o!==s.retryLane&&(s.retryLane=o,an(e,o),St(r,e,o,-1))}return Nc(),r=pl(Error(O(421))),Qs(e,t,i,r)}return o.data==="$?"?(t.flags|=128,t.child=e.child,t=zw.bind(null,e),o._reactRetry=t,null):(e=s.treeContext,tt=jn(o.nextSibling),nt=t,fe=!0,_t=null,e!==null&&(lt[ut++]=Jt,lt[ut++]=qt,lt[ut++]=ar,Jt=e.id,qt=e.overflow,ar=t),t=_c(t,r.children),t.flags|=4096,t)}function pf(e,t,n){e.lanes|=t;var r=e.alternate;r!==null&&(r.lanes|=t),iu(e.return,t,n)}function hl(e,t,n,r,o){var s=e.memoizedState;s===null?e.memoizedState={isBackwards:t,rendering:null,renderingStartTime:0,last:r,tail:n,tailMode:o}:(s.isBackwards=t,s.rendering=null,s.renderingStartTime=0,s.last=r,s.tail=n,s.tailMode=o)}function Qh(e,t,n){var r=t.pendingProps,o=r.revealOrder,s=r.tail;if(Be(e,t,r.children,n),r=he.current,r&2)r=r&1|2,t.flags|=128;else{if(e!==null&&e.flags&128)e:for(e=t.child;e!==null;){if(e.tag===13)e.memoizedState!==null&&pf(e,n,t);else if(e.tag===19)pf(e,n,t);else if(e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break e;for(;e.sibling===null;){if(e.return===null||e.return===t)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}r&=1}if(ae(he,r),!(t.mode&1))t.memoizedState=null;else switch(o){case"forwards":for(n=t.child,o=null;n!==null;)e=n.alternate,e!==null&&Bi(e)===null&&(o=n),n=n.sibling;n=o,n===null?(o=t.child,t.child=null):(o=n.sibling,n.sibling=null),hl(t,!1,o,n,s);break;case"backwards":for(n=null,o=t.child,t.child=null;o!==null;){if(e=o.alternate,e!==null&&Bi(e)===null){t.child=o;break}e=o.sibling,o.sibling=n,n=o,o=e}hl(t,!0,n,null,s);break;case"together":hl(t,!1,null,null,void 0);break;default:t.memoizedState=null}return t.child}function vi(e,t){!(t.mode&1)&&e!==null&&(e.alternate=null,t.alternate=null,t.flags|=2)}function ln(e,t,n){if(e!==null&&(t.dependencies=e.dependencies),ur|=t.lanes,!(n&t.childLanes))return null;if(e!==null&&t.child!==e.child)throw Error(O(153));if(t.child!==null){for(e=t.child,n=On(e,e.pendingProps),t.child=n,n.return=t;e.sibling!==null;)e=e.sibling,n=n.sibling=On(e,e.pendingProps),n.return=t;n.sibling=null}return t.child}function Nw(e,t,n){switch(t.tag){case 3:Yh(t),Jr();break;case 5:Ch(t);break;case 1:Xe(t.type)&&Ii(t);break;case 4:pc(t,t.stateNode.containerInfo);break;case 10:var r=t.type._context,o=t.memoizedProps.value;ae($i,r._currentValue),r._currentValue=o;break;case 13:if(r=t.memoizedState,r!==null)return r.dehydrated!==null?(ae(he,he.current&1),t.flags|=128,null):n&t.child.childLanes?Kh(e,t,n):(ae(he,he.current&1),e=ln(e,t,n),e!==null?e.sibling:null);ae(he,he.current&1);break;case 19:if(r=(n&t.childLanes)!==0,e.flags&128){if(r)return Qh(e,t,n);t.flags|=128}if(o=t.memoizedState,o!==null&&(o.rendering=null,o.tail=null,o.lastEffect=null),ae(he,he.current),r)break;return null;case 22:case 23:return t.lanes=0,Zh(e,t,n)}return ln(e,t,n)}var Xh,hu,Jh,qh;Xh=function(e,t){for(var n=t.child;n!==null;){if(n.tag===5||n.tag===6)e.appendChild(n.stateNode);else if(n.tag!==4&&n.child!==null){n.child.return=n,n=n.child;continue}if(n===t)break;for(;n.sibling===null;){if(n.return===null||n.return===t)return;n=n.return}n.sibling.return=n.return,n=n.sibling}};hu=function(){};Jh=function(e,t,n,r){var o=e.memoizedProps;if(o!==r){e=t.stateNode,nr(Lt.current);var s=null;switch(n){case"input":o=Dl(e,o),r=Dl(e,r),s=[];break;case"select":o=ve({},o,{value:void 0}),r=ve({},r,{value:void 0}),s=[];break;case"textarea":o=zl(e,o),r=zl(e,r),s=[];break;default:typeof o.onClick!="function"&&typeof r.onClick=="function"&&(e.onclick=Ai)}Bl(n,r);var i;n=null;for(u in o)if(!r.hasOwnProperty(u)&&o.hasOwnProperty(u)&&o[u]!=null)if(u==="style"){var a=o[u];for(i in a)a.hasOwnProperty(i)&&(n||(n={}),n[i]="")}else u!=="dangerouslySetInnerHTML"&&u!=="children"&&u!=="suppressContentEditableWarning"&&u!=="suppressHydrationWarning"&&u!=="autoFocus"&&(Zo.hasOwnProperty(u)?s||(s=[]):(s=s||[]).push(u,null));for(u in r){var l=r[u];if(a=o!=null?o[u]:void 0,r.hasOwnProperty(u)&&l!==a&&(l!=null||a!=null))if(u==="style")if(a){for(i in a)!a.hasOwnProperty(i)||l&&l.hasOwnProperty(i)||(n||(n={}),n[i]="");for(i in l)l.hasOwnProperty(i)&&a[i]!==l[i]&&(n||(n={}),n[i]=l[i])}else n||(s||(s=[]),s.push(u,n)),n=l;else u==="dangerouslySetInnerHTML"?(l=l?l.__html:void 0,a=a?a.__html:void 0,l!=null&&a!==l&&(s=s||[]).push(u,l)):u==="children"?typeof l!="string"&&typeof l!="number"||(s=s||[]).push(u,""+l):u!=="suppressContentEditableWarning"&&u!=="suppressHydrationWarning"&&(Zo.hasOwnProperty(u)?(l!=null&&u==="onScroll"&&ue("scroll",e),s||a===l||(s=[])):(s=s||[]).push(u,l))}n&&(s=s||[]).push("style",n);var u=s;(t.updateQueue=u)&&(t.flags|=4)}};qh=function(e,t,n,r){n!==r&&(t.flags|=4)};function So(e,t){if(!fe)switch(e.tailMode){case"hidden":t=e.tail;for(var n=null;t!==null;)t.alternate!==null&&(n=t),t=t.sibling;n===null?e.tail=null:n.sibling=null;break;case"collapsed":n=e.tail;for(var r=null;n!==null;)n.alternate!==null&&(r=n),n=n.sibling;r===null?t||e.tail===null?e.tail=null:e.tail.sibling=null:r.sibling=null}}function Le(e){var t=e.alternate!==null&&e.alternate.child===e.child,n=0,r=0;if(t)for(var o=e.child;o!==null;)n|=o.lanes|o.childLanes,r|=o.subtreeFlags&14680064,r|=o.flags&14680064,o.return=e,o=o.sibling;else for(o=e.child;o!==null;)n|=o.lanes|o.childLanes,r|=o.subtreeFlags,r|=o.flags,o.return=e,o=o.sibling;return e.subtreeFlags|=r,e.childLanes=n,t}function Tw(e,t,n){var r=t.pendingProps;switch(ic(t),t.tag){case 2:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return Le(t),null;case 1:return Xe(t.type)&&Oi(),Le(t),null;case 3:return r=t.stateNode,eo(),ce(Qe),ce(ze),mc(),r.pendingContext&&(r.context=r.pendingContext,r.pendingContext=null),(e===null||e.child===null)&&(Ys(t)?t.flags|=4:e===null||e.memoizedState.isDehydrated&&!(t.flags&256)||(t.flags|=1024,_t!==null&&(Cu(_t),_t=null))),hu(e,t),Le(t),null;case 5:hc(t);var o=nr(os.current);if(n=t.type,e!==null&&t.stateNode!=null)Jh(e,t,n,r,o),e.ref!==t.ref&&(t.flags|=512,t.flags|=2097152);else{if(!r){if(t.stateNode===null)throw Error(O(166));return Le(t),null}if(e=nr(Lt.current),Ys(t)){r=t.stateNode,n=t.type;var s=t.memoizedProps;switch(r[Ot]=t,r[ns]=s,e=(t.mode&1)!==0,n){case"dialog":ue("cancel",r),ue("close",r);break;case"iframe":case"object":case"embed":ue("load",r);break;case"video":case"audio":for(o=0;o<Mo.length;o++)ue(Mo[o],r);break;case"source":ue("error",r);break;case"img":case"image":case"link":ue("error",r),ue("load",r);break;case"details":ue("toggle",r);break;case"input":_d(r,s),ue("invalid",r);break;case"select":r._wrapperState={wasMultiple:!!s.multiple},ue("invalid",r);break;case"textarea":kd(r,s),ue("invalid",r)}Bl(n,s),o=null;for(var i in s)if(s.hasOwnProperty(i)){var a=s[i];i==="children"?typeof a=="string"?r.textContent!==a&&(s.suppressHydrationWarning!==!0&&Gs(r.textContent,a,e),o=["children",a]):typeof a=="number"&&r.textContent!==""+a&&(s.suppressHydrationWarning!==!0&&Gs(r.textContent,a,e),o=["children",""+a]):Zo.hasOwnProperty(i)&&a!=null&&i==="onScroll"&&ue("scroll",r)}switch(n){case"input":zs(r),Cd(r,s,!0);break;case"textarea":zs(r),Sd(r);break;case"select":case"option":break;default:typeof s.onClick=="function"&&(r.onclick=Ai)}r=o,t.updateQueue=r,r!==null&&(t.flags|=4)}else{i=o.nodeType===9?o:o.ownerDocument,e==="http://www.w3.org/1999/xhtml"&&(e=Np(n)),e==="http://www.w3.org/1999/xhtml"?n==="script"?(e=i.createElement("div"),e.innerHTML="<script><\/script>",e=e.removeChild(e.firstChild)):typeof r.is=="string"?e=i.createElement(n,{is:r.is}):(e=i.createElement(n),n==="select"&&(i=e,r.multiple?i.multiple=!0:r.size&&(i.size=r.size))):e=i.createElementNS(e,n),e[Ot]=t,e[ns]=r,Xh(e,t,!1,!1),t.stateNode=e;e:{switch(i=Vl(n,r),n){case"dialog":ue("cancel",e),ue("close",e),o=r;break;case"iframe":case"object":case"embed":ue("load",e),o=r;break;case"video":case"audio":for(o=0;o<Mo.length;o++)ue(Mo[o],e);o=r;break;case"source":ue("error",e),o=r;break;case"img":case"image":case"link":ue("error",e),ue("load",e),o=r;break;case"details":ue("toggle",e),o=r;break;case"input":_d(e,r),o=Dl(e,r),ue("invalid",e);break;case"option":o=r;break;case"select":e._wrapperState={wasMultiple:!!r.multiple},o=ve({},r,{value:void 0}),ue("invalid",e);break;case"textarea":kd(e,r),o=zl(e,r),ue("invalid",e);break;default:o=r}Bl(n,o),a=o;for(s in a)if(a.hasOwnProperty(s)){var l=a[s];s==="style"?jp(e,l):s==="dangerouslySetInnerHTML"?(l=l?l.__html:void 0,l!=null&&Tp(e,l)):s==="children"?typeof l=="string"?(n!=="textarea"||l!=="")&&Go(e,l):typeof l=="number"&&Go(e,""+l):s!=="suppressContentEditableWarning"&&s!=="suppressHydrationWarning"&&s!=="autoFocus"&&(Zo.hasOwnProperty(s)?l!=null&&s==="onScroll"&&ue("scroll",e):l!=null&&Hu(e,s,l,i))}switch(n){case"input":zs(e),Cd(e,r,!1);break;case"textarea":zs(e),Sd(e);break;case"option":r.value!=null&&e.setAttribute("value",""+In(r.value));break;case"select":e.multiple=!!r.multiple,s=r.value,s!=null?zr(e,!!r.multiple,s,!1):r.defaultValue!=null&&zr(e,!!r.multiple,r.defaultValue,!0);break;default:typeof o.onClick=="function"&&(e.onclick=Ai)}switch(n){case"button":case"input":case"select":case"textarea":r=!!r.autoFocus;break e;case"img":r=!0;break e;default:r=!1}}r&&(t.flags|=4)}t.ref!==null&&(t.flags|=512,t.flags|=2097152)}return Le(t),null;case 6:if(e&&t.stateNode!=null)qh(e,t,e.memoizedProps,r);else{if(typeof r!="string"&&t.stateNode===null)throw Error(O(166));if(n=nr(os.current),nr(Lt.current),Ys(t)){if(r=t.stateNode,n=t.memoizedProps,r[Ot]=t,(s=r.nodeValue!==n)&&(e=nt,e!==null))switch(e.tag){case 3:Gs(r.nodeValue,n,(e.mode&1)!==0);break;case 5:e.memoizedProps.suppressHydrationWarning!==!0&&Gs(r.nodeValue,n,(e.mode&1)!==0)}s&&(t.flags|=4)}else r=(n.nodeType===9?n:n.ownerDocument).createTextNode(r),r[Ot]=t,t.stateNode=r}return Le(t),null;case 13:if(ce(he),r=t.memoizedState,e===null||e.memoizedState!==null&&e.memoizedState.dehydrated!==null){if(fe&&tt!==null&&t.mode&1&&!(t.flags&128))gh(),Jr(),t.flags|=98560,s=!1;else if(s=Ys(t),r!==null&&r.dehydrated!==null){if(e===null){if(!s)throw Error(O(318));if(s=t.memoizedState,s=s!==null?s.dehydrated:null,!s)throw Error(O(317));s[Ot]=t}else Jr(),!(t.flags&128)&&(t.memoizedState=null),t.flags|=4;Le(t),s=!1}else _t!==null&&(Cu(_t),_t=null),s=!0;if(!s)return t.flags&65536?t:null}return t.flags&128?(t.lanes=n,t):(r=r!==null,r!==(e!==null&&e.memoizedState!==null)&&r&&(t.child.flags|=8192,t.mode&1&&(e===null||he.current&1?Re===0&&(Re=3):Nc())),t.updateQueue!==null&&(t.flags|=4),Le(t),null);case 4:return eo(),hu(e,t),e===null&&es(t.stateNode.containerInfo),Le(t),null;case 10:return cc(t.type._context),Le(t),null;case 17:return Xe(t.type)&&Oi(),Le(t),null;case 19:if(ce(he),s=t.memoizedState,s===null)return Le(t),null;if(r=(t.flags&128)!==0,i=s.rendering,i===null)if(r)So(s,!1);else{if(Re!==0||e!==null&&e.flags&128)for(e=t.child;e!==null;){if(i=Bi(e),i!==null){for(t.flags|=128,So(s,!1),r=i.updateQueue,r!==null&&(t.updateQueue=r,t.flags|=4),t.subtreeFlags=0,r=n,n=t.child;n!==null;)s=n,e=r,s.flags&=14680066,i=s.alternate,i===null?(s.childLanes=0,s.lanes=e,s.child=null,s.subtreeFlags=0,s.memoizedProps=null,s.memoizedState=null,s.updateQueue=null,s.dependencies=null,s.stateNode=null):(s.childLanes=i.childLanes,s.lanes=i.lanes,s.child=i.child,s.subtreeFlags=0,s.deletions=null,s.memoizedProps=i.memoizedProps,s.memoizedState=i.memoizedState,s.updateQueue=i.updateQueue,s.type=i.type,e=i.dependencies,s.dependencies=e===null?null:{lanes:e.lanes,firstContext:e.firstContext}),n=n.sibling;return ae(he,he.current&1|2),t.child}e=e.sibling}s.tail!==null&&ke()>no&&(t.flags|=128,r=!0,So(s,!1),t.lanes=4194304)}else{if(!r)if(e=Bi(i),e!==null){if(t.flags|=128,r=!0,n=e.updateQueue,n!==null&&(t.updateQueue=n,t.flags|=4),So(s,!0),s.tail===null&&s.tailMode==="hidden"&&!i.alternate&&!fe)return Le(t),null}else 2*ke()-s.renderingStartTime>no&&n!==1073741824&&(t.flags|=128,r=!0,So(s,!1),t.lanes=4194304);s.isBackwards?(i.sibling=t.child,t.child=i):(n=s.last,n!==null?n.sibling=i:t.child=i,s.last=i)}return s.tail!==null?(t=s.tail,s.rendering=t,s.tail=t.sibling,s.renderingStartTime=ke(),t.sibling=null,n=he.current,ae(he,r?n&1|2:n&1),t):(Le(t),null);case 22:case 23:return bc(),r=t.memoizedState!==null,e!==null&&e.memoizedState!==null!==r&&(t.flags|=8192),r&&t.mode&1?qe&1073741824&&(Le(t),t.subtreeFlags&6&&(t.flags|=8192)):Le(t),null;case 24:return null;case 25:return null}throw Error(O(156,t.tag))}function Rw(e,t){switch(ic(t),t.tag){case 1:return Xe(t.type)&&Oi(),e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 3:return eo(),ce(Qe),ce(ze),mc(),e=t.flags,e&65536&&!(e&128)?(t.flags=e&-65537|128,t):null;case 5:return hc(t),null;case 13:if(ce(he),e=t.memoizedState,e!==null&&e.dehydrated!==null){if(t.alternate===null)throw Error(O(340));Jr()}return e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 19:return ce(he),null;case 4:return eo(),null;case 10:return cc(t.type._context),null;case 22:case 23:return bc(),null;case 24:return null;default:return null}}var Xs=!1,$e=!1,jw=typeof WeakSet=="function"?WeakSet:Set,W=null;function Ir(e,t){var n=e.ref;if(n!==null)if(typeof n=="function")try{n(null)}catch(r){xe(e,t,r)}else n.current=null}function mu(e,t,n){try{n()}catch(r){xe(e,t,r)}}var hf=!1;function Mw(e,t){if(Jl=ji,e=oh(),oc(e)){if("selectionStart"in e)var n={start:e.selectionStart,end:e.selectionEnd};else e:{n=(n=e.ownerDocument)&&n.defaultView||window;var r=n.getSelection&&n.getSelection();if(r&&r.rangeCount!==0){n=r.anchorNode;var o=r.anchorOffset,s=r.focusNode;r=r.focusOffset;try{n.nodeType,s.nodeType}catch{n=null;break e}var i=0,a=-1,l=-1,u=0,d=0,f=e,p=null;t:for(;;){for(var y;f!==n||o!==0&&f.nodeType!==3||(a=i+o),f!==s||r!==0&&f.nodeType!==3||(l=i+r),f.nodeType===3&&(i+=f.nodeValue.length),(y=f.firstChild)!==null;)p=f,f=y;for(;;){if(f===e)break t;if(p===n&&++u===o&&(a=i),p===s&&++d===r&&(l=i),(y=f.nextSibling)!==null)break;f=p,p=f.parentNode}f=y}n=a===-1||l===-1?null:{start:a,end:l}}else n=null}n=n||{start:0,end:0}}else n=null;for(ql={focusedElem:e,selectionRange:n},ji=!1,W=t;W!==null;)if(t=W,e=t.child,(t.subtreeFlags&1028)!==0&&e!==null)e.return=t,W=e;else for(;W!==null;){t=W;try{var w=t.alternate;if(t.flags&1024)switch(t.tag){case 0:case 11:case 15:break;case 1:if(w!==null){var g=w.memoizedProps,k=w.memoizedState,m=t.stateNode,h=m.getSnapshotBeforeUpdate(t.elementType===t.type?g:xt(t.type,g),k);m.__reactInternalSnapshotBeforeUpdate=h}break;case 3:var x=t.stateNode.containerInfo;x.nodeType===1?x.textContent="":x.nodeType===9&&x.documentElement&&x.removeChild(x.documentElement);break;case 5:case 6:case 4:case 17:break;default:throw Error(O(163))}}catch(_){xe(t,t.return,_)}if(e=t.sibling,e!==null){e.return=t.return,W=e;break}W=t.return}return w=hf,hf=!1,w}function Bo(e,t,n){var r=t.updateQueue;if(r=r!==null?r.lastEffect:null,r!==null){var o=r=r.next;do{if((o.tag&e)===e){var s=o.destroy;o.destroy=void 0,s!==void 0&&mu(t,n,s)}o=o.next}while(o!==r)}}function xa(e,t){if(t=t.updateQueue,t=t!==null?t.lastEffect:null,t!==null){var n=t=t.next;do{if((n.tag&e)===e){var r=n.create;n.destroy=r()}n=n.next}while(n!==t)}}function vu(e){var t=e.ref;if(t!==null){var n=e.stateNode;switch(e.tag){case 5:e=n;break;default:e=n}typeof t=="function"?t(e):t.current=e}}function em(e){var t=e.alternate;t!==null&&(e.alternate=null,em(t)),e.child=null,e.deletions=null,e.sibling=null,e.tag===5&&(t=e.stateNode,t!==null&&(delete t[Ot],delete t[ns],delete t[nu],delete t[pw],delete t[hw])),e.stateNode=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}function tm(e){return e.tag===5||e.tag===3||e.tag===4}function mf(e){e:for(;;){for(;e.sibling===null;){if(e.return===null||tm(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;e.tag!==5&&e.tag!==6&&e.tag!==18;){if(e.flags&2||e.child===null||e.tag===4)continue e;e.child.return=e,e=e.child}if(!(e.flags&2))return e.stateNode}}function gu(e,t,n){var r=e.tag;if(r===5||r===6)e=e.stateNode,t?n.nodeType===8?n.parentNode.insertBefore(e,t):n.insertBefore(e,t):(n.nodeType===8?(t=n.parentNode,t.insertBefore(e,n)):(t=n,t.appendChild(e)),n=n._reactRootContainer,n!=null||t.onclick!==null||(t.onclick=Ai));else if(r!==4&&(e=e.child,e!==null))for(gu(e,t,n),e=e.sibling;e!==null;)gu(e,t,n),e=e.sibling}function yu(e,t,n){var r=e.tag;if(r===5||r===6)e=e.stateNode,t?n.insertBefore(e,t):n.appendChild(e);else if(r!==4&&(e=e.child,e!==null))for(yu(e,t,n),e=e.sibling;e!==null;)yu(e,t,n),e=e.sibling}var Ae=null,wt=!1;function gn(e,t,n){for(n=n.child;n!==null;)nm(e,t,n),n=n.sibling}function nm(e,t,n){if(Dt&&typeof Dt.onCommitFiberUnmount=="function")try{Dt.onCommitFiberUnmount(da,n)}catch{}switch(n.tag){case 5:$e||Ir(n,t);case 6:var r=Ae,o=wt;Ae=null,gn(e,t,n),Ae=r,wt=o,Ae!==null&&(wt?(e=Ae,n=n.stateNode,e.nodeType===8?e.parentNode.removeChild(n):e.removeChild(n)):Ae.removeChild(n.stateNode));break;case 18:Ae!==null&&(wt?(e=Ae,n=n.stateNode,e.nodeType===8?al(e.parentNode,n):e.nodeType===1&&al(e,n),Xo(e)):al(Ae,n.stateNode));break;case 4:r=Ae,o=wt,Ae=n.stateNode.containerInfo,wt=!0,gn(e,t,n),Ae=r,wt=o;break;case 0:case 11:case 14:case 15:if(!$e&&(r=n.updateQueue,r!==null&&(r=r.lastEffect,r!==null))){o=r=r.next;do{var s=o,i=s.destroy;s=s.tag,i!==void 0&&(s&2||s&4)&&mu(n,t,i),o=o.next}while(o!==r)}gn(e,t,n);break;case 1:if(!$e&&(Ir(n,t),r=n.stateNode,typeof r.componentWillUnmount=="function"))try{r.props=n.memoizedProps,r.state=n.memoizedState,r.componentWillUnmount()}catch(a){xe(n,t,a)}gn(e,t,n);break;case 21:gn(e,t,n);break;case 22:n.mode&1?($e=(r=$e)||n.memoizedState!==null,gn(e,t,n),$e=r):gn(e,t,n);break;default:gn(e,t,n)}}function vf(e){var t=e.updateQueue;if(t!==null){e.updateQueue=null;var n=e.stateNode;n===null&&(n=e.stateNode=new jw),t.forEach(function(r){var o=Fw.bind(null,e,r);n.has(r)||(n.add(r),r.then(o,o))})}}function yt(e,t){var n=t.deletions;if(n!==null)for(var r=0;r<n.length;r++){var o=n[r];try{var s=e,i=t,a=i;e:for(;a!==null;){switch(a.tag){case 5:Ae=a.stateNode,wt=!1;break e;case 3:Ae=a.stateNode.containerInfo,wt=!0;break e;case 4:Ae=a.stateNode.containerInfo,wt=!0;break e}a=a.return}if(Ae===null)throw Error(O(160));nm(s,i,o),Ae=null,wt=!1;var l=o.alternate;l!==null&&(l.return=null),o.return=null}catch(u){xe(o,t,u)}}if(t.subtreeFlags&12854)for(t=t.child;t!==null;)rm(t,e),t=t.sibling}function rm(e,t){var n=e.alternate,r=e.flags;switch(e.tag){case 0:case 11:case 14:case 15:if(yt(t,e),Mt(e),r&4){try{Bo(3,e,e.return),xa(3,e)}catch(g){xe(e,e.return,g)}try{Bo(5,e,e.return)}catch(g){xe(e,e.return,g)}}break;case 1:yt(t,e),Mt(e),r&512&&n!==null&&Ir(n,n.return);break;case 5:if(yt(t,e),Mt(e),r&512&&n!==null&&Ir(n,n.return),e.flags&32){var o=e.stateNode;try{Go(o,"")}catch(g){xe(e,e.return,g)}}if(r&4&&(o=e.stateNode,o!=null)){var s=e.memoizedProps,i=n!==null?n.memoizedProps:s,a=e.type,l=e.updateQueue;if(e.updateQueue=null,l!==null)try{a==="input"&&s.type==="radio"&&s.name!=null&&Ep(o,s),Vl(a,i);var u=Vl(a,s);for(i=0;i<l.length;i+=2){var d=l[i],f=l[i+1];d==="style"?jp(o,f):d==="dangerouslySetInnerHTML"?Tp(o,f):d==="children"?Go(o,f):Hu(o,d,f,u)}switch(a){case"input":Ll(o,s);break;case"textarea":bp(o,s);break;case"select":var p=o._wrapperState.wasMultiple;o._wrapperState.wasMultiple=!!s.multiple;var y=s.value;y!=null?zr(o,!!s.multiple,y,!1):p!==!!s.multiple&&(s.defaultValue!=null?zr(o,!!s.multiple,s.defaultValue,!0):zr(o,!!s.multiple,s.multiple?[]:"",!1))}o[ns]=s}catch(g){xe(e,e.return,g)}}break;case 6:if(yt(t,e),Mt(e),r&4){if(e.stateNode===null)throw Error(O(162));o=e.stateNode,s=e.memoizedProps;try{o.nodeValue=s}catch(g){xe(e,e.return,g)}}break;case 3:if(yt(t,e),Mt(e),r&4&&n!==null&&n.memoizedState.isDehydrated)try{Xo(t.containerInfo)}catch(g){xe(e,e.return,g)}break;case 4:yt(t,e),Mt(e);break;case 13:yt(t,e),Mt(e),o=e.child,o.flags&8192&&(s=o.memoizedState!==null,o.stateNode.isHidden=s,!s||o.alternate!==null&&o.alternate.memoizedState!==null||(Sc=ke())),r&4&&vf(e);break;case 22:if(d=n!==null&&n.memoizedState!==null,e.mode&1?($e=(u=$e)||d,yt(t,e),$e=u):yt(t,e),Mt(e),r&8192){if(u=e.memoizedState!==null,(e.stateNode.isHidden=u)&&!d&&e.mode&1)for(W=e,d=e.child;d!==null;){for(f=W=d;W!==null;){switch(p=W,y=p.child,p.tag){case 0:case 11:case 14:case 15:Bo(4,p,p.return);break;case 1:Ir(p,p.return);var w=p.stateNode;if(typeof w.componentWillUnmount=="function"){r=p,n=p.return;try{t=r,w.props=t.memoizedProps,w.state=t.memoizedState,w.componentWillUnmount()}catch(g){xe(r,n,g)}}break;case 5:Ir(p,p.return);break;case 22:if(p.memoizedState!==null){yf(f);continue}}y!==null?(y.return=p,W=y):yf(f)}d=d.sibling}e:for(d=null,f=e;;){if(f.tag===5){if(d===null){d=f;try{o=f.stateNode,u?(s=o.style,typeof s.setProperty=="function"?s.setProperty("display","none","important"):s.display="none"):(a=f.stateNode,l=f.memoizedProps.style,i=l!=null&&l.hasOwnProperty("display")?l.display:null,a.style.display=Rp("display",i))}catch(g){xe(e,e.return,g)}}}else if(f.tag===6){if(d===null)try{f.stateNode.nodeValue=u?"":f.memoizedProps}catch(g){xe(e,e.return,g)}}else if((f.tag!==22&&f.tag!==23||f.memoizedState===null||f===e)&&f.child!==null){f.child.return=f,f=f.child;continue}if(f===e)break e;for(;f.sibling===null;){if(f.return===null||f.return===e)break e;d===f&&(d=null),f=f.return}d===f&&(d=null),f.sibling.return=f.return,f=f.sibling}}break;case 19:yt(t,e),Mt(e),r&4&&vf(e);break;case 21:break;default:yt(t,e),Mt(e)}}function Mt(e){var t=e.flags;if(t&2){try{e:{for(var n=e.return;n!==null;){if(tm(n)){var r=n;break e}n=n.return}throw Error(O(160))}switch(r.tag){case 5:var o=r.stateNode;r.flags&32&&(Go(o,""),r.flags&=-33);var s=mf(e);yu(e,s,o);break;case 3:case 4:var i=r.stateNode.containerInfo,a=mf(e);gu(e,a,i);break;default:throw Error(O(161))}}catch(l){xe(e,e.return,l)}e.flags&=-3}t&4096&&(e.flags&=-4097)}function Pw(e,t,n){W=e,om(e)}function om(e,t,n){for(var r=(e.mode&1)!==0;W!==null;){var o=W,s=o.child;if(o.tag===22&&r){var i=o.memoizedState!==null||Xs;if(!i){var a=o.alternate,l=a!==null&&a.memoizedState!==null||$e;a=Xs;var u=$e;if(Xs=i,($e=l)&&!u)for(W=o;W!==null;)i=W,l=i.child,i.tag===22&&i.memoizedState!==null?xf(o):l!==null?(l.return=i,W=l):xf(o);for(;s!==null;)W=s,om(s),s=s.sibling;W=o,Xs=a,$e=u}gf(e)}else o.subtreeFlags&8772&&s!==null?(s.return=o,W=s):gf(e)}}function gf(e){for(;W!==null;){var t=W;if(t.flags&8772){var n=t.alternate;try{if(t.flags&8772)switch(t.tag){case 0:case 11:case 15:$e||xa(5,t);break;case 1:var r=t.stateNode;if(t.flags&4&&!$e)if(n===null)r.componentDidMount();else{var o=t.elementType===t.type?n.memoizedProps:xt(t.type,n.memoizedProps);r.componentDidUpdate(o,n.memoizedState,r.__reactInternalSnapshotBeforeUpdate)}var s=t.updateQueue;s!==null&&ef(t,s,r);break;case 3:var i=t.updateQueue;if(i!==null){if(n=null,t.child!==null)switch(t.child.tag){case 5:n=t.child.stateNode;break;case 1:n=t.child.stateNode}ef(t,i,n)}break;case 5:var a=t.stateNode;if(n===null&&t.flags&4){n=a;var l=t.memoizedProps;switch(t.type){case"button":case"input":case"select":case"textarea":l.autoFocus&&n.focus();break;case"img":l.src&&(n.src=l.src)}}break;case 6:break;case 4:break;case 12:break;case 13:if(t.memoizedState===null){var u=t.alternate;if(u!==null){var d=u.memoizedState;if(d!==null){var f=d.dehydrated;f!==null&&Xo(f)}}}break;case 19:case 17:case 21:case 22:case 23:case 25:break;default:throw Error(O(163))}$e||t.flags&512&&vu(t)}catch(p){xe(t,t.return,p)}}if(t===e){W=null;break}if(n=t.sibling,n!==null){n.return=t.return,W=n;break}W=t.return}}function yf(e){for(;W!==null;){var t=W;if(t===e){W=null;break}var n=t.sibling;if(n!==null){n.return=t.return,W=n;break}W=t.return}}function xf(e){for(;W!==null;){var t=W;try{switch(t.tag){case 0:case 11:case 15:var n=t.return;try{xa(4,t)}catch(l){xe(t,n,l)}break;case 1:var r=t.stateNode;if(typeof r.componentDidMount=="function"){var o=t.return;try{r.componentDidMount()}catch(l){xe(t,o,l)}}var s=t.return;try{vu(t)}catch(l){xe(t,s,l)}break;case 5:var i=t.return;try{vu(t)}catch(l){xe(t,i,l)}}}catch(l){xe(t,t.return,l)}if(t===e){W=null;break}var a=t.sibling;if(a!==null){a.return=t.return,W=a;break}W=t.return}}var Aw=Math.ceil,Wi=fn.ReactCurrentDispatcher,Cc=fn.ReactCurrentOwner,dt=fn.ReactCurrentBatchConfig,re=0,Me=null,Ne=null,Oe=0,qe=0,Dr=Kn(0),Re=0,ls=null,ur=0,wa=0,kc=0,Vo=null,Ge=null,Sc=0,no=1/0,Kt=null,Hi=!1,xu=null,Pn=null,Js=!1,bn=null,Zi=0,Uo=0,wu=null,gi=-1,yi=0;function Ve(){return re&6?ke():gi!==-1?gi:gi=ke()}function An(e){return e.mode&1?re&2&&Oe!==0?Oe&-Oe:vw.transition!==null?(yi===0&&(yi=Vp()),yi):(e=ie,e!==0||(e=window.event,e=e===void 0?16:Kp(e.type)),e):1}function St(e,t,n,r){if(50<Uo)throw Uo=0,wu=null,Error(O(185));Ns(e,n,r),(!(re&2)||e!==Me)&&(e===Me&&(!(re&2)&&(wa|=n),Re===4&&Sn(e,Oe)),Je(e,r),n===1&&re===0&&!(t.mode&1)&&(no=ke()+500,va&&Qn()))}function Je(e,t){var n=e.callbackNode;vx(e,t);var r=Ri(e,e===Me?Oe:0);if(r===0)n!==null&&Nd(n),e.callbackNode=null,e.callbackPriority=0;else if(t=r&-r,e.callbackPriority!==t){if(n!=null&&Nd(n),t===1)e.tag===0?mw(wf.bind(null,e)):hh(wf.bind(null,e)),dw(function(){!(re&6)&&Qn()}),n=null;else{switch(Up(r)){case 1:n=Qu;break;case 4:n=Fp;break;case 16:n=Ti;break;case 536870912:n=Bp;break;default:n=Ti}n=fm(n,sm.bind(null,e))}e.callbackPriority=t,e.callbackNode=n}}function sm(e,t){if(gi=-1,yi=0,re&6)throw Error(O(327));var n=e.callbackNode;if(Wr()&&e.callbackNode!==n)return null;var r=Ri(e,e===Me?Oe:0);if(r===0)return null;if(r&30||r&e.expiredLanes||t)t=Gi(e,r);else{t=r;var o=re;re|=2;var s=am();(Me!==e||Oe!==t)&&(Kt=null,no=ke()+500,rr(e,t));do try{Dw();break}catch(a){im(e,a)}while(!0);uc(),Wi.current=s,re=o,Ne!==null?t=0:(Me=null,Oe=0,t=Re)}if(t!==0){if(t===2&&(o=Gl(e),o!==0&&(r=o,t=_u(e,o))),t===1)throw n=ls,rr(e,0),Sn(e,r),Je(e,ke()),n;if(t===6)Sn(e,r);else{if(o=e.current.alternate,!(r&30)&&!Ow(o)&&(t=Gi(e,r),t===2&&(s=Gl(e),s!==0&&(r=s,t=_u(e,s))),t===1))throw n=ls,rr(e,0),Sn(e,r),Je(e,ke()),n;switch(e.finishedWork=o,e.finishedLanes=r,t){case 0:case 1:throw Error(O(345));case 2:qn(e,Ge,Kt);break;case 3:if(Sn(e,r),(r&130023424)===r&&(t=Sc+500-ke(),10<t)){if(Ri(e,0)!==0)break;if(o=e.suspendedLanes,(o&r)!==r){Ve(),e.pingedLanes|=e.suspendedLanes&o;break}e.timeoutHandle=tu(qn.bind(null,e,Ge,Kt),t);break}qn(e,Ge,Kt);break;case 4:if(Sn(e,r),(r&4194240)===r)break;for(t=e.eventTimes,o=-1;0<r;){var i=31-kt(r);s=1<<i,i=t[i],i>o&&(o=i),r&=~s}if(r=o,r=ke()-r,r=(120>r?120:480>r?480:1080>r?1080:1920>r?1920:3e3>r?3e3:4320>r?4320:1960*Aw(r/1960))-r,10<r){e.timeoutHandle=tu(qn.bind(null,e,Ge,Kt),r);break}qn(e,Ge,Kt);break;case 5:qn(e,Ge,Kt);break;default:throw Error(O(329))}}}return Je(e,ke()),e.callbackNode===n?sm.bind(null,e):null}function _u(e,t){var n=Vo;return e.current.memoizedState.isDehydrated&&(rr(e,t).flags|=256),e=Gi(e,t),e!==2&&(t=Ge,Ge=n,t!==null&&Cu(t)),e}function Cu(e){Ge===null?Ge=e:Ge.push.apply(Ge,e)}function Ow(e){for(var t=e;;){if(t.flags&16384){var n=t.updateQueue;if(n!==null&&(n=n.stores,n!==null))for(var r=0;r<n.length;r++){var o=n[r],s=o.getSnapshot;o=o.value;try{if(!bt(s(),o))return!1}catch{return!1}}}if(n=t.child,t.subtreeFlags&16384&&n!==null)n.return=t,t=n;else{if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return!0;t=t.return}t.sibling.return=t.return,t=t.sibling}}return!0}function Sn(e,t){for(t&=~kc,t&=~wa,e.suspendedLanes|=t,e.pingedLanes&=~t,e=e.expirationTimes;0<t;){var n=31-kt(t),r=1<<n;e[n]=-1,t&=~r}}function wf(e){if(re&6)throw Error(O(327));Wr();var t=Ri(e,0);if(!(t&1))return Je(e,ke()),null;var n=Gi(e,t);if(e.tag!==0&&n===2){var r=Gl(e);r!==0&&(t=r,n=_u(e,r))}if(n===1)throw n=ls,rr(e,0),Sn(e,t),Je(e,ke()),n;if(n===6)throw Error(O(345));return e.finishedWork=e.current.alternate,e.finishedLanes=t,qn(e,Ge,Kt),Je(e,ke()),null}function Ec(e,t){var n=re;re|=1;try{return e(t)}finally{re=n,re===0&&(no=ke()+500,va&&Qn())}}function cr(e){bn!==null&&bn.tag===0&&!(re&6)&&Wr();var t=re;re|=1;var n=dt.transition,r=ie;try{if(dt.transition=null,ie=1,e)return e()}finally{ie=r,dt.transition=n,re=t,!(re&6)&&Qn()}}function bc(){qe=Dr.current,ce(Dr)}function rr(e,t){e.finishedWork=null,e.finishedLanes=0;var n=e.timeoutHandle;if(n!==-1&&(e.timeoutHandle=-1,cw(n)),Ne!==null)for(n=Ne.return;n!==null;){var r=n;switch(ic(r),r.tag){case 1:r=r.type.childContextTypes,r!=null&&Oi();break;case 3:eo(),ce(Qe),ce(ze),mc();break;case 5:hc(r);break;case 4:eo();break;case 13:ce(he);break;case 19:ce(he);break;case 10:cc(r.type._context);break;case 22:case 23:bc()}n=n.return}if(Me=e,Ne=e=On(e.current,null),Oe=qe=t,Re=0,ls=null,kc=wa=ur=0,Ge=Vo=null,tr!==null){for(t=0;t<tr.length;t++)if(n=tr[t],r=n.interleaved,r!==null){n.interleaved=null;var o=r.next,s=n.pending;if(s!==null){var i=s.next;s.next=o,r.next=i}n.pending=r}tr=null}return e}function im(e,t){do{var n=Ne;try{if(uc(),hi.current=Ui,Vi){for(var r=me.memoizedState;r!==null;){var o=r.queue;o!==null&&(o.pending=null),r=r.next}Vi=!1}if(lr=0,je=Te=me=null,Fo=!1,ss=0,Cc.current=null,n===null||n.return===null){Re=1,ls=t,Ne=null;break}e:{var s=e,i=n.return,a=n,l=t;if(t=Oe,a.flags|=32768,l!==null&&typeof l=="object"&&typeof l.then=="function"){var u=l,d=a,f=d.tag;if(!(d.mode&1)&&(f===0||f===11||f===15)){var p=d.alternate;p?(d.updateQueue=p.updateQueue,d.memoizedState=p.memoizedState,d.lanes=p.lanes):(d.updateQueue=null,d.memoizedState=null)}var y=af(i);if(y!==null){y.flags&=-257,lf(y,i,a,s,t),y.mode&1&&sf(s,u,t),t=y,l=u;var w=t.updateQueue;if(w===null){var g=new Set;g.add(l),t.updateQueue=g}else w.add(l);break e}else{if(!(t&1)){sf(s,u,t),Nc();break e}l=Error(O(426))}}else if(fe&&a.mode&1){var k=af(i);if(k!==null){!(k.flags&65536)&&(k.flags|=256),lf(k,i,a,s,t),ac(to(l,a));break e}}s=l=to(l,a),Re!==4&&(Re=2),Vo===null?Vo=[s]:Vo.push(s),s=i;do{switch(s.tag){case 3:s.flags|=65536,t&=-t,s.lanes|=t;var m=Uh(s,l,t);qd(s,m);break e;case 1:a=l;var h=s.type,x=s.stateNode;if(!(s.flags&128)&&(typeof h.getDerivedStateFromError=="function"||x!==null&&typeof x.componentDidCatch=="function"&&(Pn===null||!Pn.has(x)))){s.flags|=65536,t&=-t,s.lanes|=t;var _=Wh(s,a,t);qd(s,_);break e}}s=s.return}while(s!==null)}um(n)}catch(b){t=b,Ne===n&&n!==null&&(Ne=n=n.return);continue}break}while(!0)}function am(){var e=Wi.current;return Wi.current=Ui,e===null?Ui:e}function Nc(){(Re===0||Re===3||Re===2)&&(Re=4),Me===null||!(ur&268435455)&&!(wa&268435455)||Sn(Me,Oe)}function Gi(e,t){var n=re;re|=2;var r=am();(Me!==e||Oe!==t)&&(Kt=null,rr(e,t));do try{Iw();break}catch(o){im(e,o)}while(!0);if(uc(),re=n,Wi.current=r,Ne!==null)throw Error(O(261));return Me=null,Oe=0,Re}function Iw(){for(;Ne!==null;)lm(Ne)}function Dw(){for(;Ne!==null&&!ax();)lm(Ne)}function lm(e){var t=dm(e.alternate,e,qe);e.memoizedProps=e.pendingProps,t===null?um(e):Ne=t,Cc.current=null}function um(e){var t=e;do{var n=t.alternate;if(e=t.return,t.flags&32768){if(n=Rw(n,t),n!==null){n.flags&=32767,Ne=n;return}if(e!==null)e.flags|=32768,e.subtreeFlags=0,e.deletions=null;else{Re=6,Ne=null;return}}else if(n=Tw(n,t,qe),n!==null){Ne=n;return}if(t=t.sibling,t!==null){Ne=t;return}Ne=t=e}while(t!==null);Re===0&&(Re=5)}function qn(e,t,n){var r=ie,o=dt.transition;try{dt.transition=null,ie=1,Lw(e,t,n,r)}finally{dt.transition=o,ie=r}return null}function Lw(e,t,n,r){do Wr();while(bn!==null);if(re&6)throw Error(O(327));n=e.finishedWork;var o=e.finishedLanes;if(n===null)return null;if(e.finishedWork=null,e.finishedLanes=0,n===e.current)throw Error(O(177));e.callbackNode=null,e.callbackPriority=0;var s=n.lanes|n.childLanes;if(gx(e,s),e===Me&&(Ne=Me=null,Oe=0),!(n.subtreeFlags&2064)&&!(n.flags&2064)||Js||(Js=!0,fm(Ti,function(){return Wr(),null})),s=(n.flags&15990)!==0,n.subtreeFlags&15990||s){s=dt.transition,dt.transition=null;var i=ie;ie=1;var a=re;re|=4,Cc.current=null,Mw(e,n),rm(n,e),rw(ql),ji=!!Jl,ql=Jl=null,e.current=n,Pw(n),lx(),re=a,ie=i,dt.transition=s}else e.current=n;if(Js&&(Js=!1,bn=e,Zi=o),s=e.pendingLanes,s===0&&(Pn=null),dx(n.stateNode),Je(e,ke()),t!==null)for(r=e.onRecoverableError,n=0;n<t.length;n++)o=t[n],r(o.value,{componentStack:o.stack,digest:o.digest});if(Hi)throw Hi=!1,e=xu,xu=null,e;return Zi&1&&e.tag!==0&&Wr(),s=e.pendingLanes,s&1?e===wu?Uo++:(Uo=0,wu=e):Uo=0,Qn(),null}function Wr(){if(bn!==null){var e=Up(Zi),t=dt.transition,n=ie;try{if(dt.transition=null,ie=16>e?16:e,bn===null)var r=!1;else{if(e=bn,bn=null,Zi=0,re&6)throw Error(O(331));var o=re;for(re|=4,W=e.current;W!==null;){var s=W,i=s.child;if(W.flags&16){var a=s.deletions;if(a!==null){for(var l=0;l<a.length;l++){var u=a[l];for(W=u;W!==null;){var d=W;switch(d.tag){case 0:case 11:case 15:Bo(8,d,s)}var f=d.child;if(f!==null)f.return=d,W=f;else for(;W!==null;){d=W;var p=d.sibling,y=d.return;if(em(d),d===u){W=null;break}if(p!==null){p.return=y,W=p;break}W=y}}}var w=s.alternate;if(w!==null){var g=w.child;if(g!==null){w.child=null;do{var k=g.sibling;g.sibling=null,g=k}while(g!==null)}}W=s}}if(s.subtreeFlags&2064&&i!==null)i.return=s,W=i;else e:for(;W!==null;){if(s=W,s.flags&2048)switch(s.tag){case 0:case 11:case 15:Bo(9,s,s.return)}var m=s.sibling;if(m!==null){m.return=s.return,W=m;break e}W=s.return}}var h=e.current;for(W=h;W!==null;){i=W;var x=i.child;if(i.subtreeFlags&2064&&x!==null)x.return=i,W=x;else e:for(i=h;W!==null;){if(a=W,a.flags&2048)try{switch(a.tag){case 0:case 11:case 15:xa(9,a)}}catch(b){xe(a,a.return,b)}if(a===i){W=null;break e}var _=a.sibling;if(_!==null){_.return=a.return,W=_;break e}W=a.return}}if(re=o,Qn(),Dt&&typeof Dt.onPostCommitFiberRoot=="function")try{Dt.onPostCommitFiberRoot(da,e)}catch{}r=!0}return r}finally{ie=n,dt.transition=t}}return!1}function _f(e,t,n){t=to(n,t),t=Uh(e,t,1),e=Mn(e,t,1),t=Ve(),e!==null&&(Ns(e,1,t),Je(e,t))}function xe(e,t,n){if(e.tag===3)_f(e,e,n);else for(;t!==null;){if(t.tag===3){_f(t,e,n);break}else if(t.tag===1){var r=t.stateNode;if(typeof t.type.getDerivedStateFromError=="function"||typeof r.componentDidCatch=="function"&&(Pn===null||!Pn.has(r))){e=to(n,e),e=Wh(t,e,1),t=Mn(t,e,1),e=Ve(),t!==null&&(Ns(t,1,e),Je(t,e));break}}t=t.return}}function $w(e,t,n){var r=e.pingCache;r!==null&&r.delete(t),t=Ve(),e.pingedLanes|=e.suspendedLanes&n,Me===e&&(Oe&n)===n&&(Re===4||Re===3&&(Oe&130023424)===Oe&&500>ke()-Sc?rr(e,0):kc|=n),Je(e,t)}function cm(e,t){t===0&&(e.mode&1?(t=Vs,Vs<<=1,!(Vs&130023424)&&(Vs=4194304)):t=1);var n=Ve();e=an(e,t),e!==null&&(Ns(e,t,n),Je(e,n))}function zw(e){var t=e.memoizedState,n=0;t!==null&&(n=t.retryLane),cm(e,n)}function Fw(e,t){var n=0;switch(e.tag){case 13:var r=e.stateNode,o=e.memoizedState;o!==null&&(n=o.retryLane);break;case 19:r=e.stateNode;break;default:throw Error(O(314))}r!==null&&r.delete(t),cm(e,n)}var dm;dm=function(e,t,n){if(e!==null)if(e.memoizedProps!==t.pendingProps||Qe.current)Ke=!0;else{if(!(e.lanes&n)&&!(t.flags&128))return Ke=!1,Nw(e,t,n);Ke=!!(e.flags&131072)}else Ke=!1,fe&&t.flags&1048576&&mh(t,Li,t.index);switch(t.lanes=0,t.tag){case 2:var r=t.type;vi(e,t),e=t.pendingProps;var o=Xr(t,ze.current);Ur(t,n),o=gc(null,t,r,e,o,n);var s=yc();return t.flags|=1,typeof o=="object"&&o!==null&&typeof o.render=="function"&&o.$$typeof===void 0?(t.tag=1,t.memoizedState=null,t.updateQueue=null,Xe(r)?(s=!0,Ii(t)):s=!1,t.memoizedState=o.state!==null&&o.state!==void 0?o.state:null,fc(t),o.updater=ya,t.stateNode=o,o._reactInternals=t,lu(t,r,e,n),t=du(null,t,r,!0,s,n)):(t.tag=0,fe&&s&&sc(t),Be(null,t,o,n),t=t.child),t;case 16:r=t.elementType;e:{switch(vi(e,t),e=t.pendingProps,o=r._init,r=o(r._payload),t.type=r,o=t.tag=Vw(r),e=xt(r,e),o){case 0:t=cu(null,t,r,e,n);break e;case 1:t=df(null,t,r,e,n);break e;case 11:t=uf(null,t,r,e,n);break e;case 14:t=cf(null,t,r,xt(r.type,e),n);break e}throw Error(O(306,r,""))}return t;case 0:return r=t.type,o=t.pendingProps,o=t.elementType===r?o:xt(r,o),cu(e,t,r,o,n);case 1:return r=t.type,o=t.pendingProps,o=t.elementType===r?o:xt(r,o),df(e,t,r,o,n);case 3:e:{if(Yh(t),e===null)throw Error(O(387));r=t.pendingProps,s=t.memoizedState,o=s.element,_h(e,t),Fi(t,r,null,n);var i=t.memoizedState;if(r=i.element,s.isDehydrated)if(s={element:r,isDehydrated:!1,cache:i.cache,pendingSuspenseBoundaries:i.pendingSuspenseBoundaries,transitions:i.transitions},t.updateQueue.baseState=s,t.memoizedState=s,t.flags&256){o=to(Error(O(423)),t),t=ff(e,t,r,n,o);break e}else if(r!==o){o=to(Error(O(424)),t),t=ff(e,t,r,n,o);break e}else for(tt=jn(t.stateNode.containerInfo.firstChild),nt=t,fe=!0,_t=null,n=xh(t,null,r,n),t.child=n;n;)n.flags=n.flags&-3|4096,n=n.sibling;else{if(Jr(),r===o){t=ln(e,t,n);break e}Be(e,t,r,n)}t=t.child}return t;case 5:return Ch(t),e===null&&su(t),r=t.type,o=t.pendingProps,s=e!==null?e.memoizedProps:null,i=o.children,eu(r,o)?i=null:s!==null&&eu(r,s)&&(t.flags|=32),Gh(e,t),Be(e,t,i,n),t.child;case 6:return e===null&&su(t),null;case 13:return Kh(e,t,n);case 4:return pc(t,t.stateNode.containerInfo),r=t.pendingProps,e===null?t.child=qr(t,null,r,n):Be(e,t,r,n),t.child;case 11:return r=t.type,o=t.pendingProps,o=t.elementType===r?o:xt(r,o),uf(e,t,r,o,n);case 7:return Be(e,t,t.pendingProps,n),t.child;case 8:return Be(e,t,t.pendingProps.children,n),t.child;case 12:return Be(e,t,t.pendingProps.children,n),t.child;case 10:e:{if(r=t.type._context,o=t.pendingProps,s=t.memoizedProps,i=o.value,ae($i,r._currentValue),r._currentValue=i,s!==null)if(bt(s.value,i)){if(s.children===o.children&&!Qe.current){t=ln(e,t,n);break e}}else for(s=t.child,s!==null&&(s.return=t);s!==null;){var a=s.dependencies;if(a!==null){i=s.child;for(var l=a.firstContext;l!==null;){if(l.context===r){if(s.tag===1){l=en(-1,n&-n),l.tag=2;var u=s.updateQueue;if(u!==null){u=u.shared;var d=u.pending;d===null?l.next=l:(l.next=d.next,d.next=l),u.pending=l}}s.lanes|=n,l=s.alternate,l!==null&&(l.lanes|=n),iu(s.return,n,t),a.lanes|=n;break}l=l.next}}else if(s.tag===10)i=s.type===t.type?null:s.child;else if(s.tag===18){if(i=s.return,i===null)throw Error(O(341));i.lanes|=n,a=i.alternate,a!==null&&(a.lanes|=n),iu(i,n,t),i=s.sibling}else i=s.child;if(i!==null)i.return=s;else for(i=s;i!==null;){if(i===t){i=null;break}if(s=i.sibling,s!==null){s.return=i.return,i=s;break}i=i.return}s=i}Be(e,t,o.children,n),t=t.child}return t;case 9:return o=t.type,r=t.pendingProps.children,Ur(t,n),o=pt(o),r=r(o),t.flags|=1,Be(e,t,r,n),t.child;case 14:return r=t.type,o=xt(r,t.pendingProps),o=xt(r.type,o),cf(e,t,r,o,n);case 15:return Hh(e,t,t.type,t.pendingProps,n);case 17:return r=t.type,o=t.pendingProps,o=t.elementType===r?o:xt(r,o),vi(e,t),t.tag=1,Xe(r)?(e=!0,Ii(t)):e=!1,Ur(t,n),Vh(t,r,o),lu(t,r,o,n),du(null,t,r,!0,e,n);case 19:return Qh(e,t,n);case 22:return Zh(e,t,n)}throw Error(O(156,t.tag))};function fm(e,t){return zp(e,t)}function Bw(e,t,n,r){this.tag=e,this.key=n,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.ref=null,this.pendingProps=t,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=r,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function ct(e,t,n,r){return new Bw(e,t,n,r)}function Tc(e){return e=e.prototype,!(!e||!e.isReactComponent)}function Vw(e){if(typeof e=="function")return Tc(e)?1:0;if(e!=null){if(e=e.$$typeof,e===Gu)return 11;if(e===Yu)return 14}return 2}function On(e,t){var n=e.alternate;return n===null?(n=ct(e.tag,t,e.key,e.mode),n.elementType=e.elementType,n.type=e.type,n.stateNode=e.stateNode,n.alternate=e,e.alternate=n):(n.pendingProps=t,n.type=e.type,n.flags=0,n.subtreeFlags=0,n.deletions=null),n.flags=e.flags&14680064,n.childLanes=e.childLanes,n.lanes=e.lanes,n.child=e.child,n.memoizedProps=e.memoizedProps,n.memoizedState=e.memoizedState,n.updateQueue=e.updateQueue,t=e.dependencies,n.dependencies=t===null?null:{lanes:t.lanes,firstContext:t.firstContext},n.sibling=e.sibling,n.index=e.index,n.ref=e.ref,n}function xi(e,t,n,r,o,s){var i=2;if(r=e,typeof e=="function")Tc(e)&&(i=1);else if(typeof e=="string")i=5;else e:switch(e){case br:return or(n.children,o,s,t);case Zu:i=8,o|=8;break;case Pl:return e=ct(12,n,t,o|2),e.elementType=Pl,e.lanes=s,e;case Al:return e=ct(13,n,t,o),e.elementType=Al,e.lanes=s,e;case Ol:return e=ct(19,n,t,o),e.elementType=Ol,e.lanes=s,e;case Cp:return _a(n,o,s,t);default:if(typeof e=="object"&&e!==null)switch(e.$$typeof){case wp:i=10;break e;case _p:i=9;break e;case Gu:i=11;break e;case Yu:i=14;break e;case _n:i=16,r=null;break e}throw Error(O(130,e==null?e:typeof e,""))}return t=ct(i,n,t,o),t.elementType=e,t.type=r,t.lanes=s,t}function or(e,t,n,r){return e=ct(7,e,r,t),e.lanes=n,e}function _a(e,t,n,r){return e=ct(22,e,r,t),e.elementType=Cp,e.lanes=n,e.stateNode={isHidden:!1},e}function ml(e,t,n){return e=ct(6,e,null,t),e.lanes=n,e}function vl(e,t,n){return t=ct(4,e.children!==null?e.children:[],e.key,t),t.lanes=n,t.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},t}function Uw(e,t,n,r,o){this.tag=t,this.containerInfo=e,this.finishedWork=this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.pendingContext=this.context=null,this.callbackPriority=0,this.eventTimes=Qa(0),this.expirationTimes=Qa(-1),this.entangledLanes=this.finishedLanes=this.mutableReadLanes=this.expiredLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=Qa(0),this.identifierPrefix=r,this.onRecoverableError=o,this.mutableSourceEagerHydrationData=null}function Rc(e,t,n,r,o,s,i,a,l){return e=new Uw(e,t,n,a,l),t===1?(t=1,s===!0&&(t|=8)):t=0,s=ct(3,null,null,t),e.current=s,s.stateNode=e,s.memoizedState={element:r,isDehydrated:n,cache:null,transitions:null,pendingSuspenseBoundaries:null},fc(s),e}function Ww(e,t,n){var r=3<arguments.length&&arguments[3]!==void 0?arguments[3]:null;return{$$typeof:Er,key:r==null?null:""+r,children:e,containerInfo:t,implementation:n}}function pm(e){if(!e)return Dn;e=e._reactInternals;e:{if(gr(e)!==e||e.tag!==1)throw Error(O(170));var t=e;do{switch(t.tag){case 3:t=t.stateNode.context;break e;case 1:if(Xe(t.type)){t=t.stateNode.__reactInternalMemoizedMergedChildContext;break e}}t=t.return}while(t!==null);throw Error(O(171))}if(e.tag===1){var n=e.type;if(Xe(n))return ph(e,n,t)}return t}function hm(e,t,n,r,o,s,i,a,l){return e=Rc(n,r,!0,e,o,s,i,a,l),e.context=pm(null),n=e.current,r=Ve(),o=An(n),s=en(r,o),s.callback=t??null,Mn(n,s,o),e.current.lanes=o,Ns(e,o,r),Je(e,r),e}function Ca(e,t,n,r){var o=t.current,s=Ve(),i=An(o);return n=pm(n),t.context===null?t.context=n:t.pendingContext=n,t=en(s,i),t.payload={element:e},r=r===void 0?null:r,r!==null&&(t.callback=r),e=Mn(o,t,i),e!==null&&(St(e,o,i,s),pi(e,o,i)),i}function Yi(e){if(e=e.current,!e.child)return null;switch(e.child.tag){case 5:return e.child.stateNode;default:return e.child.stateNode}}function Cf(e,t){if(e=e.memoizedState,e!==null&&e.dehydrated!==null){var n=e.retryLane;e.retryLane=n!==0&&n<t?n:t}}function jc(e,t){Cf(e,t),(e=e.alternate)&&Cf(e,t)}function Hw(){return null}var mm=typeof reportError=="function"?reportError:function(e){console.error(e)};function Mc(e){this._internalRoot=e}ka.prototype.render=Mc.prototype.render=function(e){var t=this._internalRoot;if(t===null)throw Error(O(409));Ca(e,t,null,null)};ka.prototype.unmount=Mc.prototype.unmount=function(){var e=this._internalRoot;if(e!==null){this._internalRoot=null;var t=e.containerInfo;cr(function(){Ca(null,e,null,null)}),t[sn]=null}};function ka(e){this._internalRoot=e}ka.prototype.unstable_scheduleHydration=function(e){if(e){var t=Zp();e={blockedOn:null,target:e,priority:t};for(var n=0;n<kn.length&&t!==0&&t<kn[n].priority;n++);kn.splice(n,0,e),n===0&&Yp(e)}};function Pc(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11)}function Sa(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11&&(e.nodeType!==8||e.nodeValue!==" react-mount-point-unstable "))}function kf(){}function Zw(e,t,n,r,o){if(o){if(typeof r=="function"){var s=r;r=function(){var u=Yi(i);s.call(u)}}var i=hm(t,r,e,0,null,!1,!1,"",kf);return e._reactRootContainer=i,e[sn]=i.current,es(e.nodeType===8?e.parentNode:e),cr(),i}for(;o=e.lastChild;)e.removeChild(o);if(typeof r=="function"){var a=r;r=function(){var u=Yi(l);a.call(u)}}var l=Rc(e,0,!1,null,null,!1,!1,"",kf);return e._reactRootContainer=l,e[sn]=l.current,es(e.nodeType===8?e.parentNode:e),cr(function(){Ca(t,l,n,r)}),l}function Ea(e,t,n,r,o){var s=n._reactRootContainer;if(s){var i=s;if(typeof o=="function"){var a=o;o=function(){var l=Yi(i);a.call(l)}}Ca(t,i,e,o)}else i=Zw(n,t,e,o,r);return Yi(i)}Wp=function(e){switch(e.tag){case 3:var t=e.stateNode;if(t.current.memoizedState.isDehydrated){var n=jo(t.pendingLanes);n!==0&&(Xu(t,n|1),Je(t,ke()),!(re&6)&&(no=ke()+500,Qn()))}break;case 13:cr(function(){var r=an(e,1);if(r!==null){var o=Ve();St(r,e,1,o)}}),jc(e,1)}};Ju=function(e){if(e.tag===13){var t=an(e,134217728);if(t!==null){var n=Ve();St(t,e,134217728,n)}jc(e,134217728)}};Hp=function(e){if(e.tag===13){var t=An(e),n=an(e,t);if(n!==null){var r=Ve();St(n,e,t,r)}jc(e,t)}};Zp=function(){return ie};Gp=function(e,t){var n=ie;try{return ie=e,t()}finally{ie=n}};Wl=function(e,t,n){switch(t){case"input":if(Ll(e,n),t=n.name,n.type==="radio"&&t!=null){for(n=e;n.parentNode;)n=n.parentNode;for(n=n.querySelectorAll("input[name="+JSON.stringify(""+t)+'][type="radio"]'),t=0;t<n.length;t++){var r=n[t];if(r!==e&&r.form===e.form){var o=ma(r);if(!o)throw Error(O(90));Sp(r),Ll(r,o)}}}break;case"textarea":bp(e,n);break;case"select":t=n.value,t!=null&&zr(e,!!n.multiple,t,!1)}};Ap=Ec;Op=cr;var Gw={usingClientEntryPoint:!1,Events:[Rs,jr,ma,Mp,Pp,Ec]},Eo={findFiberByHostInstance:er,bundleType:0,version:"18.3.1",rendererPackageName:"react-dom"},Yw={bundleType:Eo.bundleType,version:Eo.version,rendererPackageName:Eo.rendererPackageName,rendererConfig:Eo.rendererConfig,overrideHookState:null,overrideHookStateDeletePath:null,overrideHookStateRenamePath:null,overrideProps:null,overridePropsDeletePath:null,overridePropsRenamePath:null,setErrorHandler:null,setSuspenseHandler:null,scheduleUpdate:null,currentDispatcherRef:fn.ReactCurrentDispatcher,findHostInstanceByFiber:function(e){return e=Lp(e),e===null?null:e.stateNode},findFiberByHostInstance:Eo.findFiberByHostInstance||Hw,findHostInstancesForRefresh:null,scheduleRefresh:null,scheduleRoot:null,setRefreshHandler:null,getCurrentFiber:null,reconcilerVersion:"18.3.1-next-f1338f8080-20240426"};if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"){var qs=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!qs.isDisabled&&qs.supportsFiber)try{da=qs.inject(Yw),Dt=qs}catch{}}it.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=Gw;it.createPortal=function(e,t){var n=2<arguments.length&&arguments[2]!==void 0?arguments[2]:null;if(!Pc(t))throw Error(O(200));return Ww(e,t,null,n)};it.createRoot=function(e,t){if(!Pc(e))throw Error(O(299));var n=!1,r="",o=mm;return t!=null&&(t.unstable_strictMode===!0&&(n=!0),t.identifierPrefix!==void 0&&(r=t.identifierPrefix),t.onRecoverableError!==void 0&&(o=t.onRecoverableError)),t=Rc(e,1,!1,null,null,n,!1,r,o),e[sn]=t.current,es(e.nodeType===8?e.parentNode:e),new Mc(t)};it.findDOMNode=function(e){if(e==null)return null;if(e.nodeType===1)return e;var t=e._reactInternals;if(t===void 0)throw typeof e.render=="function"?Error(O(188)):(e=Object.keys(e).join(","),Error(O(268,e)));return e=Lp(t),e=e===null?null:e.stateNode,e};it.flushSync=function(e){return cr(e)};it.hydrate=function(e,t,n){if(!Sa(t))throw Error(O(200));return Ea(null,e,t,!0,n)};it.hydrateRoot=function(e,t,n){if(!Pc(e))throw Error(O(405));var r=n!=null&&n.hydratedSources||null,o=!1,s="",i=mm;if(n!=null&&(n.unstable_strictMode===!0&&(o=!0),n.identifierPrefix!==void 0&&(s=n.identifierPrefix),n.onRecoverableError!==void 0&&(i=n.onRecoverableError)),t=hm(t,null,e,1,n??null,o,!1,s,i),e[sn]=t.current,es(e),r)for(e=0;e<r.length;e++)n=r[e],o=n._getVersion,o=o(n._source),t.mutableSourceEagerHydrationData==null?t.mutableSourceEagerHydrationData=[n,o]:t.mutableSourceEagerHydrationData.push(n,o);return new ka(t)};it.render=function(e,t,n){if(!Sa(t))throw Error(O(200));return Ea(null,e,t,!1,n)};it.unmountComponentAtNode=function(e){if(!Sa(e))throw Error(O(40));return e._reactRootContainer?(cr(function(){Ea(null,null,e,!1,function(){e._reactRootContainer=null,e[sn]=null})}),!0):!1};it.unstable_batchedUpdates=Ec;it.unstable_renderSubtreeIntoContainer=function(e,t,n,r){if(!Sa(n))throw Error(O(200));if(e==null||e._reactInternals===void 0)throw Error(O(38));return Ea(e,t,n,!1,r)};it.version="18.3.1-next-f1338f8080-20240426";function vm(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(vm)}catch(e){console.error(e)}}vm(),vp.exports=it;var ba=vp.exports;const Kw=mr(ba);var Sf=ba;jl.createRoot=Sf.createRoot,jl.hydrateRoot=Sf.hydrateRoot;var ne;(function(e){e.assertEqual=o=>o;function t(o){}e.assertIs=t;function n(o){throw new Error}e.assertNever=n,e.arrayToEnum=o=>{const s={};for(const i of o)s[i]=i;return s},e.getValidEnumValues=o=>{const s=e.objectKeys(o).filter(a=>typeof o[o[a]]!="number"),i={};for(const a of s)i[a]=o[a];return e.objectValues(i)},e.objectValues=o=>e.objectKeys(o).map(function(s){return o[s]}),e.objectKeys=typeof Object.keys=="function"?o=>Object.keys(o):o=>{const s=[];for(const i in o)Object.prototype.hasOwnProperty.call(o,i)&&s.push(i);return s},e.find=(o,s)=>{for(const i of o)if(s(i))return i},e.isInteger=typeof Number.isInteger=="function"?o=>Number.isInteger(o):o=>typeof o=="number"&&isFinite(o)&&Math.floor(o)===o;function r(o,s=" | "){return o.map(i=>typeof i=="string"?`'${i}'`:i).join(s)}e.joinValues=r,e.jsonStringifyReplacer=(o,s)=>typeof s=="bigint"?s.toString():s})(ne||(ne={}));var ku;(function(e){e.mergeShapes=(t,n)=>({...t,...n})})(ku||(ku={}));const U=ne.arrayToEnum(["string","nan","number","integer","float","boolean","date","bigint","symbol","function","undefined","null","array","object","unknown","promise","void","never","map","set"]),Xt=e=>{switch(typeof e){case"undefined":return U.undefined;case"string":return U.string;case"number":return isNaN(e)?U.nan:U.number;case"boolean":return U.boolean;case"function":return U.function;case"bigint":return U.bigint;case"symbol":return U.symbol;case"object":return Array.isArray(e)?U.array:e===null?U.null:e.then&&typeof e.then=="function"&&e.catch&&typeof e.catch=="function"?U.promise:typeof Map<"u"&&e instanceof Map?U.map:typeof Set<"u"&&e instanceof Set?U.set:typeof Date<"u"&&e instanceof Date?U.date:U.object;default:return U.unknown}},A=ne.arrayToEnum(["invalid_type","invalid_literal","custom","invalid_union","invalid_union_discriminator","invalid_enum_value","unrecognized_keys","invalid_arguments","invalid_return_type","invalid_date","invalid_string","too_small","too_big","invalid_intersection_types","not_multiple_of","not_finite"]),Qw=e=>JSON.stringify(e,null,2).replace(/"([^"]+)":/g,"$1:");class rt extends Error{get errors(){return this.issues}constructor(t){super(),this.issues=[],this.addIssue=r=>{this.issues=[...this.issues,r]},this.addIssues=(r=[])=>{this.issues=[...this.issues,...r]};const n=new.target.prototype;Object.setPrototypeOf?Object.setPrototypeOf(this,n):this.__proto__=n,this.name="ZodError",this.issues=t}format(t){const n=t||function(s){return s.message},r={_errors:[]},o=s=>{for(const i of s.issues)if(i.code==="invalid_union")i.unionErrors.map(o);else if(i.code==="invalid_return_type")o(i.returnTypeError);else if(i.code==="invalid_arguments")o(i.argumentsError);else if(i.path.length===0)r._errors.push(n(i));else{let a=r,l=0;for(;l<i.path.length;){const u=i.path[l];l===i.path.length-1?(a[u]=a[u]||{_errors:[]},a[u]._errors.push(n(i))):a[u]=a[u]||{_errors:[]},a=a[u],l++}}};return o(this),r}static assert(t){if(!(t instanceof rt))throw new Error(`Not a ZodError: ${t}`)}toString(){return this.message}get message(){return JSON.stringify(this.issues,ne.jsonStringifyReplacer,2)}get isEmpty(){return this.issues.length===0}flatten(t=n=>n.message){const n={},r=[];for(const o of this.issues)o.path.length>0?(n[o.path[0]]=n[o.path[0]]||[],n[o.path[0]].push(t(o))):r.push(t(o));return{formErrors:r,fieldErrors:n}}get formErrors(){return this.flatten()}}rt.create=e=>new rt(e);const ro=(e,t)=>{let n;switch(e.code){case A.invalid_type:e.received===U.undefined?n="Required":n=`Expected ${e.expected}, received ${e.received}`;break;case A.invalid_literal:n=`Invalid literal value, expected ${JSON.stringify(e.expected,ne.jsonStringifyReplacer)}`;break;case A.unrecognized_keys:n=`Unrecognized key(s) in object: ${ne.joinValues(e.keys,", ")}`;break;case A.invalid_union:n="Invalid input";break;case A.invalid_union_discriminator:n=`Invalid discriminator value. Expected ${ne.joinValues(e.options)}`;break;case A.invalid_enum_value:n=`Invalid enum value. Expected ${ne.joinValues(e.options)}, received '${e.received}'`;break;case A.invalid_arguments:n="Invalid function arguments";break;case A.invalid_return_type:n="Invalid function return type";break;case A.invalid_date:n="Invalid date";break;case A.invalid_string:typeof e.validation=="object"?"includes"in e.validation?(n=`Invalid input: must include "${e.validation.includes}"`,typeof e.validation.position=="number"&&(n=`${n} at one or more positions greater than or equal to ${e.validation.position}`)):"startsWith"in e.validation?n=`Invalid input: must start with "${e.validation.startsWith}"`:"endsWith"in e.validation?n=`Invalid input: must end with "${e.validation.endsWith}"`:ne.assertNever(e.validation):e.validation!=="regex"?n=`Invalid ${e.validation}`:n="Invalid";break;case A.too_small:e.type==="array"?n=`Array must contain ${e.exact?"exactly":e.inclusive?"at least":"more than"} ${e.minimum} element(s)`:e.type==="string"?n=`String must contain ${e.exact?"exactly":e.inclusive?"at least":"over"} ${e.minimum} character(s)`:e.type==="number"?n=`Number must be ${e.exact?"exactly equal to ":e.inclusive?"greater than or equal to ":"greater than "}${e.minimum}`:e.type==="date"?n=`Date must be ${e.exact?"exactly equal to ":e.inclusive?"greater than or equal to ":"greater than "}${new Date(Number(e.minimum))}`:n="Invalid input";break;case A.too_big:e.type==="array"?n=`Array must contain ${e.exact?"exactly":e.inclusive?"at most":"less than"} ${e.maximum} element(s)`:e.type==="string"?n=`String must contain ${e.exact?"exactly":e.inclusive?"at most":"under"} ${e.maximum} character(s)`:e.type==="number"?n=`Number must be ${e.exact?"exactly":e.inclusive?"less than or equal to":"less than"} ${e.maximum}`:e.type==="bigint"?n=`BigInt must be ${e.exact?"exactly":e.inclusive?"less than or equal to":"less than"} ${e.maximum}`:e.type==="date"?n=`Date must be ${e.exact?"exactly":e.inclusive?"smaller than or equal to":"smaller than"} ${new Date(Number(e.maximum))}`:n="Invalid input";break;case A.custom:n="Invalid input";break;case A.invalid_intersection_types:n="Intersection results could not be merged";break;case A.not_multiple_of:n=`Number must be a multiple of ${e.multipleOf}`;break;case A.not_finite:n="Number must be finite";break;default:n=t.defaultError,ne.assertNever(e)}return{message:n}};let gm=ro;function Xw(e){gm=e}function Ki(){return gm}const Qi=e=>{const{data:t,path:n,errorMaps:r,issueData:o}=e,s=[...n,...o.path||[]],i={...o,path:s};if(o.message!==void 0)return{...o,path:s,message:o.message};let a="";const l=r.filter(u=>!!u).slice().reverse();for(const u of l)a=u(i,{data:t,defaultError:a}).message;return{...o,path:s,message:a}},Jw=[];function F(e,t){const n=Ki(),r=Qi({issueData:t,data:e.data,path:e.path,errorMaps:[e.common.contextualErrorMap,e.schemaErrorMap,n,n===ro?void 0:ro].filter(o=>!!o)});e.common.issues.push(r)}class Fe{constructor(){this.value="valid"}dirty(){this.value==="valid"&&(this.value="dirty")}abort(){this.value!=="aborted"&&(this.value="aborted")}static mergeArray(t,n){const r=[];for(const o of n){if(o.status==="aborted")return K;o.status==="dirty"&&t.dirty(),r.push(o.value)}return{status:t.value,value:r}}static async mergeObjectAsync(t,n){const r=[];for(const o of n){const s=await o.key,i=await o.value;r.push({key:s,value:i})}return Fe.mergeObjectSync(t,r)}static mergeObjectSync(t,n){const r={};for(const o of n){const{key:s,value:i}=o;if(s.status==="aborted"||i.status==="aborted")return K;s.status==="dirty"&&t.dirty(),i.status==="dirty"&&t.dirty(),s.value!=="__proto__"&&(typeof i.value<"u"||o.alwaysSet)&&(r[s.value]=i.value)}return{status:t.value,value:r}}}const K=Object.freeze({status:"aborted"}),Lr=e=>({status:"dirty",value:e}),Ue=e=>({status:"valid",value:e}),Su=e=>e.status==="aborted",Eu=e=>e.status==="dirty",dr=e=>e.status==="valid",us=e=>typeof Promise<"u"&&e instanceof Promise;function Xi(e,t,n,r){if(typeof t=="function"?e!==t||!0:!t.has(e))throw new TypeError("Cannot read private member from an object whose class did not declare it");return t.get(e)}function ym(e,t,n,r,o){if(typeof t=="function"?e!==t||!0:!t.has(e))throw new TypeError("Cannot write private member to an object whose class did not declare it");return t.set(e,n),n}var H;(function(e){e.errToObj=t=>typeof t=="string"?{message:t}:t||{},e.toString=t=>typeof t=="string"?t:t==null?void 0:t.message})(H||(H={}));var Po,Ao;class Vt{constructor(t,n,r,o){this._cachedPath=[],this.parent=t,this.data=n,this._path=r,this._key=o}get path(){return this._cachedPath.length||(this._key instanceof Array?this._cachedPath.push(...this._path,...this._key):this._cachedPath.push(...this._path,this._key)),this._cachedPath}}const Ef=(e,t)=>{if(dr(t))return{success:!0,data:t.value};if(!e.common.issues.length)throw new Error("Validation failed but no issues detected.");return{success:!1,get error(){if(this._error)return this._error;const n=new rt(e.common.issues);return this._error=n,this._error}}};function J(e){if(!e)return{};const{errorMap:t,invalid_type_error:n,required_error:r,description:o}=e;if(t&&(n||r))throw new Error(`Can't use "invalid_type_error" or "required_error" in conjunction with custom error map.`);return t?{errorMap:t,description:o}:{errorMap:(i,a)=>{var l,u;const{message:d}=e;return i.code==="invalid_enum_value"?{message:d??a.defaultError}:typeof a.data>"u"?{message:(l=d??r)!==null&&l!==void 0?l:a.defaultError}:i.code!=="invalid_type"?{message:a.defaultError}:{message:(u=d??n)!==null&&u!==void 0?u:a.defaultError}},description:o}}class ee{get description(){return this._def.description}_getType(t){return Xt(t.data)}_getOrReturnCtx(t,n){return n||{common:t.parent.common,data:t.data,parsedType:Xt(t.data),schemaErrorMap:this._def.errorMap,path:t.path,parent:t.parent}}_processInputParams(t){return{status:new Fe,ctx:{common:t.parent.common,data:t.data,parsedType:Xt(t.data),schemaErrorMap:this._def.errorMap,path:t.path,parent:t.parent}}}_parseSync(t){const n=this._parse(t);if(us(n))throw new Error("Synchronous parse encountered promise.");return n}_parseAsync(t){const n=this._parse(t);return Promise.resolve(n)}parse(t,n){const r=this.safeParse(t,n);if(r.success)return r.data;throw r.error}safeParse(t,n){var r;const o={common:{issues:[],async:(r=n==null?void 0:n.async)!==null&&r!==void 0?r:!1,contextualErrorMap:n==null?void 0:n.errorMap},path:(n==null?void 0:n.path)||[],schemaErrorMap:this._def.errorMap,parent:null,data:t,parsedType:Xt(t)},s=this._parseSync({data:t,path:o.path,parent:o});return Ef(o,s)}"~validate"(t){var n,r;const o={common:{issues:[],async:!!this["~standard"].async},path:[],schemaErrorMap:this._def.errorMap,parent:null,data:t,parsedType:Xt(t)};if(!this["~standard"].async)try{const s=this._parseSync({data:t,path:[],parent:o});return dr(s)?{value:s.value}:{issues:o.common.issues}}catch(s){!((r=(n=s==null?void 0:s.message)===null||n===void 0?void 0:n.toLowerCase())===null||r===void 0)&&r.includes("encountered")&&(this["~standard"].async=!0),o.common={issues:[],async:!0}}return this._parseAsync({data:t,path:[],parent:o}).then(s=>dr(s)?{value:s.value}:{issues:o.common.issues})}async parseAsync(t,n){const r=await this.safeParseAsync(t,n);if(r.success)return r.data;throw r.error}async safeParseAsync(t,n){const r={common:{issues:[],contextualErrorMap:n==null?void 0:n.errorMap,async:!0},path:(n==null?void 0:n.path)||[],schemaErrorMap:this._def.errorMap,parent:null,data:t,parsedType:Xt(t)},o=this._parse({data:t,path:r.path,parent:r}),s=await(us(o)?o:Promise.resolve(o));return Ef(r,s)}refine(t,n){const r=o=>typeof n=="string"||typeof n>"u"?{message:n}:typeof n=="function"?n(o):n;return this._refinement((o,s)=>{const i=t(o),a=()=>s.addIssue({code:A.custom,...r(o)});return typeof Promise<"u"&&i instanceof Promise?i.then(l=>l?!0:(a(),!1)):i?!0:(a(),!1)})}refinement(t,n){return this._refinement((r,o)=>t(r)?!0:(o.addIssue(typeof n=="function"?n(r,o):n),!1))}_refinement(t){return new Nt({schema:this,typeName:Y.ZodEffects,effect:{type:"refinement",refinement:t}})}superRefine(t){return this._refinement(t)}constructor(t){this.spa=this.safeParseAsync,this._def=t,this.parse=this.parse.bind(this),this.safeParse=this.safeParse.bind(this),this.parseAsync=this.parseAsync.bind(this),this.safeParseAsync=this.safeParseAsync.bind(this),this.spa=this.spa.bind(this),this.refine=this.refine.bind(this),this.refinement=this.refinement.bind(this),this.superRefine=this.superRefine.bind(this),this.optional=this.optional.bind(this),this.nullable=this.nullable.bind(this),this.nullish=this.nullish.bind(this),this.array=this.array.bind(this),this.promise=this.promise.bind(this),this.or=this.or.bind(this),this.and=this.and.bind(this),this.transform=this.transform.bind(this),this.brand=this.brand.bind(this),this.default=this.default.bind(this),this.catch=this.catch.bind(this),this.describe=this.describe.bind(this),this.pipe=this.pipe.bind(this),this.readonly=this.readonly.bind(this),this.isNullable=this.isNullable.bind(this),this.isOptional=this.isOptional.bind(this),this["~standard"]={version:1,vendor:"zod",validate:n=>this["~validate"](n)}}optional(){return $t.create(this,this._def)}nullable(){return Fn.create(this,this._def)}nullish(){return this.nullable().optional()}array(){return Et.create(this)}promise(){return so.create(this,this._def)}or(t){return ps.create([this,t],this._def)}and(t){return hs.create(this,t,this._def)}transform(t){return new Nt({...J(this._def),schema:this,typeName:Y.ZodEffects,effect:{type:"transform",transform:t}})}default(t){const n=typeof t=="function"?t:()=>t;return new xs({...J(this._def),innerType:this,defaultValue:n,typeName:Y.ZodDefault})}brand(){return new Ac({typeName:Y.ZodBranded,type:this,...J(this._def)})}catch(t){const n=typeof t=="function"?t:()=>t;return new ws({...J(this._def),innerType:this,catchValue:n,typeName:Y.ZodCatch})}describe(t){const n=this.constructor;return new n({...this._def,description:t})}pipe(t){return Ms.create(this,t)}readonly(){return _s.create(this)}isOptional(){return this.safeParse(void 0).success}isNullable(){return this.safeParse(null).success}}const qw=/^c[^\s-]{8,}$/i,e0=/^[0-9a-z]+$/,t0=/^[0-9A-HJKMNP-TV-Z]{26}$/i,n0=/^[0-9a-fA-F]{8}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{12}$/i,r0=/^[a-z0-9_-]{21}$/i,o0=/^[A-Za-z0-9-_]+\.[A-Za-z0-9-_]+\.[A-Za-z0-9-_]*$/,s0=/^[-+]?P(?!$)(?:(?:[-+]?\d+Y)|(?:[-+]?\d+[.,]\d+Y$))?(?:(?:[-+]?\d+M)|(?:[-+]?\d+[.,]\d+M$))?(?:(?:[-+]?\d+W)|(?:[-+]?\d+[.,]\d+W$))?(?:(?:[-+]?\d+D)|(?:[-+]?\d+[.,]\d+D$))?(?:T(?=[\d+-])(?:(?:[-+]?\d+H)|(?:[-+]?\d+[.,]\d+H$))?(?:(?:[-+]?\d+M)|(?:[-+]?\d+[.,]\d+M$))?(?:[-+]?\d+(?:[.,]\d+)?S)?)??$/,i0=/^(?!\.)(?!.*\.\.)([A-Z0-9_'+\-\.]*)[A-Z0-9_+-]@([A-Z0-9][A-Z0-9\-]*\.)+[A-Z]{2,}$/i,a0="^(\\p{Extended_Pictographic}|\\p{Emoji_Component})+$";let gl;const l0=/^(?:(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\.){3}(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])$/,u0=/^(?:(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\.){3}(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\/(3[0-2]|[12]?[0-9])$/,c0=/^(([0-9a-fA-F]{1,4}:){7,7}[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,7}:|([0-9a-fA-F]{1,4}:){1,6}:[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,5}(:[0-9a-fA-F]{1,4}){1,2}|([0-9a-fA-F]{1,4}:){1,4}(:[0-9a-fA-F]{1,4}){1,3}|([0-9a-fA-F]{1,4}:){1,3}(:[0-9a-fA-F]{1,4}){1,4}|([0-9a-fA-F]{1,4}:){1,2}(:[0-9a-fA-F]{1,4}){1,5}|[0-9a-fA-F]{1,4}:((:[0-9a-fA-F]{1,4}){1,6})|:((:[0-9a-fA-F]{1,4}){1,7}|:)|fe80:(:[0-9a-fA-F]{0,4}){0,4}%[0-9a-zA-Z]{1,}|::(ffff(:0{1,4}){0,1}:){0,1}((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])|([0-9a-fA-F]{1,4}:){1,4}:((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9]))$/,d0=/^(([0-9a-fA-F]{1,4}:){7,7}[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,7}:|([0-9a-fA-F]{1,4}:){1,6}:[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,5}(:[0-9a-fA-F]{1,4}){1,2}|([0-9a-fA-F]{1,4}:){1,4}(:[0-9a-fA-F]{1,4}){1,3}|([0-9a-fA-F]{1,4}:){1,3}(:[0-9a-fA-F]{1,4}){1,4}|([0-9a-fA-F]{1,4}:){1,2}(:[0-9a-fA-F]{1,4}){1,5}|[0-9a-fA-F]{1,4}:((:[0-9a-fA-F]{1,4}){1,6})|:((:[0-9a-fA-F]{1,4}){1,7}|:)|fe80:(:[0-9a-fA-F]{0,4}){0,4}%[0-9a-zA-Z]{1,}|::(ffff(:0{1,4}){0,1}:){0,1}((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])|([0-9a-fA-F]{1,4}:){1,4}:((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9]))\/(12[0-8]|1[01][0-9]|[1-9]?[0-9])$/,f0=/^([0-9a-zA-Z+/]{4})*(([0-9a-zA-Z+/]{2}==)|([0-9a-zA-Z+/]{3}=))?$/,p0=/^([0-9a-zA-Z-_]{4})*(([0-9a-zA-Z-_]{2}(==)?)|([0-9a-zA-Z-_]{3}(=)?))?$/,xm="((\\d\\d[2468][048]|\\d\\d[13579][26]|\\d\\d0[48]|[02468][048]00|[13579][26]00)-02-29|\\d{4}-((0[13578]|1[02])-(0[1-9]|[12]\\d|3[01])|(0[469]|11)-(0[1-9]|[12]\\d|30)|(02)-(0[1-9]|1\\d|2[0-8])))",h0=new RegExp(`^${xm}$`);function wm(e){let t="([01]\\d|2[0-3]):[0-5]\\d:[0-5]\\d";return e.precision?t=`${t}\\.\\d{${e.precision}}`:e.precision==null&&(t=`${t}(\\.\\d+)?`),t}function m0(e){return new RegExp(`^${wm(e)}$`)}function _m(e){let t=`${xm}T${wm(e)}`;const n=[];return n.push(e.local?"Z?":"Z"),e.offset&&n.push("([+-]\\d{2}:?\\d{2})"),t=`${t}(${n.join("|")})`,new RegExp(`^${t}$`)}function v0(e,t){return!!((t==="v4"||!t)&&l0.test(e)||(t==="v6"||!t)&&c0.test(e))}function g0(e,t){if(!o0.test(e))return!1;try{const[n]=e.split("."),r=n.replace(/-/g,"+").replace(/_/g,"/").padEnd(n.length+(4-n.length%4)%4,"="),o=JSON.parse(atob(r));return!(typeof o!="object"||o===null||!o.typ||!o.alg||t&&o.alg!==t)}catch{return!1}}function y0(e,t){return!!((t==="v4"||!t)&&u0.test(e)||(t==="v6"||!t)&&d0.test(e))}class Ct extends ee{_parse(t){if(this._def.coerce&&(t.data=String(t.data)),this._getType(t)!==U.string){const s=this._getOrReturnCtx(t);return F(s,{code:A.invalid_type,expected:U.string,received:s.parsedType}),K}const r=new Fe;let o;for(const s of this._def.checks)if(s.kind==="min")t.data.length<s.value&&(o=this._getOrReturnCtx(t,o),F(o,{code:A.too_small,minimum:s.value,type:"string",inclusive:!0,exact:!1,message:s.message}),r.dirty());else if(s.kind==="max")t.data.length>s.value&&(o=this._getOrReturnCtx(t,o),F(o,{code:A.too_big,maximum:s.value,type:"string",inclusive:!0,exact:!1,message:s.message}),r.dirty());else if(s.kind==="length"){const i=t.data.length>s.value,a=t.data.length<s.value;(i||a)&&(o=this._getOrReturnCtx(t,o),i?F(o,{code:A.too_big,maximum:s.value,type:"string",inclusive:!0,exact:!0,message:s.message}):a&&F(o,{code:A.too_small,minimum:s.value,type:"string",inclusive:!0,exact:!0,message:s.message}),r.dirty())}else if(s.kind==="email")i0.test(t.data)||(o=this._getOrReturnCtx(t,o),F(o,{validation:"email",code:A.invalid_string,message:s.message}),r.dirty());else if(s.kind==="emoji")gl||(gl=new RegExp(a0,"u")),gl.test(t.data)||(o=this._getOrReturnCtx(t,o),F(o,{validation:"emoji",code:A.invalid_string,message:s.message}),r.dirty());else if(s.kind==="uuid")n0.test(t.data)||(o=this._getOrReturnCtx(t,o),F(o,{validation:"uuid",code:A.invalid_string,message:s.message}),r.dirty());else if(s.kind==="nanoid")r0.test(t.data)||(o=this._getOrReturnCtx(t,o),F(o,{validation:"nanoid",code:A.invalid_string,message:s.message}),r.dirty());else if(s.kind==="cuid")qw.test(t.data)||(o=this._getOrReturnCtx(t,o),F(o,{validation:"cuid",code:A.invalid_string,message:s.message}),r.dirty());else if(s.kind==="cuid2")e0.test(t.data)||(o=this._getOrReturnCtx(t,o),F(o,{validation:"cuid2",code:A.invalid_string,message:s.message}),r.dirty());else if(s.kind==="ulid")t0.test(t.data)||(o=this._getOrReturnCtx(t,o),F(o,{validation:"ulid",code:A.invalid_string,message:s.message}),r.dirty());else if(s.kind==="url")try{new URL(t.data)}catch{o=this._getOrReturnCtx(t,o),F(o,{validation:"url",code:A.invalid_string,message:s.message}),r.dirty()}else s.kind==="regex"?(s.regex.lastIndex=0,s.regex.test(t.data)||(o=this._getOrReturnCtx(t,o),F(o,{validation:"regex",code:A.invalid_string,message:s.message}),r.dirty())):s.kind==="trim"?t.data=t.data.trim():s.kind==="includes"?t.data.includes(s.value,s.position)||(o=this._getOrReturnCtx(t,o),F(o,{code:A.invalid_string,validation:{includes:s.value,position:s.position},message:s.message}),r.dirty()):s.kind==="toLowerCase"?t.data=t.data.toLowerCase():s.kind==="toUpperCase"?t.data=t.data.toUpperCase():s.kind==="startsWith"?t.data.startsWith(s.value)||(o=this._getOrReturnCtx(t,o),F(o,{code:A.invalid_string,validation:{startsWith:s.value},message:s.message}),r.dirty()):s.kind==="endsWith"?t.data.endsWith(s.value)||(o=this._getOrReturnCtx(t,o),F(o,{code:A.invalid_string,validation:{endsWith:s.value},message:s.message}),r.dirty()):s.kind==="datetime"?_m(s).test(t.data)||(o=this._getOrReturnCtx(t,o),F(o,{code:A.invalid_string,validation:"datetime",message:s.message}),r.dirty()):s.kind==="date"?h0.test(t.data)||(o=this._getOrReturnCtx(t,o),F(o,{code:A.invalid_string,validation:"date",message:s.message}),r.dirty()):s.kind==="time"?m0(s).test(t.data)||(o=this._getOrReturnCtx(t,o),F(o,{code:A.invalid_string,validation:"time",message:s.message}),r.dirty()):s.kind==="duration"?s0.test(t.data)||(o=this._getOrReturnCtx(t,o),F(o,{validation:"duration",code:A.invalid_string,message:s.message}),r.dirty()):s.kind==="ip"?v0(t.data,s.version)||(o=this._getOrReturnCtx(t,o),F(o,{validation:"ip",code:A.invalid_string,message:s.message}),r.dirty()):s.kind==="jwt"?g0(t.data,s.alg)||(o=this._getOrReturnCtx(t,o),F(o,{validation:"jwt",code:A.invalid_string,message:s.message}),r.dirty()):s.kind==="cidr"?y0(t.data,s.version)||(o=this._getOrReturnCtx(t,o),F(o,{validation:"cidr",code:A.invalid_string,message:s.message}),r.dirty()):s.kind==="base64"?f0.test(t.data)||(o=this._getOrReturnCtx(t,o),F(o,{validation:"base64",code:A.invalid_string,message:s.message}),r.dirty()):s.kind==="base64url"?p0.test(t.data)||(o=this._getOrReturnCtx(t,o),F(o,{validation:"base64url",code:A.invalid_string,message:s.message}),r.dirty()):ne.assertNever(s);return{status:r.value,value:t.data}}_regex(t,n,r){return this.refinement(o=>t.test(o),{validation:n,code:A.invalid_string,...H.errToObj(r)})}_addCheck(t){return new Ct({...this._def,checks:[...this._def.checks,t]})}email(t){return this._addCheck({kind:"email",...H.errToObj(t)})}url(t){return this._addCheck({kind:"url",...H.errToObj(t)})}emoji(t){return this._addCheck({kind:"emoji",...H.errToObj(t)})}uuid(t){return this._addCheck({kind:"uuid",...H.errToObj(t)})}nanoid(t){return this._addCheck({kind:"nanoid",...H.errToObj(t)})}cuid(t){return this._addCheck({kind:"cuid",...H.errToObj(t)})}cuid2(t){return this._addCheck({kind:"cuid2",...H.errToObj(t)})}ulid(t){return this._addCheck({kind:"ulid",...H.errToObj(t)})}base64(t){return this._addCheck({kind:"base64",...H.errToObj(t)})}base64url(t){return this._addCheck({kind:"base64url",...H.errToObj(t)})}jwt(t){return this._addCheck({kind:"jwt",...H.errToObj(t)})}ip(t){return this._addCheck({kind:"ip",...H.errToObj(t)})}cidr(t){return this._addCheck({kind:"cidr",...H.errToObj(t)})}datetime(t){var n,r;return typeof t=="string"?this._addCheck({kind:"datetime",precision:null,offset:!1,local:!1,message:t}):this._addCheck({kind:"datetime",precision:typeof(t==null?void 0:t.precision)>"u"?null:t==null?void 0:t.precision,offset:(n=t==null?void 0:t.offset)!==null&&n!==void 0?n:!1,local:(r=t==null?void 0:t.local)!==null&&r!==void 0?r:!1,...H.errToObj(t==null?void 0:t.message)})}date(t){return this._addCheck({kind:"date",message:t})}time(t){return typeof t=="string"?this._addCheck({kind:"time",precision:null,message:t}):this._addCheck({kind:"time",precision:typeof(t==null?void 0:t.precision)>"u"?null:t==null?void 0:t.precision,...H.errToObj(t==null?void 0:t.message)})}duration(t){return this._addCheck({kind:"duration",...H.errToObj(t)})}regex(t,n){return this._addCheck({kind:"regex",regex:t,...H.errToObj(n)})}includes(t,n){return this._addCheck({kind:"includes",value:t,position:n==null?void 0:n.position,...H.errToObj(n==null?void 0:n.message)})}startsWith(t,n){return this._addCheck({kind:"startsWith",value:t,...H.errToObj(n)})}endsWith(t,n){return this._addCheck({kind:"endsWith",value:t,...H.errToObj(n)})}min(t,n){return this._addCheck({kind:"min",value:t,...H.errToObj(n)})}max(t,n){return this._addCheck({kind:"max",value:t,...H.errToObj(n)})}length(t,n){return this._addCheck({kind:"length",value:t,...H.errToObj(n)})}nonempty(t){return this.min(1,H.errToObj(t))}trim(){return new Ct({...this._def,checks:[...this._def.checks,{kind:"trim"}]})}toLowerCase(){return new Ct({...this._def,checks:[...this._def.checks,{kind:"toLowerCase"}]})}toUpperCase(){return new Ct({...this._def,checks:[...this._def.checks,{kind:"toUpperCase"}]})}get isDatetime(){return!!this._def.checks.find(t=>t.kind==="datetime")}get isDate(){return!!this._def.checks.find(t=>t.kind==="date")}get isTime(){return!!this._def.checks.find(t=>t.kind==="time")}get isDuration(){return!!this._def.checks.find(t=>t.kind==="duration")}get isEmail(){return!!this._def.checks.find(t=>t.kind==="email")}get isURL(){return!!this._def.checks.find(t=>t.kind==="url")}get isEmoji(){return!!this._def.checks.find(t=>t.kind==="emoji")}get isUUID(){return!!this._def.checks.find(t=>t.kind==="uuid")}get isNANOID(){return!!this._def.checks.find(t=>t.kind==="nanoid")}get isCUID(){return!!this._def.checks.find(t=>t.kind==="cuid")}get isCUID2(){return!!this._def.checks.find(t=>t.kind==="cuid2")}get isULID(){return!!this._def.checks.find(t=>t.kind==="ulid")}get isIP(){return!!this._def.checks.find(t=>t.kind==="ip")}get isCIDR(){return!!this._def.checks.find(t=>t.kind==="cidr")}get isBase64(){return!!this._def.checks.find(t=>t.kind==="base64")}get isBase64url(){return!!this._def.checks.find(t=>t.kind==="base64url")}get minLength(){let t=null;for(const n of this._def.checks)n.kind==="min"&&(t===null||n.value>t)&&(t=n.value);return t}get maxLength(){let t=null;for(const n of this._def.checks)n.kind==="max"&&(t===null||n.value<t)&&(t=n.value);return t}}Ct.create=e=>{var t;return new Ct({checks:[],typeName:Y.ZodString,coerce:(t=e==null?void 0:e.coerce)!==null&&t!==void 0?t:!1,...J(e)})};function x0(e,t){const n=(e.toString().split(".")[1]||"").length,r=(t.toString().split(".")[1]||"").length,o=n>r?n:r,s=parseInt(e.toFixed(o).replace(".","")),i=parseInt(t.toFixed(o).replace(".",""));return s%i/Math.pow(10,o)}class Ln extends ee{constructor(){super(...arguments),this.min=this.gte,this.max=this.lte,this.step=this.multipleOf}_parse(t){if(this._def.coerce&&(t.data=Number(t.data)),this._getType(t)!==U.number){const s=this._getOrReturnCtx(t);return F(s,{code:A.invalid_type,expected:U.number,received:s.parsedType}),K}let r;const o=new Fe;for(const s of this._def.checks)s.kind==="int"?ne.isInteger(t.data)||(r=this._getOrReturnCtx(t,r),F(r,{code:A.invalid_type,expected:"integer",received:"float",message:s.message}),o.dirty()):s.kind==="min"?(s.inclusive?t.data<s.value:t.data<=s.value)&&(r=this._getOrReturnCtx(t,r),F(r,{code:A.too_small,minimum:s.value,type:"number",inclusive:s.inclusive,exact:!1,message:s.message}),o.dirty()):s.kind==="max"?(s.inclusive?t.data>s.value:t.data>=s.value)&&(r=this._getOrReturnCtx(t,r),F(r,{code:A.too_big,maximum:s.value,type:"number",inclusive:s.inclusive,exact:!1,message:s.message}),o.dirty()):s.kind==="multipleOf"?x0(t.data,s.value)!==0&&(r=this._getOrReturnCtx(t,r),F(r,{code:A.not_multiple_of,multipleOf:s.value,message:s.message}),o.dirty()):s.kind==="finite"?Number.isFinite(t.data)||(r=this._getOrReturnCtx(t,r),F(r,{code:A.not_finite,message:s.message}),o.dirty()):ne.assertNever(s);return{status:o.value,value:t.data}}gte(t,n){return this.setLimit("min",t,!0,H.toString(n))}gt(t,n){return this.setLimit("min",t,!1,H.toString(n))}lte(t,n){return this.setLimit("max",t,!0,H.toString(n))}lt(t,n){return this.setLimit("max",t,!1,H.toString(n))}setLimit(t,n,r,o){return new Ln({...this._def,checks:[...this._def.checks,{kind:t,value:n,inclusive:r,message:H.toString(o)}]})}_addCheck(t){return new Ln({...this._def,checks:[...this._def.checks,t]})}int(t){return this._addCheck({kind:"int",message:H.toString(t)})}positive(t){return this._addCheck({kind:"min",value:0,inclusive:!1,message:H.toString(t)})}negative(t){return this._addCheck({kind:"max",value:0,inclusive:!1,message:H.toString(t)})}nonpositive(t){return this._addCheck({kind:"max",value:0,inclusive:!0,message:H.toString(t)})}nonnegative(t){return this._addCheck({kind:"min",value:0,inclusive:!0,message:H.toString(t)})}multipleOf(t,n){return this._addCheck({kind:"multipleOf",value:t,message:H.toString(n)})}finite(t){return this._addCheck({kind:"finite",message:H.toString(t)})}safe(t){return this._addCheck({kind:"min",inclusive:!0,value:Number.MIN_SAFE_INTEGER,message:H.toString(t)})._addCheck({kind:"max",inclusive:!0,value:Number.MAX_SAFE_INTEGER,message:H.toString(t)})}get minValue(){let t=null;for(const n of this._def.checks)n.kind==="min"&&(t===null||n.value>t)&&(t=n.value);return t}get maxValue(){let t=null;for(const n of this._def.checks)n.kind==="max"&&(t===null||n.value<t)&&(t=n.value);return t}get isInt(){return!!this._def.checks.find(t=>t.kind==="int"||t.kind==="multipleOf"&&ne.isInteger(t.value))}get isFinite(){let t=null,n=null;for(const r of this._def.checks){if(r.kind==="finite"||r.kind==="int"||r.kind==="multipleOf")return!0;r.kind==="min"?(n===null||r.value>n)&&(n=r.value):r.kind==="max"&&(t===null||r.value<t)&&(t=r.value)}return Number.isFinite(n)&&Number.isFinite(t)}}Ln.create=e=>new Ln({checks:[],typeName:Y.ZodNumber,coerce:(e==null?void 0:e.coerce)||!1,...J(e)});class $n extends ee{constructor(){super(...arguments),this.min=this.gte,this.max=this.lte}_parse(t){if(this._def.coerce)try{t.data=BigInt(t.data)}catch{return this._getInvalidInput(t)}if(this._getType(t)!==U.bigint)return this._getInvalidInput(t);let r;const o=new Fe;for(const s of this._def.checks)s.kind==="min"?(s.inclusive?t.data<s.value:t.data<=s.value)&&(r=this._getOrReturnCtx(t,r),F(r,{code:A.too_small,type:"bigint",minimum:s.value,inclusive:s.inclusive,message:s.message}),o.dirty()):s.kind==="max"?(s.inclusive?t.data>s.value:t.data>=s.value)&&(r=this._getOrReturnCtx(t,r),F(r,{code:A.too_big,type:"bigint",maximum:s.value,inclusive:s.inclusive,message:s.message}),o.dirty()):s.kind==="multipleOf"?t.data%s.value!==BigInt(0)&&(r=this._getOrReturnCtx(t,r),F(r,{code:A.not_multiple_of,multipleOf:s.value,message:s.message}),o.dirty()):ne.assertNever(s);return{status:o.value,value:t.data}}_getInvalidInput(t){const n=this._getOrReturnCtx(t);return F(n,{code:A.invalid_type,expected:U.bigint,received:n.parsedType}),K}gte(t,n){return this.setLimit("min",t,!0,H.toString(n))}gt(t,n){return this.setLimit("min",t,!1,H.toString(n))}lte(t,n){return this.setLimit("max",t,!0,H.toString(n))}lt(t,n){return this.setLimit("max",t,!1,H.toString(n))}setLimit(t,n,r,o){return new $n({...this._def,checks:[...this._def.checks,{kind:t,value:n,inclusive:r,message:H.toString(o)}]})}_addCheck(t){return new $n({...this._def,checks:[...this._def.checks,t]})}positive(t){return this._addCheck({kind:"min",value:BigInt(0),inclusive:!1,message:H.toString(t)})}negative(t){return this._addCheck({kind:"max",value:BigInt(0),inclusive:!1,message:H.toString(t)})}nonpositive(t){return this._addCheck({kind:"max",value:BigInt(0),inclusive:!0,message:H.toString(t)})}nonnegative(t){return this._addCheck({kind:"min",value:BigInt(0),inclusive:!0,message:H.toString(t)})}multipleOf(t,n){return this._addCheck({kind:"multipleOf",value:t,message:H.toString(n)})}get minValue(){let t=null;for(const n of this._def.checks)n.kind==="min"&&(t===null||n.value>t)&&(t=n.value);return t}get maxValue(){let t=null;for(const n of this._def.checks)n.kind==="max"&&(t===null||n.value<t)&&(t=n.value);return t}}$n.create=e=>{var t;return new $n({checks:[],typeName:Y.ZodBigInt,coerce:(t=e==null?void 0:e.coerce)!==null&&t!==void 0?t:!1,...J(e)})};class cs extends ee{_parse(t){if(this._def.coerce&&(t.data=!!t.data),this._getType(t)!==U.boolean){const r=this._getOrReturnCtx(t);return F(r,{code:A.invalid_type,expected:U.boolean,received:r.parsedType}),K}return Ue(t.data)}}cs.create=e=>new cs({typeName:Y.ZodBoolean,coerce:(e==null?void 0:e.coerce)||!1,...J(e)});class fr extends ee{_parse(t){if(this._def.coerce&&(t.data=new Date(t.data)),this._getType(t)!==U.date){const s=this._getOrReturnCtx(t);return F(s,{code:A.invalid_type,expected:U.date,received:s.parsedType}),K}if(isNaN(t.data.getTime())){const s=this._getOrReturnCtx(t);return F(s,{code:A.invalid_date}),K}const r=new Fe;let o;for(const s of this._def.checks)s.kind==="min"?t.data.getTime()<s.value&&(o=this._getOrReturnCtx(t,o),F(o,{code:A.too_small,message:s.message,inclusive:!0,exact:!1,minimum:s.value,type:"date"}),r.dirty()):s.kind==="max"?t.data.getTime()>s.value&&(o=this._getOrReturnCtx(t,o),F(o,{code:A.too_big,message:s.message,inclusive:!0,exact:!1,maximum:s.value,type:"date"}),r.dirty()):ne.assertNever(s);return{status:r.value,value:new Date(t.data.getTime())}}_addCheck(t){return new fr({...this._def,checks:[...this._def.checks,t]})}min(t,n){return this._addCheck({kind:"min",value:t.getTime(),message:H.toString(n)})}max(t,n){return this._addCheck({kind:"max",value:t.getTime(),message:H.toString(n)})}get minDate(){let t=null;for(const n of this._def.checks)n.kind==="min"&&(t===null||n.value>t)&&(t=n.value);return t!=null?new Date(t):null}get maxDate(){let t=null;for(const n of this._def.checks)n.kind==="max"&&(t===null||n.value<t)&&(t=n.value);return t!=null?new Date(t):null}}fr.create=e=>new fr({checks:[],coerce:(e==null?void 0:e.coerce)||!1,typeName:Y.ZodDate,...J(e)});class Ji extends ee{_parse(t){if(this._getType(t)!==U.symbol){const r=this._getOrReturnCtx(t);return F(r,{code:A.invalid_type,expected:U.symbol,received:r.parsedType}),K}return Ue(t.data)}}Ji.create=e=>new Ji({typeName:Y.ZodSymbol,...J(e)});class ds extends ee{_parse(t){if(this._getType(t)!==U.undefined){const r=this._getOrReturnCtx(t);return F(r,{code:A.invalid_type,expected:U.undefined,received:r.parsedType}),K}return Ue(t.data)}}ds.create=e=>new ds({typeName:Y.ZodUndefined,...J(e)});class fs extends ee{_parse(t){if(this._getType(t)!==U.null){const r=this._getOrReturnCtx(t);return F(r,{code:A.invalid_type,expected:U.null,received:r.parsedType}),K}return Ue(t.data)}}fs.create=e=>new fs({typeName:Y.ZodNull,...J(e)});class oo extends ee{constructor(){super(...arguments),this._any=!0}_parse(t){return Ue(t.data)}}oo.create=e=>new oo({typeName:Y.ZodAny,...J(e)});class sr extends ee{constructor(){super(...arguments),this._unknown=!0}_parse(t){return Ue(t.data)}}sr.create=e=>new sr({typeName:Y.ZodUnknown,...J(e)});class un extends ee{_parse(t){const n=this._getOrReturnCtx(t);return F(n,{code:A.invalid_type,expected:U.never,received:n.parsedType}),K}}un.create=e=>new un({typeName:Y.ZodNever,...J(e)});class qi extends ee{_parse(t){if(this._getType(t)!==U.undefined){const r=this._getOrReturnCtx(t);return F(r,{code:A.invalid_type,expected:U.void,received:r.parsedType}),K}return Ue(t.data)}}qi.create=e=>new qi({typeName:Y.ZodVoid,...J(e)});class Et extends ee{_parse(t){const{ctx:n,status:r}=this._processInputParams(t),o=this._def;if(n.parsedType!==U.array)return F(n,{code:A.invalid_type,expected:U.array,received:n.parsedType}),K;if(o.exactLength!==null){const i=n.data.length>o.exactLength.value,a=n.data.length<o.exactLength.value;(i||a)&&(F(n,{code:i?A.too_big:A.too_small,minimum:a?o.exactLength.value:void 0,maximum:i?o.exactLength.value:void 0,type:"array",inclusive:!0,exact:!0,message:o.exactLength.message}),r.dirty())}if(o.minLength!==null&&n.data.length<o.minLength.value&&(F(n,{code:A.too_small,minimum:o.minLength.value,type:"array",inclusive:!0,exact:!1,message:o.minLength.message}),r.dirty()),o.maxLength!==null&&n.data.length>o.maxLength.value&&(F(n,{code:A.too_big,maximum:o.maxLength.value,type:"array",inclusive:!0,exact:!1,message:o.maxLength.message}),r.dirty()),n.common.async)return Promise.all([...n.data].map((i,a)=>o.type._parseAsync(new Vt(n,i,n.path,a)))).then(i=>Fe.mergeArray(r,i));const s=[...n.data].map((i,a)=>o.type._parseSync(new Vt(n,i,n.path,a)));return Fe.mergeArray(r,s)}get element(){return this._def.type}min(t,n){return new Et({...this._def,minLength:{value:t,message:H.toString(n)}})}max(t,n){return new Et({...this._def,maxLength:{value:t,message:H.toString(n)}})}length(t,n){return new Et({...this._def,exactLength:{value:t,message:H.toString(n)}})}nonempty(t){return this.min(1,t)}}Et.create=(e,t)=>new Et({type:e,minLength:null,maxLength:null,exactLength:null,typeName:Y.ZodArray,...J(t)});function Sr(e){if(e instanceof pe){const t={};for(const n in e.shape){const r=e.shape[n];t[n]=$t.create(Sr(r))}return new pe({...e._def,shape:()=>t})}else return e instanceof Et?new Et({...e._def,type:Sr(e.element)}):e instanceof $t?$t.create(Sr(e.unwrap())):e instanceof Fn?Fn.create(Sr(e.unwrap())):e instanceof Ut?Ut.create(e.items.map(t=>Sr(t))):e}class pe extends ee{constructor(){super(...arguments),this._cached=null,this.nonstrict=this.passthrough,this.augment=this.extend}_getCached(){if(this._cached!==null)return this._cached;const t=this._def.shape(),n=ne.objectKeys(t);return this._cached={shape:t,keys:n}}_parse(t){if(this._getType(t)!==U.object){const u=this._getOrReturnCtx(t);return F(u,{code:A.invalid_type,expected:U.object,received:u.parsedType}),K}const{status:r,ctx:o}=this._processInputParams(t),{shape:s,keys:i}=this._getCached(),a=[];if(!(this._def.catchall instanceof un&&this._def.unknownKeys==="strip"))for(const u in o.data)i.includes(u)||a.push(u);const l=[];for(const u of i){const d=s[u],f=o.data[u];l.push({key:{status:"valid",value:u},value:d._parse(new Vt(o,f,o.path,u)),alwaysSet:u in o.data})}if(this._def.catchall instanceof un){const u=this._def.unknownKeys;if(u==="passthrough")for(const d of a)l.push({key:{status:"valid",value:d},value:{status:"valid",value:o.data[d]}});else if(u==="strict")a.length>0&&(F(o,{code:A.unrecognized_keys,keys:a}),r.dirty());else if(u!=="strip")throw new Error("Internal ZodObject error: invalid unknownKeys value.")}else{const u=this._def.catchall;for(const d of a){const f=o.data[d];l.push({key:{status:"valid",value:d},value:u._parse(new Vt(o,f,o.path,d)),alwaysSet:d in o.data})}}return o.common.async?Promise.resolve().then(async()=>{const u=[];for(const d of l){const f=await d.key,p=await d.value;u.push({key:f,value:p,alwaysSet:d.alwaysSet})}return u}).then(u=>Fe.mergeObjectSync(r,u)):Fe.mergeObjectSync(r,l)}get shape(){return this._def.shape()}strict(t){return H.errToObj,new pe({...this._def,unknownKeys:"strict",...t!==void 0?{errorMap:(n,r)=>{var o,s,i,a;const l=(i=(s=(o=this._def).errorMap)===null||s===void 0?void 0:s.call(o,n,r).message)!==null&&i!==void 0?i:r.defaultError;return n.code==="unrecognized_keys"?{message:(a=H.errToObj(t).message)!==null&&a!==void 0?a:l}:{message:l}}}:{}})}strip(){return new pe({...this._def,unknownKeys:"strip"})}passthrough(){return new pe({...this._def,unknownKeys:"passthrough"})}extend(t){return new pe({...this._def,shape:()=>({...this._def.shape(),...t})})}merge(t){return new pe({unknownKeys:t._def.unknownKeys,catchall:t._def.catchall,shape:()=>({...this._def.shape(),...t._def.shape()}),typeName:Y.ZodObject})}setKey(t,n){return this.augment({[t]:n})}catchall(t){return new pe({...this._def,catchall:t})}pick(t){const n={};return ne.objectKeys(t).forEach(r=>{t[r]&&this.shape[r]&&(n[r]=this.shape[r])}),new pe({...this._def,shape:()=>n})}omit(t){const n={};return ne.objectKeys(this.shape).forEach(r=>{t[r]||(n[r]=this.shape[r])}),new pe({...this._def,shape:()=>n})}deepPartial(){return Sr(this)}partial(t){const n={};return ne.objectKeys(this.shape).forEach(r=>{const o=this.shape[r];t&&!t[r]?n[r]=o:n[r]=o.optional()}),new pe({...this._def,shape:()=>n})}required(t){const n={};return ne.objectKeys(this.shape).forEach(r=>{if(t&&!t[r])n[r]=this.shape[r];else{let s=this.shape[r];for(;s instanceof $t;)s=s._def.innerType;n[r]=s}}),new pe({...this._def,shape:()=>n})}keyof(){return Cm(ne.objectKeys(this.shape))}}pe.create=(e,t)=>new pe({shape:()=>e,unknownKeys:"strip",catchall:un.create(),typeName:Y.ZodObject,...J(t)});pe.strictCreate=(e,t)=>new pe({shape:()=>e,unknownKeys:"strict",catchall:un.create(),typeName:Y.ZodObject,...J(t)});pe.lazycreate=(e,t)=>new pe({shape:e,unknownKeys:"strip",catchall:un.create(),typeName:Y.ZodObject,...J(t)});class ps extends ee{_parse(t){const{ctx:n}=this._processInputParams(t),r=this._def.options;function o(s){for(const a of s)if(a.result.status==="valid")return a.result;for(const a of s)if(a.result.status==="dirty")return n.common.issues.push(...a.ctx.common.issues),a.result;const i=s.map(a=>new rt(a.ctx.common.issues));return F(n,{code:A.invalid_union,unionErrors:i}),K}if(n.common.async)return Promise.all(r.map(async s=>{const i={...n,common:{...n.common,issues:[]},parent:null};return{result:await s._parseAsync({data:n.data,path:n.path,parent:i}),ctx:i}})).then(o);{let s;const i=[];for(const l of r){const u={...n,common:{...n.common,issues:[]},parent:null},d=l._parseSync({data:n.data,path:n.path,parent:u});if(d.status==="valid")return d;d.status==="dirty"&&!s&&(s={result:d,ctx:u}),u.common.issues.length&&i.push(u.common.issues)}if(s)return n.common.issues.push(...s.ctx.common.issues),s.result;const a=i.map(l=>new rt(l));return F(n,{code:A.invalid_union,unionErrors:a}),K}}get options(){return this._def.options}}ps.create=(e,t)=>new ps({options:e,typeName:Y.ZodUnion,...J(t)});const Yt=e=>e instanceof vs?Yt(e.schema):e instanceof Nt?Yt(e.innerType()):e instanceof gs?[e.value]:e instanceof zn?e.options:e instanceof ys?ne.objectValues(e.enum):e instanceof xs?Yt(e._def.innerType):e instanceof ds?[void 0]:e instanceof fs?[null]:e instanceof $t?[void 0,...Yt(e.unwrap())]:e instanceof Fn?[null,...Yt(e.unwrap())]:e instanceof Ac||e instanceof _s?Yt(e.unwrap()):e instanceof ws?Yt(e._def.innerType):[];class Na extends ee{_parse(t){const{ctx:n}=this._processInputParams(t);if(n.parsedType!==U.object)return F(n,{code:A.invalid_type,expected:U.object,received:n.parsedType}),K;const r=this.discriminator,o=n.data[r],s=this.optionsMap.get(o);return s?n.common.async?s._parseAsync({data:n.data,path:n.path,parent:n}):s._parseSync({data:n.data,path:n.path,parent:n}):(F(n,{code:A.invalid_union_discriminator,options:Array.from(this.optionsMap.keys()),path:[r]}),K)}get discriminator(){return this._def.discriminator}get options(){return this._def.options}get optionsMap(){return this._def.optionsMap}static create(t,n,r){const o=new Map;for(const s of n){const i=Yt(s.shape[t]);if(!i.length)throw new Error(`A discriminator value for key \`${t}\` could not be extracted from all schema options`);for(const a of i){if(o.has(a))throw new Error(`Discriminator property ${String(t)} has duplicate value ${String(a)}`);o.set(a,s)}}return new Na({typeName:Y.ZodDiscriminatedUnion,discriminator:t,options:n,optionsMap:o,...J(r)})}}function bu(e,t){const n=Xt(e),r=Xt(t);if(e===t)return{valid:!0,data:e};if(n===U.object&&r===U.object){const o=ne.objectKeys(t),s=ne.objectKeys(e).filter(a=>o.indexOf(a)!==-1),i={...e,...t};for(const a of s){const l=bu(e[a],t[a]);if(!l.valid)return{valid:!1};i[a]=l.data}return{valid:!0,data:i}}else if(n===U.array&&r===U.array){if(e.length!==t.length)return{valid:!1};const o=[];for(let s=0;s<e.length;s++){const i=e[s],a=t[s],l=bu(i,a);if(!l.valid)return{valid:!1};o.push(l.data)}return{valid:!0,data:o}}else return n===U.date&&r===U.date&&+e==+t?{valid:!0,data:e}:{valid:!1}}class hs extends ee{_parse(t){const{status:n,ctx:r}=this._processInputParams(t),o=(s,i)=>{if(Su(s)||Su(i))return K;const a=bu(s.value,i.value);return a.valid?((Eu(s)||Eu(i))&&n.dirty(),{status:n.value,value:a.data}):(F(r,{code:A.invalid_intersection_types}),K)};return r.common.async?Promise.all([this._def.left._parseAsync({data:r.data,path:r.path,parent:r}),this._def.right._parseAsync({data:r.data,path:r.path,parent:r})]).then(([s,i])=>o(s,i)):o(this._def.left._parseSync({data:r.data,path:r.path,parent:r}),this._def.right._parseSync({data:r.data,path:r.path,parent:r}))}}hs.create=(e,t,n)=>new hs({left:e,right:t,typeName:Y.ZodIntersection,...J(n)});class Ut extends ee{_parse(t){const{status:n,ctx:r}=this._processInputParams(t);if(r.parsedType!==U.array)return F(r,{code:A.invalid_type,expected:U.array,received:r.parsedType}),K;if(r.data.length<this._def.items.length)return F(r,{code:A.too_small,minimum:this._def.items.length,inclusive:!0,exact:!1,type:"array"}),K;!this._def.rest&&r.data.length>this._def.items.length&&(F(r,{code:A.too_big,maximum:this._def.items.length,inclusive:!0,exact:!1,type:"array"}),n.dirty());const s=[...r.data].map((i,a)=>{const l=this._def.items[a]||this._def.rest;return l?l._parse(new Vt(r,i,r.path,a)):null}).filter(i=>!!i);return r.common.async?Promise.all(s).then(i=>Fe.mergeArray(n,i)):Fe.mergeArray(n,s)}get items(){return this._def.items}rest(t){return new Ut({...this._def,rest:t})}}Ut.create=(e,t)=>{if(!Array.isArray(e))throw new Error("You must pass an array of schemas to z.tuple([ ... ])");return new Ut({items:e,typeName:Y.ZodTuple,rest:null,...J(t)})};class ms extends ee{get keySchema(){return this._def.keyType}get valueSchema(){return this._def.valueType}_parse(t){const{status:n,ctx:r}=this._processInputParams(t);if(r.parsedType!==U.object)return F(r,{code:A.invalid_type,expected:U.object,received:r.parsedType}),K;const o=[],s=this._def.keyType,i=this._def.valueType;for(const a in r.data)o.push({key:s._parse(new Vt(r,a,r.path,a)),value:i._parse(new Vt(r,r.data[a],r.path,a)),alwaysSet:a in r.data});return r.common.async?Fe.mergeObjectAsync(n,o):Fe.mergeObjectSync(n,o)}get element(){return this._def.valueType}static create(t,n,r){return n instanceof ee?new ms({keyType:t,valueType:n,typeName:Y.ZodRecord,...J(r)}):new ms({keyType:Ct.create(),valueType:t,typeName:Y.ZodRecord,...J(n)})}}class ea extends ee{get keySchema(){return this._def.keyType}get valueSchema(){return this._def.valueType}_parse(t){const{status:n,ctx:r}=this._processInputParams(t);if(r.parsedType!==U.map)return F(r,{code:A.invalid_type,expected:U.map,received:r.parsedType}),K;const o=this._def.keyType,s=this._def.valueType,i=[...r.data.entries()].map(([a,l],u)=>({key:o._parse(new Vt(r,a,r.path,[u,"key"])),value:s._parse(new Vt(r,l,r.path,[u,"value"]))}));if(r.common.async){const a=new Map;return Promise.resolve().then(async()=>{for(const l of i){const u=await l.key,d=await l.value;if(u.status==="aborted"||d.status==="aborted")return K;(u.status==="dirty"||d.status==="dirty")&&n.dirty(),a.set(u.value,d.value)}return{status:n.value,value:a}})}else{const a=new Map;for(const l of i){const u=l.key,d=l.value;if(u.status==="aborted"||d.status==="aborted")return K;(u.status==="dirty"||d.status==="dirty")&&n.dirty(),a.set(u.value,d.value)}return{status:n.value,value:a}}}}ea.create=(e,t,n)=>new ea({valueType:t,keyType:e,typeName:Y.ZodMap,...J(n)});class pr extends ee{_parse(t){const{status:n,ctx:r}=this._processInputParams(t);if(r.parsedType!==U.set)return F(r,{code:A.invalid_type,expected:U.set,received:r.parsedType}),K;const o=this._def;o.minSize!==null&&r.data.size<o.minSize.value&&(F(r,{code:A.too_small,minimum:o.minSize.value,type:"set",inclusive:!0,exact:!1,message:o.minSize.message}),n.dirty()),o.maxSize!==null&&r.data.size>o.maxSize.value&&(F(r,{code:A.too_big,maximum:o.maxSize.value,type:"set",inclusive:!0,exact:!1,message:o.maxSize.message}),n.dirty());const s=this._def.valueType;function i(l){const u=new Set;for(const d of l){if(d.status==="aborted")return K;d.status==="dirty"&&n.dirty(),u.add(d.value)}return{status:n.value,value:u}}const a=[...r.data.values()].map((l,u)=>s._parse(new Vt(r,l,r.path,u)));return r.common.async?Promise.all(a).then(l=>i(l)):i(a)}min(t,n){return new pr({...this._def,minSize:{value:t,message:H.toString(n)}})}max(t,n){return new pr({...this._def,maxSize:{value:t,message:H.toString(n)}})}size(t,n){return this.min(t,n).max(t,n)}nonempty(t){return this.min(1,t)}}pr.create=(e,t)=>new pr({valueType:e,minSize:null,maxSize:null,typeName:Y.ZodSet,...J(t)});class Hr extends ee{constructor(){super(...arguments),this.validate=this.implement}_parse(t){const{ctx:n}=this._processInputParams(t);if(n.parsedType!==U.function)return F(n,{code:A.invalid_type,expected:U.function,received:n.parsedType}),K;function r(a,l){return Qi({data:a,path:n.path,errorMaps:[n.common.contextualErrorMap,n.schemaErrorMap,Ki(),ro].filter(u=>!!u),issueData:{code:A.invalid_arguments,argumentsError:l}})}function o(a,l){return Qi({data:a,path:n.path,errorMaps:[n.common.contextualErrorMap,n.schemaErrorMap,Ki(),ro].filter(u=>!!u),issueData:{code:A.invalid_return_type,returnTypeError:l}})}const s={errorMap:n.common.contextualErrorMap},i=n.data;if(this._def.returns instanceof so){const a=this;return Ue(async function(...l){const u=new rt([]),d=await a._def.args.parseAsync(l,s).catch(y=>{throw u.addIssue(r(l,y)),u}),f=await Reflect.apply(i,this,d);return await a._def.returns._def.type.parseAsync(f,s).catch(y=>{throw u.addIssue(o(f,y)),u})})}else{const a=this;return Ue(function(...l){const u=a._def.args.safeParse(l,s);if(!u.success)throw new rt([r(l,u.error)]);const d=Reflect.apply(i,this,u.data),f=a._def.returns.safeParse(d,s);if(!f.success)throw new rt([o(d,f.error)]);return f.data})}}parameters(){return this._def.args}returnType(){return this._def.returns}args(...t){return new Hr({...this._def,args:Ut.create(t).rest(sr.create())})}returns(t){return new Hr({...this._def,returns:t})}implement(t){return this.parse(t)}strictImplement(t){return this.parse(t)}static create(t,n,r){return new Hr({args:t||Ut.create([]).rest(sr.create()),returns:n||sr.create(),typeName:Y.ZodFunction,...J(r)})}}class vs extends ee{get schema(){return this._def.getter()}_parse(t){const{ctx:n}=this._processInputParams(t);return this._def.getter()._parse({data:n.data,path:n.path,parent:n})}}vs.create=(e,t)=>new vs({getter:e,typeName:Y.ZodLazy,...J(t)});class gs extends ee{_parse(t){if(t.data!==this._def.value){const n=this._getOrReturnCtx(t);return F(n,{received:n.data,code:A.invalid_literal,expected:this._def.value}),K}return{status:"valid",value:t.data}}get value(){return this._def.value}}gs.create=(e,t)=>new gs({value:e,typeName:Y.ZodLiteral,...J(t)});function Cm(e,t){return new zn({values:e,typeName:Y.ZodEnum,...J(t)})}class zn extends ee{constructor(){super(...arguments),Po.set(this,void 0)}_parse(t){if(typeof t.data!="string"){const n=this._getOrReturnCtx(t),r=this._def.values;return F(n,{expected:ne.joinValues(r),received:n.parsedType,code:A.invalid_type}),K}if(Xi(this,Po)||ym(this,Po,new Set(this._def.values)),!Xi(this,Po).has(t.data)){const n=this._getOrReturnCtx(t),r=this._def.values;return F(n,{received:n.data,code:A.invalid_enum_value,options:r}),K}return Ue(t.data)}get options(){return this._def.values}get enum(){const t={};for(const n of this._def.values)t[n]=n;return t}get Values(){const t={};for(const n of this._def.values)t[n]=n;return t}get Enum(){const t={};for(const n of this._def.values)t[n]=n;return t}extract(t,n=this._def){return zn.create(t,{...this._def,...n})}exclude(t,n=this._def){return zn.create(this.options.filter(r=>!t.includes(r)),{...this._def,...n})}}Po=new WeakMap;zn.create=Cm;class ys extends ee{constructor(){super(...arguments),Ao.set(this,void 0)}_parse(t){const n=ne.getValidEnumValues(this._def.values),r=this._getOrReturnCtx(t);if(r.parsedType!==U.string&&r.parsedType!==U.number){const o=ne.objectValues(n);return F(r,{expected:ne.joinValues(o),received:r.parsedType,code:A.invalid_type}),K}if(Xi(this,Ao)||ym(this,Ao,new Set(ne.getValidEnumValues(this._def.values))),!Xi(this,Ao).has(t.data)){const o=ne.objectValues(n);return F(r,{received:r.data,code:A.invalid_enum_value,options:o}),K}return Ue(t.data)}get enum(){return this._def.values}}Ao=new WeakMap;ys.create=(e,t)=>new ys({values:e,typeName:Y.ZodNativeEnum,...J(t)});class so extends ee{unwrap(){return this._def.type}_parse(t){const{ctx:n}=this._processInputParams(t);if(n.parsedType!==U.promise&&n.common.async===!1)return F(n,{code:A.invalid_type,expected:U.promise,received:n.parsedType}),K;const r=n.parsedType===U.promise?n.data:Promise.resolve(n.data);return Ue(r.then(o=>this._def.type.parseAsync(o,{path:n.path,errorMap:n.common.contextualErrorMap})))}}so.create=(e,t)=>new so({type:e,typeName:Y.ZodPromise,...J(t)});class Nt extends ee{innerType(){return this._def.schema}sourceType(){return this._def.schema._def.typeName===Y.ZodEffects?this._def.schema.sourceType():this._def.schema}_parse(t){const{status:n,ctx:r}=this._processInputParams(t),o=this._def.effect||null,s={addIssue:i=>{F(r,i),i.fatal?n.abort():n.dirty()},get path(){return r.path}};if(s.addIssue=s.addIssue.bind(s),o.type==="preprocess"){const i=o.transform(r.data,s);if(r.common.async)return Promise.resolve(i).then(async a=>{if(n.value==="aborted")return K;const l=await this._def.schema._parseAsync({data:a,path:r.path,parent:r});return l.status==="aborted"?K:l.status==="dirty"||n.value==="dirty"?Lr(l.value):l});{if(n.value==="aborted")return K;const a=this._def.schema._parseSync({data:i,path:r.path,parent:r});return a.status==="aborted"?K:a.status==="dirty"||n.value==="dirty"?Lr(a.value):a}}if(o.type==="refinement"){const i=a=>{const l=o.refinement(a,s);if(r.common.async)return Promise.resolve(l);if(l instanceof Promise)throw new Error("Async refinement encountered during synchronous parse operation. Use .parseAsync instead.");return a};if(r.common.async===!1){const a=this._def.schema._parseSync({data:r.data,path:r.path,parent:r});return a.status==="aborted"?K:(a.status==="dirty"&&n.dirty(),i(a.value),{status:n.value,value:a.value})}else return this._def.schema._parseAsync({data:r.data,path:r.path,parent:r}).then(a=>a.status==="aborted"?K:(a.status==="dirty"&&n.dirty(),i(a.value).then(()=>({status:n.value,value:a.value}))))}if(o.type==="transform")if(r.common.async===!1){const i=this._def.schema._parseSync({data:r.data,path:r.path,parent:r});if(!dr(i))return i;const a=o.transform(i.value,s);if(a instanceof Promise)throw new Error("Asynchronous transform encountered during synchronous parse operation. Use .parseAsync instead.");return{status:n.value,value:a}}else return this._def.schema._parseAsync({data:r.data,path:r.path,parent:r}).then(i=>dr(i)?Promise.resolve(o.transform(i.value,s)).then(a=>({status:n.value,value:a})):i);ne.assertNever(o)}}Nt.create=(e,t,n)=>new Nt({schema:e,typeName:Y.ZodEffects,effect:t,...J(n)});Nt.createWithPreprocess=(e,t,n)=>new Nt({schema:t,effect:{type:"preprocess",transform:e},typeName:Y.ZodEffects,...J(n)});class $t extends ee{_parse(t){return this._getType(t)===U.undefined?Ue(void 0):this._def.innerType._parse(t)}unwrap(){return this._def.innerType}}$t.create=(e,t)=>new $t({innerType:e,typeName:Y.ZodOptional,...J(t)});class Fn extends ee{_parse(t){return this._getType(t)===U.null?Ue(null):this._def.innerType._parse(t)}unwrap(){return this._def.innerType}}Fn.create=(e,t)=>new Fn({innerType:e,typeName:Y.ZodNullable,...J(t)});class xs extends ee{_parse(t){const{ctx:n}=this._processInputParams(t);let r=n.data;return n.parsedType===U.undefined&&(r=this._def.defaultValue()),this._def.innerType._parse({data:r,path:n.path,parent:n})}removeDefault(){return this._def.innerType}}xs.create=(e,t)=>new xs({innerType:e,typeName:Y.ZodDefault,defaultValue:typeof t.default=="function"?t.default:()=>t.default,...J(t)});class ws extends ee{_parse(t){const{ctx:n}=this._processInputParams(t),r={...n,common:{...n.common,issues:[]}},o=this._def.innerType._parse({data:r.data,path:r.path,parent:{...r}});return us(o)?o.then(s=>({status:"valid",value:s.status==="valid"?s.value:this._def.catchValue({get error(){return new rt(r.common.issues)},input:r.data})})):{status:"valid",value:o.status==="valid"?o.value:this._def.catchValue({get error(){return new rt(r.common.issues)},input:r.data})}}removeCatch(){return this._def.innerType}}ws.create=(e,t)=>new ws({innerType:e,typeName:Y.ZodCatch,catchValue:typeof t.catch=="function"?t.catch:()=>t.catch,...J(t)});class ta extends ee{_parse(t){if(this._getType(t)!==U.nan){const r=this._getOrReturnCtx(t);return F(r,{code:A.invalid_type,expected:U.nan,received:r.parsedType}),K}return{status:"valid",value:t.data}}}ta.create=e=>new ta({typeName:Y.ZodNaN,...J(e)});const w0=Symbol("zod_brand");class Ac extends ee{_parse(t){const{ctx:n}=this._processInputParams(t),r=n.data;return this._def.type._parse({data:r,path:n.path,parent:n})}unwrap(){return this._def.type}}class Ms extends ee{_parse(t){const{status:n,ctx:r}=this._processInputParams(t);if(r.common.async)return(async()=>{const s=await this._def.in._parseAsync({data:r.data,path:r.path,parent:r});return s.status==="aborted"?K:s.status==="dirty"?(n.dirty(),Lr(s.value)):this._def.out._parseAsync({data:s.value,path:r.path,parent:r})})();{const o=this._def.in._parseSync({data:r.data,path:r.path,parent:r});return o.status==="aborted"?K:o.status==="dirty"?(n.dirty(),{status:"dirty",value:o.value}):this._def.out._parseSync({data:o.value,path:r.path,parent:r})}}static create(t,n){return new Ms({in:t,out:n,typeName:Y.ZodPipeline})}}class _s extends ee{_parse(t){const n=this._def.innerType._parse(t),r=o=>(dr(o)&&(o.value=Object.freeze(o.value)),o);return us(n)?n.then(o=>r(o)):r(n)}unwrap(){return this._def.innerType}}_s.create=(e,t)=>new _s({innerType:e,typeName:Y.ZodReadonly,...J(t)});function bf(e,t){const n=typeof e=="function"?e(t):typeof e=="string"?{message:e}:e;return typeof n=="string"?{message:n}:n}function km(e,t={},n){return e?oo.create().superRefine((r,o)=>{var s,i;const a=e(r);if(a instanceof Promise)return a.then(l=>{var u,d;if(!l){const f=bf(t,r),p=(d=(u=f.fatal)!==null&&u!==void 0?u:n)!==null&&d!==void 0?d:!0;o.addIssue({code:"custom",...f,fatal:p})}});if(!a){const l=bf(t,r),u=(i=(s=l.fatal)!==null&&s!==void 0?s:n)!==null&&i!==void 0?i:!0;o.addIssue({code:"custom",...l,fatal:u})}}):oo.create()}const _0={object:pe.lazycreate};var Y;(function(e){e.ZodString="ZodString",e.ZodNumber="ZodNumber",e.ZodNaN="ZodNaN",e.ZodBigInt="ZodBigInt",e.ZodBoolean="ZodBoolean",e.ZodDate="ZodDate",e.ZodSymbol="ZodSymbol",e.ZodUndefined="ZodUndefined",e.ZodNull="ZodNull",e.ZodAny="ZodAny",e.ZodUnknown="ZodUnknown",e.ZodNever="ZodNever",e.ZodVoid="ZodVoid",e.ZodArray="ZodArray",e.ZodObject="ZodObject",e.ZodUnion="ZodUnion",e.ZodDiscriminatedUnion="ZodDiscriminatedUnion",e.ZodIntersection="ZodIntersection",e.ZodTuple="ZodTuple",e.ZodRecord="ZodRecord",e.ZodMap="ZodMap",e.ZodSet="ZodSet",e.ZodFunction="ZodFunction",e.ZodLazy="ZodLazy",e.ZodLiteral="ZodLiteral",e.ZodEnum="ZodEnum",e.ZodEffects="ZodEffects",e.ZodNativeEnum="ZodNativeEnum",e.ZodOptional="ZodOptional",e.ZodNullable="ZodNullable",e.ZodDefault="ZodDefault",e.ZodCatch="ZodCatch",e.ZodPromise="ZodPromise",e.ZodBranded="ZodBranded",e.ZodPipeline="ZodPipeline",e.ZodReadonly="ZodReadonly"})(Y||(Y={}));const C0=(e,t={message:`Input not instance of ${e.name}`})=>km(n=>n instanceof e,t),Sm=Ct.create,Em=Ln.create,k0=ta.create,S0=$n.create,bm=cs.create,E0=fr.create,b0=Ji.create,N0=ds.create,T0=fs.create,R0=oo.create,j0=sr.create,M0=un.create,P0=qi.create,A0=Et.create,O0=pe.create,I0=pe.strictCreate,D0=ps.create,L0=Na.create,$0=hs.create,z0=Ut.create,F0=ms.create,B0=ea.create,V0=pr.create,U0=Hr.create,W0=vs.create,H0=gs.create,Z0=zn.create,G0=ys.create,Y0=so.create,Nf=Nt.create,K0=$t.create,Q0=Fn.create,X0=Nt.createWithPreprocess,J0=Ms.create,q0=()=>Sm().optional(),e1=()=>Em().optional(),t1=()=>bm().optional(),n1={string:e=>Ct.create({...e,coerce:!0}),number:e=>Ln.create({...e,coerce:!0}),boolean:e=>cs.create({...e,coerce:!0}),bigint:e=>$n.create({...e,coerce:!0}),date:e=>fr.create({...e,coerce:!0})},r1=K;var R=Object.freeze({__proto__:null,defaultErrorMap:ro,setErrorMap:Xw,getErrorMap:Ki,makeIssue:Qi,EMPTY_PATH:Jw,addIssueToContext:F,ParseStatus:Fe,INVALID:K,DIRTY:Lr,OK:Ue,isAborted:Su,isDirty:Eu,isValid:dr,isAsync:us,get util(){return ne},get objectUtil(){return ku},ZodParsedType:U,getParsedType:Xt,ZodType:ee,datetimeRegex:_m,ZodString:Ct,ZodNumber:Ln,ZodBigInt:$n,ZodBoolean:cs,ZodDate:fr,ZodSymbol:Ji,ZodUndefined:ds,ZodNull:fs,ZodAny:oo,ZodUnknown:sr,ZodNever:un,ZodVoid:qi,ZodArray:Et,ZodObject:pe,ZodUnion:ps,ZodDiscriminatedUnion:Na,ZodIntersection:hs,ZodTuple:Ut,ZodRecord:ms,ZodMap:ea,ZodSet:pr,ZodFunction:Hr,ZodLazy:vs,ZodLiteral:gs,ZodEnum:zn,ZodNativeEnum:ys,ZodPromise:so,ZodEffects:Nt,ZodTransformer:Nt,ZodOptional:$t,ZodNullable:Fn,ZodDefault:xs,ZodCatch:ws,ZodNaN:ta,BRAND:w0,ZodBranded:Ac,ZodPipeline:Ms,ZodReadonly:_s,custom:km,Schema:ee,ZodSchema:ee,late:_0,get ZodFirstPartyTypeKind(){return Y},coerce:n1,any:R0,array:A0,bigint:S0,boolean:bm,date:E0,discriminatedUnion:L0,effect:Nf,enum:Z0,function:U0,instanceof:C0,intersection:$0,lazy:W0,literal:H0,map:B0,nan:k0,nativeEnum:G0,never:M0,null:T0,nullable:Q0,number:Em,object:O0,oboolean:t1,onumber:e1,optional:K0,ostring:q0,pipeline:J0,preprocess:X0,promise:Y0,record:F0,set:V0,strictObject:I0,string:Sm,symbol:b0,transformer:Nf,tuple:z0,undefined:N0,union:D0,unknown:j0,void:P0,NEVER:r1,ZodIssueCode:A,quotelessJson:Qw,ZodError:rt}),Nm=(e=>(e[e.INDEX_MODIFIED=0]="INDEX_MODIFIED",e[e.INDEX_ADDED=1]="INDEX_ADDED",e[e.INDEX_DELETED=2]="INDEX_DELETED",e[e.INDEX_RENAMED=3]="INDEX_RENAMED",e[e.INDEX_COPIED=4]="INDEX_COPIED",e[e.MODIFIED=5]="MODIFIED",e[e.DELETED=6]="DELETED",e[e.UNTRACKED=7]="UNTRACKED",e[e.IGNORED=8]="IGNORED",e[e.INTENT_TO_ADD=9]="INTENT_TO_ADD",e[e.INTENT_TO_RENAME=10]="INTENT_TO_RENAME",e[e.TYPE_CHANGED=11]="TYPE_CHANGED",e[e.ADDED_BY_US=12]="ADDED_BY_US",e[e.ADDED_BY_THEM=13]="ADDED_BY_THEM",e[e.DELETED_BY_US=14]="DELETED_BY_US",e[e.DELETED_BY_THEM=15]="DELETED_BY_THEM",e[e.BOTH_ADDED=16]="BOTH_ADDED",e[e.BOTH_DELETED=17]="BOTH_DELETED",e[e.BOTH_MODIFIED=18]="BOTH_MODIFIED",e))(Nm||{});const we={login:"login",doReview:"doReview",onInfo:"onInfo",onError:"onError",applyChanges:"applyChanges",openFile:"openFile",openFolder:"openFolder",showLines:"showLines",syncState:"syncState",getFilesToReview:"getFilesToReview",stopReview:"stopReview",initializeGit:"initializeGit",enableGit:"enableGit",switchRepo:"switchRepo",deleteReviews:"deleteReviews",changeBaseBranch:"changeBaseBranch",getBranchInfo:"getBranchInfo",setReviewType:"setReviewType",showFileDiffForReview:"showFileDiffForReview",changeOrg:"changeOrg",loginWithToken:"loginWithToken"};R.object({type:R.literal(we.login),value:R.string()});const Tm=R.enum(["uncommitted","committed","all"]);R.object({type:R.literal(we.doReview)});R.object({type:R.literal(we.onInfo),value:R.string()});R.object({type:R.literal(we.onError),value:R.string()});R.object({type:R.literal(we.applyChanges),value:R.object({filename:R.string(),commentId:R.string(),reviewId:R.string().nullable()})});R.object({type:R.literal(we.openFile),value:R.object({filePath:R.string()})});R.object({type:R.literal(we.showFileDiffForReview),value:R.object({filePath:R.string(),reviewId:R.string()})});R.object({type:R.literal(we.getFilesToReview)});R.object({type:R.literal(we.showLines),value:R.object({filename:R.string(),commentId:R.string(),reviewId:R.string().nullable()})});R.object({type:R.literal(we.syncState)});R.object({type:R.literal("walk_through"),payload:R.object({changes:R.string(),poem:R.string(),sequence:R.array(R.string()),type:R.literal("walk_through"),walkthrough:R.string()}).optional()});R.object({type:R.literal("pr_title"),reviewId:R.string(),payload:R.string()});R.object({type:R.literal(we.stopReview),data:R.object({reviewId:R.string()})});R.object({type:R.literal(we.initializeGit)});R.object({type:R.literal(we.enableGit)});R.object({type:R.literal(we.switchRepo)});R.object({type:R.literal(we.openFolder)});R.object({type:R.literal(we.deleteReviews),data:R.array(R.string())});R.object({type:R.literal(we.changeBaseBranch)});R.object({type:R.literal(we.getBranchInfo)});R.object({type:R.literal(we.setReviewType),value:R.object({reviewType:Tm})});R.object({type:R.literal(we.changeOrg)});R.object({type:R.literal(we.loginWithToken)});const Ye={clearState:"clearState",getAllState:"getAllState",getBranchInfo:"getBranchInfo",syncReviewState:"syncReviewState",getFilesToReview:"getFilesToReview",onAuthenticationStateChange:"onAuthenticationStateChange",rateLimitExceeded:"rate_limit_exceeded",currentOrganization:"currentOrganization"};R.object({type:R.literal(Ye.syncReviewState),data:R.object({reviews:R.array(R.custom()),currentReviewId:R.string().optional()})});R.object({type:R.literal(Ye.clearState)});const Rm=R.object({state:R.enum(["success","pending","idle"]),user:R.custom().nullable()});R.object({type:R.literal(Ye.getAllState),data:R.object({currentReviewId:R.string().nullable(),reviews:R.array(R.custom()),git:R.object({isInitialized:R.boolean(),isConfigured:R.boolean(),hasMultipleRepos:R.boolean(),currentRepo:R.string()}),workspaces:R.object({length:R.number()}),auth:Rm,reviewType:Tm,currentOrg:R.object({name:R.string()}).nullable()})});R.object({type:R.literal(Ye.getFilesToReview),data:R.object({files:R.array(R.object({filePath:R.string(),status:R.nativeEnum(Nm)}))})});R.object({type:R.literal(Ye.onAuthenticationStateChange),data:R.object({auth:Rm})});R.object({type:R.literal(Ye.rateLimitExceeded),data:R.object({waitTime:R.string()})});R.object({type:R.literal(Ye.getBranchInfo),data:R.object({current:R.custom(),base:R.custom(),isDefaultBranch:R.boolean()})});R.object({type:R.literal(Ye.currentOrganization),data:R.object({organization:R.object({name:R.string()})})});const pn=v.createContext({reviews:[],currentReviewId:null,branchInfo:{current:null,base:null,isDefaultBranch:!1},filesToReview:{files:[]},git:{isInitialized:!1,isConfigured:!1,hasMultipleRepos:!1,currentRepo:""},workspaces:{length:0},isBusy:!1,setIsBusy:()=>{},auth:{state:"idle",user:null},reviewType:"all",currentOrg:null});function jm(e){var t,n,r="";if(typeof e=="string"||typeof e=="number")r+=e;else if(typeof e=="object")if(Array.isArray(e)){var o=e.length;for(t=0;t<o;t++)e[t]&&(n=jm(e[t]))&&(r&&(r+=" "),r+=n)}else for(n in e)e[n]&&(r&&(r+=" "),r+=n);return r}function Mm(){for(var e,t,n=0,r="",o=arguments.length;n<o;n++)(e=arguments[n])&&(t=jm(e))&&(r&&(r+=" "),r+=t);return r}const Oc="-",o1=e=>{const t=i1(e),{conflictingClassGroups:n,conflictingClassGroupModifiers:r}=e;return{getClassGroupId:i=>{const a=i.split(Oc);return a[0]===""&&a.length!==1&&a.shift(),Pm(a,t)||s1(i)},getConflictingClassGroupIds:(i,a)=>{const l=n[i]||[];return a&&r[i]?[...l,...r[i]]:l}}},Pm=(e,t)=>{var i;if(e.length===0)return t.classGroupId;const n=e[0],r=t.nextPart.get(n),o=r?Pm(e.slice(1),r):void 0;if(o)return o;if(t.validators.length===0)return;const s=e.join(Oc);return(i=t.validators.find(({validator:a})=>a(s)))==null?void 0:i.classGroupId},Tf=/^\[(.+)\]$/,s1=e=>{if(Tf.test(e)){const t=Tf.exec(e)[1],n=t==null?void 0:t.substring(0,t.indexOf(":"));if(n)return"arbitrary.."+n}},i1=e=>{const{theme:t,prefix:n}=e,r={nextPart:new Map,validators:[]};return l1(Object.entries(e.classGroups),n).forEach(([s,i])=>{Nu(i,r,s,t)}),r},Nu=(e,t,n,r)=>{e.forEach(o=>{if(typeof o=="string"){const s=o===""?t:Rf(t,o);s.classGroupId=n;return}if(typeof o=="function"){if(a1(o)){Nu(o(r),t,n,r);return}t.validators.push({validator:o,classGroupId:n});return}Object.entries(o).forEach(([s,i])=>{Nu(i,Rf(t,s),n,r)})})},Rf=(e,t)=>{let n=e;return t.split(Oc).forEach(r=>{n.nextPart.has(r)||n.nextPart.set(r,{nextPart:new Map,validators:[]}),n=n.nextPart.get(r)}),n},a1=e=>e.isThemeGetter,l1=(e,t)=>t?e.map(([n,r])=>{const o=r.map(s=>typeof s=="string"?t+s:typeof s=="object"?Object.fromEntries(Object.entries(s).map(([i,a])=>[t+i,a])):s);return[n,o]}):e,u1=e=>{if(e<1)return{get:()=>{},set:()=>{}};let t=0,n=new Map,r=new Map;const o=(s,i)=>{n.set(s,i),t++,t>e&&(t=0,r=n,n=new Map)};return{get(s){let i=n.get(s);if(i!==void 0)return i;if((i=r.get(s))!==void 0)return o(s,i),i},set(s,i){n.has(s)?n.set(s,i):o(s,i)}}},Am="!",c1=e=>{const{separator:t,experimentalParseClassName:n}=e,r=t.length===1,o=t[0],s=t.length,i=a=>{const l=[];let u=0,d=0,f;for(let k=0;k<a.length;k++){let m=a[k];if(u===0){if(m===o&&(r||a.slice(k,k+s)===t)){l.push(a.slice(d,k)),d=k+s;continue}if(m==="/"){f=k;continue}}m==="["?u++:m==="]"&&u--}const p=l.length===0?a:a.substring(d),y=p.startsWith(Am),w=y?p.substring(1):p,g=f&&f>d?f-d:void 0;return{modifiers:l,hasImportantModifier:y,baseClassName:w,maybePostfixModifierPosition:g}};return n?a=>n({className:a,parseClassName:i}):i},d1=e=>{if(e.length<=1)return e;const t=[];let n=[];return e.forEach(r=>{r[0]==="["?(t.push(...n.sort(),r),n=[]):n.push(r)}),t.push(...n.sort()),t},f1=e=>({cache:u1(e.cacheSize),parseClassName:c1(e),...o1(e)}),p1=/\s+/,h1=(e,t)=>{const{parseClassName:n,getClassGroupId:r,getConflictingClassGroupIds:o}=t,s=[],i=e.trim().split(p1);let a="";for(let l=i.length-1;l>=0;l-=1){const u=i[l],{modifiers:d,hasImportantModifier:f,baseClassName:p,maybePostfixModifierPosition:y}=n(u);let w=!!y,g=r(w?p.substring(0,y):p);if(!g){if(!w){a=u+(a.length>0?" "+a:a);continue}if(g=r(p),!g){a=u+(a.length>0?" "+a:a);continue}w=!1}const k=d1(d).join(":"),m=f?k+Am:k,h=m+g;if(s.includes(h))continue;s.push(h);const x=o(g,w);for(let _=0;_<x.length;++_){const b=x[_];s.push(m+b)}a=u+(a.length>0?" "+a:a)}return a};function m1(){let e=0,t,n,r="";for(;e<arguments.length;)(t=arguments[e++])&&(n=Om(t))&&(r&&(r+=" "),r+=n);return r}const Om=e=>{if(typeof e=="string")return e;let t,n="";for(let r=0;r<e.length;r++)e[r]&&(t=Om(e[r]))&&(n&&(n+=" "),n+=t);return n};function v1(e,...t){let n,r,o,s=i;function i(l){const u=t.reduce((d,f)=>f(d),e());return n=f1(u),r=n.cache.get,o=n.cache.set,s=a,a(l)}function a(l){const u=r(l);if(u)return u;const d=h1(l,n);return o(l,d),d}return function(){return s(m1.apply(null,arguments))}}const le=e=>{const t=n=>n[e]||[];return t.isThemeGetter=!0,t},Im=/^\[(?:([a-z-]+):)?(.+)\]$/i,g1=/^\d+\/\d+$/,y1=new Set(["px","full","screen"]),x1=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,w1=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,_1=/^(rgba?|hsla?|hwb|(ok)?(lab|lch))\(.+\)$/,C1=/^(inset_)?-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/,k1=/^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\(.+\)$/,Gt=e=>Zr(e)||y1.has(e)||g1.test(e),yn=e=>ho(e,"length",M1),Zr=e=>!!e&&!Number.isNaN(Number(e)),yl=e=>ho(e,"number",Zr),bo=e=>!!e&&Number.isInteger(Number(e)),S1=e=>e.endsWith("%")&&Zr(e.slice(0,-1)),q=e=>Im.test(e),xn=e=>x1.test(e),E1=new Set(["length","size","percentage"]),b1=e=>ho(e,E1,Dm),N1=e=>ho(e,"position",Dm),T1=new Set(["image","url"]),R1=e=>ho(e,T1,A1),j1=e=>ho(e,"",P1),No=()=>!0,ho=(e,t,n)=>{const r=Im.exec(e);return r?r[1]?typeof t=="string"?r[1]===t:t.has(r[1]):n(r[2]):!1},M1=e=>w1.test(e)&&!_1.test(e),Dm=()=>!1,P1=e=>C1.test(e),A1=e=>k1.test(e),O1=()=>{const e=le("colors"),t=le("spacing"),n=le("blur"),r=le("brightness"),o=le("borderColor"),s=le("borderRadius"),i=le("borderSpacing"),a=le("borderWidth"),l=le("contrast"),u=le("grayscale"),d=le("hueRotate"),f=le("invert"),p=le("gap"),y=le("gradientColorStops"),w=le("gradientColorStopPositions"),g=le("inset"),k=le("margin"),m=le("opacity"),h=le("padding"),x=le("saturate"),_=le("scale"),b=le("sepia"),N=le("skew"),S=le("space"),C=le("translate"),L=()=>["auto","contain","none"],M=()=>["auto","hidden","clip","visible","scroll"],$=()=>["auto",q,t],V=()=>[q,t],G=()=>["",Gt,yn],I=()=>["auto",Zr,q],D=()=>["bottom","center","left","left-bottom","left-top","right","right-bottom","right-top","top"],j=()=>["solid","dashed","dotted","double","none"],z=()=>["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"],E=()=>["start","end","center","between","around","evenly","stretch"],T=()=>["","0",q],P=()=>["auto","avoid","all","avoid-page","page","left","right","column"],B=()=>[Zr,q];return{cacheSize:500,separator:":",theme:{colors:[No],spacing:[Gt,yn],blur:["none","",xn,q],brightness:B(),borderColor:[e],borderRadius:["none","","full",xn,q],borderSpacing:V(),borderWidth:G(),contrast:B(),grayscale:T(),hueRotate:B(),invert:T(),gap:V(),gradientColorStops:[e],gradientColorStopPositions:[S1,yn],inset:$(),margin:$(),opacity:B(),padding:V(),saturate:B(),scale:B(),sepia:T(),skew:B(),space:V(),translate:V()},classGroups:{aspect:[{aspect:["auto","square","video",q]}],container:["container"],columns:[{columns:[xn]}],"break-after":[{"break-after":P()}],"break-before":[{"break-before":P()}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],float:[{float:["right","left","none","start","end"]}],clear:[{clear:["left","right","both","none","start","end"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:[...D(),q]}],overflow:[{overflow:M()}],"overflow-x":[{"overflow-x":M()}],"overflow-y":[{"overflow-y":M()}],overscroll:[{overscroll:L()}],"overscroll-x":[{"overscroll-x":L()}],"overscroll-y":[{"overscroll-y":L()}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:[g]}],"inset-x":[{"inset-x":[g]}],"inset-y":[{"inset-y":[g]}],start:[{start:[g]}],end:[{end:[g]}],top:[{top:[g]}],right:[{right:[g]}],bottom:[{bottom:[g]}],left:[{left:[g]}],visibility:["visible","invisible","collapse"],z:[{z:["auto",bo,q]}],basis:[{basis:$()}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["wrap","wrap-reverse","nowrap"]}],flex:[{flex:["1","auto","initial","none",q]}],grow:[{grow:T()}],shrink:[{shrink:T()}],order:[{order:["first","last","none",bo,q]}],"grid-cols":[{"grid-cols":[No]}],"col-start-end":[{col:["auto",{span:["full",bo,q]},q]}],"col-start":[{"col-start":I()}],"col-end":[{"col-end":I()}],"grid-rows":[{"grid-rows":[No]}],"row-start-end":[{row:["auto",{span:[bo,q]},q]}],"row-start":[{"row-start":I()}],"row-end":[{"row-end":I()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":["auto","min","max","fr",q]}],"auto-rows":[{"auto-rows":["auto","min","max","fr",q]}],gap:[{gap:[p]}],"gap-x":[{"gap-x":[p]}],"gap-y":[{"gap-y":[p]}],"justify-content":[{justify:["normal",...E()]}],"justify-items":[{"justify-items":["start","end","center","stretch"]}],"justify-self":[{"justify-self":["auto","start","end","center","stretch"]}],"align-content":[{content:["normal",...E(),"baseline"]}],"align-items":[{items:["start","end","center","baseline","stretch"]}],"align-self":[{self:["auto","start","end","center","stretch","baseline"]}],"place-content":[{"place-content":[...E(),"baseline"]}],"place-items":[{"place-items":["start","end","center","baseline","stretch"]}],"place-self":[{"place-self":["auto","start","end","center","stretch"]}],p:[{p:[h]}],px:[{px:[h]}],py:[{py:[h]}],ps:[{ps:[h]}],pe:[{pe:[h]}],pt:[{pt:[h]}],pr:[{pr:[h]}],pb:[{pb:[h]}],pl:[{pl:[h]}],m:[{m:[k]}],mx:[{mx:[k]}],my:[{my:[k]}],ms:[{ms:[k]}],me:[{me:[k]}],mt:[{mt:[k]}],mr:[{mr:[k]}],mb:[{mb:[k]}],ml:[{ml:[k]}],"space-x":[{"space-x":[S]}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":[S]}],"space-y-reverse":["space-y-reverse"],w:[{w:["auto","min","max","fit","svw","lvw","dvw",q,t]}],"min-w":[{"min-w":[q,t,"min","max","fit"]}],"max-w":[{"max-w":[q,t,"none","full","min","max","fit","prose",{screen:[xn]},xn]}],h:[{h:[q,t,"auto","min","max","fit","svh","lvh","dvh"]}],"min-h":[{"min-h":[q,t,"min","max","fit","svh","lvh","dvh"]}],"max-h":[{"max-h":[q,t,"min","max","fit","svh","lvh","dvh"]}],size:[{size:[q,t,"auto","min","max","fit"]}],"font-size":[{text:["base",xn,yn]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:["thin","extralight","light","normal","medium","semibold","bold","extrabold","black",yl]}],"font-family":[{font:[No]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractions"],tracking:[{tracking:["tighter","tight","normal","wide","wider","widest",q]}],"line-clamp":[{"line-clamp":["none",Zr,yl]}],leading:[{leading:["none","tight","snug","normal","relaxed","loose",Gt,q]}],"list-image":[{"list-image":["none",q]}],"list-style-type":[{list:["none","disc","decimal",q]}],"list-style-position":[{list:["inside","outside"]}],"placeholder-color":[{placeholder:[e]}],"placeholder-opacity":[{"placeholder-opacity":[m]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"text-color":[{text:[e]}],"text-opacity":[{"text-opacity":[m]}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:[...j(),"wavy"]}],"text-decoration-thickness":[{decoration:["auto","from-font",Gt,yn]}],"underline-offset":[{"underline-offset":["auto",Gt,q]}],"text-decoration-color":[{decoration:[e]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],"text-wrap":[{text:["wrap","nowrap","balance","pretty"]}],indent:[{indent:V()}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",q]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",q]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-opacity":[{"bg-opacity":[m]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:[...D(),N1]}],"bg-repeat":[{bg:["no-repeat",{repeat:["","x","y","round","space"]}]}],"bg-size":[{bg:["auto","cover","contain",b1]}],"bg-image":[{bg:["none",{"gradient-to":["t","tr","r","br","b","bl","l","tl"]},R1]}],"bg-color":[{bg:[e]}],"gradient-from-pos":[{from:[w]}],"gradient-via-pos":[{via:[w]}],"gradient-to-pos":[{to:[w]}],"gradient-from":[{from:[y]}],"gradient-via":[{via:[y]}],"gradient-to":[{to:[y]}],rounded:[{rounded:[s]}],"rounded-s":[{"rounded-s":[s]}],"rounded-e":[{"rounded-e":[s]}],"rounded-t":[{"rounded-t":[s]}],"rounded-r":[{"rounded-r":[s]}],"rounded-b":[{"rounded-b":[s]}],"rounded-l":[{"rounded-l":[s]}],"rounded-ss":[{"rounded-ss":[s]}],"rounded-se":[{"rounded-se":[s]}],"rounded-ee":[{"rounded-ee":[s]}],"rounded-es":[{"rounded-es":[s]}],"rounded-tl":[{"rounded-tl":[s]}],"rounded-tr":[{"rounded-tr":[s]}],"rounded-br":[{"rounded-br":[s]}],"rounded-bl":[{"rounded-bl":[s]}],"border-w":[{border:[a]}],"border-w-x":[{"border-x":[a]}],"border-w-y":[{"border-y":[a]}],"border-w-s":[{"border-s":[a]}],"border-w-e":[{"border-e":[a]}],"border-w-t":[{"border-t":[a]}],"border-w-r":[{"border-r":[a]}],"border-w-b":[{"border-b":[a]}],"border-w-l":[{"border-l":[a]}],"border-opacity":[{"border-opacity":[m]}],"border-style":[{border:[...j(),"hidden"]}],"divide-x":[{"divide-x":[a]}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":[a]}],"divide-y-reverse":["divide-y-reverse"],"divide-opacity":[{"divide-opacity":[m]}],"divide-style":[{divide:j()}],"border-color":[{border:[o]}],"border-color-x":[{"border-x":[o]}],"border-color-y":[{"border-y":[o]}],"border-color-s":[{"border-s":[o]}],"border-color-e":[{"border-e":[o]}],"border-color-t":[{"border-t":[o]}],"border-color-r":[{"border-r":[o]}],"border-color-b":[{"border-b":[o]}],"border-color-l":[{"border-l":[o]}],"divide-color":[{divide:[o]}],"outline-style":[{outline:["",...j()]}],"outline-offset":[{"outline-offset":[Gt,q]}],"outline-w":[{outline:[Gt,yn]}],"outline-color":[{outline:[e]}],"ring-w":[{ring:G()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:[e]}],"ring-opacity":[{"ring-opacity":[m]}],"ring-offset-w":[{"ring-offset":[Gt,yn]}],"ring-offset-color":[{"ring-offset":[e]}],shadow:[{shadow:["","inner","none",xn,j1]}],"shadow-color":[{shadow:[No]}],opacity:[{opacity:[m]}],"mix-blend":[{"mix-blend":[...z(),"plus-lighter","plus-darker"]}],"bg-blend":[{"bg-blend":z()}],filter:[{filter:["","none"]}],blur:[{blur:[n]}],brightness:[{brightness:[r]}],contrast:[{contrast:[l]}],"drop-shadow":[{"drop-shadow":["","none",xn,q]}],grayscale:[{grayscale:[u]}],"hue-rotate":[{"hue-rotate":[d]}],invert:[{invert:[f]}],saturate:[{saturate:[x]}],sepia:[{sepia:[b]}],"backdrop-filter":[{"backdrop-filter":["","none"]}],"backdrop-blur":[{"backdrop-blur":[n]}],"backdrop-brightness":[{"backdrop-brightness":[r]}],"backdrop-contrast":[{"backdrop-contrast":[l]}],"backdrop-grayscale":[{"backdrop-grayscale":[u]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[d]}],"backdrop-invert":[{"backdrop-invert":[f]}],"backdrop-opacity":[{"backdrop-opacity":[m]}],"backdrop-saturate":[{"backdrop-saturate":[x]}],"backdrop-sepia":[{"backdrop-sepia":[b]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":[i]}],"border-spacing-x":[{"border-spacing-x":[i]}],"border-spacing-y":[{"border-spacing-y":[i]}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["none","all","","colors","opacity","shadow","transform",q]}],duration:[{duration:B()}],ease:[{ease:["linear","in","out","in-out",q]}],delay:[{delay:B()}],animate:[{animate:["none","spin","ping","pulse","bounce",q]}],transform:[{transform:["","gpu","none"]}],scale:[{scale:[_]}],"scale-x":[{"scale-x":[_]}],"scale-y":[{"scale-y":[_]}],rotate:[{rotate:[bo,q]}],"translate-x":[{"translate-x":[C]}],"translate-y":[{"translate-y":[C]}],"skew-x":[{"skew-x":[N]}],"skew-y":[{"skew-y":[N]}],"transform-origin":[{origin:["center","top","top-right","right","bottom-right","bottom","bottom-left","left","top-left",q]}],accent:[{accent:["auto",e]}],appearance:[{appearance:["none","auto"]}],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",q]}],"caret-color":[{caret:[e]}],"pointer-events":[{"pointer-events":["none","auto"]}],resize:[{resize:["none","y","x",""]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":V()}],"scroll-mx":[{"scroll-mx":V()}],"scroll-my":[{"scroll-my":V()}],"scroll-ms":[{"scroll-ms":V()}],"scroll-me":[{"scroll-me":V()}],"scroll-mt":[{"scroll-mt":V()}],"scroll-mr":[{"scroll-mr":V()}],"scroll-mb":[{"scroll-mb":V()}],"scroll-ml":[{"scroll-ml":V()}],"scroll-p":[{"scroll-p":V()}],"scroll-px":[{"scroll-px":V()}],"scroll-py":[{"scroll-py":V()}],"scroll-ps":[{"scroll-ps":V()}],"scroll-pe":[{"scroll-pe":V()}],"scroll-pt":[{"scroll-pt":V()}],"scroll-pr":[{"scroll-pr":V()}],"scroll-pb":[{"scroll-pb":V()}],"scroll-pl":[{"scroll-pl":V()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","manipulation"]}],"touch-x":[{"touch-pan":["x","left","right"]}],"touch-y":[{"touch-pan":["y","up","down"]}],"touch-pz":["touch-pinch-zoom"],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",q]}],fill:[{fill:[e,"none"]}],"stroke-w":[{stroke:[Gt,yn,yl]}],stroke:[{stroke:[e,"none"]}],sr:["sr-only","not-sr-only"],"forced-color-adjust":[{"forced-color-adjust":["auto","none"]}]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],size:["w","h"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],"line-clamp":["display","overflow"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-s","border-color-e","border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"],touch:["touch-x","touch-y","touch-pz"],"touch-x":["touch"],"touch-y":["touch"],"touch-pz":["touch"]},conflictingClassGroupModifiers:{"font-size":["leading"]}}},I1=v1(O1),wi={cancelled:"cancelled",completed:"completed",failed:"failed"},ei={potentialIssue:"potential_issue",refactorSuggestion:"refactor_suggestion"},$r={setting_up:"setting_up",review_started:"review_started",summarizing:"summarizing",reviewing:"reviewing",review_skipped:"review_skipped"},D1={auto:"auto"};function Q(...e){return I1(Mm(e))}class L1{constructor(){hd(this,"vsCodeApi");typeof acquireVsCodeApi=="function"&&(this.vsCodeApi=acquireVsCodeApi())}postMessage(t){this.vsCodeApi?this.vsCodeApi.postMessage(t):console.log(t)}getState(){if(this.vsCodeApi)return this.vsCodeApi.getState();{const t=localStorage.getItem("vscodeState");return t?JSON.parse(t):void 0}}setState(t){return this.vsCodeApi?this.vsCodeApi.setState(t):(localStorage.setItem("vscodeState",JSON.stringify(t)),t)}}const Se=new L1;function hn(e,t=[]){let n=[];function r(s,i){const a=v.createContext(i),l=n.length;n=[...n,i];const u=f=>{var m;const{scope:p,children:y,...w}=f,g=((m=p==null?void 0:p[e])==null?void 0:m[l])||a,k=v.useMemo(()=>w,Object.values(w));return c.jsx(g.Provider,{value:k,children:y})};u.displayName=s+"Provider";function d(f,p){var g;const y=((g=p==null?void 0:p[e])==null?void 0:g[l])||a,w=v.useContext(y);if(w)return w;if(i!==void 0)return i;throw new Error(`\`${f}\` must be used within \`${s}\``)}return[u,d]}const o=()=>{const s=n.map(i=>v.createContext(i));return function(a){const l=(a==null?void 0:a[e])||s;return v.useMemo(()=>({[`__scope${e}`]:{...a,[e]:l}}),[a,l])}};return o.scopeName=e,[r,$1(o,...t)]}function $1(...e){const t=e[0];if(e.length===1)return t;const n=()=>{const r=e.map(o=>({useScope:o(),scopeName:o.scopeName}));return function(s){const i=r.reduce((a,{useScope:l,scopeName:u})=>{const f=l(s)[`__scope${u}`];return{...a,...f}},{});return v.useMemo(()=>({[`__scope${t.scopeName}`]:i}),[i])}};return n.scopeName=t.scopeName,n}function jf(e,t){if(typeof e=="function")return e(t);e!=null&&(e.current=t)}function Ta(...e){return t=>{let n=!1;const r=e.map(o=>{const s=jf(o,t);return!n&&typeof s=="function"&&(n=!0),s});if(n)return()=>{for(let o=0;o<r.length;o++){const s=r[o];typeof s=="function"?s():jf(e[o],null)}}}}function Pe(...e){return v.useCallback(Ta(...e),e)}var io=v.forwardRef((e,t)=>{const{children:n,...r}=e,o=v.Children.toArray(n),s=o.find(z1);if(s){const i=s.props.children,a=o.map(l=>l===s?v.Children.count(i)>1?v.Children.only(null):v.isValidElement(i)?i.props.children:null:l);return c.jsx(Tu,{...r,ref:t,children:v.isValidElement(i)?v.cloneElement(i,void 0,a):null})}return c.jsx(Tu,{...r,ref:t,children:n})});io.displayName="Slot";var Tu=v.forwardRef((e,t)=>{const{children:n,...r}=e;if(v.isValidElement(n)){const o=B1(n),s=F1(r,n.props);return n.type!==v.Fragment&&(s.ref=t?Ta(t,o):o),v.cloneElement(n,s)}return v.Children.count(n)>1?v.Children.only(null):null});Tu.displayName="SlotClone";var Lm=({children:e})=>c.jsx(c.Fragment,{children:e});function z1(e){return v.isValidElement(e)&&e.type===Lm}function F1(e,t){const n={...t};for(const r in t){const o=e[r],s=t[r];/^on[A-Z]/.test(r)?o&&s?n[r]=(...a)=>{s(...a),o(...a)}:o&&(n[r]=o):r==="style"?n[r]={...o,...s}:r==="className"&&(n[r]=[o,s].filter(Boolean).join(" "))}return{...e,...n}}function B1(e){var r,o;let t=(r=Object.getOwnPropertyDescriptor(e.props,"ref"))==null?void 0:r.get,n=t&&"isReactWarning"in t&&t.isReactWarning;return n?e.ref:(t=(o=Object.getOwnPropertyDescriptor(e,"ref"))==null?void 0:o.get,n=t&&"isReactWarning"in t&&t.isReactWarning,n?e.props.ref:e.props.ref||e.ref)}function Ic(e){const t=e+"CollectionProvider",[n,r]=hn(t),[o,s]=n(t,{collectionRef:{current:null},itemMap:new Map}),i=y=>{const{scope:w,children:g}=y,k=Ce.useRef(null),m=Ce.useRef(new Map).current;return c.jsx(o,{scope:w,itemMap:m,collectionRef:k,children:g})};i.displayName=t;const a=e+"CollectionSlot",l=Ce.forwardRef((y,w)=>{const{scope:g,children:k}=y,m=s(a,g),h=Pe(w,m.collectionRef);return c.jsx(io,{ref:h,children:k})});l.displayName=a;const u=e+"CollectionItemSlot",d="data-radix-collection-item",f=Ce.forwardRef((y,w)=>{const{scope:g,children:k,...m}=y,h=Ce.useRef(null),x=Pe(w,h),_=s(u,g);return Ce.useEffect(()=>(_.itemMap.set(h,{ref:h,...m}),()=>void _.itemMap.delete(h))),c.jsx(io,{[d]:"",ref:x,children:k})});f.displayName=u;function p(y){const w=s(e+"CollectionConsumer",y);return Ce.useCallback(()=>{const k=w.collectionRef.current;if(!k)return[];const m=Array.from(k.querySelectorAll(`[${d}]`));return Array.from(w.itemMap.values()).sort((_,b)=>m.indexOf(_.ref.current)-m.indexOf(b.ref.current))},[w.collectionRef,w.itemMap])}return[{Provider:i,Slot:l,ItemSlot:f},p,r]}function X(e,t,{checkForDefaultPrevented:n=!0}={}){return function(o){if(e==null||e(o),n===!1||!o.defaultPrevented)return t==null?void 0:t(o)}}function mt(e){const t=v.useRef(e);return v.useEffect(()=>{t.current=e}),v.useMemo(()=>(...n)=>{var r;return(r=t.current)==null?void 0:r.call(t,...n)},[])}function mo({prop:e,defaultProp:t,onChange:n=()=>{}}){const[r,o]=V1({defaultProp:t,onChange:n}),s=e!==void 0,i=s?e:r,a=mt(n),l=v.useCallback(u=>{if(s){const f=typeof u=="function"?u(e):u;f!==e&&a(f)}else o(u)},[s,e,o,a]);return[i,l]}function V1({defaultProp:e,onChange:t}){const n=v.useState(e),[r]=n,o=v.useRef(r),s=mt(t);return v.useEffect(()=>{o.current!==r&&(s(r),o.current=r)},[r,o,s]),n}var U1=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","span","svg","ul"],de=U1.reduce((e,t)=>{const n=v.forwardRef((r,o)=>{const{asChild:s,...i}=r,a=s?io:t;return typeof window<"u"&&(window[Symbol.for("radix-ui")]=!0),c.jsx(a,{...i,ref:o})});return n.displayName=`Primitive.${t}`,{...e,[t]:n}},{});function $m(e,t){e&&ba.flushSync(()=>e.dispatchEvent(t))}var Wt=globalThis!=null&&globalThis.document?v.useLayoutEffect:()=>{};function W1(e,t){return v.useReducer((n,r)=>t[n][r]??n,e)}var Xn=e=>{const{present:t,children:n}=e,r=H1(t),o=typeof n=="function"?n({present:r.isPresent}):v.Children.only(n),s=Pe(r.ref,Z1(o));return typeof n=="function"||r.isPresent?v.cloneElement(o,{ref:s}):null};Xn.displayName="Presence";function H1(e){const[t,n]=v.useState(),r=v.useRef({}),o=v.useRef(e),s=v.useRef("none"),i=e?"mounted":"unmounted",[a,l]=W1(i,{mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}});return v.useEffect(()=>{const u=ti(r.current);s.current=a==="mounted"?u:"none"},[a]),Wt(()=>{const u=r.current,d=o.current;if(d!==e){const p=s.current,y=ti(u);e?l("MOUNT"):y==="none"||(u==null?void 0:u.display)==="none"?l("UNMOUNT"):l(d&&p!==y?"ANIMATION_OUT":"UNMOUNT"),o.current=e}},[e,l]),Wt(()=>{if(t){let u;const d=t.ownerDocument.defaultView??window,f=y=>{const g=ti(r.current).includes(y.animationName);if(y.target===t&&g&&(l("ANIMATION_END"),!o.current)){const k=t.style.animationFillMode;t.style.animationFillMode="forwards",u=d.setTimeout(()=>{t.style.animationFillMode==="forwards"&&(t.style.animationFillMode=k)})}},p=y=>{y.target===t&&(s.current=ti(r.current))};return t.addEventListener("animationstart",p),t.addEventListener("animationcancel",f),t.addEventListener("animationend",f),()=>{d.clearTimeout(u),t.removeEventListener("animationstart",p),t.removeEventListener("animationcancel",f),t.removeEventListener("animationend",f)}}else l("ANIMATION_END")},[t,l]),{isPresent:["mounted","unmountSuspended"].includes(a),ref:v.useCallback(u=>{u&&(r.current=getComputedStyle(u)),n(u)},[])}}function ti(e){return(e==null?void 0:e.animationName)||"none"}function Z1(e){var r,o;let t=(r=Object.getOwnPropertyDescriptor(e.props,"ref"))==null?void 0:r.get,n=t&&"isReactWarning"in t&&t.isReactWarning;return n?e.ref:(t=(o=Object.getOwnPropertyDescriptor(e,"ref"))==null?void 0:o.get,n=t&&"isReactWarning"in t&&t.isReactWarning,n?e.props.ref:e.props.ref||e.ref)}var G1=$y.useId||(()=>{}),Y1=0;function ao(e){const[t,n]=v.useState(G1());return Wt(()=>{n(r=>r??String(Y1++))},[e]),t?`radix-${t}`:""}var Dc="Collapsible",[K1,zm]=hn(Dc),[Q1,Lc]=K1(Dc),Fm=v.forwardRef((e,t)=>{const{__scopeCollapsible:n,open:r,defaultOpen:o,disabled:s,onOpenChange:i,...a}=e,[l=!1,u]=mo({prop:r,defaultProp:o,onChange:i});return c.jsx(Q1,{scope:n,disabled:s,contentId:ao(),open:l,onOpenToggle:v.useCallback(()=>u(d=>!d),[u]),children:c.jsx(de.div,{"data-state":zc(l),"data-disabled":s?"":void 0,...a,ref:t})})});Fm.displayName=Dc;var Bm="CollapsibleTrigger",Vm=v.forwardRef((e,t)=>{const{__scopeCollapsible:n,...r}=e,o=Lc(Bm,n);return c.jsx(de.button,{type:"button","aria-controls":o.contentId,"aria-expanded":o.open||!1,"data-state":zc(o.open),"data-disabled":o.disabled?"":void 0,disabled:o.disabled,...r,ref:t,onClick:X(e.onClick,o.onOpenToggle)})});Vm.displayName=Bm;var $c="CollapsibleContent",Um=v.forwardRef((e,t)=>{const{forceMount:n,...r}=e,o=Lc($c,e.__scopeCollapsible);return c.jsx(Xn,{present:n||o.open,children:({present:s})=>c.jsx(X1,{...r,ref:t,present:s})})});Um.displayName=$c;var X1=v.forwardRef((e,t)=>{const{__scopeCollapsible:n,present:r,children:o,...s}=e,i=Lc($c,n),[a,l]=v.useState(r),u=v.useRef(null),d=Pe(t,u),f=v.useRef(0),p=f.current,y=v.useRef(0),w=y.current,g=i.open||a,k=v.useRef(g),m=v.useRef(void 0);return v.useEffect(()=>{const h=requestAnimationFrame(()=>k.current=!1);return()=>cancelAnimationFrame(h)},[]),Wt(()=>{const h=u.current;if(h){m.current=m.current||{transitionDuration:h.style.transitionDuration,animationName:h.style.animationName},h.style.transitionDuration="0s",h.style.animationName="none";const x=h.getBoundingClientRect();f.current=x.height,y.current=x.width,k.current||(h.style.transitionDuration=m.current.transitionDuration,h.style.animationName=m.current.animationName),l(r)}},[i.open,r]),c.jsx(de.div,{"data-state":zc(i.open),"data-disabled":i.disabled?"":void 0,id:i.contentId,hidden:!g,...s,ref:d,style:{"--radix-collapsible-content-height":p?`${p}px`:void 0,"--radix-collapsible-content-width":w?`${w}px`:void 0,...e.style},children:g&&o})});function zc(e){return e?"open":"closed"}var J1=Fm,q1=Vm,e_=Um,t_=v.createContext(void 0);function Fc(e){const t=v.useContext(t_);return e||t||"ltr"}var mn="Accordion",n_=["Home","End","ArrowDown","ArrowUp","ArrowLeft","ArrowRight"],[Bc,r_,o_]=Ic(mn),[Ra,pb]=hn(mn,[o_,zm]),Vc=zm(),Wm=Ce.forwardRef((e,t)=>{const{type:n,...r}=e,o=r,s=r;return c.jsx(Bc.Provider,{scope:e.__scopeAccordion,children:n==="multiple"?c.jsx(l_,{...s,ref:t}):c.jsx(a_,{...o,ref:t})})});Wm.displayName=mn;var[Hm,s_]=Ra(mn),[Zm,i_]=Ra(mn,{collapsible:!1}),a_=Ce.forwardRef((e,t)=>{const{value:n,defaultValue:r,onValueChange:o=()=>{},collapsible:s=!1,...i}=e,[a,l]=mo({prop:n,defaultProp:r,onChange:o});return c.jsx(Hm,{scope:e.__scopeAccordion,value:a?[a]:[],onItemOpen:l,onItemClose:Ce.useCallback(()=>s&&l(""),[s,l]),children:c.jsx(Zm,{scope:e.__scopeAccordion,collapsible:s,children:c.jsx(Gm,{...i,ref:t})})})}),l_=Ce.forwardRef((e,t)=>{const{value:n,defaultValue:r,onValueChange:o=()=>{},...s}=e,[i=[],a]=mo({prop:n,defaultProp:r,onChange:o}),l=Ce.useCallback(d=>a((f=[])=>[...f,d]),[a]),u=Ce.useCallback(d=>a((f=[])=>f.filter(p=>p!==d)),[a]);return c.jsx(Hm,{scope:e.__scopeAccordion,value:i,onItemOpen:l,onItemClose:u,children:c.jsx(Zm,{scope:e.__scopeAccordion,collapsible:!0,children:c.jsx(Gm,{...s,ref:t})})})}),[u_,ja]=Ra(mn),Gm=Ce.forwardRef((e,t)=>{const{__scopeAccordion:n,disabled:r,dir:o,orientation:s="vertical",...i}=e,a=Ce.useRef(null),l=Pe(a,t),u=r_(n),f=Fc(o)==="ltr",p=X(e.onKeyDown,y=>{var C;if(!n_.includes(y.key))return;const w=y.target,g=u().filter(L=>{var M;return!((M=L.ref.current)!=null&&M.disabled)}),k=g.findIndex(L=>L.ref.current===w),m=g.length;if(k===-1)return;y.preventDefault();let h=k;const x=0,_=m-1,b=()=>{h=k+1,h>_&&(h=x)},N=()=>{h=k-1,h<x&&(h=_)};switch(y.key){case"Home":h=x;break;case"End":h=_;break;case"ArrowRight":s==="horizontal"&&(f?b():N());break;case"ArrowDown":s==="vertical"&&b();break;case"ArrowLeft":s==="horizontal"&&(f?N():b());break;case"ArrowUp":s==="vertical"&&N();break}const S=h%m;(C=g[S].ref.current)==null||C.focus()});return c.jsx(u_,{scope:n,disabled:r,direction:o,orientation:s,children:c.jsx(Bc.Slot,{scope:n,children:c.jsx(de.div,{...i,"data-orientation":s,ref:l,onKeyDown:r?void 0:p})})})}),na="AccordionItem",[c_,Uc]=Ra(na),Ym=Ce.forwardRef((e,t)=>{const{__scopeAccordion:n,value:r,...o}=e,s=ja(na,n),i=s_(na,n),a=Vc(n),l=ao(),u=r&&i.value.includes(r)||!1,d=s.disabled||e.disabled;return c.jsx(c_,{scope:n,open:u,disabled:d,triggerId:l,children:c.jsx(J1,{"data-orientation":s.orientation,"data-state":ev(u),...a,...o,ref:t,disabled:d,open:u,onOpenChange:f=>{f?i.onItemOpen(r):i.onItemClose(r)}})})});Ym.displayName=na;var Km="AccordionHeader",Qm=Ce.forwardRef((e,t)=>{const{__scopeAccordion:n,...r}=e,o=ja(mn,n),s=Uc(Km,n);return c.jsx(de.h3,{"data-orientation":o.orientation,"data-state":ev(s.open),"data-disabled":s.disabled?"":void 0,...r,ref:t})});Qm.displayName=Km;var Ru="AccordionTrigger",Xm=Ce.forwardRef((e,t)=>{const{__scopeAccordion:n,...r}=e,o=ja(mn,n),s=Uc(Ru,n),i=i_(Ru,n),a=Vc(n);return c.jsx(Bc.ItemSlot,{scope:n,children:c.jsx(q1,{"aria-disabled":s.open&&!i.collapsible||void 0,"data-orientation":o.orientation,id:s.triggerId,...a,...r,ref:t})})});Xm.displayName=Ru;var Jm="AccordionContent",qm=Ce.forwardRef((e,t)=>{const{__scopeAccordion:n,...r}=e,o=ja(mn,n),s=Uc(Jm,n),i=Vc(n);return c.jsx(e_,{role:"region","aria-labelledby":s.triggerId,"data-orientation":o.orientation,...i,...r,ref:t,style:{"--radix-accordion-content-height":"var(--radix-collapsible-content-height)","--radix-accordion-content-width":"var(--radix-collapsible-content-width)",...e.style}})});qm.displayName=Jm;function ev(e){return e?"open":"closed"}var d_=Wm,f_=Ym,p_=Qm,h_=Xm,m_=qm;/**
 * @license lucide-react v0.368.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */var v_={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};/**
 * @license lucide-react v0.368.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const g_=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase();/**
 * @license lucide-react v0.368.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ma=(e,t)=>{const n=v.forwardRef(({color:r="currentColor",size:o=24,strokeWidth:s=2,absoluteStrokeWidth:i,className:a="",children:l,...u},d)=>v.createElement("svg",{ref:d,...v_,width:o,height:o,stroke:r,strokeWidth:i?Number(s)*24/Number(o):s,className:["lucide",`lucide-${g_(e)}`,a].join(" "),...u},[...t.map(([f,p])=>v.createElement(f,p)),...Array.isArray(l)?l:[l]]));return n.displayName=`${e}`,n};/**
 * @license lucide-react v0.368.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const y_=Ma("Check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]]);/**
 * @license lucide-react v0.368.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const tv=Ma("ChevronDown",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]]);/**
 * @license lucide-react v0.368.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const x_=Ma("ChevronRight",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]]);/**
 * @license lucide-react v0.368.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const w_=Ma("Circle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}]]);function Bn({...e}){return c.jsx(d_,{"data-slot":"accordion",...e})}function Vn({className:e,...t}){return c.jsx(f_,{"data-slot":"accordion-item",className:Q(e),...t})}function Un({className:e,children:t,...n}){return c.jsx(p_,{className:"flex",children:c.jsxs(h_,{"data-slot":"accordion-trigger",className:Q("focus-visible:border-ring focus-visible:ring-ring/50 flex flex-1 items-start justify-between gap-4 rounded-[1px] py-4 text-left text-md font-medium transition-all outline-none hover:underline focus-visible:ring-[3px] disabled:pointer-events-none disabled:opacity-50 [&[data-state=open]>svg]:rotate-180",e),...n,children:[t,c.jsx(tv,{className:"pointer-events-none size-4.5 p-0.5 shrink-0 transition-transform duration-200"})]})})}function Wn({className:e,children:t,...n}){return c.jsx(m_,{"data-slot":"accordion-content",className:"data-[state=closed]:animate-accordion-up data-[state=open]:animate-accordion-down overflow-hidden text-md",...n,children:c.jsx("div",{className:Q("pt-0 pb-4",e),children:t})})}var Wc="Avatar",[__,hb]=hn(Wc),[C_,nv]=__(Wc),rv=v.forwardRef((e,t)=>{const{__scopeAvatar:n,...r}=e,[o,s]=v.useState("idle");return c.jsx(C_,{scope:n,imageLoadingStatus:o,onImageLoadingStatusChange:s,children:c.jsx(de.span,{...r,ref:t})})});rv.displayName=Wc;var ov="AvatarImage",sv=v.forwardRef((e,t)=>{const{__scopeAvatar:n,src:r,onLoadingStatusChange:o=()=>{},...s}=e,i=nv(ov,n),a=k_(r,s.referrerPolicy),l=mt(u=>{o(u),i.onImageLoadingStatusChange(u)});return Wt(()=>{a!=="idle"&&l(a)},[a,l]),a==="loaded"?c.jsx(de.img,{...s,ref:t,src:r}):null});sv.displayName=ov;var iv="AvatarFallback",av=v.forwardRef((e,t)=>{const{__scopeAvatar:n,delayMs:r,...o}=e,s=nv(iv,n),[i,a]=v.useState(r===void 0);return v.useEffect(()=>{if(r!==void 0){const l=window.setTimeout(()=>a(!0),r);return()=>window.clearTimeout(l)}},[r]),i&&s.imageLoadingStatus!=="loaded"?c.jsx(de.span,{...o,ref:t}):null});av.displayName=iv;function k_(e,t){const[n,r]=v.useState("idle");return Wt(()=>{if(!e){r("error");return}let o=!0;const s=new window.Image,i=a=>()=>{o&&r(a)};return r("loading"),s.onload=i("loaded"),s.onerror=i("error"),s.src=e,t&&(s.referrerPolicy=t),()=>{o=!1}},[e,t]),n}var lv=rv,uv=sv,cv=av;const dv=v.forwardRef(({className:e,...t},n)=>c.jsx(lv,{ref:n,className:Q("relative flex h-10 w-10 shrink-0 overflow-hidden rounded-full",e),...t}));dv.displayName=lv.displayName;const fv=v.forwardRef(({className:e,...t},n)=>c.jsx(uv,{ref:n,className:Q("aspect-square h-full w-full",e),...t}));fv.displayName=uv.displayName;const pv=v.forwardRef(({className:e,...t},n)=>c.jsx(cv,{ref:n,className:Q("flex h-full w-full items-center justify-center rounded-full bg-muted",e),...t}));pv.displayName=cv.displayName;const S_=()=>{const{auth:{user:e},currentOrg:t}=v.useContext(pn),n=()=>{Se.postMessage({type:"changeOrg"})};return c.jsx(Bn,{type:"single",collapsible:!0,children:c.jsxs(Vn,{value:"account-details",children:[c.jsx(Un,{className:Q("no-underline hover:no-underline justify-end gap-0 flex-row-reverse p-0 px-0.5 pr-2","[&>svg]:-rotate-90 [&[data-state=open]>svg]:rotate-0 cursor-pointer [&>svg]:-mt-0.5 mt-2"),children:c.jsx("div",{className:"flex flex-row flex-wrap justify-between items-center w-full",children:c.jsx("span",{className:"text-sm uppercase font-bold tracking-tight",children:"Account"})})}),c.jsx(Wn,{className:"pb-0",children:c.jsxs("div",{className:"pl-5 mt-1 flex flex-col gap-0.5",children:[c.jsxs("div",{className:"flex flex-row items-center gap-1 h-6 leading-[22px]",children:[c.jsxs(dv,{className:"size-6",children:[c.jsx(fv,{src:e==null?void 0:e.avatar_url,alt:"@avatar"}),c.jsx(pv,{children:c.jsx("span",{className:"codicon codicon-account !text-md"})})]}),c.jsxs("div",{className:"flex flex-row gap-1 grow pr-2 items-center w-full truncate",children:[c.jsx("span",{children:e==null?void 0:e.name}),c.jsx("span",{className:"text-md text-muted-foreground truncate",children:e!=null&&e.user_name?c.jsxs(c.Fragment,{children:["@",e.user_name]}):c.jsx(c.Fragment,{})})]})]}),t&&c.jsxs("div",{className:"flex flex-row items-center gap-1 h-6 leading-[22px] group",children:[c.jsx("span",{className:"codicon codicon-organization"}),c.jsx("div",{className:"flex flex-row gap-1 items-center",children:c.jsx("span",{children:t.name})}),c.jsx("span",{onClick:()=>{n()},className:"codicon codicon-symbol-property opacity-0 group-hover:opacity-100 transition-opacity duration-200 cursor-pointer"})]})]})})]})})},Mf=e=>typeof e=="boolean"?`${e}`:e===0?"0":e,Pf=Mm,hv=(e,t)=>n=>{var r;if((t==null?void 0:t.variants)==null)return Pf(e,n==null?void 0:n.class,n==null?void 0:n.className);const{variants:o,defaultVariants:s}=t,i=Object.keys(o).map(u=>{const d=n==null?void 0:n[u],f=s==null?void 0:s[u];if(d===null)return null;const p=Mf(d)||Mf(f);return o[u][p]}),a=n&&Object.entries(n).reduce((u,d)=>{let[f,p]=d;return p===void 0||(u[f]=p),u},{}),l=t==null||(r=t.compoundVariants)===null||r===void 0?void 0:r.reduce((u,d)=>{let{class:f,className:p,...y}=d;return Object.entries(y).every(w=>{let[g,k]=w;return Array.isArray(k)?k.includes({...s,...a}[g]):{...s,...a}[g]===k})?[...u,f,p]:u},[]);return Pf(e,i,l,n==null?void 0:n.class,n==null?void 0:n.className)},E_=hv(Q("inline-flex items-center justify-center text-md rounded-[10px] border transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2","px-1.5 py-0"),{variants:{variant:{primary:"bg-[var(--vscode-badge-background)] text-[var(--vscode-badge-foreground)] border-transparent",secondary:"bg-accent text-accent-foreground border-border"}},defaultVariants:{variant:"primary"}});function Wo({className:e,variant:t,...n}){return c.jsx("div",{className:Q(E_({variant:t}),e),...n})}function b_(e,t=globalThis==null?void 0:globalThis.document){const n=mt(e);v.useEffect(()=>{const r=o=>{o.key==="Escape"&&n(o)};return t.addEventListener("keydown",r,{capture:!0}),()=>t.removeEventListener("keydown",r,{capture:!0})},[n,t])}var N_="DismissableLayer",ju="dismissableLayer.update",T_="dismissableLayer.pointerDownOutside",R_="dismissableLayer.focusOutside",Af,mv=v.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),Hc=v.forwardRef((e,t)=>{const{disableOutsidePointerEvents:n=!1,onEscapeKeyDown:r,onPointerDownOutside:o,onFocusOutside:s,onInteractOutside:i,onDismiss:a,...l}=e,u=v.useContext(mv),[d,f]=v.useState(null),p=(d==null?void 0:d.ownerDocument)??(globalThis==null?void 0:globalThis.document),[,y]=v.useState({}),w=Pe(t,S=>f(S)),g=Array.from(u.layers),[k]=[...u.layersWithOutsidePointerEventsDisabled].slice(-1),m=g.indexOf(k),h=d?g.indexOf(d):-1,x=u.layersWithOutsidePointerEventsDisabled.size>0,_=h>=m,b=P_(S=>{const C=S.target,L=[...u.branches].some(M=>M.contains(C));!_||L||(o==null||o(S),i==null||i(S),S.defaultPrevented||a==null||a())},p),N=A_(S=>{const C=S.target;[...u.branches].some(M=>M.contains(C))||(s==null||s(S),i==null||i(S),S.defaultPrevented||a==null||a())},p);return b_(S=>{h===u.layers.size-1&&(r==null||r(S),!S.defaultPrevented&&a&&(S.preventDefault(),a()))},p),v.useEffect(()=>{if(d)return n&&(u.layersWithOutsidePointerEventsDisabled.size===0&&(Af=p.body.style.pointerEvents,p.body.style.pointerEvents="none"),u.layersWithOutsidePointerEventsDisabled.add(d)),u.layers.add(d),Of(),()=>{n&&u.layersWithOutsidePointerEventsDisabled.size===1&&(p.body.style.pointerEvents=Af)}},[d,p,n,u]),v.useEffect(()=>()=>{d&&(u.layers.delete(d),u.layersWithOutsidePointerEventsDisabled.delete(d),Of())},[d,u]),v.useEffect(()=>{const S=()=>y({});return document.addEventListener(ju,S),()=>document.removeEventListener(ju,S)},[]),c.jsx(de.div,{...l,ref:w,style:{pointerEvents:x?_?"auto":"none":void 0,...e.style},onFocusCapture:X(e.onFocusCapture,N.onFocusCapture),onBlurCapture:X(e.onBlurCapture,N.onBlurCapture),onPointerDownCapture:X(e.onPointerDownCapture,b.onPointerDownCapture)})});Hc.displayName=N_;var j_="DismissableLayerBranch",M_=v.forwardRef((e,t)=>{const n=v.useContext(mv),r=v.useRef(null),o=Pe(t,r);return v.useEffect(()=>{const s=r.current;if(s)return n.branches.add(s),()=>{n.branches.delete(s)}},[n.branches]),c.jsx(de.div,{...e,ref:o})});M_.displayName=j_;function P_(e,t=globalThis==null?void 0:globalThis.document){const n=mt(e),r=v.useRef(!1),o=v.useRef(()=>{});return v.useEffect(()=>{const s=a=>{if(a.target&&!r.current){let l=function(){vv(T_,n,u,{discrete:!0})};const u={originalEvent:a};a.pointerType==="touch"?(t.removeEventListener("click",o.current),o.current=l,t.addEventListener("click",o.current,{once:!0})):l()}else t.removeEventListener("click",o.current);r.current=!1},i=window.setTimeout(()=>{t.addEventListener("pointerdown",s)},0);return()=>{window.clearTimeout(i),t.removeEventListener("pointerdown",s),t.removeEventListener("click",o.current)}},[t,n]),{onPointerDownCapture:()=>r.current=!0}}function A_(e,t=globalThis==null?void 0:globalThis.document){const n=mt(e),r=v.useRef(!1);return v.useEffect(()=>{const o=s=>{s.target&&!r.current&&vv(R_,n,{originalEvent:s},{discrete:!1})};return t.addEventListener("focusin",o),()=>t.removeEventListener("focusin",o)},[t,n]),{onFocusCapture:()=>r.current=!0,onBlurCapture:()=>r.current=!1}}function Of(){const e=new CustomEvent(ju);document.dispatchEvent(e)}function vv(e,t,n,{discrete:r}){const o=n.originalEvent.target,s=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:n});t&&o.addEventListener(e,t,{once:!0}),r?$m(o,s):o.dispatchEvent(s)}const O_=["top","right","bottom","left"],Hn=Math.min,et=Math.max,ra=Math.round,ni=Math.floor,zt=e=>({x:e,y:e}),I_={left:"right",right:"left",bottom:"top",top:"bottom"},D_={start:"end",end:"start"};function Mu(e,t,n){return et(e,Hn(t,n))}function cn(e,t){return typeof e=="function"?e(t):e}function dn(e){return e.split("-")[0]}function vo(e){return e.split("-")[1]}function Zc(e){return e==="x"?"y":"x"}function Gc(e){return e==="y"?"height":"width"}function Zn(e){return["top","bottom"].includes(dn(e))?"y":"x"}function Yc(e){return Zc(Zn(e))}function L_(e,t,n){n===void 0&&(n=!1);const r=vo(e),o=Yc(e),s=Gc(o);let i=o==="x"?r===(n?"end":"start")?"right":"left":r==="start"?"bottom":"top";return t.reference[s]>t.floating[s]&&(i=oa(i)),[i,oa(i)]}function $_(e){const t=oa(e);return[Pu(e),t,Pu(t)]}function Pu(e){return e.replace(/start|end/g,t=>D_[t])}function z_(e,t,n){const r=["left","right"],o=["right","left"],s=["top","bottom"],i=["bottom","top"];switch(e){case"top":case"bottom":return n?t?o:r:t?r:o;case"left":case"right":return t?s:i;default:return[]}}function F_(e,t,n,r){const o=vo(e);let s=z_(dn(e),n==="start",r);return o&&(s=s.map(i=>i+"-"+o),t&&(s=s.concat(s.map(Pu)))),s}function oa(e){return e.replace(/left|right|bottom|top/g,t=>I_[t])}function B_(e){return{top:0,right:0,bottom:0,left:0,...e}}function gv(e){return typeof e!="number"?B_(e):{top:e,right:e,bottom:e,left:e}}function sa(e){const{x:t,y:n,width:r,height:o}=e;return{width:r,height:o,top:n,left:t,right:t+r,bottom:n+o,x:t,y:n}}function If(e,t,n){let{reference:r,floating:o}=e;const s=Zn(t),i=Yc(t),a=Gc(i),l=dn(t),u=s==="y",d=r.x+r.width/2-o.width/2,f=r.y+r.height/2-o.height/2,p=r[a]/2-o[a]/2;let y;switch(l){case"top":y={x:d,y:r.y-o.height};break;case"bottom":y={x:d,y:r.y+r.height};break;case"right":y={x:r.x+r.width,y:f};break;case"left":y={x:r.x-o.width,y:f};break;default:y={x:r.x,y:r.y}}switch(vo(t)){case"start":y[i]-=p*(n&&u?-1:1);break;case"end":y[i]+=p*(n&&u?-1:1);break}return y}const V_=async(e,t,n)=>{const{placement:r="bottom",strategy:o="absolute",middleware:s=[],platform:i}=n,a=s.filter(Boolean),l=await(i.isRTL==null?void 0:i.isRTL(t));let u=await i.getElementRects({reference:e,floating:t,strategy:o}),{x:d,y:f}=If(u,r,l),p=r,y={},w=0;for(let g=0;g<a.length;g++){const{name:k,fn:m}=a[g],{x:h,y:x,data:_,reset:b}=await m({x:d,y:f,initialPlacement:r,placement:p,strategy:o,middlewareData:y,rects:u,platform:i,elements:{reference:e,floating:t}});d=h??d,f=x??f,y={...y,[k]:{...y[k],..._}},b&&w<=50&&(w++,typeof b=="object"&&(b.placement&&(p=b.placement),b.rects&&(u=b.rects===!0?await i.getElementRects({reference:e,floating:t,strategy:o}):b.rects),{x:d,y:f}=If(u,p,l)),g=-1)}return{x:d,y:f,placement:p,strategy:o,middlewareData:y}};async function Cs(e,t){var n;t===void 0&&(t={});const{x:r,y:o,platform:s,rects:i,elements:a,strategy:l}=e,{boundary:u="clippingAncestors",rootBoundary:d="viewport",elementContext:f="floating",altBoundary:p=!1,padding:y=0}=cn(t,e),w=gv(y),k=a[p?f==="floating"?"reference":"floating":f],m=sa(await s.getClippingRect({element:(n=await(s.isElement==null?void 0:s.isElement(k)))==null||n?k:k.contextElement||await(s.getDocumentElement==null?void 0:s.getDocumentElement(a.floating)),boundary:u,rootBoundary:d,strategy:l})),h=f==="floating"?{x:r,y:o,width:i.floating.width,height:i.floating.height}:i.reference,x=await(s.getOffsetParent==null?void 0:s.getOffsetParent(a.floating)),_=await(s.isElement==null?void 0:s.isElement(x))?await(s.getScale==null?void 0:s.getScale(x))||{x:1,y:1}:{x:1,y:1},b=sa(s.convertOffsetParentRelativeRectToViewportRelativeRect?await s.convertOffsetParentRelativeRectToViewportRelativeRect({elements:a,rect:h,offsetParent:x,strategy:l}):h);return{top:(m.top-b.top+w.top)/_.y,bottom:(b.bottom-m.bottom+w.bottom)/_.y,left:(m.left-b.left+w.left)/_.x,right:(b.right-m.right+w.right)/_.x}}const U_=e=>({name:"arrow",options:e,async fn(t){const{x:n,y:r,placement:o,rects:s,platform:i,elements:a,middlewareData:l}=t,{element:u,padding:d=0}=cn(e,t)||{};if(u==null)return{};const f=gv(d),p={x:n,y:r},y=Yc(o),w=Gc(y),g=await i.getDimensions(u),k=y==="y",m=k?"top":"left",h=k?"bottom":"right",x=k?"clientHeight":"clientWidth",_=s.reference[w]+s.reference[y]-p[y]-s.floating[w],b=p[y]-s.reference[y],N=await(i.getOffsetParent==null?void 0:i.getOffsetParent(u));let S=N?N[x]:0;(!S||!await(i.isElement==null?void 0:i.isElement(N)))&&(S=a.floating[x]||s.floating[w]);const C=_/2-b/2,L=S/2-g[w]/2-1,M=Hn(f[m],L),$=Hn(f[h],L),V=M,G=S-g[w]-$,I=S/2-g[w]/2+C,D=Mu(V,I,G),j=!l.arrow&&vo(o)!=null&&I!==D&&s.reference[w]/2-(I<V?M:$)-g[w]/2<0,z=j?I<V?I-V:I-G:0;return{[y]:p[y]+z,data:{[y]:D,centerOffset:I-D-z,...j&&{alignmentOffset:z}},reset:j}}}),W_=function(e){return e===void 0&&(e={}),{name:"flip",options:e,async fn(t){var n,r;const{placement:o,middlewareData:s,rects:i,initialPlacement:a,platform:l,elements:u}=t,{mainAxis:d=!0,crossAxis:f=!0,fallbackPlacements:p,fallbackStrategy:y="bestFit",fallbackAxisSideDirection:w="none",flipAlignment:g=!0,...k}=cn(e,t);if((n=s.arrow)!=null&&n.alignmentOffset)return{};const m=dn(o),h=Zn(a),x=dn(a)===a,_=await(l.isRTL==null?void 0:l.isRTL(u.floating)),b=p||(x||!g?[oa(a)]:$_(a)),N=w!=="none";!p&&N&&b.push(...F_(a,g,w,_));const S=[a,...b],C=await Cs(t,k),L=[];let M=((r=s.flip)==null?void 0:r.overflows)||[];if(d&&L.push(C[m]),f){const I=L_(o,i,_);L.push(C[I[0]],C[I[1]])}if(M=[...M,{placement:o,overflows:L}],!L.every(I=>I<=0)){var $,V;const I=((($=s.flip)==null?void 0:$.index)||0)+1,D=S[I];if(D)return{data:{index:I,overflows:M},reset:{placement:D}};let j=(V=M.filter(z=>z.overflows[0]<=0).sort((z,E)=>z.overflows[1]-E.overflows[1])[0])==null?void 0:V.placement;if(!j)switch(y){case"bestFit":{var G;const z=(G=M.filter(E=>{if(N){const T=Zn(E.placement);return T===h||T==="y"}return!0}).map(E=>[E.placement,E.overflows.filter(T=>T>0).reduce((T,P)=>T+P,0)]).sort((E,T)=>E[1]-T[1])[0])==null?void 0:G[0];z&&(j=z);break}case"initialPlacement":j=a;break}if(o!==j)return{reset:{placement:j}}}return{}}}};function Df(e,t){return{top:e.top-t.height,right:e.right-t.width,bottom:e.bottom-t.height,left:e.left-t.width}}function Lf(e){return O_.some(t=>e[t]>=0)}const H_=function(e){return e===void 0&&(e={}),{name:"hide",options:e,async fn(t){const{rects:n}=t,{strategy:r="referenceHidden",...o}=cn(e,t);switch(r){case"referenceHidden":{const s=await Cs(t,{...o,elementContext:"reference"}),i=Df(s,n.reference);return{data:{referenceHiddenOffsets:i,referenceHidden:Lf(i)}}}case"escaped":{const s=await Cs(t,{...o,altBoundary:!0}),i=Df(s,n.floating);return{data:{escapedOffsets:i,escaped:Lf(i)}}}default:return{}}}}};async function Z_(e,t){const{placement:n,platform:r,elements:o}=e,s=await(r.isRTL==null?void 0:r.isRTL(o.floating)),i=dn(n),a=vo(n),l=Zn(n)==="y",u=["left","top"].includes(i)?-1:1,d=s&&l?-1:1,f=cn(t,e);let{mainAxis:p,crossAxis:y,alignmentAxis:w}=typeof f=="number"?{mainAxis:f,crossAxis:0,alignmentAxis:null}:{mainAxis:f.mainAxis||0,crossAxis:f.crossAxis||0,alignmentAxis:f.alignmentAxis};return a&&typeof w=="number"&&(y=a==="end"?w*-1:w),l?{x:y*d,y:p*u}:{x:p*u,y:y*d}}const G_=function(e){return e===void 0&&(e=0),{name:"offset",options:e,async fn(t){var n,r;const{x:o,y:s,placement:i,middlewareData:a}=t,l=await Z_(t,e);return i===((n=a.offset)==null?void 0:n.placement)&&(r=a.arrow)!=null&&r.alignmentOffset?{}:{x:o+l.x,y:s+l.y,data:{...l,placement:i}}}}},Y_=function(e){return e===void 0&&(e={}),{name:"shift",options:e,async fn(t){const{x:n,y:r,placement:o}=t,{mainAxis:s=!0,crossAxis:i=!1,limiter:a={fn:k=>{let{x:m,y:h}=k;return{x:m,y:h}}},...l}=cn(e,t),u={x:n,y:r},d=await Cs(t,l),f=Zn(dn(o)),p=Zc(f);let y=u[p],w=u[f];if(s){const k=p==="y"?"top":"left",m=p==="y"?"bottom":"right",h=y+d[k],x=y-d[m];y=Mu(h,y,x)}if(i){const k=f==="y"?"top":"left",m=f==="y"?"bottom":"right",h=w+d[k],x=w-d[m];w=Mu(h,w,x)}const g=a.fn({...t,[p]:y,[f]:w});return{...g,data:{x:g.x-n,y:g.y-r,enabled:{[p]:s,[f]:i}}}}}},K_=function(e){return e===void 0&&(e={}),{options:e,fn(t){const{x:n,y:r,placement:o,rects:s,middlewareData:i}=t,{offset:a=0,mainAxis:l=!0,crossAxis:u=!0}=cn(e,t),d={x:n,y:r},f=Zn(o),p=Zc(f);let y=d[p],w=d[f];const g=cn(a,t),k=typeof g=="number"?{mainAxis:g,crossAxis:0}:{mainAxis:0,crossAxis:0,...g};if(l){const x=p==="y"?"height":"width",_=s.reference[p]-s.floating[x]+k.mainAxis,b=s.reference[p]+s.reference[x]-k.mainAxis;y<_?y=_:y>b&&(y=b)}if(u){var m,h;const x=p==="y"?"width":"height",_=["top","left"].includes(dn(o)),b=s.reference[f]-s.floating[x]+(_&&((m=i.offset)==null?void 0:m[f])||0)+(_?0:k.crossAxis),N=s.reference[f]+s.reference[x]+(_?0:((h=i.offset)==null?void 0:h[f])||0)-(_?k.crossAxis:0);w<b?w=b:w>N&&(w=N)}return{[p]:y,[f]:w}}}},Q_=function(e){return e===void 0&&(e={}),{name:"size",options:e,async fn(t){var n,r;const{placement:o,rects:s,platform:i,elements:a}=t,{apply:l=()=>{},...u}=cn(e,t),d=await Cs(t,u),f=dn(o),p=vo(o),y=Zn(o)==="y",{width:w,height:g}=s.floating;let k,m;f==="top"||f==="bottom"?(k=f,m=p===(await(i.isRTL==null?void 0:i.isRTL(a.floating))?"start":"end")?"left":"right"):(m=f,k=p==="end"?"top":"bottom");const h=g-d.top-d.bottom,x=w-d.left-d.right,_=Hn(g-d[k],h),b=Hn(w-d[m],x),N=!t.middlewareData.shift;let S=_,C=b;if((n=t.middlewareData.shift)!=null&&n.enabled.x&&(C=x),(r=t.middlewareData.shift)!=null&&r.enabled.y&&(S=h),N&&!p){const M=et(d.left,0),$=et(d.right,0),V=et(d.top,0),G=et(d.bottom,0);y?C=w-2*(M!==0||$!==0?M+$:et(d.left,d.right)):S=g-2*(V!==0||G!==0?V+G:et(d.top,d.bottom))}await l({...t,availableWidth:C,availableHeight:S});const L=await i.getDimensions(a.floating);return w!==L.width||g!==L.height?{reset:{rects:!0}}:{}}}};function Pa(){return typeof window<"u"}function go(e){return yv(e)?(e.nodeName||"").toLowerCase():"#document"}function ot(e){var t;return(e==null||(t=e.ownerDocument)==null?void 0:t.defaultView)||window}function Zt(e){var t;return(t=(yv(e)?e.ownerDocument:e.document)||window.document)==null?void 0:t.documentElement}function yv(e){return Pa()?e instanceof Node||e instanceof ot(e).Node:!1}function Tt(e){return Pa()?e instanceof Element||e instanceof ot(e).Element:!1}function Ht(e){return Pa()?e instanceof HTMLElement||e instanceof ot(e).HTMLElement:!1}function $f(e){return!Pa()||typeof ShadowRoot>"u"?!1:e instanceof ShadowRoot||e instanceof ot(e).ShadowRoot}function Ps(e){const{overflow:t,overflowX:n,overflowY:r,display:o}=Rt(e);return/auto|scroll|overlay|hidden|clip/.test(t+r+n)&&!["inline","contents"].includes(o)}function X_(e){return["table","td","th"].includes(go(e))}function Aa(e){return[":popover-open",":modal"].some(t=>{try{return e.matches(t)}catch{return!1}})}function Kc(e){const t=Qc(),n=Tt(e)?Rt(e):e;return["transform","translate","scale","rotate","perspective"].some(r=>n[r]?n[r]!=="none":!1)||(n.containerType?n.containerType!=="normal":!1)||!t&&(n.backdropFilter?n.backdropFilter!=="none":!1)||!t&&(n.filter?n.filter!=="none":!1)||["transform","translate","scale","rotate","perspective","filter"].some(r=>(n.willChange||"").includes(r))||["paint","layout","strict","content"].some(r=>(n.contain||"").includes(r))}function J_(e){let t=Gn(e);for(;Ht(t)&&!lo(t);){if(Kc(t))return t;if(Aa(t))return null;t=Gn(t)}return null}function Qc(){return typeof CSS>"u"||!CSS.supports?!1:CSS.supports("-webkit-backdrop-filter","none")}function lo(e){return["html","body","#document"].includes(go(e))}function Rt(e){return ot(e).getComputedStyle(e)}function Oa(e){return Tt(e)?{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}:{scrollLeft:e.scrollX,scrollTop:e.scrollY}}function Gn(e){if(go(e)==="html")return e;const t=e.assignedSlot||e.parentNode||$f(e)&&e.host||Zt(e);return $f(t)?t.host:t}function xv(e){const t=Gn(e);return lo(t)?e.ownerDocument?e.ownerDocument.body:e.body:Ht(t)&&Ps(t)?t:xv(t)}function ks(e,t,n){var r;t===void 0&&(t=[]),n===void 0&&(n=!0);const o=xv(e),s=o===((r=e.ownerDocument)==null?void 0:r.body),i=ot(o);if(s){const a=Au(i);return t.concat(i,i.visualViewport||[],Ps(o)?o:[],a&&n?ks(a):[])}return t.concat(o,ks(o,[],n))}function Au(e){return e.parent&&Object.getPrototypeOf(e.parent)?e.frameElement:null}function wv(e){const t=Rt(e);let n=parseFloat(t.width)||0,r=parseFloat(t.height)||0;const o=Ht(e),s=o?e.offsetWidth:n,i=o?e.offsetHeight:r,a=ra(n)!==s||ra(r)!==i;return a&&(n=s,r=i),{width:n,height:r,$:a}}function Xc(e){return Tt(e)?e:e.contextElement}function Gr(e){const t=Xc(e);if(!Ht(t))return zt(1);const n=t.getBoundingClientRect(),{width:r,height:o,$:s}=wv(t);let i=(s?ra(n.width):n.width)/r,a=(s?ra(n.height):n.height)/o;return(!i||!Number.isFinite(i))&&(i=1),(!a||!Number.isFinite(a))&&(a=1),{x:i,y:a}}const q_=zt(0);function _v(e){const t=ot(e);return!Qc()||!t.visualViewport?q_:{x:t.visualViewport.offsetLeft,y:t.visualViewport.offsetTop}}function eC(e,t,n){return t===void 0&&(t=!1),!n||t&&n!==ot(e)?!1:t}function hr(e,t,n,r){t===void 0&&(t=!1),n===void 0&&(n=!1);const o=e.getBoundingClientRect(),s=Xc(e);let i=zt(1);t&&(r?Tt(r)&&(i=Gr(r)):i=Gr(e));const a=eC(s,n,r)?_v(s):zt(0);let l=(o.left+a.x)/i.x,u=(o.top+a.y)/i.y,d=o.width/i.x,f=o.height/i.y;if(s){const p=ot(s),y=r&&Tt(r)?ot(r):r;let w=p,g=Au(w);for(;g&&r&&y!==w;){const k=Gr(g),m=g.getBoundingClientRect(),h=Rt(g),x=m.left+(g.clientLeft+parseFloat(h.paddingLeft))*k.x,_=m.top+(g.clientTop+parseFloat(h.paddingTop))*k.y;l*=k.x,u*=k.y,d*=k.x,f*=k.y,l+=x,u+=_,w=ot(g),g=Au(w)}}return sa({width:d,height:f,x:l,y:u})}function Jc(e,t){const n=Oa(e).scrollLeft;return t?t.left+n:hr(Zt(e)).left+n}function Cv(e,t,n){n===void 0&&(n=!1);const r=e.getBoundingClientRect(),o=r.left+t.scrollLeft-(n?0:Jc(e,r)),s=r.top+t.scrollTop;return{x:o,y:s}}function tC(e){let{elements:t,rect:n,offsetParent:r,strategy:o}=e;const s=o==="fixed",i=Zt(r),a=t?Aa(t.floating):!1;if(r===i||a&&s)return n;let l={scrollLeft:0,scrollTop:0},u=zt(1);const d=zt(0),f=Ht(r);if((f||!f&&!s)&&((go(r)!=="body"||Ps(i))&&(l=Oa(r)),Ht(r))){const y=hr(r);u=Gr(r),d.x=y.x+r.clientLeft,d.y=y.y+r.clientTop}const p=i&&!f&&!s?Cv(i,l,!0):zt(0);return{width:n.width*u.x,height:n.height*u.y,x:n.x*u.x-l.scrollLeft*u.x+d.x+p.x,y:n.y*u.y-l.scrollTop*u.y+d.y+p.y}}function nC(e){return Array.from(e.getClientRects())}function rC(e){const t=Zt(e),n=Oa(e),r=e.ownerDocument.body,o=et(t.scrollWidth,t.clientWidth,r.scrollWidth,r.clientWidth),s=et(t.scrollHeight,t.clientHeight,r.scrollHeight,r.clientHeight);let i=-n.scrollLeft+Jc(e);const a=-n.scrollTop;return Rt(r).direction==="rtl"&&(i+=et(t.clientWidth,r.clientWidth)-o),{width:o,height:s,x:i,y:a}}function oC(e,t){const n=ot(e),r=Zt(e),o=n.visualViewport;let s=r.clientWidth,i=r.clientHeight,a=0,l=0;if(o){s=o.width,i=o.height;const u=Qc();(!u||u&&t==="fixed")&&(a=o.offsetLeft,l=o.offsetTop)}return{width:s,height:i,x:a,y:l}}function sC(e,t){const n=hr(e,!0,t==="fixed"),r=n.top+e.clientTop,o=n.left+e.clientLeft,s=Ht(e)?Gr(e):zt(1),i=e.clientWidth*s.x,a=e.clientHeight*s.y,l=o*s.x,u=r*s.y;return{width:i,height:a,x:l,y:u}}function zf(e,t,n){let r;if(t==="viewport")r=oC(e,n);else if(t==="document")r=rC(Zt(e));else if(Tt(t))r=sC(t,n);else{const o=_v(e);r={x:t.x-o.x,y:t.y-o.y,width:t.width,height:t.height}}return sa(r)}function kv(e,t){const n=Gn(e);return n===t||!Tt(n)||lo(n)?!1:Rt(n).position==="fixed"||kv(n,t)}function iC(e,t){const n=t.get(e);if(n)return n;let r=ks(e,[],!1).filter(a=>Tt(a)&&go(a)!=="body"),o=null;const s=Rt(e).position==="fixed";let i=s?Gn(e):e;for(;Tt(i)&&!lo(i);){const a=Rt(i),l=Kc(i);!l&&a.position==="fixed"&&(o=null),(s?!l&&!o:!l&&a.position==="static"&&!!o&&["absolute","fixed"].includes(o.position)||Ps(i)&&!l&&kv(e,i))?r=r.filter(d=>d!==i):o=a,i=Gn(i)}return t.set(e,r),r}function aC(e){let{element:t,boundary:n,rootBoundary:r,strategy:o}=e;const i=[...n==="clippingAncestors"?Aa(t)?[]:iC(t,this._c):[].concat(n),r],a=i[0],l=i.reduce((u,d)=>{const f=zf(t,d,o);return u.top=et(f.top,u.top),u.right=Hn(f.right,u.right),u.bottom=Hn(f.bottom,u.bottom),u.left=et(f.left,u.left),u},zf(t,a,o));return{width:l.right-l.left,height:l.bottom-l.top,x:l.left,y:l.top}}function lC(e){const{width:t,height:n}=wv(e);return{width:t,height:n}}function uC(e,t,n){const r=Ht(t),o=Zt(t),s=n==="fixed",i=hr(e,!0,s,t);let a={scrollLeft:0,scrollTop:0};const l=zt(0);if(r||!r&&!s)if((go(t)!=="body"||Ps(o))&&(a=Oa(t)),r){const p=hr(t,!0,s,t);l.x=p.x+t.clientLeft,l.y=p.y+t.clientTop}else o&&(l.x=Jc(o));const u=o&&!r&&!s?Cv(o,a):zt(0),d=i.left+a.scrollLeft-l.x-u.x,f=i.top+a.scrollTop-l.y-u.y;return{x:d,y:f,width:i.width,height:i.height}}function xl(e){return Rt(e).position==="static"}function Ff(e,t){if(!Ht(e)||Rt(e).position==="fixed")return null;if(t)return t(e);let n=e.offsetParent;return Zt(e)===n&&(n=n.ownerDocument.body),n}function Sv(e,t){const n=ot(e);if(Aa(e))return n;if(!Ht(e)){let o=Gn(e);for(;o&&!lo(o);){if(Tt(o)&&!xl(o))return o;o=Gn(o)}return n}let r=Ff(e,t);for(;r&&X_(r)&&xl(r);)r=Ff(r,t);return r&&lo(r)&&xl(r)&&!Kc(r)?n:r||J_(e)||n}const cC=async function(e){const t=this.getOffsetParent||Sv,n=this.getDimensions,r=await n(e.floating);return{reference:uC(e.reference,await t(e.floating),e.strategy),floating:{x:0,y:0,width:r.width,height:r.height}}};function dC(e){return Rt(e).direction==="rtl"}const fC={convertOffsetParentRelativeRectToViewportRelativeRect:tC,getDocumentElement:Zt,getClippingRect:aC,getOffsetParent:Sv,getElementRects:cC,getClientRects:nC,getDimensions:lC,getScale:Gr,isElement:Tt,isRTL:dC};function Ev(e,t){return e.x===t.x&&e.y===t.y&&e.width===t.width&&e.height===t.height}function pC(e,t){let n=null,r;const o=Zt(e);function s(){var a;clearTimeout(r),(a=n)==null||a.disconnect(),n=null}function i(a,l){a===void 0&&(a=!1),l===void 0&&(l=1),s();const u=e.getBoundingClientRect(),{left:d,top:f,width:p,height:y}=u;if(a||t(),!p||!y)return;const w=ni(f),g=ni(o.clientWidth-(d+p)),k=ni(o.clientHeight-(f+y)),m=ni(d),x={rootMargin:-w+"px "+-g+"px "+-k+"px "+-m+"px",threshold:et(0,Hn(1,l))||1};let _=!0;function b(N){const S=N[0].intersectionRatio;if(S!==l){if(!_)return i();S?i(!1,S):r=setTimeout(()=>{i(!1,1e-7)},1e3)}S===1&&!Ev(u,e.getBoundingClientRect())&&i(),_=!1}try{n=new IntersectionObserver(b,{...x,root:o.ownerDocument})}catch{n=new IntersectionObserver(b,x)}n.observe(e)}return i(!0),s}function hC(e,t,n,r){r===void 0&&(r={});const{ancestorScroll:o=!0,ancestorResize:s=!0,elementResize:i=typeof ResizeObserver=="function",layoutShift:a=typeof IntersectionObserver=="function",animationFrame:l=!1}=r,u=Xc(e),d=o||s?[...u?ks(u):[],...ks(t)]:[];d.forEach(m=>{o&&m.addEventListener("scroll",n,{passive:!0}),s&&m.addEventListener("resize",n)});const f=u&&a?pC(u,n):null;let p=-1,y=null;i&&(y=new ResizeObserver(m=>{let[h]=m;h&&h.target===u&&y&&(y.unobserve(t),cancelAnimationFrame(p),p=requestAnimationFrame(()=>{var x;(x=y)==null||x.observe(t)})),n()}),u&&!l&&y.observe(u),y.observe(t));let w,g=l?hr(e):null;l&&k();function k(){const m=hr(e);g&&!Ev(g,m)&&n(),g=m,w=requestAnimationFrame(k)}return n(),()=>{var m;d.forEach(h=>{o&&h.removeEventListener("scroll",n),s&&h.removeEventListener("resize",n)}),f==null||f(),(m=y)==null||m.disconnect(),y=null,l&&cancelAnimationFrame(w)}}const mC=G_,vC=Y_,gC=W_,yC=Q_,xC=H_,Bf=U_,wC=K_,_C=(e,t,n)=>{const r=new Map,o={platform:fC,...n},s={...o.platform,_c:r};return V_(e,t,{...o,platform:s})};var _i=typeof document<"u"?v.useLayoutEffect:v.useEffect;function ia(e,t){if(e===t)return!0;if(typeof e!=typeof t)return!1;if(typeof e=="function"&&e.toString()===t.toString())return!0;let n,r,o;if(e&&t&&typeof e=="object"){if(Array.isArray(e)){if(n=e.length,n!==t.length)return!1;for(r=n;r--!==0;)if(!ia(e[r],t[r]))return!1;return!0}if(o=Object.keys(e),n=o.length,n!==Object.keys(t).length)return!1;for(r=n;r--!==0;)if(!{}.hasOwnProperty.call(t,o[r]))return!1;for(r=n;r--!==0;){const s=o[r];if(!(s==="_owner"&&e.$$typeof)&&!ia(e[s],t[s]))return!1}return!0}return e!==e&&t!==t}function bv(e){return typeof window>"u"?1:(e.ownerDocument.defaultView||window).devicePixelRatio||1}function Vf(e,t){const n=bv(e);return Math.round(t*n)/n}function wl(e){const t=v.useRef(e);return _i(()=>{t.current=e}),t}function CC(e){e===void 0&&(e={});const{placement:t="bottom",strategy:n="absolute",middleware:r=[],platform:o,elements:{reference:s,floating:i}={},transform:a=!0,whileElementsMounted:l,open:u}=e,[d,f]=v.useState({x:0,y:0,strategy:n,placement:t,middlewareData:{},isPositioned:!1}),[p,y]=v.useState(r);ia(p,r)||y(r);const[w,g]=v.useState(null),[k,m]=v.useState(null),h=v.useCallback(E=>{E!==N.current&&(N.current=E,g(E))},[]),x=v.useCallback(E=>{E!==S.current&&(S.current=E,m(E))},[]),_=s||w,b=i||k,N=v.useRef(null),S=v.useRef(null),C=v.useRef(d),L=l!=null,M=wl(l),$=wl(o),V=wl(u),G=v.useCallback(()=>{if(!N.current||!S.current)return;const E={placement:t,strategy:n,middleware:p};$.current&&(E.platform=$.current),_C(N.current,S.current,E).then(T=>{const P={...T,isPositioned:V.current!==!1};I.current&&!ia(C.current,P)&&(C.current=P,ba.flushSync(()=>{f(P)}))})},[p,t,n,$,V]);_i(()=>{u===!1&&C.current.isPositioned&&(C.current.isPositioned=!1,f(E=>({...E,isPositioned:!1})))},[u]);const I=v.useRef(!1);_i(()=>(I.current=!0,()=>{I.current=!1}),[]),_i(()=>{if(_&&(N.current=_),b&&(S.current=b),_&&b){if(M.current)return M.current(_,b,G);G()}},[_,b,G,M,L]);const D=v.useMemo(()=>({reference:N,floating:S,setReference:h,setFloating:x}),[h,x]),j=v.useMemo(()=>({reference:_,floating:b}),[_,b]),z=v.useMemo(()=>{const E={position:n,left:0,top:0};if(!j.floating)return E;const T=Vf(j.floating,d.x),P=Vf(j.floating,d.y);return a?{...E,transform:"translate("+T+"px, "+P+"px)",...bv(j.floating)>=1.5&&{willChange:"transform"}}:{position:n,left:T,top:P}},[n,a,j.floating,d.x,d.y]);return v.useMemo(()=>({...d,update:G,refs:D,elements:j,floatingStyles:z}),[d,G,D,j,z])}const kC=e=>{function t(n){return{}.hasOwnProperty.call(n,"current")}return{name:"arrow",options:e,fn(n){const{element:r,padding:o}=typeof e=="function"?e(n):e;return r&&t(r)?r.current!=null?Bf({element:r.current,padding:o}).fn(n):{}:r?Bf({element:r,padding:o}).fn(n):{}}}},SC=(e,t)=>({...mC(e),options:[e,t]}),EC=(e,t)=>({...vC(e),options:[e,t]}),bC=(e,t)=>({...wC(e),options:[e,t]}),NC=(e,t)=>({...gC(e),options:[e,t]}),TC=(e,t)=>({...yC(e),options:[e,t]}),RC=(e,t)=>({...xC(e),options:[e,t]}),jC=(e,t)=>({...kC(e),options:[e,t]});var MC="Arrow",Nv=v.forwardRef((e,t)=>{const{children:n,width:r=10,height:o=5,...s}=e;return c.jsx(de.svg,{...s,ref:t,width:r,height:o,viewBox:"0 0 30 10",preserveAspectRatio:"none",children:e.asChild?n:c.jsx("polygon",{points:"0,0 30,0 15,10"})})});Nv.displayName=MC;var PC=Nv;function AC(e){const[t,n]=v.useState(void 0);return Wt(()=>{if(e){n({width:e.offsetWidth,height:e.offsetHeight});const r=new ResizeObserver(o=>{if(!Array.isArray(o)||!o.length)return;const s=o[0];let i,a;if("borderBoxSize"in s){const l=s.borderBoxSize,u=Array.isArray(l)?l[0]:l;i=u.inlineSize,a=u.blockSize}else i=e.offsetWidth,a=e.offsetHeight;n({width:i,height:a})});return r.observe(e,{box:"border-box"}),()=>r.unobserve(e)}else n(void 0)},[e]),t}var qc="Popper",[Tv,Ia]=hn(qc),[OC,Rv]=Tv(qc),jv=e=>{const{__scopePopper:t,children:n}=e,[r,o]=v.useState(null);return c.jsx(OC,{scope:t,anchor:r,onAnchorChange:o,children:n})};jv.displayName=qc;var Mv="PopperAnchor",Pv=v.forwardRef((e,t)=>{const{__scopePopper:n,virtualRef:r,...o}=e,s=Rv(Mv,n),i=v.useRef(null),a=Pe(t,i);return v.useEffect(()=>{s.onAnchorChange((r==null?void 0:r.current)||i.current)}),r?null:c.jsx(de.div,{...o,ref:a})});Pv.displayName=Mv;var ed="PopperContent",[IC,DC]=Tv(ed),Av=v.forwardRef((e,t)=>{var ye,vt,Ee,be,gt,vn;const{__scopePopper:n,side:r="bottom",sideOffset:o=0,align:s="center",alignOffset:i=0,arrowPadding:a=0,avoidCollisions:l=!0,collisionBoundary:u=[],collisionPadding:d=0,sticky:f="partial",hideWhenDetached:p=!1,updatePositionStrategy:y="optimized",onPlaced:w,...g}=e,k=Rv(ed,n),[m,h]=v.useState(null),x=Pe(t,jt=>h(jt)),[_,b]=v.useState(null),N=AC(_),S=(N==null?void 0:N.width)??0,C=(N==null?void 0:N.height)??0,L=r+(s!=="center"?"-"+s:""),M=typeof d=="number"?d:{top:0,right:0,bottom:0,left:0,...d},$=Array.isArray(u)?u:[u],V=$.length>0,G={padding:M,boundary:$.filter($C),altBoundary:V},{refs:I,floatingStyles:D,placement:j,isPositioned:z,middlewareData:E}=CC({strategy:"fixed",placement:L,whileElementsMounted:(...jt)=>hC(...jt,{animationFrame:y==="always"}),elements:{reference:k.anchor},middleware:[SC({mainAxis:o+C,alignmentAxis:i}),l&&EC({mainAxis:!0,crossAxis:!1,limiter:f==="partial"?bC():void 0,...G}),l&&NC({...G}),TC({...G,apply:({elements:jt,rects:pd,availableWidth:vy,availableHeight:gy})=>{const{width:yy,height:xy}=pd.reference,Ds=jt.floating.style;Ds.setProperty("--radix-popper-available-width",`${vy}px`),Ds.setProperty("--radix-popper-available-height",`${gy}px`),Ds.setProperty("--radix-popper-anchor-width",`${yy}px`),Ds.setProperty("--radix-popper-anchor-height",`${xy}px`)}}),_&&jC({element:_,padding:a}),zC({arrowWidth:S,arrowHeight:C}),p&&RC({strategy:"referenceHidden",...G})]}),[T,P]=Dv(j),B=mt(w);Wt(()=>{z&&(B==null||B())},[z,B]);const Z=(ye=E.arrow)==null?void 0:ye.x,se=(vt=E.arrow)==null?void 0:vt.y,oe=((Ee=E.arrow)==null?void 0:Ee.centerOffset)!==0,[_e,ge]=v.useState();return Wt(()=>{m&&ge(window.getComputedStyle(m).zIndex)},[m]),c.jsx("div",{ref:I.setFloating,"data-radix-popper-content-wrapper":"",style:{...D,transform:z?D.transform:"translate(0, -200%)",minWidth:"max-content",zIndex:_e,"--radix-popper-transform-origin":[(be=E.transformOrigin)==null?void 0:be.x,(gt=E.transformOrigin)==null?void 0:gt.y].join(" "),...((vn=E.hide)==null?void 0:vn.referenceHidden)&&{visibility:"hidden",pointerEvents:"none"}},dir:e.dir,children:c.jsx(IC,{scope:n,placedSide:T,onArrowChange:b,arrowX:Z,arrowY:se,shouldHideArrow:oe,children:c.jsx(de.div,{"data-side":T,"data-align":P,...g,ref:x,style:{...g.style,animation:z?void 0:"none"}})})})});Av.displayName=ed;var Ov="PopperArrow",LC={top:"bottom",right:"left",bottom:"top",left:"right"},Iv=v.forwardRef(function(t,n){const{__scopePopper:r,...o}=t,s=DC(Ov,r),i=LC[s.placedSide];return c.jsx("span",{ref:s.onArrowChange,style:{position:"absolute",left:s.arrowX,top:s.arrowY,[i]:0,transformOrigin:{top:"",right:"0 0",bottom:"center 0",left:"100% 0"}[s.placedSide],transform:{top:"translateY(100%)",right:"translateY(50%) rotate(90deg) translateX(-50%)",bottom:"rotate(180deg)",left:"translateY(50%) rotate(-90deg) translateX(50%)"}[s.placedSide],visibility:s.shouldHideArrow?"hidden":void 0},children:c.jsx(PC,{...o,ref:n,style:{...o.style,display:"block"}})})});Iv.displayName=Ov;function $C(e){return e!==null}var zC=e=>({name:"transformOrigin",options:e,fn(t){var k,m,h;const{placement:n,rects:r,middlewareData:o}=t,i=((k=o.arrow)==null?void 0:k.centerOffset)!==0,a=i?0:e.arrowWidth,l=i?0:e.arrowHeight,[u,d]=Dv(n),f={start:"0%",center:"50%",end:"100%"}[d],p=(((m=o.arrow)==null?void 0:m.x)??0)+a/2,y=(((h=o.arrow)==null?void 0:h.y)??0)+l/2;let w="",g="";return u==="bottom"?(w=i?f:`${p}px`,g=`${-l}px`):u==="top"?(w=i?f:`${p}px`,g=`${r.floating.height+l}px`):u==="right"?(w=`${-l}px`,g=i?f:`${y}px`):u==="left"&&(w=`${r.floating.width+l}px`,g=i?f:`${y}px`),{data:{x:w,y:g}}}});function Dv(e){const[t,n="center"]=e.split("-");return[t,n]}var Lv=jv,$v=Pv,zv=Av,Fv=Iv,FC="Portal",td=v.forwardRef((e,t)=>{var a;const{container:n,...r}=e,[o,s]=v.useState(!1);Wt(()=>s(!0),[]);const i=n||o&&((a=globalThis==null?void 0:globalThis.document)==null?void 0:a.body);return i?Kw.createPortal(c.jsx(de.div,{...r,ref:t}),i):null});td.displayName=FC;var BC="VisuallyHidden",Bv=v.forwardRef((e,t)=>c.jsx(de.span,{...e,ref:t,style:{position:"absolute",border:0,width:1,height:1,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",wordWrap:"normal",...e.style}}));Bv.displayName=BC;var VC=Bv,[Da,mb]=hn("Tooltip",[Ia]),La=Ia(),Vv="TooltipProvider",UC=700,Ou="tooltip.open",[WC,nd]=Da(Vv),Uv=e=>{const{__scopeTooltip:t,delayDuration:n=UC,skipDelayDuration:r=300,disableHoverableContent:o=!1,children:s}=e,[i,a]=v.useState(!0),l=v.useRef(!1),u=v.useRef(0);return v.useEffect(()=>{const d=u.current;return()=>window.clearTimeout(d)},[]),c.jsx(WC,{scope:t,isOpenDelayed:i,delayDuration:n,onOpen:v.useCallback(()=>{window.clearTimeout(u.current),a(!1)},[]),onClose:v.useCallback(()=>{window.clearTimeout(u.current),u.current=window.setTimeout(()=>a(!0),r)},[r]),isPointerInTransitRef:l,onPointerInTransitChange:v.useCallback(d=>{l.current=d},[]),disableHoverableContent:o,children:s})};Uv.displayName=Vv;var $a="Tooltip",[HC,As]=Da($a),Wv=e=>{const{__scopeTooltip:t,children:n,open:r,defaultOpen:o=!1,onOpenChange:s,disableHoverableContent:i,delayDuration:a}=e,l=nd($a,e.__scopeTooltip),u=La(t),[d,f]=v.useState(null),p=ao(),y=v.useRef(0),w=i??l.disableHoverableContent,g=a??l.delayDuration,k=v.useRef(!1),[m=!1,h]=mo({prop:r,defaultProp:o,onChange:S=>{S?(l.onOpen(),document.dispatchEvent(new CustomEvent(Ou))):l.onClose(),s==null||s(S)}}),x=v.useMemo(()=>m?k.current?"delayed-open":"instant-open":"closed",[m]),_=v.useCallback(()=>{window.clearTimeout(y.current),y.current=0,k.current=!1,h(!0)},[h]),b=v.useCallback(()=>{window.clearTimeout(y.current),y.current=0,h(!1)},[h]),N=v.useCallback(()=>{window.clearTimeout(y.current),y.current=window.setTimeout(()=>{k.current=!0,h(!0),y.current=0},g)},[g,h]);return v.useEffect(()=>()=>{y.current&&(window.clearTimeout(y.current),y.current=0)},[]),c.jsx(Lv,{...u,children:c.jsx(HC,{scope:t,contentId:p,open:m,stateAttribute:x,trigger:d,onTriggerChange:f,onTriggerEnter:v.useCallback(()=>{l.isOpenDelayed?N():_()},[l.isOpenDelayed,N,_]),onTriggerLeave:v.useCallback(()=>{w?b():(window.clearTimeout(y.current),y.current=0)},[b,w]),onOpen:_,onClose:b,disableHoverableContent:w,children:n})})};Wv.displayName=$a;var Iu="TooltipTrigger",Hv=v.forwardRef((e,t)=>{const{__scopeTooltip:n,...r}=e,o=As(Iu,n),s=nd(Iu,n),i=La(n),a=v.useRef(null),l=Pe(t,a,o.onTriggerChange),u=v.useRef(!1),d=v.useRef(!1),f=v.useCallback(()=>u.current=!1,[]);return v.useEffect(()=>()=>document.removeEventListener("pointerup",f),[f]),c.jsx($v,{asChild:!0,...i,children:c.jsx(de.button,{"aria-describedby":o.open?o.contentId:void 0,"data-state":o.stateAttribute,...r,ref:l,onPointerMove:X(e.onPointerMove,p=>{p.pointerType!=="touch"&&!d.current&&!s.isPointerInTransitRef.current&&(o.onTriggerEnter(),d.current=!0)}),onPointerLeave:X(e.onPointerLeave,()=>{o.onTriggerLeave(),d.current=!1}),onPointerDown:X(e.onPointerDown,()=>{u.current=!0,document.addEventListener("pointerup",f,{once:!0})}),onFocus:X(e.onFocus,()=>{u.current||o.onOpen()}),onBlur:X(e.onBlur,o.onClose),onClick:X(e.onClick,o.onClose)})})});Hv.displayName=Iu;var rd="TooltipPortal",[ZC,GC]=Da(rd,{forceMount:void 0}),Zv=e=>{const{__scopeTooltip:t,forceMount:n,children:r,container:o}=e,s=As(rd,t);return c.jsx(ZC,{scope:t,forceMount:n,children:c.jsx(Xn,{present:n||s.open,children:c.jsx(td,{asChild:!0,container:o,children:r})})})};Zv.displayName=rd;var uo="TooltipContent",Gv=v.forwardRef((e,t)=>{const n=GC(uo,e.__scopeTooltip),{forceMount:r=n.forceMount,side:o="top",...s}=e,i=As(uo,e.__scopeTooltip);return c.jsx(Xn,{present:r||i.open,children:i.disableHoverableContent?c.jsx(Yv,{side:o,...s,ref:t}):c.jsx(YC,{side:o,...s,ref:t})})}),YC=v.forwardRef((e,t)=>{const n=As(uo,e.__scopeTooltip),r=nd(uo,e.__scopeTooltip),o=v.useRef(null),s=Pe(t,o),[i,a]=v.useState(null),{trigger:l,onClose:u}=n,d=o.current,{onPointerInTransitChange:f}=r,p=v.useCallback(()=>{a(null),f(!1)},[f]),y=v.useCallback((w,g)=>{const k=w.currentTarget,m={x:w.clientX,y:w.clientY},h=JC(m,k.getBoundingClientRect()),x=qC(m,h),_=ek(g.getBoundingClientRect()),b=nk([...x,..._]);a(b),f(!0)},[f]);return v.useEffect(()=>()=>p(),[p]),v.useEffect(()=>{if(l&&d){const w=k=>y(k,d),g=k=>y(k,l);return l.addEventListener("pointerleave",w),d.addEventListener("pointerleave",g),()=>{l.removeEventListener("pointerleave",w),d.removeEventListener("pointerleave",g)}}},[l,d,y,p]),v.useEffect(()=>{if(i){const w=g=>{const k=g.target,m={x:g.clientX,y:g.clientY},h=(l==null?void 0:l.contains(k))||(d==null?void 0:d.contains(k)),x=!tk(m,i);h?p():x&&(p(),u())};return document.addEventListener("pointermove",w),()=>document.removeEventListener("pointermove",w)}},[l,d,i,u,p]),c.jsx(Yv,{...e,ref:s})}),[KC,QC]=Da($a,{isInside:!1}),Yv=v.forwardRef((e,t)=>{const{__scopeTooltip:n,children:r,"aria-label":o,onEscapeKeyDown:s,onPointerDownOutside:i,...a}=e,l=As(uo,n),u=La(n),{onClose:d}=l;return v.useEffect(()=>(document.addEventListener(Ou,d),()=>document.removeEventListener(Ou,d)),[d]),v.useEffect(()=>{if(l.trigger){const f=p=>{const y=p.target;y!=null&&y.contains(l.trigger)&&d()};return window.addEventListener("scroll",f,{capture:!0}),()=>window.removeEventListener("scroll",f,{capture:!0})}},[l.trigger,d]),c.jsx(Hc,{asChild:!0,disableOutsidePointerEvents:!1,onEscapeKeyDown:s,onPointerDownOutside:i,onFocusOutside:f=>f.preventDefault(),onDismiss:d,children:c.jsxs(zv,{"data-state":l.stateAttribute,...u,...a,ref:t,style:{...a.style,"--radix-tooltip-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-tooltip-content-available-width":"var(--radix-popper-available-width)","--radix-tooltip-content-available-height":"var(--radix-popper-available-height)","--radix-tooltip-trigger-width":"var(--radix-popper-anchor-width)","--radix-tooltip-trigger-height":"var(--radix-popper-anchor-height)"},children:[c.jsx(Lm,{children:r}),c.jsx(KC,{scope:n,isInside:!0,children:c.jsx(VC,{id:l.contentId,role:"tooltip",children:o||r})})]})})});Gv.displayName=uo;var Kv="TooltipArrow",XC=v.forwardRef((e,t)=>{const{__scopeTooltip:n,...r}=e,o=La(n);return QC(Kv,n).isInside?null:c.jsx(Fv,{...o,...r,ref:t})});XC.displayName=Kv;function JC(e,t){const n=Math.abs(t.top-e.y),r=Math.abs(t.bottom-e.y),o=Math.abs(t.right-e.x),s=Math.abs(t.left-e.x);switch(Math.min(n,r,o,s)){case s:return"left";case o:return"right";case n:return"top";case r:return"bottom";default:throw new Error("unreachable")}}function qC(e,t,n=5){const r=[];switch(t){case"top":r.push({x:e.x-n,y:e.y+n},{x:e.x+n,y:e.y+n});break;case"bottom":r.push({x:e.x-n,y:e.y-n},{x:e.x+n,y:e.y-n});break;case"left":r.push({x:e.x+n,y:e.y-n},{x:e.x+n,y:e.y+n});break;case"right":r.push({x:e.x-n,y:e.y-n},{x:e.x-n,y:e.y+n});break}return r}function ek(e){const{top:t,right:n,bottom:r,left:o}=e;return[{x:o,y:t},{x:n,y:t},{x:n,y:r},{x:o,y:r}]}function tk(e,t){const{x:n,y:r}=e;let o=!1;for(let s=0,i=t.length-1;s<t.length;i=s++){const a=t[s].x,l=t[s].y,u=t[i].x,d=t[i].y;l>r!=d>r&&n<(u-a)*(r-l)/(d-l)+a&&(o=!o)}return o}function nk(e){const t=e.slice();return t.sort((n,r)=>n.x<r.x?-1:n.x>r.x?1:n.y<r.y?-1:n.y>r.y?1:0),rk(t)}function rk(e){if(e.length<=1)return e.slice();const t=[];for(let r=0;r<e.length;r++){const o=e[r];for(;t.length>=2;){const s=t[t.length-1],i=t[t.length-2];if((s.x-i.x)*(o.y-i.y)>=(s.y-i.y)*(o.x-i.x))t.pop();else break}t.push(o)}t.pop();const n=[];for(let r=e.length-1;r>=0;r--){const o=e[r];for(;n.length>=2;){const s=n[n.length-1],i=n[n.length-2];if((s.x-i.x)*(o.y-i.y)>=(s.y-i.y)*(o.x-i.x))n.pop();else break}n.push(o)}return n.pop(),t.length===1&&n.length===1&&t[0].x===n[0].x&&t[0].y===n[0].y?t:t.concat(n)}var ok=Uv,sk=Wv,ik=Hv,ak=Zv,lk=Gv;function Ft({delayDuration:e=500,...t}){return c.jsx(ok,{"data-slot":"tooltip-provider",delayDuration:e,...t})}function tn({...e}){return c.jsx(Ft,{children:c.jsx(sk,{"data-slot":"tooltip",...e})})}function nn({...e}){return c.jsx(ik,{"data-slot":"tooltip-trigger",...e})}function rn({className:e,sideOffset:t=4,children:n,...r}){return c.jsx(ak,{children:c.jsx(lk,{"data-slot":"tooltip-content",sideOffset:t,className:Q(`bg-accent text-accent-foreground animate-in fade-in-0
					zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95
					data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2
					data-[side=top]:slide-in-from-bottom-2 z-50 max-w-2xs rounded-[3px]`,"px-2 py-1 text-md","border border-border",e),...r,children:n})})}const Uf=({branch:e,commit:t})=>t?c.jsxs("div",{className:"flex flex-col gap-1",children:[(e==null?void 0:e.name)&&c.jsxs("div",{className:"flex flex-row flex-nowrap gap-0.5 items-center truncate ml-0.5",children:[c.jsx("span",{className:"codicon codicon-git-pull-request !text-sm grow-0"}),c.jsx("span",{className:"text-xs truncate",children:e.name})]}),c.jsxs("div",{className:"flex flex-row flex-nowrap gap-0.5 items-center truncate",children:[c.jsx("span",{className:"codicon codicon-git-commit !text-icon grow-0"}),c.jsx("span",{className:"text-xs truncate",children:t.hash})]}),c.jsx("div",{className:"flex flex-row flex-wrap gap-1 pl-4",children:c.jsx("span",{className:"text-xs line-clamp-1",children:t.message})}),c.jsxs("div",{className:"flex flex-row flex-wrap gap-1 pl-4",children:[c.jsx("span",{className:"text-xs",children:t.authorName||t.authorEmail}),t.authorName&&c.jsxs("span",{className:"text-xs",children:["-- ",t.authorEmail]})]})]}):null,Qv=()=>{const{branchInfo:{current:e,base:t,isDefaultBranch:n}}=v.useContext(pn);function r(){Se.postMessage({type:"changeBaseBranch"})}const o=t==null?void 0:t.latestCommit,s=e==null?void 0:e.latestCommit;return!t&&!e?c.jsx(Wo,{className:"ml-5 mt-1",children:c.jsx(Ft,{children:c.jsxs(tn,{children:[c.jsx(nn,{onClick:()=>{r()},children:c.jsxs("div",{className:"flex flex-row items-center gap-1 cursor-pointer",children:[c.jsx("span",{className:"codicon codicon-git-pull-request !text-sm grow-0"}),c.jsx("div",{className:"flex flex-row items-center gap-1 grow w-full truncate",children:c.jsx("span",{className:"text-sm",children:"Select a base branch"})}),c.jsx("span",{className:"codicon codicon-edit !text-sm grow-0"})]})}),c.jsx(rn,{className:"pb-2",align:"start",children:c.jsx("span",{children:"Choose a base branch to compare with the currently checked-out branch. You can select any branch except the one you're currently on."})})]})})}):c.jsxs("div",{className:"flex flex-row items-center gap-1.5 flex-wrap pl-4 pr-2 mt-1",children:[t&&c.jsx(Wo,{children:c.jsx(Ft,{children:c.jsxs(tn,{children:[c.jsx(nn,{onClick:()=>{r()},children:c.jsxs("div",{className:"flex flex-row items-center gap-1 cursor-pointer",children:[c.jsx("span",{className:"codicon codicon-git-pull-request !text-sm grow-0"}),c.jsx("div",{className:"flex flex-row items-center gap-1 grow w-full truncate",children:c.jsx("span",{className:"text-sm max-w-24 truncate",children:[t.remote,t.name].filter(Boolean).join("/")})}),c.jsx("span",{className:"codicon codicon-edit !text-sm grow-0"})]})}),c.jsx(rn,{className:"pb-2",align:"start",children:c.jsx(Uf,{commit:o,branch:t})})]})})}),!n&&e&&c.jsxs("div",{className:"flex flex-row items-center flex-nowrap gap-1",children:[t&&c.jsx("span",{className:"codicon codicon-arrow-left"}),c.jsx(Wo,{children:c.jsx(Ft,{children:c.jsxs(tn,{children:[c.jsx(nn,{children:c.jsxs("div",{className:"flex flex-row items-center gap-1",children:[c.jsx("span",{className:"codicon codicon-git-pull-request !text-sm grow-0"}),c.jsx("div",{className:"flex flex-row items-center gap-1 grow w-full truncate",children:c.jsx("span",{className:"text-sm max-w-24 truncate",children:[e.remote,e.name].filter(Boolean).join("/")})})]})}),c.jsx(rn,{className:"pb-2",align:"start",children:c.jsx(Uf,{commit:s,branch:e})})]})})})]})]})},uk=()=>(v.useEffect(()=>{Se.postMessage({type:"getBranchInfo"})},[]),c.jsx(Bn,{type:"single",collapsible:!0,defaultValue:"branch-details",children:c.jsxs(Vn,{value:"branch-details",children:[c.jsx(Un,{className:Q("no-underline hover:no-underline justify-end gap-0 flex-row-reverse p-0 px-0.5 pr-2 mt-2","[&>svg]:-rotate-90 [&[data-state=open]>svg]:rotate-0 cursor-pointer [&>svg]:-mt-0.5"),children:c.jsx("div",{className:"flex flex-row flex-wrap justify-between items-center w-full",children:c.jsx("span",{className:"text-sm uppercase font-bold tracking-tight",children:"Branch"})})}),c.jsx(Wn,{className:"pb-0 p-0.5",children:c.jsx(Qv,{})})]})}));function Pt(e){if(typeof e!="string")throw new TypeError("Path must be a string. Received "+JSON.stringify(e))}function Wf(e,t){for(var n="",r=0,o=-1,s=0,i,a=0;a<=e.length;++a){if(a<e.length)i=e.charCodeAt(a);else{if(i===47)break;i=47}if(i===47){if(!(o===a-1||s===1))if(o!==a-1&&s===2){if(n.length<2||r!==2||n.charCodeAt(n.length-1)!==46||n.charCodeAt(n.length-2)!==46){if(n.length>2){var l=n.lastIndexOf("/");if(l!==n.length-1){l===-1?(n="",r=0):(n=n.slice(0,l),r=n.length-1-n.lastIndexOf("/")),o=a,s=0;continue}}else if(n.length===2||n.length===1){n="",r=0,o=a,s=0;continue}}t&&(n.length>0?n+="/..":n="..",r=2)}else n.length>0?n+="/"+e.slice(o+1,a):n=e.slice(o+1,a),r=a-o-1;o=a,s=0}else i===46&&s!==-1?++s:s=-1}return n}function ck(e,t){var n=t.dir||t.root,r=t.base||(t.name||"")+(t.ext||"");return n?n===t.root?n+r:n+e+r:r}var Yr={resolve:function(){for(var t="",n=!1,r,o=arguments.length-1;o>=-1&&!n;o--){var s;o>=0?s=arguments[o]:(r===void 0&&(r=process.cwd()),s=r),Pt(s),s.length!==0&&(t=s+"/"+t,n=s.charCodeAt(0)===47)}return t=Wf(t,!n),n?t.length>0?"/"+t:"/":t.length>0?t:"."},normalize:function(t){if(Pt(t),t.length===0)return".";var n=t.charCodeAt(0)===47,r=t.charCodeAt(t.length-1)===47;return t=Wf(t,!n),t.length===0&&!n&&(t="."),t.length>0&&r&&(t+="/"),n?"/"+t:t},isAbsolute:function(t){return Pt(t),t.length>0&&t.charCodeAt(0)===47},join:function(){if(arguments.length===0)return".";for(var t,n=0;n<arguments.length;++n){var r=arguments[n];Pt(r),r.length>0&&(t===void 0?t=r:t+="/"+r)}return t===void 0?".":Yr.normalize(t)},relative:function(t,n){if(Pt(t),Pt(n),t===n||(t=Yr.resolve(t),n=Yr.resolve(n),t===n))return"";for(var r=1;r<t.length&&t.charCodeAt(r)===47;++r);for(var o=t.length,s=o-r,i=1;i<n.length&&n.charCodeAt(i)===47;++i);for(var a=n.length,l=a-i,u=s<l?s:l,d=-1,f=0;f<=u;++f){if(f===u){if(l>u){if(n.charCodeAt(i+f)===47)return n.slice(i+f+1);if(f===0)return n.slice(i+f)}else s>u&&(t.charCodeAt(r+f)===47?d=f:f===0&&(d=0));break}var p=t.charCodeAt(r+f),y=n.charCodeAt(i+f);if(p!==y)break;p===47&&(d=f)}var w="";for(f=r+d+1;f<=o;++f)(f===o||t.charCodeAt(f)===47)&&(w.length===0?w+="..":w+="/..");return w.length>0?w+n.slice(i+d):(i+=d,n.charCodeAt(i)===47&&++i,n.slice(i))},_makeLong:function(t){return t},dirname:function(t){if(Pt(t),t.length===0)return".";for(var n=t.charCodeAt(0),r=n===47,o=-1,s=!0,i=t.length-1;i>=1;--i)if(n=t.charCodeAt(i),n===47){if(!s){o=i;break}}else s=!1;return o===-1?r?"/":".":r&&o===1?"//":t.slice(0,o)},basename:function(t,n){if(n!==void 0&&typeof n!="string")throw new TypeError('"ext" argument must be a string');Pt(t);var r=0,o=-1,s=!0,i;if(n!==void 0&&n.length>0&&n.length<=t.length){if(n.length===t.length&&n===t)return"";var a=n.length-1,l=-1;for(i=t.length-1;i>=0;--i){var u=t.charCodeAt(i);if(u===47){if(!s){r=i+1;break}}else l===-1&&(s=!1,l=i+1),a>=0&&(u===n.charCodeAt(a)?--a===-1&&(o=i):(a=-1,o=l))}return r===o?o=l:o===-1&&(o=t.length),t.slice(r,o)}else{for(i=t.length-1;i>=0;--i)if(t.charCodeAt(i)===47){if(!s){r=i+1;break}}else o===-1&&(s=!1,o=i+1);return o===-1?"":t.slice(r,o)}},extname:function(t){Pt(t);for(var n=-1,r=0,o=-1,s=!0,i=0,a=t.length-1;a>=0;--a){var l=t.charCodeAt(a);if(l===47){if(!s){r=a+1;break}continue}o===-1&&(s=!1,o=a+1),l===46?n===-1?n=a:i!==1&&(i=1):n!==-1&&(i=-1)}return n===-1||o===-1||i===0||i===1&&n===o-1&&n===r+1?"":t.slice(n,o)},format:function(t){if(t===null||typeof t!="object")throw new TypeError('The "pathObject" argument must be of type Object. Received type '+typeof t);return ck("/",t)},parse:function(t){Pt(t);var n={root:"",dir:"",base:"",ext:"",name:""};if(t.length===0)return n;var r=t.charCodeAt(0),o=r===47,s;o?(n.root="/",s=1):s=0;for(var i=-1,a=0,l=-1,u=!0,d=t.length-1,f=0;d>=s;--d){if(r=t.charCodeAt(d),r===47){if(!u){a=d+1;break}continue}l===-1&&(u=!1,l=d+1),r===46?i===-1?i=d:f!==1&&(f=1):i!==-1&&(f=-1)}return i===-1||l===-1||f===0||f===1&&i===l-1&&i===a+1?l!==-1&&(a===0&&o?n.base=n.name=t.slice(1,l):n.base=n.name=t.slice(a,l)):(a===0&&o?(n.name=t.slice(1,i),n.base=t.slice(1,l)):(n.name=t.slice(a,i),n.base=t.slice(a,l)),n.ext=t.slice(i,l)),a>0?n.dir=t.slice(0,a-1):o&&(n.dir="/"),n},sep:"/",delimiter:":",win32:null,posix:null};Yr.posix=Yr;var dk=Yr;const Xv=mr(dk);function za(e){return(e==null?void 0:e.status)==="in_progress"||(e==null?void 0:e.status)==="pending"}function fk(e){return(e==null?void 0:e.status)==="completed"}function pk(e){switch(e){case"refactor_suggestion":return"Refactor Suggestion";case"potential_issue":return"Potential Issue";case"verification":return"Verification";case"nitpick":return"Nitpick";default:return null}}const hk=hv(`inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-xs
	transition-colors focus-visible:outline-none focus-visible:ring-1
	focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none
	[&_svg]:size-4 [&_svg]:shrink-0 px-[13px] py-[1px]`,{variants:{variant:{primary:`bg-[var(--vscode-button-background)] border text-[var(--vscode-button-foreground)]
					hover:bg-[var(--vscode-button-hoverBackground)] border-transparent`,secondary:`bg-[var(--vscode-button-secondaryBackground)] border
				text-[var(--vscode-button-secondaryForeground)] hover:bg-[var(--vscode-button-border)]
				border-transparent`,link:`bg-transparent hover:bg-transparent text-[var(--vscode-textLink-foreground)]
				border border-transparent !p-0`},size:{md:"h-[28px]",sm:"h-[20px]",lg:"h-[36px]"}},defaultVariants:{variant:"primary",size:"md"}}),Bt=v.forwardRef(({className:e,variant:t,size:n,asChild:r=!1,...o},s)=>{const i=r?io:"button";return c.jsx(i,{className:Q(hk({variant:t,size:n,className:e})),ref:s,...o})});Bt.displayName="Button";var _l=0;function mk(){v.useEffect(()=>{const e=document.querySelectorAll("[data-radix-focus-guard]");return document.body.insertAdjacentElement("afterbegin",e[0]??Hf()),document.body.insertAdjacentElement("beforeend",e[1]??Hf()),_l++,()=>{_l===1&&document.querySelectorAll("[data-radix-focus-guard]").forEach(t=>t.remove()),_l--}},[])}function Hf(){const e=document.createElement("span");return e.setAttribute("data-radix-focus-guard",""),e.tabIndex=0,e.style.outline="none",e.style.opacity="0",e.style.position="fixed",e.style.pointerEvents="none",e}var Cl="focusScope.autoFocusOnMount",kl="focusScope.autoFocusOnUnmount",Zf={bubbles:!1,cancelable:!0},vk="FocusScope",Jv=v.forwardRef((e,t)=>{const{loop:n=!1,trapped:r=!1,onMountAutoFocus:o,onUnmountAutoFocus:s,...i}=e,[a,l]=v.useState(null),u=mt(o),d=mt(s),f=v.useRef(null),p=Pe(t,g=>l(g)),y=v.useRef({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}}).current;v.useEffect(()=>{if(r){let g=function(x){if(y.paused||!a)return;const _=x.target;a.contains(_)?f.current=_:wn(f.current,{select:!0})},k=function(x){if(y.paused||!a)return;const _=x.relatedTarget;_!==null&&(a.contains(_)||wn(f.current,{select:!0}))},m=function(x){if(document.activeElement===document.body)for(const b of x)b.removedNodes.length>0&&wn(a)};document.addEventListener("focusin",g),document.addEventListener("focusout",k);const h=new MutationObserver(m);return a&&h.observe(a,{childList:!0,subtree:!0}),()=>{document.removeEventListener("focusin",g),document.removeEventListener("focusout",k),h.disconnect()}}},[r,a,y.paused]),v.useEffect(()=>{if(a){Yf.add(y);const g=document.activeElement;if(!a.contains(g)){const m=new CustomEvent(Cl,Zf);a.addEventListener(Cl,u),a.dispatchEvent(m),m.defaultPrevented||(gk(Ck(qv(a)),{select:!0}),document.activeElement===g&&wn(a))}return()=>{a.removeEventListener(Cl,u),setTimeout(()=>{const m=new CustomEvent(kl,Zf);a.addEventListener(kl,d),a.dispatchEvent(m),m.defaultPrevented||wn(g??document.body,{select:!0}),a.removeEventListener(kl,d),Yf.remove(y)},0)}}},[a,u,d,y]);const w=v.useCallback(g=>{if(!n&&!r||y.paused)return;const k=g.key==="Tab"&&!g.altKey&&!g.ctrlKey&&!g.metaKey,m=document.activeElement;if(k&&m){const h=g.currentTarget,[x,_]=yk(h);x&&_?!g.shiftKey&&m===_?(g.preventDefault(),n&&wn(x,{select:!0})):g.shiftKey&&m===x&&(g.preventDefault(),n&&wn(_,{select:!0})):m===h&&g.preventDefault()}},[n,r,y.paused]);return c.jsx(de.div,{tabIndex:-1,...i,ref:p,onKeyDown:w})});Jv.displayName=vk;function gk(e,{select:t=!1}={}){const n=document.activeElement;for(const r of e)if(wn(r,{select:t}),document.activeElement!==n)return}function yk(e){const t=qv(e),n=Gf(t,e),r=Gf(t.reverse(),e);return[n,r]}function qv(e){const t=[],n=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:r=>{const o=r.tagName==="INPUT"&&r.type==="hidden";return r.disabled||r.hidden||o?NodeFilter.FILTER_SKIP:r.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;n.nextNode();)t.push(n.currentNode);return t}function Gf(e,t){for(const n of e)if(!xk(n,{upTo:t}))return n}function xk(e,{upTo:t}){if(getComputedStyle(e).visibility==="hidden")return!0;for(;e;){if(t!==void 0&&e===t)return!1;if(getComputedStyle(e).display==="none")return!0;e=e.parentElement}return!1}function wk(e){return e instanceof HTMLInputElement&&"select"in e}function wn(e,{select:t=!1}={}){if(e&&e.focus){const n=document.activeElement;e.focus({preventScroll:!0}),e!==n&&wk(e)&&t&&e.select()}}var Yf=_k();function _k(){let e=[];return{add(t){const n=e[0];t!==n&&(n==null||n.pause()),e=Kf(e,t),e.unshift(t)},remove(t){var n;e=Kf(e,t),(n=e[0])==null||n.resume()}}}function Kf(e,t){const n=[...e],r=n.indexOf(t);return r!==-1&&n.splice(r,1),n}function Ck(e){return e.filter(t=>t.tagName!=="A")}var Sl="rovingFocusGroup.onEntryFocus",kk={bubbles:!1,cancelable:!0},Fa="RovingFocusGroup",[Du,eg,Sk]=Ic(Fa),[Ek,tg]=hn(Fa,[Sk]),[bk,Nk]=Ek(Fa),ng=v.forwardRef((e,t)=>c.jsx(Du.Provider,{scope:e.__scopeRovingFocusGroup,children:c.jsx(Du.Slot,{scope:e.__scopeRovingFocusGroup,children:c.jsx(Tk,{...e,ref:t})})}));ng.displayName=Fa;var Tk=v.forwardRef((e,t)=>{const{__scopeRovingFocusGroup:n,orientation:r,loop:o=!1,dir:s,currentTabStopId:i,defaultCurrentTabStopId:a,onCurrentTabStopIdChange:l,onEntryFocus:u,preventScrollOnEntryFocus:d=!1,...f}=e,p=v.useRef(null),y=Pe(t,p),w=Fc(s),[g=null,k]=mo({prop:i,defaultProp:a,onChange:l}),[m,h]=v.useState(!1),x=mt(u),_=eg(n),b=v.useRef(!1),[N,S]=v.useState(0);return v.useEffect(()=>{const C=p.current;if(C)return C.addEventListener(Sl,x),()=>C.removeEventListener(Sl,x)},[x]),c.jsx(bk,{scope:n,orientation:r,dir:w,loop:o,currentTabStopId:g,onItemFocus:v.useCallback(C=>k(C),[k]),onItemShiftTab:v.useCallback(()=>h(!0),[]),onFocusableItemAdd:v.useCallback(()=>S(C=>C+1),[]),onFocusableItemRemove:v.useCallback(()=>S(C=>C-1),[]),children:c.jsx(de.div,{tabIndex:m||N===0?-1:0,"data-orientation":r,...f,ref:y,style:{outline:"none",...e.style},onMouseDown:X(e.onMouseDown,()=>{b.current=!0}),onFocus:X(e.onFocus,C=>{const L=!b.current;if(C.target===C.currentTarget&&L&&!m){const M=new CustomEvent(Sl,kk);if(C.currentTarget.dispatchEvent(M),!M.defaultPrevented){const $=_().filter(j=>j.focusable),V=$.find(j=>j.active),G=$.find(j=>j.id===g),D=[V,G,...$].filter(Boolean).map(j=>j.ref.current);sg(D,d)}}b.current=!1}),onBlur:X(e.onBlur,()=>h(!1))})})}),rg="RovingFocusGroupItem",og=v.forwardRef((e,t)=>{const{__scopeRovingFocusGroup:n,focusable:r=!0,active:o=!1,tabStopId:s,...i}=e,a=ao(),l=s||a,u=Nk(rg,n),d=u.currentTabStopId===l,f=eg(n),{onFocusableItemAdd:p,onFocusableItemRemove:y}=u;return v.useEffect(()=>{if(r)return p(),()=>y()},[r,p,y]),c.jsx(Du.ItemSlot,{scope:n,id:l,focusable:r,active:o,children:c.jsx(de.span,{tabIndex:d?0:-1,"data-orientation":u.orientation,...i,ref:t,onMouseDown:X(e.onMouseDown,w=>{r?u.onItemFocus(l):w.preventDefault()}),onFocus:X(e.onFocus,()=>u.onItemFocus(l)),onKeyDown:X(e.onKeyDown,w=>{if(w.key==="Tab"&&w.shiftKey){u.onItemShiftTab();return}if(w.target!==w.currentTarget)return;const g=Mk(w,u.orientation,u.dir);if(g!==void 0){if(w.metaKey||w.ctrlKey||w.altKey||w.shiftKey)return;w.preventDefault();let m=f().filter(h=>h.focusable).map(h=>h.ref.current);if(g==="last")m.reverse();else if(g==="prev"||g==="next"){g==="prev"&&m.reverse();const h=m.indexOf(w.currentTarget);m=u.loop?Pk(m,h+1):m.slice(h+1)}setTimeout(()=>sg(m))}})})})});og.displayName=rg;var Rk={ArrowLeft:"prev",ArrowUp:"prev",ArrowRight:"next",ArrowDown:"next",PageUp:"first",Home:"first",PageDown:"last",End:"last"};function jk(e,t){return t!=="rtl"?e:e==="ArrowLeft"?"ArrowRight":e==="ArrowRight"?"ArrowLeft":e}function Mk(e,t,n){const r=jk(e.key,n);if(!(t==="vertical"&&["ArrowLeft","ArrowRight"].includes(r))&&!(t==="horizontal"&&["ArrowUp","ArrowDown"].includes(r)))return Rk[r]}function sg(e,t=!1){const n=document.activeElement;for(const r of e)if(r===n||(r.focus({preventScroll:t}),document.activeElement!==n))return}function Pk(e,t){return e.map((n,r)=>e[(t+r)%e.length])}var Ak=ng,Ok=og,Ik=function(e){if(typeof document>"u")return null;var t=Array.isArray(e)?e[0]:e;return t.ownerDocument.body},_r=new WeakMap,ri=new WeakMap,oi={},El=0,ig=function(e){return e&&(e.host||ig(e.parentNode))},Dk=function(e,t){return t.map(function(n){if(e.contains(n))return n;var r=ig(n);return r&&e.contains(r)?r:(console.error("aria-hidden",n,"in not contained inside",e,". Doing nothing"),null)}).filter(function(n){return!!n})},Lk=function(e,t,n,r){var o=Dk(t,Array.isArray(e)?e:[e]);oi[n]||(oi[n]=new WeakMap);var s=oi[n],i=[],a=new Set,l=new Set(o),u=function(f){!f||a.has(f)||(a.add(f),u(f.parentNode))};o.forEach(u);var d=function(f){!f||l.has(f)||Array.prototype.forEach.call(f.children,function(p){if(a.has(p))d(p);else try{var y=p.getAttribute(r),w=y!==null&&y!=="false",g=(_r.get(p)||0)+1,k=(s.get(p)||0)+1;_r.set(p,g),s.set(p,k),i.push(p),g===1&&w&&ri.set(p,!0),k===1&&p.setAttribute(n,"true"),w||p.setAttribute(r,"true")}catch(m){console.error("aria-hidden: cannot operate on ",p,m)}})};return d(t),a.clear(),El++,function(){i.forEach(function(f){var p=_r.get(f)-1,y=s.get(f)-1;_r.set(f,p),s.set(f,y),p||(ri.has(f)||f.removeAttribute(r),ri.delete(f)),y||f.removeAttribute(n)}),El--,El||(_r=new WeakMap,_r=new WeakMap,ri=new WeakMap,oi={})}},$k=function(e,t,n){n===void 0&&(n="data-aria-hidden");var r=Array.from(Array.isArray(e)?e:[e]),o=Ik(e);return o?(r.push.apply(r,Array.from(o.querySelectorAll("[aria-live]"))),Lk(r,o,n,"aria-hidden")):function(){return null}},It=function(){return It=Object.assign||function(t){for(var n,r=1,o=arguments.length;r<o;r++){n=arguments[r];for(var s in n)Object.prototype.hasOwnProperty.call(n,s)&&(t[s]=n[s])}return t},It.apply(this,arguments)};function ag(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var o=0,r=Object.getOwnPropertySymbols(e);o<r.length;o++)t.indexOf(r[o])<0&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]]);return n}function zk(e,t,n){if(n||arguments.length===2)for(var r=0,o=t.length,s;r<o;r++)(s||!(r in t))&&(s||(s=Array.prototype.slice.call(t,0,r)),s[r]=t[r]);return e.concat(s||Array.prototype.slice.call(t))}var Ci="right-scroll-bar-position",ki="width-before-scroll-bar",Fk="with-scroll-bars-hidden",Bk="--removed-body-scroll-bar-size";function bl(e,t){return typeof e=="function"?e(t):e&&(e.current=t),e}function Vk(e,t){var n=v.useState(function(){return{value:e,callback:t,facade:{get current(){return n.value},set current(r){var o=n.value;o!==r&&(n.value=r,n.callback(r,o))}}}})[0];return n.callback=t,n.facade}var Uk=typeof window<"u"?v.useLayoutEffect:v.useEffect,Qf=new WeakMap;function Wk(e,t){var n=Vk(null,function(r){return e.forEach(function(o){return bl(o,r)})});return Uk(function(){var r=Qf.get(n);if(r){var o=new Set(r),s=new Set(e),i=n.current;o.forEach(function(a){s.has(a)||bl(a,null)}),s.forEach(function(a){o.has(a)||bl(a,i)})}Qf.set(n,e)},[e]),n}function Hk(e){return e}function Zk(e,t){t===void 0&&(t=Hk);var n=[],r=!1,o={read:function(){if(r)throw new Error("Sidecar: could not `read` from an `assigned` medium. `read` could be used only with `useMedium`.");return n.length?n[n.length-1]:e},useMedium:function(s){var i=t(s,r);return n.push(i),function(){n=n.filter(function(a){return a!==i})}},assignSyncMedium:function(s){for(r=!0;n.length;){var i=n;n=[],i.forEach(s)}n={push:function(a){return s(a)},filter:function(){return n}}},assignMedium:function(s){r=!0;var i=[];if(n.length){var a=n;n=[],a.forEach(s),i=n}var l=function(){var d=i;i=[],d.forEach(s)},u=function(){return Promise.resolve().then(l)};u(),n={push:function(d){i.push(d),u()},filter:function(d){return i=i.filter(d),n}}}};return o}function Gk(e){e===void 0&&(e={});var t=Zk(null);return t.options=It({async:!0,ssr:!1},e),t}var lg=function(e){var t=e.sideCar,n=ag(e,["sideCar"]);if(!t)throw new Error("Sidecar: please provide `sideCar` property to import the right car");var r=t.read();if(!r)throw new Error("Sidecar medium not found");return v.createElement(r,It({},n))};lg.isSideCarExport=!0;function Yk(e,t){return e.useMedium(t),lg}var ug=Gk(),Nl=function(){},Ba=v.forwardRef(function(e,t){var n=v.useRef(null),r=v.useState({onScrollCapture:Nl,onWheelCapture:Nl,onTouchMoveCapture:Nl}),o=r[0],s=r[1],i=e.forwardProps,a=e.children,l=e.className,u=e.removeScrollBar,d=e.enabled,f=e.shards,p=e.sideCar,y=e.noIsolation,w=e.inert,g=e.allowPinchZoom,k=e.as,m=k===void 0?"div":k,h=e.gapMode,x=ag(e,["forwardProps","children","className","removeScrollBar","enabled","shards","sideCar","noIsolation","inert","allowPinchZoom","as","gapMode"]),_=p,b=Wk([n,t]),N=It(It({},x),o);return v.createElement(v.Fragment,null,d&&v.createElement(_,{sideCar:ug,removeScrollBar:u,shards:f,noIsolation:y,inert:w,setCallbacks:s,allowPinchZoom:!!g,lockRef:n,gapMode:h}),i?v.cloneElement(v.Children.only(a),It(It({},N),{ref:b})):v.createElement(m,It({},N,{className:l,ref:b}),a))});Ba.defaultProps={enabled:!0,removeScrollBar:!0,inert:!1};Ba.classNames={fullWidth:ki,zeroRight:Ci};var Kk=function(){if(typeof __webpack_nonce__<"u")return __webpack_nonce__};function Qk(){if(!document)return null;var e=document.createElement("style");e.type="text/css";var t=Kk();return t&&e.setAttribute("nonce",t),e}function Xk(e,t){e.styleSheet?e.styleSheet.cssText=t:e.appendChild(document.createTextNode(t))}function Jk(e){var t=document.head||document.getElementsByTagName("head")[0];t.appendChild(e)}var qk=function(){var e=0,t=null;return{add:function(n){e==0&&(t=Qk())&&(Xk(t,n),Jk(t)),e++},remove:function(){e--,!e&&t&&(t.parentNode&&t.parentNode.removeChild(t),t=null)}}},eS=function(){var e=qk();return function(t,n){v.useEffect(function(){return e.add(t),function(){e.remove()}},[t&&n])}},cg=function(){var e=eS(),t=function(n){var r=n.styles,o=n.dynamic;return e(r,o),null};return t},tS={left:0,top:0,right:0,gap:0},Tl=function(e){return parseInt(e||"",10)||0},nS=function(e){var t=window.getComputedStyle(document.body),n=t[e==="padding"?"paddingLeft":"marginLeft"],r=t[e==="padding"?"paddingTop":"marginTop"],o=t[e==="padding"?"paddingRight":"marginRight"];return[Tl(n),Tl(r),Tl(o)]},rS=function(e){if(e===void 0&&(e="margin"),typeof window>"u")return tS;var t=nS(e),n=document.documentElement.clientWidth,r=window.innerWidth;return{left:t[0],top:t[1],right:t[2],gap:Math.max(0,r-n+t[2]-t[0])}},oS=cg(),Kr="data-scroll-locked",sS=function(e,t,n,r){var o=e.left,s=e.top,i=e.right,a=e.gap;return n===void 0&&(n="margin"),`
  .`.concat(Fk,` {
   overflow: hidden `).concat(r,`;
   padding-right: `).concat(a,"px ").concat(r,`;
  }
  body[`).concat(Kr,`] {
    overflow: hidden `).concat(r,`;
    overscroll-behavior: contain;
    `).concat([t&&"position: relative ".concat(r,";"),n==="margin"&&`
    padding-left: `.concat(o,`px;
    padding-top: `).concat(s,`px;
    padding-right: `).concat(i,`px;
    margin-left:0;
    margin-top:0;
    margin-right: `).concat(a,"px ").concat(r,`;
    `),n==="padding"&&"padding-right: ".concat(a,"px ").concat(r,";")].filter(Boolean).join(""),`
  }
  
  .`).concat(Ci,` {
    right: `).concat(a,"px ").concat(r,`;
  }
  
  .`).concat(ki,` {
    margin-right: `).concat(a,"px ").concat(r,`;
  }
  
  .`).concat(Ci," .").concat(Ci,` {
    right: 0 `).concat(r,`;
  }
  
  .`).concat(ki," .").concat(ki,` {
    margin-right: 0 `).concat(r,`;
  }
  
  body[`).concat(Kr,`] {
    `).concat(Bk,": ").concat(a,`px;
  }
`)},Xf=function(){var e=parseInt(document.body.getAttribute(Kr)||"0",10);return isFinite(e)?e:0},iS=function(){v.useEffect(function(){return document.body.setAttribute(Kr,(Xf()+1).toString()),function(){var e=Xf()-1;e<=0?document.body.removeAttribute(Kr):document.body.setAttribute(Kr,e.toString())}},[])},aS=function(e){var t=e.noRelative,n=e.noImportant,r=e.gapMode,o=r===void 0?"margin":r;iS();var s=v.useMemo(function(){return rS(o)},[o]);return v.createElement(oS,{styles:sS(s,!t,o,n?"":"!important")})},Lu=!1;if(typeof window<"u")try{var si=Object.defineProperty({},"passive",{get:function(){return Lu=!0,!0}});window.addEventListener("test",si,si),window.removeEventListener("test",si,si)}catch{Lu=!1}var Cr=Lu?{passive:!1}:!1,lS=function(e){return e.tagName==="TEXTAREA"},dg=function(e,t){if(!(e instanceof Element))return!1;var n=window.getComputedStyle(e);return n[t]!=="hidden"&&!(n.overflowY===n.overflowX&&!lS(e)&&n[t]==="visible")},uS=function(e){return dg(e,"overflowY")},cS=function(e){return dg(e,"overflowX")},Jf=function(e,t){var n=t.ownerDocument,r=t;do{typeof ShadowRoot<"u"&&r instanceof ShadowRoot&&(r=r.host);var o=fg(e,r);if(o){var s=pg(e,r),i=s[1],a=s[2];if(i>a)return!0}r=r.parentNode}while(r&&r!==n.body);return!1},dS=function(e){var t=e.scrollTop,n=e.scrollHeight,r=e.clientHeight;return[t,n,r]},fS=function(e){var t=e.scrollLeft,n=e.scrollWidth,r=e.clientWidth;return[t,n,r]},fg=function(e,t){return e==="v"?uS(t):cS(t)},pg=function(e,t){return e==="v"?dS(t):fS(t)},pS=function(e,t){return e==="h"&&t==="rtl"?-1:1},hS=function(e,t,n,r,o){var s=pS(e,window.getComputedStyle(t).direction),i=s*r,a=n.target,l=t.contains(a),u=!1,d=i>0,f=0,p=0;do{var y=pg(e,a),w=y[0],g=y[1],k=y[2],m=g-k-s*w;(w||m)&&fg(e,a)&&(f+=m,p+=w),a instanceof ShadowRoot?a=a.host:a=a.parentNode}while(!l&&a!==document.body||l&&(t.contains(a)||t===a));return(d&&Math.abs(f)<1||!d&&Math.abs(p)<1)&&(u=!0),u},ii=function(e){return"changedTouches"in e?[e.changedTouches[0].clientX,e.changedTouches[0].clientY]:[0,0]},qf=function(e){return[e.deltaX,e.deltaY]},ep=function(e){return e&&"current"in e?e.current:e},mS=function(e,t){return e[0]===t[0]&&e[1]===t[1]},vS=function(e){return`
  .block-interactivity-`.concat(e,` {pointer-events: none;}
  .allow-interactivity-`).concat(e,` {pointer-events: all;}
`)},gS=0,kr=[];function yS(e){var t=v.useRef([]),n=v.useRef([0,0]),r=v.useRef(),o=v.useState(gS++)[0],s=v.useState(cg)[0],i=v.useRef(e);v.useEffect(function(){i.current=e},[e]),v.useEffect(function(){if(e.inert){document.body.classList.add("block-interactivity-".concat(o));var g=zk([e.lockRef.current],(e.shards||[]).map(ep),!0).filter(Boolean);return g.forEach(function(k){return k.classList.add("allow-interactivity-".concat(o))}),function(){document.body.classList.remove("block-interactivity-".concat(o)),g.forEach(function(k){return k.classList.remove("allow-interactivity-".concat(o))})}}},[e.inert,e.lockRef.current,e.shards]);var a=v.useCallback(function(g,k){if("touches"in g&&g.touches.length===2||g.type==="wheel"&&g.ctrlKey)return!i.current.allowPinchZoom;var m=ii(g),h=n.current,x="deltaX"in g?g.deltaX:h[0]-m[0],_="deltaY"in g?g.deltaY:h[1]-m[1],b,N=g.target,S=Math.abs(x)>Math.abs(_)?"h":"v";if("touches"in g&&S==="h"&&N.type==="range")return!1;var C=Jf(S,N);if(!C)return!0;if(C?b=S:(b=S==="v"?"h":"v",C=Jf(S,N)),!C)return!1;if(!r.current&&"changedTouches"in g&&(x||_)&&(r.current=b),!b)return!0;var L=r.current||b;return hS(L,k,g,L==="h"?x:_)},[]),l=v.useCallback(function(g){var k=g;if(!(!kr.length||kr[kr.length-1]!==s)){var m="deltaY"in k?qf(k):ii(k),h=t.current.filter(function(b){return b.name===k.type&&(b.target===k.target||k.target===b.shadowParent)&&mS(b.delta,m)})[0];if(h&&h.should){k.cancelable&&k.preventDefault();return}if(!h){var x=(i.current.shards||[]).map(ep).filter(Boolean).filter(function(b){return b.contains(k.target)}),_=x.length>0?a(k,x[0]):!i.current.noIsolation;_&&k.cancelable&&k.preventDefault()}}},[]),u=v.useCallback(function(g,k,m,h){var x={name:g,delta:k,target:m,should:h,shadowParent:xS(m)};t.current.push(x),setTimeout(function(){t.current=t.current.filter(function(_){return _!==x})},1)},[]),d=v.useCallback(function(g){n.current=ii(g),r.current=void 0},[]),f=v.useCallback(function(g){u(g.type,qf(g),g.target,a(g,e.lockRef.current))},[]),p=v.useCallback(function(g){u(g.type,ii(g),g.target,a(g,e.lockRef.current))},[]);v.useEffect(function(){return kr.push(s),e.setCallbacks({onScrollCapture:f,onWheelCapture:f,onTouchMoveCapture:p}),document.addEventListener("wheel",l,Cr),document.addEventListener("touchmove",l,Cr),document.addEventListener("touchstart",d,Cr),function(){kr=kr.filter(function(g){return g!==s}),document.removeEventListener("wheel",l,Cr),document.removeEventListener("touchmove",l,Cr),document.removeEventListener("touchstart",d,Cr)}},[]);var y=e.removeScrollBar,w=e.inert;return v.createElement(v.Fragment,null,w?v.createElement(s,{styles:vS(o)}):null,y?v.createElement(aS,{gapMode:e.gapMode}):null)}function xS(e){for(var t=null;e!==null;)e instanceof ShadowRoot&&(t=e.host,e=e.host),e=e.parentNode;return t}const wS=Yk(ug,yS);var hg=v.forwardRef(function(e,t){return v.createElement(Ba,It({},e,{ref:t,sideCar:wS}))});hg.classNames=Ba.classNames;var $u=["Enter"," "],_S=["ArrowDown","PageUp","Home"],mg=["ArrowUp","PageDown","End"],CS=[..._S,...mg],kS={ltr:[...$u,"ArrowRight"],rtl:[...$u,"ArrowLeft"]},SS={ltr:["ArrowLeft"],rtl:["ArrowRight"]},Os="Menu",[Ss,ES,bS]=Ic(Os),[yr,vg]=hn(Os,[bS,Ia,tg]),Va=Ia(),gg=tg(),[NS,xr]=yr(Os),[TS,Is]=yr(Os),yg=e=>{const{__scopeMenu:t,open:n=!1,children:r,dir:o,onOpenChange:s,modal:i=!0}=e,a=Va(t),[l,u]=v.useState(null),d=v.useRef(!1),f=mt(s),p=Fc(o);return v.useEffect(()=>{const y=()=>{d.current=!0,document.addEventListener("pointerdown",w,{capture:!0,once:!0}),document.addEventListener("pointermove",w,{capture:!0,once:!0})},w=()=>d.current=!1;return document.addEventListener("keydown",y,{capture:!0}),()=>{document.removeEventListener("keydown",y,{capture:!0}),document.removeEventListener("pointerdown",w,{capture:!0}),document.removeEventListener("pointermove",w,{capture:!0})}},[]),c.jsx(Lv,{...a,children:c.jsx(NS,{scope:t,open:n,onOpenChange:f,content:l,onContentChange:u,children:c.jsx(TS,{scope:t,onClose:v.useCallback(()=>f(!1),[f]),isUsingKeyboardRef:d,dir:p,modal:i,children:r})})})};yg.displayName=Os;var RS="MenuAnchor",od=v.forwardRef((e,t)=>{const{__scopeMenu:n,...r}=e,o=Va(n);return c.jsx($v,{...o,...r,ref:t})});od.displayName=RS;var sd="MenuPortal",[jS,xg]=yr(sd,{forceMount:void 0}),wg=e=>{const{__scopeMenu:t,forceMount:n,children:r,container:o}=e,s=xr(sd,t);return c.jsx(jS,{scope:t,forceMount:n,children:c.jsx(Xn,{present:n||s.open,children:c.jsx(td,{asChild:!0,container:o,children:r})})})};wg.displayName=sd;var ft="MenuContent",[MS,id]=yr(ft),_g=v.forwardRef((e,t)=>{const n=xg(ft,e.__scopeMenu),{forceMount:r=n.forceMount,...o}=e,s=xr(ft,e.__scopeMenu),i=Is(ft,e.__scopeMenu);return c.jsx(Ss.Provider,{scope:e.__scopeMenu,children:c.jsx(Xn,{present:r||s.open,children:c.jsx(Ss.Slot,{scope:e.__scopeMenu,children:i.modal?c.jsx(PS,{...o,ref:t}):c.jsx(AS,{...o,ref:t})})})})}),PS=v.forwardRef((e,t)=>{const n=xr(ft,e.__scopeMenu),r=v.useRef(null),o=Pe(t,r);return v.useEffect(()=>{const s=r.current;if(s)return $k(s)},[]),c.jsx(ad,{...e,ref:o,trapFocus:n.open,disableOutsidePointerEvents:n.open,disableOutsideScroll:!0,onFocusOutside:X(e.onFocusOutside,s=>s.preventDefault(),{checkForDefaultPrevented:!1}),onDismiss:()=>n.onOpenChange(!1)})}),AS=v.forwardRef((e,t)=>{const n=xr(ft,e.__scopeMenu);return c.jsx(ad,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,disableOutsideScroll:!1,onDismiss:()=>n.onOpenChange(!1)})}),ad=v.forwardRef((e,t)=>{const{__scopeMenu:n,loop:r=!1,trapFocus:o,onOpenAutoFocus:s,onCloseAutoFocus:i,disableOutsidePointerEvents:a,onEntryFocus:l,onEscapeKeyDown:u,onPointerDownOutside:d,onFocusOutside:f,onInteractOutside:p,onDismiss:y,disableOutsideScroll:w,...g}=e,k=xr(ft,n),m=Is(ft,n),h=Va(n),x=gg(n),_=ES(n),[b,N]=v.useState(null),S=v.useRef(null),C=Pe(t,S,k.onContentChange),L=v.useRef(0),M=v.useRef(""),$=v.useRef(0),V=v.useRef(null),G=v.useRef("right"),I=v.useRef(0),D=w?hg:v.Fragment,j=w?{as:io,allowPinchZoom:!0}:void 0,z=T=>{var ye,vt;const P=M.current+T,B=_().filter(Ee=>!Ee.disabled),Z=document.activeElement,se=(ye=B.find(Ee=>Ee.ref.current===Z))==null?void 0:ye.textValue,oe=B.map(Ee=>Ee.textValue),_e=HS(oe,P,se),ge=(vt=B.find(Ee=>Ee.textValue===_e))==null?void 0:vt.ref.current;(function Ee(be){M.current=be,window.clearTimeout(L.current),be!==""&&(L.current=window.setTimeout(()=>Ee(""),1e3))})(P),ge&&setTimeout(()=>ge.focus())};v.useEffect(()=>()=>window.clearTimeout(L.current),[]),mk();const E=v.useCallback(T=>{var B,Z;return G.current===((B=V.current)==null?void 0:B.side)&&GS(T,(Z=V.current)==null?void 0:Z.area)},[]);return c.jsx(MS,{scope:n,searchRef:M,onItemEnter:v.useCallback(T=>{E(T)&&T.preventDefault()},[E]),onItemLeave:v.useCallback(T=>{var P;E(T)||((P=S.current)==null||P.focus(),N(null))},[E]),onTriggerLeave:v.useCallback(T=>{E(T)&&T.preventDefault()},[E]),pointerGraceTimerRef:$,onPointerGraceIntentChange:v.useCallback(T=>{V.current=T},[]),children:c.jsx(D,{...j,children:c.jsx(Jv,{asChild:!0,trapped:o,onMountAutoFocus:X(s,T=>{var P;T.preventDefault(),(P=S.current)==null||P.focus({preventScroll:!0})}),onUnmountAutoFocus:i,children:c.jsx(Hc,{asChild:!0,disableOutsidePointerEvents:a,onEscapeKeyDown:u,onPointerDownOutside:d,onFocusOutside:f,onInteractOutside:p,onDismiss:y,children:c.jsx(Ak,{asChild:!0,...x,dir:m.dir,orientation:"vertical",loop:r,currentTabStopId:b,onCurrentTabStopIdChange:N,onEntryFocus:X(l,T=>{m.isUsingKeyboardRef.current||T.preventDefault()}),preventScrollOnEntryFocus:!0,children:c.jsx(zv,{role:"menu","aria-orientation":"vertical","data-state":Lg(k.open),"data-radix-menu-content":"",dir:m.dir,...h,...g,ref:C,style:{outline:"none",...g.style},onKeyDown:X(g.onKeyDown,T=>{const B=T.target.closest("[data-radix-menu-content]")===T.currentTarget,Z=T.ctrlKey||T.altKey||T.metaKey,se=T.key.length===1;B&&(T.key==="Tab"&&T.preventDefault(),!Z&&se&&z(T.key));const oe=S.current;if(T.target!==oe||!CS.includes(T.key))return;T.preventDefault();const ge=_().filter(ye=>!ye.disabled).map(ye=>ye.ref.current);mg.includes(T.key)&&ge.reverse(),US(ge)}),onBlur:X(e.onBlur,T=>{T.currentTarget.contains(T.target)||(window.clearTimeout(L.current),M.current="")}),onPointerMove:X(e.onPointerMove,Es(T=>{const P=T.target,B=I.current!==T.clientX;if(T.currentTarget.contains(P)&&B){const Z=T.clientX>I.current?"right":"left";G.current=Z,I.current=T.clientX}}))})})})})})})});_g.displayName=ft;var OS="MenuGroup",ld=v.forwardRef((e,t)=>{const{__scopeMenu:n,...r}=e;return c.jsx(de.div,{role:"group",...r,ref:t})});ld.displayName=OS;var IS="MenuLabel",Cg=v.forwardRef((e,t)=>{const{__scopeMenu:n,...r}=e;return c.jsx(de.div,{...r,ref:t})});Cg.displayName=IS;var aa="MenuItem",tp="menu.itemSelect",Ua=v.forwardRef((e,t)=>{const{disabled:n=!1,onSelect:r,...o}=e,s=v.useRef(null),i=Is(aa,e.__scopeMenu),a=id(aa,e.__scopeMenu),l=Pe(t,s),u=v.useRef(!1),d=()=>{const f=s.current;if(!n&&f){const p=new CustomEvent(tp,{bubbles:!0,cancelable:!0});f.addEventListener(tp,y=>r==null?void 0:r(y),{once:!0}),$m(f,p),p.defaultPrevented?u.current=!1:i.onClose()}};return c.jsx(kg,{...o,ref:l,disabled:n,onClick:X(e.onClick,d),onPointerDown:f=>{var p;(p=e.onPointerDown)==null||p.call(e,f),u.current=!0},onPointerUp:X(e.onPointerUp,f=>{var p;u.current||(p=f.currentTarget)==null||p.click()}),onKeyDown:X(e.onKeyDown,f=>{const p=a.searchRef.current!=="";n||p&&f.key===" "||$u.includes(f.key)&&(f.currentTarget.click(),f.preventDefault())})})});Ua.displayName=aa;var kg=v.forwardRef((e,t)=>{const{__scopeMenu:n,disabled:r=!1,textValue:o,...s}=e,i=id(aa,n),a=gg(n),l=v.useRef(null),u=Pe(t,l),[d,f]=v.useState(!1),[p,y]=v.useState("");return v.useEffect(()=>{const w=l.current;w&&y((w.textContent??"").trim())},[s.children]),c.jsx(Ss.ItemSlot,{scope:n,disabled:r,textValue:o??p,children:c.jsx(Ok,{asChild:!0,...a,focusable:!r,children:c.jsx(de.div,{role:"menuitem","data-highlighted":d?"":void 0,"aria-disabled":r||void 0,"data-disabled":r?"":void 0,...s,ref:u,onPointerMove:X(e.onPointerMove,Es(w=>{r?i.onItemLeave(w):(i.onItemEnter(w),w.defaultPrevented||w.currentTarget.focus({preventScroll:!0}))})),onPointerLeave:X(e.onPointerLeave,Es(w=>i.onItemLeave(w))),onFocus:X(e.onFocus,()=>f(!0)),onBlur:X(e.onBlur,()=>f(!1))})})})}),DS="MenuCheckboxItem",Sg=v.forwardRef((e,t)=>{const{checked:n=!1,onCheckedChange:r,...o}=e;return c.jsx(Rg,{scope:e.__scopeMenu,checked:n,children:c.jsx(Ua,{role:"menuitemcheckbox","aria-checked":la(n)?"mixed":n,...o,ref:t,"data-state":cd(n),onSelect:X(o.onSelect,()=>r==null?void 0:r(la(n)?!0:!n),{checkForDefaultPrevented:!1})})})});Sg.displayName=DS;var Eg="MenuRadioGroup",[LS,$S]=yr(Eg,{value:void 0,onValueChange:()=>{}}),bg=v.forwardRef((e,t)=>{const{value:n,onValueChange:r,...o}=e,s=mt(r);return c.jsx(LS,{scope:e.__scopeMenu,value:n,onValueChange:s,children:c.jsx(ld,{...o,ref:t})})});bg.displayName=Eg;var Ng="MenuRadioItem",Tg=v.forwardRef((e,t)=>{const{value:n,...r}=e,o=$S(Ng,e.__scopeMenu),s=n===o.value;return c.jsx(Rg,{scope:e.__scopeMenu,checked:s,children:c.jsx(Ua,{role:"menuitemradio","aria-checked":s,...r,ref:t,"data-state":cd(s),onSelect:X(r.onSelect,()=>{var i;return(i=o.onValueChange)==null?void 0:i.call(o,n)},{checkForDefaultPrevented:!1})})})});Tg.displayName=Ng;var ud="MenuItemIndicator",[Rg,zS]=yr(ud,{checked:!1}),jg=v.forwardRef((e,t)=>{const{__scopeMenu:n,forceMount:r,...o}=e,s=zS(ud,n);return c.jsx(Xn,{present:r||la(s.checked)||s.checked===!0,children:c.jsx(de.span,{...o,ref:t,"data-state":cd(s.checked)})})});jg.displayName=ud;var FS="MenuSeparator",Mg=v.forwardRef((e,t)=>{const{__scopeMenu:n,...r}=e;return c.jsx(de.div,{role:"separator","aria-orientation":"horizontal",...r,ref:t})});Mg.displayName=FS;var BS="MenuArrow",Pg=v.forwardRef((e,t)=>{const{__scopeMenu:n,...r}=e,o=Va(n);return c.jsx(Fv,{...o,...r,ref:t})});Pg.displayName=BS;var VS="MenuSub",[vb,Ag]=yr(VS),Oo="MenuSubTrigger",Og=v.forwardRef((e,t)=>{const n=xr(Oo,e.__scopeMenu),r=Is(Oo,e.__scopeMenu),o=Ag(Oo,e.__scopeMenu),s=id(Oo,e.__scopeMenu),i=v.useRef(null),{pointerGraceTimerRef:a,onPointerGraceIntentChange:l}=s,u={__scopeMenu:e.__scopeMenu},d=v.useCallback(()=>{i.current&&window.clearTimeout(i.current),i.current=null},[]);return v.useEffect(()=>d,[d]),v.useEffect(()=>{const f=a.current;return()=>{window.clearTimeout(f),l(null)}},[a,l]),c.jsx(od,{asChild:!0,...u,children:c.jsx(kg,{id:o.triggerId,"aria-haspopup":"menu","aria-expanded":n.open,"aria-controls":o.contentId,"data-state":Lg(n.open),...e,ref:Ta(t,o.onTriggerChange),onClick:f=>{var p;(p=e.onClick)==null||p.call(e,f),!(e.disabled||f.defaultPrevented)&&(f.currentTarget.focus(),n.open||n.onOpenChange(!0))},onPointerMove:X(e.onPointerMove,Es(f=>{s.onItemEnter(f),!f.defaultPrevented&&!e.disabled&&!n.open&&!i.current&&(s.onPointerGraceIntentChange(null),i.current=window.setTimeout(()=>{n.onOpenChange(!0),d()},100))})),onPointerLeave:X(e.onPointerLeave,Es(f=>{var y,w;d();const p=(y=n.content)==null?void 0:y.getBoundingClientRect();if(p){const g=(w=n.content)==null?void 0:w.dataset.side,k=g==="right",m=k?-5:5,h=p[k?"left":"right"],x=p[k?"right":"left"];s.onPointerGraceIntentChange({area:[{x:f.clientX+m,y:f.clientY},{x:h,y:p.top},{x,y:p.top},{x,y:p.bottom},{x:h,y:p.bottom}],side:g}),window.clearTimeout(a.current),a.current=window.setTimeout(()=>s.onPointerGraceIntentChange(null),300)}else{if(s.onTriggerLeave(f),f.defaultPrevented)return;s.onPointerGraceIntentChange(null)}})),onKeyDown:X(e.onKeyDown,f=>{var y;const p=s.searchRef.current!=="";e.disabled||p&&f.key===" "||kS[r.dir].includes(f.key)&&(n.onOpenChange(!0),(y=n.content)==null||y.focus(),f.preventDefault())})})})});Og.displayName=Oo;var Ig="MenuSubContent",Dg=v.forwardRef((e,t)=>{const n=xg(ft,e.__scopeMenu),{forceMount:r=n.forceMount,...o}=e,s=xr(ft,e.__scopeMenu),i=Is(ft,e.__scopeMenu),a=Ag(Ig,e.__scopeMenu),l=v.useRef(null),u=Pe(t,l);return c.jsx(Ss.Provider,{scope:e.__scopeMenu,children:c.jsx(Xn,{present:r||s.open,children:c.jsx(Ss.Slot,{scope:e.__scopeMenu,children:c.jsx(ad,{id:a.contentId,"aria-labelledby":a.triggerId,...o,ref:u,align:"start",side:i.dir==="rtl"?"left":"right",disableOutsidePointerEvents:!1,disableOutsideScroll:!1,trapFocus:!1,onOpenAutoFocus:d=>{var f;i.isUsingKeyboardRef.current&&((f=l.current)==null||f.focus()),d.preventDefault()},onCloseAutoFocus:d=>d.preventDefault(),onFocusOutside:X(e.onFocusOutside,d=>{d.target!==a.trigger&&s.onOpenChange(!1)}),onEscapeKeyDown:X(e.onEscapeKeyDown,d=>{i.onClose(),d.preventDefault()}),onKeyDown:X(e.onKeyDown,d=>{var y;const f=d.currentTarget.contains(d.target),p=SS[i.dir].includes(d.key);f&&p&&(s.onOpenChange(!1),(y=a.trigger)==null||y.focus(),d.preventDefault())})})})})})});Dg.displayName=Ig;function Lg(e){return e?"open":"closed"}function la(e){return e==="indeterminate"}function cd(e){return la(e)?"indeterminate":e?"checked":"unchecked"}function US(e){const t=document.activeElement;for(const n of e)if(n===t||(n.focus(),document.activeElement!==t))return}function WS(e,t){return e.map((n,r)=>e[(t+r)%e.length])}function HS(e,t,n){const o=t.length>1&&Array.from(t).every(u=>u===t[0])?t[0]:t,s=n?e.indexOf(n):-1;let i=WS(e,Math.max(s,0));o.length===1&&(i=i.filter(u=>u!==n));const l=i.find(u=>u.toLowerCase().startsWith(o.toLowerCase()));return l!==n?l:void 0}function ZS(e,t){const{x:n,y:r}=e;let o=!1;for(let s=0,i=t.length-1;s<t.length;i=s++){const a=t[s].x,l=t[s].y,u=t[i].x,d=t[i].y;l>r!=d>r&&n<(u-a)*(r-l)/(d-l)+a&&(o=!o)}return o}function GS(e,t){if(!t)return!1;const n={x:e.clientX,y:e.clientY};return ZS(n,t)}function Es(e){return t=>t.pointerType==="mouse"?e(t):void 0}var YS=yg,KS=od,QS=wg,XS=_g,JS=ld,qS=Cg,eE=Ua,tE=Sg,nE=bg,rE=Tg,oE=jg,sE=Mg,iE=Pg,aE=Og,lE=Dg,dd="DropdownMenu",[uE,gb]=hn(dd,[vg]),Ze=vg(),[cE,$g]=uE(dd),zg=e=>{const{__scopeDropdownMenu:t,children:n,dir:r,open:o,defaultOpen:s,onOpenChange:i,modal:a=!0}=e,l=Ze(t),u=v.useRef(null),[d=!1,f]=mo({prop:o,defaultProp:s,onChange:i});return c.jsx(cE,{scope:t,triggerId:ao(),triggerRef:u,contentId:ao(),open:d,onOpenChange:f,onOpenToggle:v.useCallback(()=>f(p=>!p),[f]),modal:a,children:c.jsx(YS,{...l,open:d,onOpenChange:f,dir:r,modal:a,children:n})})};zg.displayName=dd;var Fg="DropdownMenuTrigger",Bg=v.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,disabled:r=!1,...o}=e,s=$g(Fg,n),i=Ze(n);return c.jsx(KS,{asChild:!0,...i,children:c.jsx(de.button,{type:"button",id:s.triggerId,"aria-haspopup":"menu","aria-expanded":s.open,"aria-controls":s.open?s.contentId:void 0,"data-state":s.open?"open":"closed","data-disabled":r?"":void 0,disabled:r,...o,ref:Ta(t,s.triggerRef),onPointerDown:X(e.onPointerDown,a=>{!r&&a.button===0&&a.ctrlKey===!1&&(s.onOpenToggle(),s.open||a.preventDefault())}),onKeyDown:X(e.onKeyDown,a=>{r||(["Enter"," "].includes(a.key)&&s.onOpenToggle(),a.key==="ArrowDown"&&s.onOpenChange(!0),["Enter"," ","ArrowDown"].includes(a.key)&&a.preventDefault())})})})});Bg.displayName=Fg;var dE="DropdownMenuPortal",Vg=e=>{const{__scopeDropdownMenu:t,...n}=e,r=Ze(t);return c.jsx(QS,{...r,...n})};Vg.displayName=dE;var Ug="DropdownMenuContent",Wg=v.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,...r}=e,o=$g(Ug,n),s=Ze(n),i=v.useRef(!1);return c.jsx(XS,{id:o.contentId,"aria-labelledby":o.triggerId,...s,...r,ref:t,onCloseAutoFocus:X(e.onCloseAutoFocus,a=>{var l;i.current||(l=o.triggerRef.current)==null||l.focus(),i.current=!1,a.preventDefault()}),onInteractOutside:X(e.onInteractOutside,a=>{const l=a.detail.originalEvent,u=l.button===0&&l.ctrlKey===!0,d=l.button===2||u;(!o.modal||d)&&(i.current=!0)}),style:{...e.style,"--radix-dropdown-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-dropdown-menu-content-available-width":"var(--radix-popper-available-width)","--radix-dropdown-menu-content-available-height":"var(--radix-popper-available-height)","--radix-dropdown-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-dropdown-menu-trigger-height":"var(--radix-popper-anchor-height)"}})});Wg.displayName=Ug;var fE="DropdownMenuGroup",pE=v.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,...r}=e,o=Ze(n);return c.jsx(JS,{...o,...r,ref:t})});pE.displayName=fE;var hE="DropdownMenuLabel",Hg=v.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,...r}=e,o=Ze(n);return c.jsx(qS,{...o,...r,ref:t})});Hg.displayName=hE;var mE="DropdownMenuItem",Zg=v.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,...r}=e,o=Ze(n);return c.jsx(eE,{...o,...r,ref:t})});Zg.displayName=mE;var vE="DropdownMenuCheckboxItem",Gg=v.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,...r}=e,o=Ze(n);return c.jsx(tE,{...o,...r,ref:t})});Gg.displayName=vE;var gE="DropdownMenuRadioGroup",yE=v.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,...r}=e,o=Ze(n);return c.jsx(nE,{...o,...r,ref:t})});yE.displayName=gE;var xE="DropdownMenuRadioItem",Yg=v.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,...r}=e,o=Ze(n);return c.jsx(rE,{...o,...r,ref:t})});Yg.displayName=xE;var wE="DropdownMenuItemIndicator",Kg=v.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,...r}=e,o=Ze(n);return c.jsx(oE,{...o,...r,ref:t})});Kg.displayName=wE;var _E="DropdownMenuSeparator",Qg=v.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,...r}=e,o=Ze(n);return c.jsx(sE,{...o,...r,ref:t})});Qg.displayName=_E;var CE="DropdownMenuArrow",kE=v.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,...r}=e,o=Ze(n);return c.jsx(iE,{...o,...r,ref:t})});kE.displayName=CE;var SE="DropdownMenuSubTrigger",Xg=v.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,...r}=e,o=Ze(n);return c.jsx(aE,{...o,...r,ref:t})});Xg.displayName=SE;var EE="DropdownMenuSubContent",Jg=v.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,...r}=e,o=Ze(n);return c.jsx(lE,{...o,...r,ref:t,style:{...e.style,"--radix-dropdown-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-dropdown-menu-content-available-width":"var(--radix-popper-available-width)","--radix-dropdown-menu-content-available-height":"var(--radix-popper-available-height)","--radix-dropdown-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-dropdown-menu-trigger-height":"var(--radix-popper-anchor-height)"}})});Jg.displayName=EE;var bE=zg,NE=Bg,TE=Vg,qg=Wg,ey=Hg,ty=Zg,ny=Gg,ry=Yg,oy=Kg,sy=Qg,iy=Xg,ay=Jg;const RE=bE,jE=NE,ME=v.forwardRef(({className:e,inset:t,children:n,...r},o)=>c.jsxs(iy,{ref:o,className:Q("flex cursor-default select-none items-center gap-2 rounded-sm px-2 py-1.5 text-md outline-none","focus:bg-[var(--vscode-list-hoverBackground)] text-[var(--vscode-menu-foreground)]","data-[state=open]:bg-[var(--vscode-list-hoverBackground)]","[&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",t&&"pl-8",e),...r,children:[n,c.jsx(x_,{className:"ml-auto"})]}));ME.displayName=iy.displayName;const PE=v.forwardRef(({className:e,...t},n)=>c.jsx(ay,{ref:n,className:Q("z-50 min-w-[8rem] overflow-hidden rounded-sm border border-[var(--vscode-menu-border)]","bg-[var(--vscode-menu-background)] text-[var(--vscode-menu-foreground)] p-1",e),...t}));PE.displayName=ay.displayName;const ly=v.forwardRef(({className:e,sideOffset:t=4,...n},r)=>c.jsx(TE,{children:c.jsx(qg,{ref:r,sideOffset:t,className:Q("z-50 min-w-[6rem] overflow-y-auto overflow-x-hidden rounded-[3px] border border-[var(--vscode-menu-border)]","bg-[var(--vscode-menu-background)] text-[var(--vscode-menu-foreground)] p-1",e),...n})}));ly.displayName=qg.displayName;const Si=v.forwardRef(({className:e,inset:t,...n},r)=>c.jsx(ty,{ref:r,className:Q("relative flex cursor-default select-none items-center gap-2 rounded-sm h-5 text-sm outline-none transition-colors px-2","text-[var(--vscode-menu-foreground)]","focus:bg-[var(--vscode-menu-selectionBackground)] focus:text-[var(--vscode-menu-selectionForeground)]","data-[disabled]:pointer-events-none data-[disabled]:opacity-50","[&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0","text-center",t&&"pl-4",e),...n}));Si.displayName=ty.displayName;const AE=v.forwardRef(({className:e,children:t,checked:n=!1,...r},o)=>c.jsxs(ny,{ref:o,className:Q("relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-md outline-none transition-colors","focus:bg-[var(--vscode-list-hoverBackground)] focus:text-[var(--vscode-menu-foreground)]","data-[disabled]:pointer-events-none data-[disabled]:opacity-50",e),checked:n,...r,children:[c.jsx("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:c.jsx(oy,{children:c.jsx(y_,{className:"h-4 w-4"})})}),t]}));AE.displayName=ny.displayName;const OE=v.forwardRef(({className:e,children:t,...n},r)=>c.jsxs(ry,{ref:r,className:Q("relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-md outline-none transition-colors","focus:bg-[var(--vscode-list-hoverBackground)] focus:text-[var(--vscode-menu-foreground)]","data-[disabled]:pointer-events-none data-[disabled]:opacity-50",e),...n,children:[c.jsx("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:c.jsx(oy,{children:c.jsx(w_,{className:"h-2 w-2 fill-current"})})}),t]}));OE.displayName=ry.displayName;const IE=v.forwardRef(({className:e,inset:t,...n},r)=>c.jsx(ey,{ref:r,className:Q("px-2 py-1.5 text-md font-semibold",t&&"pl-8",e),...n}));IE.displayName=ey.displayName;const DE=v.forwardRef(({className:e,...t},n)=>c.jsx(sy,{ref:n,className:Q("-mx-1 my-1 h-px bg-[var(--vscode-menu-border)]",e),...t}));DE.displayName=sy.displayName;const LE=({onClick:e=()=>{},id:t,children:n,className:r})=>c.jsx("div",{role:"treeitem",tabIndex:0,id:t,className:Q(`
        flex items-center w-full px-2 py-1 text-md rounded-sm
        transition-colors duration-100 select-none outline-none
        hover:bg-[var(--vscode-list-hoverBackground)]
        focus-visible:ring-2 focus-visible:ring-[var(--vscode-focusBorder)]
      `,r),onClick:()=>{e()},children:n}),$E={uncommitted:"Review uncommitted changes",committed:"Review committed changes",all:"Review all changes"},uy=()=>{const{isBusy:e,setIsBusy:t,currentReviewId:n,reviews:r,filesToReview:{files:o},reviewType:s}=v.useContext(pn),[i,a]=v.useState(s);v.useEffect(()=>{Se.postMessage({type:"getFilesToReview"})},[i]);function l(){n&&Se.postMessage({type:"stopReview",data:{reviewId:n}}),t(!1)}function u(){Se.postMessage({type:"doReview"}),w(""),t(!0)}function d(g){Se.postMessage({type:"setReviewType",value:{reviewType:g}}),a(g)}const f=r.find(g=>g.id===n),p=za(f),[y,w]=v.useState(p?"":"file-changes");return v.useEffect(()=>{fk(f)&&t(!1)},[f]),c.jsxs("div",{className:"flex flex-col gap-0.5 mt-3 mb-1",children:[c.jsx(Bn,{type:"single",collapsible:!0,value:y,onValueChange:g=>{w(g)},children:c.jsxs(Vn,{value:"file-changes",children:[c.jsx(Un,{className:Q("no-underline hover:no-underline justify-end gap-0 flex-row-reverse p-0 px-0.5 pr-2","[&>svg]:-rotate-90 [&>svg]:-mt-0.5 [&[data-state=open]>svg]:rotate-0 cursor-pointer"),children:c.jsx("div",{className:"flex flex-row flex-wrap justify-between items-center w-full mb-1",children:c.jsxs("div",{className:"text-sm uppercase font-bold tracking-tight",children:["Files to review (",o.length,")"]})})}),c.jsxs(Wn,{className:"pb-0",children:[o.length===0&&c.jsx("span",{className:"pl-5 text-muted-foreground",children:"No files to review"}),o.length>0&&c.jsx("div",{role:"tree","aria-label":"Files explorer",className:"max-h-40 overflow-auto flex flex-col",children:o.map(g=>g.filePath?c.jsx(LE,{id:g.filePath,className:Q("pl-5 pb-0.5 rounded-none cursor-pointer flex flex-row items-center gap-1 h-[22px] py-0.5"),onClick:()=>{Se.postMessage({type:"openFile",value:{filePath:g.filePath}})},children:c.jsxs("div",{className:"flex flex-row items-center gap-1 label mr-2",children:[c.jsx("span",{className:"codicon codicon-file !text-icon"}),c.jsx(Ft,{children:c.jsxs(tn,{children:[c.jsx(nn,{children:c.jsx("span",{className:Q("truncate text-md line-clamp-1 text-wrap"),children:Xv.basename(g.filePath)})}),c.jsx(rn,{className:"text-wrap",children:g.filePath})]})})]})},g.filePath):null)})]})]})}),p&&c.jsx(Bt,{className:"mt-2 mx-4",onClick:l,variant:"secondary",children:"Stop Review"}),!p&&c.jsxs("div",{className:"flex gap-x-1 mt-2 mx-4",children:[c.jsx(Bt,{onClick:u,disabled:e||!o.length,className:"flex-1 rounded-r-none border-r",children:e?"Starting review...":$E[i]}),c.jsxs(RE,{children:[c.jsx(jE,{asChild:!0,children:c.jsx(Bt,{className:"px-2 rounded-l-none",disabled:e,children:c.jsx(tv,{className:"h-4 w-4"})})}),c.jsxs(ly,{align:"end",children:[c.jsx(Si,{onClick:()=>{d("uncommitted")},children:"Review uncommitted changes"}),c.jsx(Si,{onClick:()=>{d("committed")},children:"Review committed changes"}),c.jsx(Si,{onClick:()=>{d("all")},children:"Review all changes"})]})]})]})]})},zE=()=>{const e=()=>{Se.postMessage({type:"enableGit"})};return c.jsxs("div",{className:"flex flex-col gap-y-4 px-4",children:[c.jsx("p",{className:"mt-4",children:"To use CodeRabbit, Git must be enabled in your workspace. Click the button below to enable Git automatically."}),c.jsx(Bt,{onClick:e,className:"w-full",children:"Enable Git and Refresh"})]})},FE=()=>{const e=()=>{Se.postMessage({type:"initializeGit"})};return c.jsxs("div",{className:"flex flex-col gap-y-4 px-2",children:[c.jsx("p",{className:"mt-4",children:"This folder doesn't have a Git repository initialized. You need to initialize Git in this project or add an existing Git repository to the workspace to use CodeRabbit."}),c.jsx(Bt,{onClick:e,className:"w-full",children:"Initialize Git"})]})},BE=({status:e})=>e===wi.cancelled||e===wi.failed?c.jsx("span",{className:"codicon codicon-circle-filled text-error-foreground !text-icon"}):e===wi.completed?c.jsx("span",{className:"codicon codicon-circle-filled !text-[var(--vscode-button-background)] !text-lg"}):c.jsx("span",{className:"codicon codicon-pending !text-icon"}),VE=e=>c.jsx("svg",{className:"size-10",viewBox:"0 0 903 784",fill:"none",xmlns:"http://www.w3.org/2000/svg",...e,children:c.jsx("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M902.854 451.593C902.854 582.796 846.86 701.41 757.072 783.909H648.729C651.049 773.307 645.416 766.018 638.127 761.048C622.886 750.445 613.278 732.885 615.597 714.663C621.561 666.621 650.717 605.326 757.072 549.001C828.969 510.568 842.222 431.382 847.192 419.786C854.481 400.569 852.493 384.334 837.252 368.762C809.752 340.599 779.933 315.088 743.819 298.522C693.127 274.666 641.44 275.992 590.085 295.208C581.14 298.522 583.128 291.564 582.465 288.582C575.507 254.787 563.911 222.317 547.345 191.836C513.881 131.535 467.165 87.1378 397.587 72.2283C387.316 69.909 376.714 69.2464 366.111 67.9211C361.142 67.2585 358.491 68.5838 359.816 74.5476C369.093 118.945 382.015 161.685 410.177 198.131C423.099 215.028 440.659 226.293 457.888 237.89C476.11 250.48 495.327 262.076 512.556 276.323C528.791 290.239 542.044 306.473 548.339 329.666C546.019 327.347 545.025 326.684 544.363 325.69C491.351 241.534 387.647 204.426 283.281 234.908C273.672 237.558 274.998 240.871 279.636 247.829C309.787 294.214 351.202 327.347 400.238 352.858C436.683 371.744 474.454 387.316 515.538 392.617C534.092 394.936 525.146 407.527 528.128 422.436C532.104 445.629 541.712 452.918 538.399 450.93C475.448 425.75 423.099 415.81 379.695 415.81C200.45 415.81 171.294 586.772 172.619 588.76C169.968 587.766 126.896 571.863 117.288 614.935C107.348 658.669 165.992 687.163 165.992 687.163C173.613 635.476 219.998 624.212 225.631 622.887C220.992 625.537 190.842 643.097 181.565 691.139C173.613 733.548 207.077 770.988 219.667 783.909H145.451C55.9935 701.41 0 582.796 0 451.593C0 202.107 201.775 0 451.261 0C700.747 0 902.854 202.107 902.854 451.593ZM574.845 783.909H527.134C529.453 781.59 531.11 778.276 531.773 774.632C535.748 751.439 515.869 746.801 515.869 746.801H405.87C405.87 746.801 449.605 744.813 489.695 728.247C529.453 711.349 561.592 681.862 567.224 673.91C548.67 717.644 557.616 754.421 565.236 772.975C566.893 777.614 570.537 781.59 574.845 783.909Z",fill:"#666666"})}),ai=5,UE=({fileReviewItem:e})=>{const t=e==null?void 0:e.comments.filter(n=>n.indicatorType==="potential_issue");return!(t!=null&&t.length)&&!(e!=null&&e.comments.length)?null:!(t!=null&&t.length)&&(e!=null&&e.comments.length)?c.jsxs(Wo,{className:"px-1 py-[2px] text-sm rounded-[11px] size-3.5 text-center w-auto",children:[Math.min(e.comments.length,ai),e.comments.length>ai&&"+"]}):c.jsx(c.Fragment,{children:!!(e!=null&&e.comments.length)&&(e==null?void 0:e.comments.length)&&c.jsxs("li",{role:"presentation","aria-label":`${e.comments.length} comments`,className:Q("flex flex-row justify-center flex-nowrap truncate items-center text-md gap-0.5 size-3.5","text-error-foreground font-normal"),children:[Math.min(e.comments.length,ai),e.comments.length>ai?"+":"!"]})})},WE=()=>{const{auth:e}=v.useContext(pn);function t(){Se.postMessage({type:"login",value:""})}const n=()=>{Se.postMessage({type:"loginWithToken"})};return c.jsxs("div",{className:"h-full flex flex-col items-center justify-center px-2 shrink",children:[c.jsx(VE,{className:"size-12"}),c.jsxs("div",{className:"flex flex-col w-full items-center justify-center gap-8",children:[c.jsxs("div",{className:"flex flex-col gap-4 items-center justify-center",children:[c.jsx("h1",{className:"text-xl mt-4 text-center",children:"Welcome to CodeRabbit"}),c.jsxs("p",{className:"text-md text-center",children:[c.jsx("a",{href:"https://www.coderabbit.ai/",target:"_blank",rel:"noreferrer",children:"CodeRabbit"})," ","cuts code review time & bugs in half."]}),c.jsx("ul",{className:"w-full max-w-[350px] bg-[var(--vscode-chat-requestBackground)] p-3 rounded-sm border border-[var(--vscode-chat-requestBorder)] flex flex-col gap-3 truncate",children:Object.keys(Rl).map(r=>c.jsxs("li",{className:"flex flex-row items-start gap-2 flex-nowrap truncate",children:[c.jsx("span",{className:Q("!text-icon codicon mt-[3px]",Rl[r].icon)}),c.jsx("span",{className:"text-wrap line-clamp-2",children:Rl[r].label})]},r))})]}),c.jsxs("div",{className:"w-full flex flex-col gap-4 max-w-[350px]",children:[c.jsxs(Bt,{disabled:e.state!=="idle",onClick:t,className:"truncate",children:[e.state==="idle"&&"Use CodeRabbit for Free",e.state==="pending"&&"Logging in..."]}),e.state==="pending"&&c.jsx("button",{className:"text-muted-foreground text-sm hover:underline",onClick:n,children:"Paste code from browser"}),e.state==="idle"&&c.jsxs("p",{className:"font-400 m-3 mb-5 text-center",children:["By continuing, you agree to the"," ",c.jsx("a",{href:"https://coderabbit.ai/terms-of-service",target:"_blank",className:"hover:underline",rel:"noreferrer","aria-label":"Terms of Use (opens in new tab)",children:"Terms of Use"})," ","and"," ",c.jsx("a",{href:"https://coderabbit.ai/privacy-policy",target:"_blank",className:"hover:underline",rel:"noreferrer","aria-label":"Privacy Policy (opens in new tab)",children:"Privacy Policy"})," ","applicable to CodeRabbit."]}),e.state==="pending"&&c.jsxs("p",{className:"font-400 m-3 mb-5 text-center",children:["You will be redirected to CodeRabbit on your browser. Accept the popups asking you to redirect out and into VSCode."," "]})]})]})]})},Rl={code:{label:"Analyze PRs with full codebase context",icon:"codicon-code"},debug:{label:"Catch bugs and security issues",icon:"codicon-debug"}},HE=()=>{const e=()=>{Se.postMessage({type:"openFolder"})};return c.jsxs("div",{className:"flex flex-col gap-y-4 px-2",children:[c.jsx("p",{className:"mt-4",children:"Open a workspace to start reviewing code with CodeRabbit."}),c.jsx(Bt,{onClick:e,className:"w-full",children:"Open Workspace"})]})},ZE=()=>(v.useEffect(()=>{Se.postMessage({type:"getBranchInfo"})},[]),c.jsxs("div",{className:"flex flex-col gap-1 pb-4 mt-1",children:[c.jsx("div",{className:"flex justify-between items-center pl-5 pr-2",children:c.jsx("span",{className:"text-sm uppercase font-bold",children:"New Review"})}),c.jsx(Qv,{}),c.jsx(uy,{})]})),GE=({comment:e,reviewId:t})=>{const n=()=>{Se.postMessage({type:"showLines",value:{filename:e.filename,commentId:e.id,reviewId:t}})},r=e.components.find(s=>s.type==="title"),o=e.indicatorType?pk(e.indicatorType):null;return c.jsx("div",{className:"w-full pl-3 py-2 group",children:c.jsxs("div",{className:"w-full rounded-md",children:[c.jsx("div",{className:"flex items-start justify-between gap-x-8",children:c.jsx("button",{type:"button",className:Q("text-md grow line-clamp-3 text-left","hover:text-muted-foreground cursor-pointer"),"aria-label":"Review comment title",onClick:()=>{n()},children:r==null?void 0:r.text})}),o&&c.jsx("div",{className:Q("text-sm mt-1 leading-[16px] max-w-40 rounded-sm px-[5px] w-fit text-[var(--vscode-button-foreground)]","bg-[var(--vscode-button-background)]",e.indicatorType==="refactor_suggestion"&&"bg-[var(--vscode-button-background)]",e.indicatorType==="potential_issue"&&"bg-error-foreground",e.indicatorType==="verification"&&"bg-[var(--vscode-ports-iconRunningProcessForeground)]",e.indicatorType==="nitpick"&&"bg-[var(--vscode-activityWarningBadge-background)]"),children:o})]})})},YE=({filePath:e,fileReviewItem:t,reviewId:n,isReviewLoading:r})=>{const o=!!(t!=null&&t.comments.length),s=()=>{Se.postMessage({type:"showFileDiffForReview",value:{filePath:e,reviewId:n}})};return c.jsxs(Vn,{value:e,children:[c.jsx(Un,{className:Q("h-[22px] leading-[22px]","no-underline hover:no-underline justify-end gap-1 p-0 pl-4 pr-2","cursor-pointer [&>svg]:hidden","!font-normal group","transition-colors duration-100 select-none outline-none","hover:bg-[var(--vscode-list-hoverBackground)]","focus-visible:ring-2 focus-visible:ring-[var(--vscode-focusBorder)]","[&[data-state=open]>div>.label>.codicon-chevron-down]:rotate-180 rounded-none","group"),onClick:i=>{o||(i.preventDefault(),s())},children:c.jsxs("div",{className:"flex flex-row items-center justify-between px-0 w-full pl-1",children:[c.jsxs("div",{className:"flex flex-row items-center gap-1 label mr-2",children:[c.jsx("span",{className:"codicon codicon-file !text-icon"}),c.jsx(Ft,{children:c.jsxs(tn,{children:[c.jsx(nn,{children:c.jsx("span",{className:Q("truncate text-md line-clamp-1 text-wrap"),children:Xv.basename(e)})}),c.jsx(rn,{className:"text-wrap",children:e})]})}),!!(t!=null&&t.comments.length)&&c.jsx("span",{className:"codicon codicon-chevron-down mt-0.5 transition-transform duration-200"})]}),o?c.jsx("ul",{role:"toolbar","aria-label":"Review file",className:Q("flex flex-row items-center review-file-actions gap-1","transition-opacity duration-100 ease-in"),children:c.jsx(UE,{fileReviewItem:t})}):c.jsx("span",{className:"text-muted-foreground text-xs group-hover:block hidden text-nowrap",children:r?"Under review":"No comments"})]})}),c.jsx(Wn,{className:"pr-2 pl-6 pb-2",children:c.jsx("div",{className:Q("flex flex-col"),children:t==null?void 0:t.comments.slice().sort((i,a)=>{const l=u=>u.indicatorType==="potential_issue"?0:u.indicatorType==="refactor_suggestion"?1:2;return l(i)-l(a)}).map(i=>c.jsx("div",{className:Q("border-l-3 mb-0.5","border-[var(--vscode-tree-indentGuidesStroke)]",i.indicatorType==="potential_issue"&&"border-error-foreground",i.indicatorType==="refactor_suggestion"&&"border-[var(--vscode-button-background)]",i.indicatorType==="verification"&&"border-[var(--vscode-ports-iconRunningProcessForeground)]",i.indicatorType==="nitpick"&&"border-[var(--vscode-activityWarningBadge-background)]"),children:c.jsx(GE,{comment:i,reviewId:n})},i.id))})})]})},np=30,KE=({files:e,reviewState:t})=>{const[n,r]=v.useState([]),o=Object.keys(t.fileReviewMap).some(a=>{var l;return(l=t.fileReviewMap[a])==null?void 0:l.comments.length}),s=t.status===wi.completed?o:!0,i=za(t);return c.jsxs("div",{className:"flex flex-col gap-0.5",children:[c.jsxs("div",{className:"flex flex-row items-center pr-2 gap-1",children:[c.jsx("span",{className:"text-sm uppercase truncate font-bold leading-[22px] tracking-tighter pl-5",children:"Files"}),!!e.length&&o&&c.jsxs("div",{className:"text-sm",children:["(",Math.min(e.length,np),e.length>np&&"+",")"]})]}),s?c.jsx(Bn,{type:"multiple",className:"flex flex-col gap-0.5 max-h-96 overflow-auto",value:n,onValueChange:r,children:e.map(a=>c.jsx(YE,{filePath:a,fileReviewItem:t.fileReviewMap[a],reviewId:t.id,isReviewLoading:i},a))}):c.jsx("span",{className:"pl-5 text-muted-foreground",children:"No issues detected in this review. You're good to go! 🎉"})]})},QE=({reviewState:e})=>{const t=za(e),[n,r]=v.useState(t);v.useEffect(()=>{t&&r(!0)},[t]);const o=()=>{t||r(!1)};return n?c.jsx("div",{className:Q("flex flex-col items-start gap-x-2 justify-center gap-2","transition-[max-height] duration-800 ease-in-out",t?"max-h-full":"max-h-0"),onTransitionEnd:o,children:c.jsx("ul",{className:"flex flex-col gap-1 pb-2 mt-2",children:Object.keys(Ho).map(s=>{const i=s;if(fd(i)||!Ho[i])return null;const a=e.status==="completed",l=JE(i,e.step)||a,u=XE(i,e.step)&&!a;return c.jsxs("li",{className:Q("flex flex-row gap-1 items-center h-5",u?"[&>*]:text-primary-foreground":"[&>*]:text-muted-foreground",l?"[&>*]:text-[var(--vscode-button-background)]":""),children:[l?c.jsx("span",{className:"codicon codicon-pass !text-icon"}):c.jsx("span",{className:"codicon codicon-circle-large !text-icon"}),c.jsx("span",{children:Ho[i]}),u&&c.jsx("div",{className:"flex items-center gap-x-2 mt-2",children:c.jsxs("div",{className:"flex items-center gap-x-1 text-muted-foreground",children:[c.jsx("span",{className:"animate-[bounce_1s_infinite_0ms]",children:"."}),c.jsx("span",{className:"animate-[bounce_1s_infinite_200ms]",children:"."}),c.jsx("span",{className:"animate-[bounce_1s_infinite_400ms]",children:"."})]})})]},i)})})}):c.jsxs("div",{className:"flex flex-row gap-1 items-center my-1 -ml-1",children:[c.jsx(BE,{status:e.status}),c.jsxs("span",{children:["Review ",e.status.replace("_"," ")]})]})};function fd(e){return e===$r.review_started||e===$r.review_skipped}function XE(e,t){const n=Object.keys(Ho).filter(s=>!fd(s)),r=n.findIndex(s=>s===e);if(r===-1)return!1;if(!t)return r===0;const o=n.findIndex(s=>s===t);return o===-1?!1:r===o}function JE(e,t){if(!t)return!1;const n=Object.keys(Ho).filter(s=>!fd(s)),r=n.findIndex(s=>s===t),o=n.findIndex(s=>s===e);return o===-1||r===-1?!1:o<r}const Ho={[$r.setting_up]:"Setting up",[$r.review_started]:"Review started",[$r.summarizing]:"Analyzing changes",[$r.reviewing]:"Reviewing files"},qE=({reviewState:e})=>c.jsx("div",{className:"flex flex-col gap-1 pl-5 pb-2 pr-2",children:c.jsx(QE,{reviewState:e})}),cy=({reviewState:e})=>{const n=Object.keys(e.fileReviewMap).sort((r,o)=>{var p,y;const s=(w,g)=>{var k;return((k=e.fileReviewMap[w])==null?void 0:k.comments.filter(m=>m.indicatorType===g).length)??0},i=s(r,ei.potentialIssue),a=s(o,ei.potentialIssue),l=s(r,ei.refactorSuggestion),u=s(o,ei.refactorSuggestion),d=((p=e.fileReviewMap[r])==null?void 0:p.comments.length)??0,f=((y=e.fileReviewMap[o])==null?void 0:y.comments.length)??0;return i!==a?a-i:l!==u?u-l:f-d});return c.jsx("div",{className:"flex flex-col gap-4 rounded-md w-full",children:c.jsxs("div",{className:"flex flex-col gap-1 divide-y space-y-1 divide-[var(--vscode-settings-sashBorder)]",children:[c.jsx(qE,{reviewState:e}),c.jsx(KE,{files:n,reviewState:e})]})})};var dy={exports:{}};(function(e,t){(function(n,r){e.exports=r()})(ua,function(){var n=1e3,r=6e4,o=36e5,s="millisecond",i="second",a="minute",l="hour",u="day",d="week",f="month",p="quarter",y="year",w="date",g="Invalid Date",k=/^(\d{4})[-/]?(\d{1,2})?[-/]?(\d{0,2})[Tt\s]*(\d{1,2})?:?(\d{1,2})?:?(\d{1,2})?[.:]?(\d+)?$/,m=/\[([^\]]+)]|Y{1,4}|M{1,4}|D{1,2}|d{1,4}|H{1,2}|h{1,2}|a|A|m{1,2}|s{1,2}|Z{1,2}|SSS/g,h={name:"en",weekdays:"Sunday_Monday_Tuesday_Wednesday_Thursday_Friday_Saturday".split("_"),months:"January_February_March_April_May_June_July_August_September_October_November_December".split("_"),ordinal:function(I){var D=["th","st","nd","rd"],j=I%100;return"["+I+(D[(j-20)%10]||D[j]||D[0])+"]"}},x=function(I,D,j){var z=String(I);return!z||z.length>=D?I:""+Array(D+1-z.length).join(j)+I},_={s:x,z:function(I){var D=-I.utcOffset(),j=Math.abs(D),z=Math.floor(j/60),E=j%60;return(D<=0?"+":"-")+x(z,2,"0")+":"+x(E,2,"0")},m:function I(D,j){if(D.date()<j.date())return-I(j,D);var z=12*(j.year()-D.year())+(j.month()-D.month()),E=D.clone().add(z,f),T=j-E<0,P=D.clone().add(z+(T?-1:1),f);return+(-(z+(j-E)/(T?E-P:P-E))||0)},a:function(I){return I<0?Math.ceil(I)||0:Math.floor(I)},p:function(I){return{M:f,y,w:d,d:u,D:w,h:l,m:a,s:i,ms:s,Q:p}[I]||String(I||"").toLowerCase().replace(/s$/,"")},u:function(I){return I===void 0}},b="en",N={};N[b]=h;var S="$isDayjsObject",C=function(I){return I instanceof V||!(!I||!I[S])},L=function I(D,j,z){var E;if(!D)return b;if(typeof D=="string"){var T=D.toLowerCase();N[T]&&(E=T),j&&(N[T]=j,E=T);var P=D.split("-");if(!E&&P.length>1)return I(P[0])}else{var B=D.name;N[B]=D,E=B}return!z&&E&&(b=E),E||!z&&b},M=function(I,D){if(C(I))return I.clone();var j=typeof D=="object"?D:{};return j.date=I,j.args=arguments,new V(j)},$=_;$.l=L,$.i=C,$.w=function(I,D){return M(I,{locale:D.$L,utc:D.$u,x:D.$x,$offset:D.$offset})};var V=function(){function I(j){this.$L=L(j.locale,null,!0),this.parse(j),this.$x=this.$x||j.x||{},this[S]=!0}var D=I.prototype;return D.parse=function(j){this.$d=function(z){var E=z.date,T=z.utc;if(E===null)return new Date(NaN);if($.u(E))return new Date;if(E instanceof Date)return new Date(E);if(typeof E=="string"&&!/Z$/i.test(E)){var P=E.match(k);if(P){var B=P[2]-1||0,Z=(P[7]||"0").substring(0,3);return T?new Date(Date.UTC(P[1],B,P[3]||1,P[4]||0,P[5]||0,P[6]||0,Z)):new Date(P[1],B,P[3]||1,P[4]||0,P[5]||0,P[6]||0,Z)}}return new Date(E)}(j),this.init()},D.init=function(){var j=this.$d;this.$y=j.getFullYear(),this.$M=j.getMonth(),this.$D=j.getDate(),this.$W=j.getDay(),this.$H=j.getHours(),this.$m=j.getMinutes(),this.$s=j.getSeconds(),this.$ms=j.getMilliseconds()},D.$utils=function(){return $},D.isValid=function(){return this.$d.toString()!==g},D.isSame=function(j,z){var E=M(j);return this.startOf(z)<=E&&E<=this.endOf(z)},D.isAfter=function(j,z){return M(j)<this.startOf(z)},D.isBefore=function(j,z){return this.endOf(z)<M(j)},D.$g=function(j,z,E){return $.u(j)?this[z]:this.set(E,j)},D.unix=function(){return Math.floor(this.valueOf()/1e3)},D.valueOf=function(){return this.$d.getTime()},D.startOf=function(j,z){var E=this,T=!!$.u(z)||z,P=$.p(j),B=function(Ee,be){var gt=$.w(E.$u?Date.UTC(E.$y,be,Ee):new Date(E.$y,be,Ee),E);return T?gt:gt.endOf(u)},Z=function(Ee,be){return $.w(E.toDate()[Ee].apply(E.toDate("s"),(T?[0,0,0,0]:[23,59,59,999]).slice(be)),E)},se=this.$W,oe=this.$M,_e=this.$D,ge="set"+(this.$u?"UTC":"");switch(P){case y:return T?B(1,0):B(31,11);case f:return T?B(1,oe):B(0,oe+1);case d:var ye=this.$locale().weekStart||0,vt=(se<ye?se+7:se)-ye;return B(T?_e-vt:_e+(6-vt),oe);case u:case w:return Z(ge+"Hours",0);case l:return Z(ge+"Minutes",1);case a:return Z(ge+"Seconds",2);case i:return Z(ge+"Milliseconds",3);default:return this.clone()}},D.endOf=function(j){return this.startOf(j,!1)},D.$set=function(j,z){var E,T=$.p(j),P="set"+(this.$u?"UTC":""),B=(E={},E[u]=P+"Date",E[w]=P+"Date",E[f]=P+"Month",E[y]=P+"FullYear",E[l]=P+"Hours",E[a]=P+"Minutes",E[i]=P+"Seconds",E[s]=P+"Milliseconds",E)[T],Z=T===u?this.$D+(z-this.$W):z;if(T===f||T===y){var se=this.clone().set(w,1);se.$d[B](Z),se.init(),this.$d=se.set(w,Math.min(this.$D,se.daysInMonth())).$d}else B&&this.$d[B](Z);return this.init(),this},D.set=function(j,z){return this.clone().$set(j,z)},D.get=function(j){return this[$.p(j)]()},D.add=function(j,z){var E,T=this;j=Number(j);var P=$.p(z),B=function(oe){var _e=M(T);return $.w(_e.date(_e.date()+Math.round(oe*j)),T)};if(P===f)return this.set(f,this.$M+j);if(P===y)return this.set(y,this.$y+j);if(P===u)return B(1);if(P===d)return B(7);var Z=(E={},E[a]=r,E[l]=o,E[i]=n,E)[P]||1,se=this.$d.getTime()+j*Z;return $.w(se,this)},D.subtract=function(j,z){return this.add(-1*j,z)},D.format=function(j){var z=this,E=this.$locale();if(!this.isValid())return E.invalidDate||g;var T=j||"YYYY-MM-DDTHH:mm:ssZ",P=$.z(this),B=this.$H,Z=this.$m,se=this.$M,oe=E.weekdays,_e=E.months,ge=E.meridiem,ye=function(be,gt,vn,jt){return be&&(be[gt]||be(z,T))||vn[gt].slice(0,jt)},vt=function(be){return $.s(B%12||12,be,"0")},Ee=ge||function(be,gt,vn){var jt=be<12?"AM":"PM";return vn?jt.toLowerCase():jt};return T.replace(m,function(be,gt){return gt||function(vn){switch(vn){case"YY":return String(z.$y).slice(-2);case"YYYY":return $.s(z.$y,4,"0");case"M":return se+1;case"MM":return $.s(se+1,2,"0");case"MMM":return ye(E.monthsShort,se,_e,3);case"MMMM":return ye(_e,se);case"D":return z.$D;case"DD":return $.s(z.$D,2,"0");case"d":return String(z.$W);case"dd":return ye(E.weekdaysMin,z.$W,oe,2);case"ddd":return ye(E.weekdaysShort,z.$W,oe,3);case"dddd":return oe[z.$W];case"H":return String(B);case"HH":return $.s(B,2,"0");case"h":return vt(1);case"hh":return vt(2);case"a":return Ee(B,Z,!0);case"A":return Ee(B,Z,!1);case"m":return String(Z);case"mm":return $.s(Z,2,"0");case"s":return String(z.$s);case"ss":return $.s(z.$s,2,"0");case"SSS":return $.s(z.$ms,3,"0");case"Z":return P}return null}(be)||P.replace(":","")})},D.utcOffset=function(){return 15*-Math.round(this.$d.getTimezoneOffset()/15)},D.diff=function(j,z,E){var T,P=this,B=$.p(z),Z=M(j),se=(Z.utcOffset()-this.utcOffset())*r,oe=this-Z,_e=function(){return $.m(P,Z)};switch(B){case y:T=_e()/12;break;case f:T=_e();break;case p:T=_e()/3;break;case d:T=(oe-se)/6048e5;break;case u:T=(oe-se)/864e5;break;case l:T=oe/o;break;case a:T=oe/r;break;case i:T=oe/n;break;default:T=oe}return E?T:$.a(T)},D.daysInMonth=function(){return this.endOf(f).$D},D.$locale=function(){return N[this.$L]},D.locale=function(j,z){if(!j)return this.$L;var E=this.clone(),T=L(j,z,!0);return T&&(E.$L=T),E},D.clone=function(){return $.w(this.$d,this)},D.toDate=function(){return new Date(this.valueOf())},D.toJSON=function(){return this.isValid()?this.toISOString():null},D.toISOString=function(){return this.$d.toISOString()},D.toString=function(){return this.$d.toUTCString()},I}(),G=V.prototype;return M.prototype=G,[["$ms",s],["$s",i],["$m",a],["$H",l],["$W",u],["$M",f],["$y",y],["$D",w]].forEach(function(I){G[I[1]]=function(D){return this.$g(D,I[0],I[1])}}),M.extend=function(I,D){return I.$i||(I(D,V,M),I.$i=!0),M},M.locale=L,M.isDayjs=C,M.unix=function(I){return M(1e3*I)},M.en=N[b],M.Ls=N,M.p={},M})})(dy);var eb=dy.exports;const Wa=mr(eb);var fy={exports:{}};(function(e,t){(function(n,r){e.exports=r()})(ua,function(){var n,r,o=1e3,s=6e4,i=36e5,a=864e5,l=/\[([^\]]+)]|Y{1,4}|M{1,4}|D{1,2}|d{1,4}|H{1,2}|h{1,2}|a|A|m{1,2}|s{1,2}|Z{1,2}|SSS/g,u=31536e6,d=2628e6,f=/^(-|\+)?P(?:([-+]?[0-9,.]*)Y)?(?:([-+]?[0-9,.]*)M)?(?:([-+]?[0-9,.]*)W)?(?:([-+]?[0-9,.]*)D)?(?:T(?:([-+]?[0-9,.]*)H)?(?:([-+]?[0-9,.]*)M)?(?:([-+]?[0-9,.]*)S)?)?$/,p={years:u,months:d,days:a,hours:i,minutes:s,seconds:o,milliseconds:1,weeks:6048e5},y=function(N){return N instanceof _},w=function(N,S,C){return new _(N,C,S.$l)},g=function(N){return r.p(N)+"s"},k=function(N){return N<0},m=function(N){return k(N)?Math.ceil(N):Math.floor(N)},h=function(N){return Math.abs(N)},x=function(N,S){return N?k(N)?{negative:!0,format:""+h(N)+S}:{negative:!1,format:""+N+S}:{negative:!1,format:""}},_=function(){function N(C,L,M){var $=this;if(this.$d={},this.$l=M,C===void 0&&(this.$ms=0,this.parseFromMilliseconds()),L)return w(C*p[g(L)],this);if(typeof C=="number")return this.$ms=C,this.parseFromMilliseconds(),this;if(typeof C=="object")return Object.keys(C).forEach(function(I){$.$d[g(I)]=C[I]}),this.calMilliseconds(),this;if(typeof C=="string"){var V=C.match(f);if(V){var G=V.slice(2).map(function(I){return I!=null?Number(I):0});return this.$d.years=G[0],this.$d.months=G[1],this.$d.weeks=G[2],this.$d.days=G[3],this.$d.hours=G[4],this.$d.minutes=G[5],this.$d.seconds=G[6],this.calMilliseconds(),this}}return this}var S=N.prototype;return S.calMilliseconds=function(){var C=this;this.$ms=Object.keys(this.$d).reduce(function(L,M){return L+(C.$d[M]||0)*p[M]},0)},S.parseFromMilliseconds=function(){var C=this.$ms;this.$d.years=m(C/u),C%=u,this.$d.months=m(C/d),C%=d,this.$d.days=m(C/a),C%=a,this.$d.hours=m(C/i),C%=i,this.$d.minutes=m(C/s),C%=s,this.$d.seconds=m(C/o),C%=o,this.$d.milliseconds=C},S.toISOString=function(){var C=x(this.$d.years,"Y"),L=x(this.$d.months,"M"),M=+this.$d.days||0;this.$d.weeks&&(M+=7*this.$d.weeks);var $=x(M,"D"),V=x(this.$d.hours,"H"),G=x(this.$d.minutes,"M"),I=this.$d.seconds||0;this.$d.milliseconds&&(I+=this.$d.milliseconds/1e3,I=Math.round(1e3*I)/1e3);var D=x(I,"S"),j=C.negative||L.negative||$.negative||V.negative||G.negative||D.negative,z=V.format||G.format||D.format?"T":"",E=(j?"-":"")+"P"+C.format+L.format+$.format+z+V.format+G.format+D.format;return E==="P"||E==="-P"?"P0D":E},S.toJSON=function(){return this.toISOString()},S.format=function(C){var L=C||"YYYY-MM-DDTHH:mm:ss",M={Y:this.$d.years,YY:r.s(this.$d.years,2,"0"),YYYY:r.s(this.$d.years,4,"0"),M:this.$d.months,MM:r.s(this.$d.months,2,"0"),D:this.$d.days,DD:r.s(this.$d.days,2,"0"),H:this.$d.hours,HH:r.s(this.$d.hours,2,"0"),m:this.$d.minutes,mm:r.s(this.$d.minutes,2,"0"),s:this.$d.seconds,ss:r.s(this.$d.seconds,2,"0"),SSS:r.s(this.$d.milliseconds,3,"0")};return L.replace(l,function($,V){return V||String(M[$])})},S.as=function(C){return this.$ms/p[g(C)]},S.get=function(C){var L=this.$ms,M=g(C);return M==="milliseconds"?L%=1e3:L=M==="weeks"?m(L/p[M]):this.$d[M],L||0},S.add=function(C,L,M){var $;return $=L?C*p[g(L)]:y(C)?C.$ms:w(C,this).$ms,w(this.$ms+$*(M?-1:1),this)},S.subtract=function(C,L){return this.add(C,L,!0)},S.locale=function(C){var L=this.clone();return L.$l=C,L},S.clone=function(){return w(this.$ms,this)},S.humanize=function(C){return n().add(this.$ms,"ms").locale(this.$l).fromNow(!C)},S.valueOf=function(){return this.asMilliseconds()},S.milliseconds=function(){return this.get("milliseconds")},S.asMilliseconds=function(){return this.as("milliseconds")},S.seconds=function(){return this.get("seconds")},S.asSeconds=function(){return this.as("seconds")},S.minutes=function(){return this.get("minutes")},S.asMinutes=function(){return this.as("minutes")},S.hours=function(){return this.get("hours")},S.asHours=function(){return this.as("hours")},S.days=function(){return this.get("days")},S.asDays=function(){return this.as("days")},S.weeks=function(){return this.get("weeks")},S.asWeeks=function(){return this.as("weeks")},S.months=function(){return this.get("months")},S.asMonths=function(){return this.as("months")},S.years=function(){return this.get("years")},S.asYears=function(){return this.as("years")},N}(),b=function(N,S,C){return N.add(S.years()*C,"y").add(S.months()*C,"M").add(S.days()*C,"d").add(S.hours()*C,"h").add(S.minutes()*C,"m").add(S.seconds()*C,"s").add(S.milliseconds()*C,"ms")};return function(N,S,C){n=C,r=C().$utils(),C.duration=function($,V){var G=C.locale();return w($,{$l:G},V)},C.isDuration=y;var L=S.prototype.add,M=S.prototype.subtract;S.prototype.add=function($,V){return y($)?b(this,$,1):L.bind(this)($,V)},S.prototype.subtract=function($,V){return y($)?b(this,$,-1):M.bind(this)($,V)}}})})(fy);var tb=fy.exports;const nb=mr(tb);var py={exports:{}};(function(e,t){(function(n,r){e.exports=r()})(ua,function(){return function(n,r,o){n=n||{};var s=r.prototype,i={future:"in %s",past:"%s ago",s:"a few seconds",m:"a minute",mm:"%d minutes",h:"an hour",hh:"%d hours",d:"a day",dd:"%d days",M:"a month",MM:"%d months",y:"a year",yy:"%d years"};function a(u,d,f,p){return s.fromToBase(u,d,f,p)}o.en.relativeTime=i,s.fromToBase=function(u,d,f,p,y){for(var w,g,k,m=f.$locale().relativeTime||i,h=n.thresholds||[{l:"s",r:44,d:"second"},{l:"m",r:89},{l:"mm",r:44,d:"minute"},{l:"h",r:89},{l:"hh",r:21,d:"hour"},{l:"d",r:35},{l:"dd",r:25,d:"day"},{l:"M",r:45},{l:"MM",r:10,d:"month"},{l:"y",r:17},{l:"yy",d:"year"}],x=h.length,_=0;_<x;_+=1){var b=h[_];b.d&&(w=p?o(u).diff(f,b.d,!0):f.diff(u,b.d,!0));var N=(n.rounding||Math.round)(Math.abs(w));if(k=w>0,N<=b.r||!b.r){N<=1&&_>0&&(b=h[_-1]);var S=m[b.l];y&&(N=y(""+N)),g=typeof S=="string"?S.replace("%d",N):S(N,d,b.l,k);break}}if(d)return g;var C=k?m.future:m.past;return typeof C=="function"?C(g):C.replace("%s",g)},s.to=function(u,d){return a(u,d,this,!0)},s.from=function(u,d){return a(u,d,this)};var l=function(u){return u.$u?o.utc():o()};s.toNow=function(u){return this.to(l(this),u)},s.fromNow=function(u){return this.from(l(this),u)}}})})(py);var rb=py.exports;const ob=mr(rb);var hy={exports:{}};(function(e,t){(function(n,r){e.exports=r()})(ua,function(){return function(n,r,o){o.updateLocale=function(s,i){var a=o.Ls[s];if(a)return(i?Object.keys(i):[]).forEach(function(l){a[l]=i[l]}),a}}})})(hy);var sb=hy.exports;const ib=mr(sb);Wa.extend(ob);Wa.extend(ib);Wa.extend(nb);Wa.updateLocale("en",{relativeTime:{future:"in %s",past:"%s ago",s:"%ds",m:"1m",mm:"%dm",h:"1h",hh:"%dh",d:"1d",dd:"%dd",w:"1w",ww:"%dw",M:"1mo",MM:"%dmo",y:"1y",yy:"%dy"}});const my=({reviewState:e})=>{function t(o){Se.postMessage({type:"deleteReviews",data:[o]})}const{currentReviewId:n}=v.useContext(pn),r=n===e.id;return c.jsx("div",{className:"flex flex-col items-center w-full py-0",children:c.jsxs("div",{className:"flex flex-row justify-between items-center w-full gap-2",children:[c.jsxs("div",{className:"flex flex-row items-center gap-1",children:[e.mode===D1.auto&&c.jsx(tn,{children:c.jsxs(Ft,{children:[c.jsx(nn,{asChild:!0,children:c.jsx(Bt,{className:"w-4 h-3.5 p-1 -mt-[1px]",children:c.jsx("span",{className:"grid place-items-center size-fit text-sm",children:"A"})})}),c.jsx(rn,{children:c.jsx("span",{className:"text-xs line-clamp-3",children:"This review was triggered automatically"})})]})}),e.title?c.jsx(tn,{children:c.jsxs(Ft,{children:[c.jsx(nn,{children:c.jsx("span",{className:"text-sm truncate font-bold text-wrap text-left line-clamp-1 uppercase tracking-tight",children:e.title})}),c.jsx(rn,{children:c.jsx("span",{className:"text-xs line-clamp-3",children:e.title})})]})}):c.jsxs("div",{className:Q("text-sm uppercase grow truncate flex flex-row items-center flex-nowrap",za(e)&&!e.title&&"text-muted-foreground"),children:[c.jsx("span",{children:"Review"}),(e.status==="cancelled"||e.status==="failed")&&c.jsxs("span",{className:"italic text-sm text-muted-foreground lowercase ml-1",children:["[",e.status,"]"]}),e.status==="in_progress"&&c.jsx("div",{className:"size-4 flex flex-col items-center justify-center",children:c.jsx("span",{className:"ml-1 before:mt-1 codicon codicon-circle-filled !text-xs text-[var(--vscode-button-background)] inline-flex animate-[ping_1s_ease-in-out_infinite]"})})]})]}),c.jsx("ul",{className:"flex flex-row items-center actions max-w-5/12",children:!r&&c.jsx(Ft,{children:c.jsxs(tn,{children:[c.jsx(nn,{children:c.jsx("li",{className:Q("size-5 flex flex-col justify-center items-center","text-[var(--vscode-icon-foreground)] p-0.5","hover:bg-[var(--vscode-toolbar-hoverBackground)]","rounded-[5px]","invisible group-hover:visible"),onClick:o=>{o.preventDefault(),t(e.id)},children:c.jsx("span",{className:"codicon codicon-trash"})})}),c.jsx(rn,{children:"Clear review"})]})})})]})})},rp=9,ab=({olderReviews:e,accordionValue:t,setAccordionValue:n})=>{function r(){Se.postMessage({type:"deleteReviews",data:e.map(o=>o.id)})}return e.length?c.jsx(Bn,{type:"single",collapsible:!0,children:c.jsxs(Vn,{value:"older-reviews",children:[c.jsxs(Un,{className:Q("no-underline hover:no-underline gap-0 justify-end flex-row-reverse py-1 group relative","[&>svg]:-rotate-90 [&[data-state=open]>svg]:rotate-0 cursor-pointer","mx-1 items-center"),children:[c.jsx("ul",{className:Q("flex flex-row items-center actions gap-1","transition-opacity duration-100 ease-in","absolute right-1"),children:c.jsx(Ft,{children:c.jsxs(tn,{children:[c.jsx(nn,{children:c.jsx("li",{className:Q("size-5 flex flex-col justify-center items-center","text-[var(--vscode-icon-foreground)] p-0.5","hover:bg-[var(--vscode-toolbar-hoverBackground)]","rounded-[5px]","invisible group-hover:visible"),onClick:o=>{o.preventDefault(),r()},children:c.jsx("span",{className:"codicon codicon-trash"})})}),c.jsx(rn,{children:"Clear all"})]})})}),c.jsxs("div",{className:"flex flex-row gap-1 items-center",children:[c.jsx("span",{className:"text-sm uppercase font-bold",children:"Previous reviews"}),c.jsxs(Wo,{className:"px-1 py-[2px] text-sm rounded-[11px] size-3.5 text-center w-auto",children:[Math.min(e.length,rp),e.length>rp&&"+"]})]})]}),c.jsx(Wn,{children:c.jsx(Bn,{type:"single",value:t,onValueChange:o=>{n(o)},collapsible:!0,className:"w-full mt-1",children:e.map(o=>c.jsxs(Vn,{value:o.id,className:"mb-2",children:[c.jsx(Un,{className:Q("no-underline hover:no-underline gap-0 justify-end flex-row-reverse py-1","[&>svg]:-rotate-90 [&[data-state=open]>svg]:rotate-0 cursor-pointer","mx-1 items-center ml-3 group"),children:c.jsx(my,{reviewState:o})}),c.jsx(Wn,{children:c.jsx(cy,{reviewState:o})})]},o.id))})})]})}):null},lb=({reviews:e})=>{const{currentReviewId:t}=v.useContext(pn),[n,r]=v.useState("");v.useEffect(()=>{t&&n!==t&&r(t)},[t]);const o=[...e].sort((a,l)=>{const u=new Date(a.startedAt).getTime();return new Date(l.startedAt).getTime()-u}),s=o.find(a=>a.id===t),i=()=>{Se.postMessage({type:"doReview"})};return e.length===0?c.jsxs("div",{className:"flex flex-col items-center justify-center py-8 h-full",children:[c.jsx("p",{className:"text-md text-muted-foreground mb-2",children:"No reviews found"}),c.jsx(Bt,{onClick:()=>{i()},children:"Start a full review"})]}):c.jsxs("div",{className:"w-full mt-1",children:[c.jsxs("div",{className:"flex items-center pr-2",children:[c.jsx("span",{className:"codicon codicon-versions !text-icon mx-1"}),c.jsx("span",{className:"text-sm uppercase font-bold",children:"Reviews"})]}),s&&c.jsx(Bn,{type:"single",collapsible:!0,value:n,onValueChange:a=>{r(a)},children:c.jsxs(Vn,{value:s.id,children:[c.jsx(Un,{className:Q("no-underline hover:no-underline gap-0 justify-end flex-row-reverse py-1","[&>svg]:-rotate-90 [&[data-state=open]>svg]:rotate-0 cursor-pointer","mx-1 items-center"),children:c.jsx(my,{reviewState:s})}),c.jsx(Wn,{children:c.jsx(cy,{reviewState:s})})]})}),c.jsx(ab,{olderReviews:o.filter(a=>a.id!==t),accordionValue:n,setAccordionValue:r})]})},op=()=>{const{git:{currentRepo:e}}=v.useContext(pn),t=()=>{Se.postMessage({type:we.switchRepo})};return c.jsx("div",{className:"flex flex-col gap-0.5 mt-3 mb-1",children:c.jsx(Bn,{type:"single",collapsible:!0,defaultValue:"project-details",children:c.jsxs(Vn,{value:"project-details",children:[c.jsx(Un,{className:Q("no-underline hover:no-underline justify-end gap-0 flex-row-reverse p-0 px-0.5 pr-2","[&>svg]:-rotate-90 [&[data-state=open]>svg]:rotate-0 cursor-pointer [&>svg]:-mt-0.5"),children:c.jsx("div",{className:"flex flex-row flex-wrap justify-between items-center w-full",children:c.jsx("span",{className:"text-sm uppercase font-bold tracking-tight",children:"Repository"})})}),c.jsx(Wn,{className:"pb-0",children:c.jsxs("div",{onClick:t,className:"flex flex-row items-center gap-1.5 flex-wrap pl-4 pr-2 mt-1 cursor-pointer",children:[c.jsx("span",{className:"codicon codicon-repo !text-sm grow-0"}),c.jsx("span",{children:e.split("/").pop()||""}),c.jsx("span",{className:"codicon codicon-edit !text-sm grow-0"})]})})]})})})},ub=()=>{const{auth:e,reviews:t,currentReviewId:n,git:{isInitialized:r,hasMultipleRepos:o,isConfigured:s},workspaces:i}=v.useContext(pn),a=t.find(d=>d.id===n),l=i.length>0,u=e.state==="success";return c.jsx("main",{className:"flex flex-col h-[100vh]",children:u?c.jsxs(c.Fragment,{children:[c.jsx(S_,{}),!l&&c.jsx(HE,{}),l&&s&&r&&c.jsxs(c.Fragment,{children:[!t.length&&!(a!=null&&a.id)&&c.jsxs("div",{className:"flex flex-col",children:[o&&c.jsx(op,{}),c.jsx(uk,{}),c.jsx(uy,{})]}),t.length>0&&c.jsxs("div",{className:"flex flex-col divide-y space-y-2 divide-[var(--vscode-settings-sashBorder)]",children:[c.jsx("div",{className:"pb-3 flex flex-col gap-y-2",children:o&&c.jsx(op,{})}),c.jsx(ZE,{}),c.jsx(lb,{reviews:t,activeReviewId:a==null?void 0:a.id})]})]}),l&&c.jsxs("div",{className:"flex flex-col gap-y-2",children:[!s&&c.jsx(zE,{}),s&&!r&&c.jsx(FE,{})]})]}):c.jsx(WE,{})})};function cb(){const[e,t]=v.useState({files:[]}),[n,r]=v.useState({current:null,base:null,isDefaultBranch:!1}),[o,s]=v.useState(!1),[i,a]=v.useState({state:"idle",user:null}),[l,u]=v.useState([]),[d,f]=v.useState(null),[p,y]=v.useState(!0),[w,g]=v.useState({isInitialized:!1,isConfigured:!1,hasMultipleRepos:!1,currentRepo:""}),[k,m]=v.useState({length:0}),[h,x]=v.useState("all"),[_,b]=v.useState(null);return v.useEffect(()=>{Se.postMessage({type:"syncState"});const N=S=>{const C=S.data;C.type===Ye.getAllState?(u(C.data.reviews),f(C.data.currentReviewId),g(C.data.git),m(C.data.workspaces),y(!1),s(!1),a(C.data.auth),x(C.data.reviewType),b(C.data.currentOrg)):C.type===Ye.onAuthenticationStateChange?(a(C.data.auth),s(!1)):C.type===Ye.syncReviewState?(u(C.data.reviews),s(!1),C.data.currentReviewId&&f(C.data.currentReviewId)):C.type===Ye.getFilesToReview?t(C.data):C.type===Ye.getBranchInfo?r(C.data):C.type===Ye.currentOrganization&&b(C.data.organization)};return window.addEventListener("message",N),()=>{window.removeEventListener("message",N)}},[]),c.jsx(pn.Provider,{value:{reviews:l,currentReviewId:d,filesToReview:e,branchInfo:n,git:w,workspaces:k,isBusy:o,setIsBusy:s,auth:i,reviewType:h,currentOrg:_},children:p?c.jsx("div",{className:"flex flex-col h-full w-full items-center justify-center",children:c.jsx("div",{className:"animate-spin codicon codicon-loading"})}):c.jsx(ub,{})})}const db=jl.createRoot(document.getElementById("root"));db.render(c.jsx(Ce.StrictMode,{children:c.jsx(cb,{})}));
