## Change Log

# 0.7.8

### Improvements

- Send local .coderabbit.yaml in review
- Show update notification when new version is available

### Fixes

- Fix auto review issue on default branch
- Fix issue in coderabbit line decorations

# 0.7.7

- Improved – Gracefully handle cases where Git is not enabled in the workspace.

# 0.7.6

- Fixed issues with detecting the correct base branch during reviews

# 0.7.5

- CodeRabbit now supports logging in using a token

# 0.7.4

- Bug fixes and performance improvements

## 0.7.3

- Support subfolders of parent git repo

## 0.7.2

- Bug fixes and performance improvements

## 0.7.1

- Fix bug related to reviews being skipped when a large number of files are excluded by the system

## 0.7.0

- Bug fixes and improvements

## 0.6.3

- Bug fixes

## 0.6.2

- Bug fixes and performance improvements

## 0.6.1

- Bug fixes and UI improvements

## 0.6.0

- CodeRabbit now works on uncommitted changes
- CodeRabbit can now automatically review your code when you commit changes
- Bug fixes and UI improvements

## 0.5.7

- You can now fix comments by CodeRabbit with other AI Agents

## 0.5.6

- Bug fixes and UI improvements

## 0.5.5

- Bug fixes and UI improvements

## 0.5.4

- Fixed an issue where the extension would lose connectivity to the server while a review was in progress
- Improved error handling and connection issues

## 0.5.3

- Fix an issue where reviews could get stuck waiting to start

## 0.5.0

- Show comments in editor

## 0.4.6

- Bug fixes and UI improvements

## 0.4.5

- Initial release
