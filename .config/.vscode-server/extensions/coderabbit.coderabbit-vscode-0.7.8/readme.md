# CodeRabbit: AI code reviews for VS Code

CodeRabbit delivers AI code reviews within your code editor. With CodeRabbit’s VS Code extension, developers can use the most advanced AI models to automate the first pass of code reviews without waiting for PR to be raised, saving review time, catching more bugs, and shipping more code.

## ✨ Why choose CodeRabbit

- Real-time code review with context-aware recommendations
- Detect and fix functional bugs, code smells, missed unit tests, security vulnerabilities, and more
- Customizable review instructions to meet your needs
- Multi-layered reviews: in the code editor and in the PRs
- Supports VS Code, Cursor and Windsurf
- Works with all programming languages

## 🚀 Key Features

- Line-by-line code reviews: each line of code gets senior developer-level attention with AI-powered inline review comments.
- Review uncommitted changes: each committed and uncommitted change is reviewed immediately, reducing number of comments in the PR
- One-click fix: use one-click fixes to easily incorporate review comments back into your codebase with just one-click.
- Fix with AI: automated hand-off of code changes associated context to your AI agent letting it know what changes to make.

## 🔄 Integrations

GitHub, GitLab, Bitbucket

## 🏁 Getting Started

1. Install the extension
2. Connect your CodeRabbit account
3. Start receiving intelligent suggestions

## ❓ Frequently Asked Questions

Q: Is CodeRabbit free to use?

A: CodeRabbit offers a free tier with limited reviews per day. Premium plans are available for teams and professionals requiring more extensive usage. Reach <NAME_EMAIL> for higher rate limits.

Q: How does CodeRabbit differ from other code review tools?

A: CodeRabbit uses advanced AI to understand context beyond simple linting, providing intelligent suggestions that consider your entire codebase and development patterns.

Q: What additional benefits do I get with the paid plans?

A: In addition to higher rate limits for reviews in the IDE, you also get reviews in PR/MR with centralized checks in your Git platforms. The PR reviews also come with additional integrations with Linters/SAST, project management tools, real-time web queries, code-graph analysis, and much more. These additional capabilities add more context and enrich the quality of reviews.

Q: How secure is my code with CodeRabbit?

A: Security is our priority. Your code is analyzed securely, never stored permanently, and all transmissions are encrypted.

---

[Learn More](http://coderabbit.ai/ide)

[Docs](https://docs.coderabbit.ai/code-editors)

[Support](https://discord.gg/coderabbit)
