// Test script to verify the orders table fix
import { Pool, neonConfig } from '@neondatabase/serverless';
import ws from 'ws';
import dotenv from 'dotenv';

// Configure WebSocket for Neon
neonConfig.webSocketConstructor = ws;

dotenv.config();

const connectionString = process.env.DATABASE_URL;

if (!connectionString) {
  console.error("DATABASE_URL must be set");
  process.exit(1);
}

async function testOrdersTable() {
  const client = new Pool({ connectionString });
  
  try {
    console.log("Testing orders table...");
    
    // Test selecting from orders table with order_details column
    const result = await client.query(`
      SELECT id, customer, items, order_details, subtotal, delivery_fee, total, status, payment_method, notes, created_at
      FROM orders 
      LIMIT 5;
    `);
    
    console.log("✅ Orders table query successful!");
    console.log(`Found ${result.rows.length} orders`);
    
    if (result.rows.length > 0) {
      console.log("Sample order:", {
        id: result.rows[0].id,
        customer: result.rows[0].customer,
        order_details: result.rows[0].order_details,
        status: result.rows[0].status
      });
    }
    
    // Test inserting a new order with order_details
    console.log("\nTesting order insertion...");
    const insertResult = await client.query(`
      INSERT INTO orders (customer, items, order_details, subtotal, delivery_fee, total, status, payment_method, notes)
      VALUES (
        '{"firstName": "Test", "lastName": "User", "email": "<EMAIL>", "phone": "12345678", "address": "Test St", "postalCode": "0001", "city": "Oslo"}',
        '[{"id": 1, "name": "Test Item", "price": 100, "quantity": 1}]',
        '{"type": "delivery", "time": "asap", "scheduledTime": null}',
        100,
        49,
        149,
        'pending',
        'card',
        'Test order'
      )
      RETURNING id;
    `);
    
    console.log("✅ Order insertion successful! New order ID:", insertResult.rows[0].id);
    
    // Clean up test order
    await client.query(`DELETE FROM orders WHERE id = $1`, [insertResult.rows[0].id]);
    console.log("✅ Test order cleaned up");
    
  } catch (error) {
    console.error("❌ Error testing orders table:", error);
  } finally {
    await client.end();
  }
}

testOrdersTable();
