-- Migration: Add orderDetails column to orders table
-- This migration adds the missing orderDetails JSONB column to the orders table

-- Add the orderDetails column
ALTER TABLE orders ADD COLUMN IF NOT EXISTS order_details JSONB;

-- Update existing orders to have default orderDetails
-- For existing orders, we'll set default values based on common patterns
UPDATE orders 
SET order_details = '{"type": "delivery", "time": "asap", "scheduledTime": null}'::jsonb
WHERE order_details IS NULL;

-- Make the column NOT NULL after setting default values
ALTER TABLE orders ALTER COLUMN order_details SET NOT NULL;
