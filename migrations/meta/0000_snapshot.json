{"id": "2370cd4e-23d6-4c7c-9af3-9ca4acd120d1", "prevId": "00000000-0000-0000-0000-000000000000", "version": "7", "dialect": "postgresql", "tables": {"public.categories": {"name": "categories", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "image_url": {"name": "image_url", "type": "text", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.contact_messages": {"name": "contact_messages", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "email": {"name": "email", "type": "text", "primaryKey": false, "notNull": true}, "subject": {"name": "subject", "type": "text", "primaryKey": false, "notNull": true}, "message": {"name": "message", "type": "text", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.customization_groups": {"name": "customization_groups", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "title": {"name": "title", "type": "text", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.customization_options": {"name": "customization_options", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "extra_price": {"name": "extra_price", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "image_url": {"name": "image_url", "type": "text", "primaryKey": false, "notNull": true, "default": "''"}, "group_id": {"name": "group_id", "type": "integer", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"customization_options_group_id_customization_groups_id_fk": {"name": "customization_options_group_id_customization_groups_id_fk", "tableFrom": "customization_options", "tableTo": "customization_groups", "columnsFrom": ["group_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.menu_items": {"name": "menu_items", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": true}, "price": {"name": "price", "type": "integer", "primaryKey": false, "notNull": true}, "image_url": {"name": "image_url", "type": "text", "primaryKey": false, "notNull": true}, "category_id": {"name": "category_id", "type": "integer", "primaryKey": false, "notNull": true}, "available": {"name": "available", "type": "boolean", "primaryKey": false, "notNull": false, "default": true}, "rating": {"name": "rating", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "reviews": {"name": "reviews", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}}, "indexes": {}, "foreignKeys": {"menu_items_category_id_categories_id_fk": {"name": "menu_items_category_id_categories_id_fk", "tableFrom": "menu_items", "tableTo": "categories", "columnsFrom": ["category_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.item_customization_map": {"name": "item_customization_map", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "item_id": {"name": "item_id", "type": "integer", "primaryKey": false, "notNull": true}, "option_id": {"name": "option_id", "type": "integer", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"item_customization_map_item_id_menu_items_id_fk": {"name": "item_customization_map_item_id_menu_items_id_fk", "tableFrom": "item_customization_map", "tableTo": "menu_items", "columnsFrom": ["item_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "item_customization_map_option_id_customization_options_id_fk": {"name": "item_customization_map_option_id_customization_options_id_fk", "tableFrom": "item_customization_map", "tableTo": "customization_options", "columnsFrom": ["option_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.orders": {"name": "orders", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "customer": {"name": "customer", "type": "jsonb", "primaryKey": false, "notNull": true}, "items": {"name": "items", "type": "jsonb", "primaryKey": false, "notNull": true}, "subtotal": {"name": "subtotal", "type": "integer", "primaryKey": false, "notNull": true}, "delivery_fee": {"name": "delivery_fee", "type": "integer", "primaryKey": false, "notNull": true}, "total": {"name": "total", "type": "integer", "primaryKey": false, "notNull": true}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": true, "default": "'pending'"}, "payment_method": {"name": "payment_method", "type": "text", "primaryKey": false, "notNull": true}, "notes": {"name": "notes", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.users": {"name": "users", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "username": {"name": "username", "type": "text", "primaryKey": false, "notNull": true}, "email": {"name": "email", "type": "text", "primaryKey": false, "notNull": true}, "password": {"name": "password", "type": "text", "primaryKey": false, "notNull": true}, "first_name": {"name": "first_name", "type": "text", "primaryKey": false, "notNull": false}, "last_name": {"name": "last_name", "type": "text", "primaryKey": false, "notNull": false}, "role": {"name": "role", "type": "text", "primaryKey": false, "notNull": true, "default": "'customer'"}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"users_username_unique": {"name": "users_username_unique", "nullsNotDistinct": false, "columns": ["username"]}, "users_email_unique": {"name": "users_email_unique", "nullsNotDistinct": false, "columns": ["email"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}}, "enums": {}, "schemas": {}, "sequences": {}, "roles": {}, "policies": {}, "views": {}, "_meta": {"columns": {}, "schemas": {}, "tables": {}}}