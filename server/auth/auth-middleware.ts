import { Request, Response, NextFunction } from 'express';

// Middleware to check if user is authenticated
export const isAuthenticated = (req: Request, res: Response, next: NextFunction) => {
  // In a real application, this would check for a valid session or JWT token
  // For this demo, we'll just check if the user object exists in the request
  if (req.user) {
    return next();
  }
  return res.status(401).json({ message: 'Unauthorized' });
};

// Middleware to check if user has admin role
export const isAdmin = (req: Request, res: Response, next: NextFunction) => {
  // First check if authenticated
  if (!req.user) {
    return res.status(401).json({ message: 'Unauthorized' });
  }
  
  // Then check if user has admin role
  if ((req.user as any).role === 'admin') {
    return next();
  }
  
  return res.status(403).json({ message: 'Forbidden: Admin access required' });
};

// Middleware to check if user has manager role
export const isManager = (req: Request, res: Response, next: NextFunction) => {
  // First check if authenticated
  if (!req.user) {
    return res.status(401).json({ message: 'Unauthorized' });
  }
  
  // Then check if user has manager role
  if ((req.user as any).role === 'manager') {
    return next();
  }
  
  return res.status(403).json({ message: 'Forbidden: Manager access required' });
};

// Middleware to check if user has driver role
export const isDriver = (req: Request, res: Response, next: NextFunction) => {
  // First check if authenticated
  if (!req.user) {
    return res.status(401).json({ message: 'Unauthorized' });
  }
  
  // Then check if user has driver role
  if ((req.user as any).role === 'driver') {
    return next();
  }
  
  return res.status(403).json({ message: 'Forbidden: Driver access required' });
};
