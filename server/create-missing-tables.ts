import { db } from "./db";

async function createMissingTables() {
  try {
    console.log("Creating missing database tables...");

    // Create contact_messages table
    try {
      await db.execute(`
        CREATE TABLE IF NOT EXISTS contact_messages (
          id SERIAL PRIMARY KEY,
          name TEXT NOT NULL,
          email TEXT NOT NULL,
          subject TEXT NOT NULL,
          message TEXT NOT NULL,
          created_at TIMESTAMP DEFAULT NOW()
        )
      `);
      console.log("✓ Created contact_messages table");
    } catch (error) {
      console.log(`Error creating contact_messages: ${error.message}`);
    }

    // Create customization_groups table
    try {
      await db.execute(`
        CREATE TABLE IF NOT EXISTS customization_groups (
          id SERIAL PRIMARY KEY,
          title TEXT NOT NULL
        )
      `);
      console.log("✓ Created customization_groups table");
    } catch (error) {
      console.log(`Error creating customization_groups: ${error.message}`);
    }

    // Create customization_options table
    try {
      await db.execute(`
        CREATE TABLE IF NOT EXISTS customization_options (
          id SERIAL PRIMARY KEY,
          name TEXT NOT NULL,
          extra_price INTEGER DEFAULT 0,
          image_url TEXT NOT NULL DEFAULT '',
          group_id INTEGER NOT NULL REFERENCES customization_groups(id)
        )
      `);
      console.log("✓ Created customization_options table");
    } catch (error) {
      console.log(`Error creating customization_options: ${error.message}`);
    }

    // Create item_customization_map table
    try {
      await db.execute(`
        CREATE TABLE IF NOT EXISTS item_customization_map (
          id SERIAL PRIMARY KEY,
          item_id INTEGER NOT NULL REFERENCES menu_items(id),
          option_id INTEGER NOT NULL REFERENCES customization_options(id)
        )
      `);
      console.log("✓ Created item_customization_map table");
    } catch (error) {
      console.log(`Error creating item_customization_map: ${error.message}`);
    }

    console.log("\nAll missing tables created successfully!");

  } catch (error) {
    console.error("Error creating tables:", error);
    throw error;
  }
}

// Run if this file is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  createMissingTables()
    .then(() => {
      console.log("Table creation completed!");
      process.exit(0);
    })
    .catch((error) => {
      console.error("Table creation failed:", error);
      process.exit(1);
    });
}

export { createMissingTables };
