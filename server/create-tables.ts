import { db } from "./db";
import { 
  categories, menuItems, orders, contactMessages, users,
  customizationGroups, customizationOptions, itemCustomizationMap
} from "@shared/schema";

async function createMissingTables() {
  try {
    console.log("Creating missing database tables...");

    // Check if tables exist by trying to query them
    const tablesToCheck = [
      { name: 'contact_messages', table: contactMessages },
      { name: 'customization_groups', table: customizationGroups },
      { name: 'customization_options', table: customizationOptions },
      { name: 'item_customization_map', table: itemCustomizationMap },
      { name: 'menu_items', table: menuItems },
      { name: 'categories', table: categories },
      { name: 'orders', table: orders },
      { name: 'users', table: users }
    ];

    for (const { name, table } of tablesToCheck) {
      try {
        await db.select().from(table).limit(1);
        console.log(`✓ Table ${name} exists`);
      } catch (error) {
        console.log(`✗ Table ${name} does not exist or has schema issues`);
        console.log(`Error: ${error.message}`);
      }
    }

    // Try to create missing columns in menu_items table
    try {
      console.log("\nAttempting to add missing columns to menu_items...");
      
      // Check if rating column exists
      try {
        await db.execute(`ALTER TABLE menu_items ADD COLUMN IF NOT EXISTS rating integer DEFAULT 0`);
        console.log("✓ Added rating column to menu_items");
      } catch (error) {
        console.log(`Rating column: ${error.message}`);
      }

      // Check if reviews column exists
      try {
        await db.execute(`ALTER TABLE menu_items ADD COLUMN IF NOT EXISTS reviews integer DEFAULT 0`);
        console.log("✓ Added reviews column to menu_items");
      } catch (error) {
        console.log(`Reviews column: ${error.message}`);
      }

    } catch (error) {
      console.log(`Error modifying menu_items table: ${error.message}`);
    }

    console.log("\nTable creation check completed!");

  } catch (error) {
    console.error("Error creating tables:", error);
    throw error;
  }
}

// Run if this file is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  createMissingTables()
    .then(() => {
      console.log("Table creation completed!");
      process.exit(0);
    })
    .catch((error) => {
      console.error("Table creation failed:", error);
      process.exit(1);
    });
}

export { createMissingTables };
